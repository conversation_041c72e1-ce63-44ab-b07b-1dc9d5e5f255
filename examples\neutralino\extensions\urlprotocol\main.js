// Neutralino URL协议处理扩展

let NL_APPID = "";
let NL_PORT = -1;
let NL_EXTENABLED = false;
let NL_EXTONMESSAGE = null;

// 初始化扩展
function __NL_INIT_EXTENSION(appId, port) {
  NL_APPID = appId;
  NL_PORT = port;
  NL_EXTENABLED = true;
  
  // 发送就绪消息
  __NL_POST_MESSAGE({
    event: "urlProtocolReady",
    data: {}
  });
}

// 发送消息到主应用
function __NL_POST_MESSAGE(message) {
  if(!NL_EXTENABLED) {
    console.error('扩展未启用');
    return;
  }
  
  fetch(`http://localhost:${NL_PORT}/extensions-messaging`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      id: NL_APPID,
      extensionId: "js.neutralino.urlprotocol",
      ...message
    })
  }).catch((err) => console.error('扩展消息发送失败:', err));
}

// 设置消息处理函数
function __NL_PROCESS_MESSAGE(message) {
  if(NL_EXTONMESSAGE) {
    NL_EXTONMESSAGE(message);
  }
}

// 注册URL协议处理器
async function registerProtocolHandler(protocol, appPath) {
  if(!NL_EXTENABLED) {
    return { success: false, error: "扩展未启用" };
  }
  
  try {
    // 根据不同平台注册协议
    if(process.platform === "win32") {
      // Windows平台使用注册表
      const { execSync } = require('child_process');
      const regContent = `Windows Registry Editor Version 5.00\n\n[HKEY_CLASSES_ROOT\\${protocol}]\n@="URL:${protocol} Protocol"\n"URL Protocol"=""\n\n[HKEY_CLASSES_ROOT\\${protocol}\\DefaultIcon]\n@="${appPath.replace(/\\/g, '\\\\')}"\n\n[HKEY_CLASSES_ROOT\\${protocol}\\shell]\n\n[HKEY_CLASSES_ROOT\\${protocol}\\shell\\open]\n\n[HKEY_CLASSES_ROOT\\${protocol}\\shell\\open\\command]\n@="\"${appPath.replace(/\\/g, '\\\\')}\" \"%1\""\n`;
      
      // 创建临时注册表文件
      const fs = require('fs');
      const path = require('path');
      const tempFile = path.join(require('os').tmpdir(), `${protocol}_protocol.reg`);
      fs.writeFileSync(tempFile, regContent);
      
      // 导入注册表
      execSync(`regedit /s "${tempFile}"`);
      
      // 删除临时文件
      fs.unlinkSync(tempFile);
      
      return { success: true };
    }
    else if(process.platform === "darwin") {
      // macOS平台使用Info.plist
      const { execSync } = require('child_process');
      const plistBuddy = '/usr/libexec/PlistBuddy';
      const appPlist = `${appPath}/Contents/Info.plist`;
      
      // 检查Info.plist是否存在
      const fs = require('fs');
      if(!fs.existsSync(appPlist)) {
        return { success: false, error: "Info.plist文件不存在" };
      }
      
      // 添加URL类型
      try {
        execSync(`${plistBuddy} -c "Add :CFBundleURLTypes array" "${appPlist}"`);
      } catch(e) {
        // 可能已经存在，忽略错误
      }
      
      try {
        execSync(`${plistBuddy} -c "Add :CFBundleURLTypes:0 dict" "${appPlist}"`);
        execSync(`${plistBuddy} -c "Add :CFBundleURLTypes:0:CFBundleURLName string ${protocol}" "${appPlist}"`);
        execSync(`${plistBuddy} -c "Add :CFBundleURLTypes:0:CFBundleURLSchemes array" "${appPlist}"`);
        execSync(`${plistBuddy} -c "Add :CFBundleURLTypes:0:CFBundleURLSchemes:0 string ${protocol}" "${appPlist}"`);
      } catch(e) {
        // 可能已经存在，尝试更新
        try {
          execSync(`${plistBuddy} -c "Set :CFBundleURLTypes:0:CFBundleURLName ${protocol}" "${appPlist}"`);
          execSync(`${plistBuddy} -c "Set :CFBundleURLTypes:0:CFBundleURLSchemes:0 ${protocol}" "${appPlist}"`);
        } catch(e) {
          return { success: false, error: `更新Info.plist失败: ${e.message}` };
        }
      }
      
      return { success: true };
    }
    else if(process.platform === "linux") {
      // Linux平台使用.desktop文件
      const { execSync } = require('child_process');
      const fs = require('fs');
      const path = require('path');
      const os = require('os');
      
      // 获取应用名称
      const appName = path.basename(appPath, path.extname(appPath));
      
      // 创建.desktop文件内容
      const desktopContent = `[Desktop Entry]\nName=${appName}\nExec=${appPath} %u\nType=Application\nMimeType=x-scheme-handler/${protocol};\nTerminal=false\nCategories=Network;\n`;
      
      // 保存.desktop文件
      const desktopPath = path.join(os.homedir(), '.local', 'share', 'applications', `${appName}-${protocol}.desktop`);
      
      // 确保目录存在
      const desktopDir = path.dirname(desktopPath);
      if(!fs.existsSync(desktopDir)) {
        fs.mkdirSync(desktopDir, { recursive: true });
      }
      
      fs.writeFileSync(desktopPath, desktopContent);
      
      // 注册MIME类型
      execSync(`xdg-mime default ${path.basename(desktopPath)} x-scheme-handler/${protocol}`);
      
      // 更新桌面数据库
      try {
        execSync('update-desktop-database');
      } catch(e) {
        // 某些系统可能没有这个命令，忽略错误
      }
      
      return { success: true };
    }
    else {
      return { success: false, error: "不支持的操作系统" };
    }
  }
  catch(error) {
    return { success: false, error: error.message };
  }
}

// 检查URL协议是否已注册
async function isProtocolRegistered(protocol) {
  if(!NL_EXTENABLED) {
    return { registered: false, error: "扩展未启用" };
  }
  
  try {
    // 根据不同平台检查协议
    if(process.platform === "win32") {
      // Windows平台检查注册表
      const { execSync } = require('child_process');
      try {
        execSync(`reg query HKEY_CLASSES_ROOT\\${protocol} /ve`, { stdio: 'ignore' });
        return { registered: true };
      } catch(e) {
        return { registered: false };
      }
    }
    else if(process.platform === "darwin") {
      // macOS平台检查默认应用
      const { execSync } = require('child_process');
      try {
        const result = execSync(`/usr/bin/defaults read com.apple.LaunchServices/com.apple.launchservices.secure LSHandlers | grep -A 3 "${protocol}:"`);
        return { registered: result.toString().trim().length > 0 };
      } catch(e) {
        return { registered: false };
      }
    }
    else if(process.platform === "linux") {
      // Linux平台检查MIME类型
      const { execSync } = require('child_process');
      try {
        const result = execSync(`xdg-mime query default x-scheme-handler/${protocol}`);
        return { registered: result.toString().trim().length > 0 };
      } catch(e) {
        return { registered: false };
      }
    }
    else {
      return { registered: false, error: "不支持的操作系统" };
    }
  }
  catch(error) {
    return { registered: false, error: error.message };
  }
}

// 注销URL协议处理器
async function unregisterProtocolHandler(protocol) {
  if(!NL_EXTENABLED) {
    return { success: false, error: "扩展未启用" };
  }
  
  try {
    // 根据不同平台注销协议
    if(process.platform === "win32") {
      // Windows平台使用注册表
      const { execSync } = require('child_process');
      try {
        execSync(`reg delete HKEY_CLASSES_ROOT\\${protocol} /f`, { stdio: 'ignore' });
        return { success: true };
      } catch(e) {
        return { success: false, error: e.message };
      }
    }
    else if(process.platform === "darwin") {
      // macOS平台使用defaults
      const { execSync } = require('child_process');
      try {
        // 这里的实现比较复杂，需要修改LSHandlers的plist文件
        // 简化处理，提示用户手动操作
        return { success: false, error: "macOS平台需要手动注销协议处理器，请在系统偏好设置中修改" };
      } catch(e) {
        return { success: false, error: e.message };
      }
    }
    else if(process.platform === "linux") {
      // Linux平台删除.desktop文件
      const fs = require('fs');
      const path = require('path');
      const os = require('os');
      const { execSync } = require('child_process');
      
      // 查找相关的.desktop文件
      const applicationsDir = path.join(os.homedir(), '.local', 'share', 'applications');
      const files = fs.readdirSync(applicationsDir);
      
      let found = false;
      for(const file of files) {
        if(file.endsWith('.desktop')) {
          const content = fs.readFileSync(path.join(applicationsDir, file), 'utf8');
          if(content.includes(`x-scheme-handler/${protocol}`)) {
            fs.unlinkSync(path.join(applicationsDir, file));
            found = true;
          }
        }
      }
      
      if(found) {
        // 更新桌面数据库
        try {
          execSync('update-desktop-database');
        } catch(e) {
          // 某些系统可能没有这个命令，忽略错误
        }
        return { success: true };
      } else {
        return { success: false, error: "未找到协议处理器" };
      }
    }
    else {
      return { success: false, error: "不支持的操作系统" };
    }
  }
  catch(error) {
    return { success: false, error: error.message };
  }
}

// 处理扩展命令
NL_EXTONMESSAGE = (message) => {
  const { event, data } = message;
  
  if(event === "urlprotocol.register") {
    registerProtocolHandler(data.protocol, data.appPath)
      .then(result => __NL_POST_MESSAGE({
        event: "urlprotocol.register.response",
        data: result
      }))
      .catch(error => __NL_POST_MESSAGE({
        event: "urlprotocol.register.response",
        data: { success: false, error: error.message }
      }));
  }
  else if(event === "urlprotocol.unregister") {
    unregisterProtocolHandler(data.protocol)
      .then(result => __NL_POST_MESSAGE({
        event: "urlprotocol.unregister.response",
        data: result
      }))
      .catch(error => __NL_POST_MESSAGE({
        event: "urlprotocol.unregister.response",
        data: { success: false, error: error.message }
      }));
  }
  else if(event === "urlprotocol.isRegistered") {
    isProtocolRegistered(data.protocol)
      .then(result => __NL_POST_MESSAGE({
        event: "urlprotocol.isRegistered.response",
        data: result
      }))
      .catch(error => __NL_POST_MESSAGE({
        event: "urlprotocol.isRegistered.response",
        data: { registered: false, error: error.message }
      }));
  }
};