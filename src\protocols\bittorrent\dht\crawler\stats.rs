use std::time::{Duration, Instant};
use tokio::sync::RwLock;

/// DHT爬虫统计
#[derive(Debug, <PERSON>lone)]
pub struct DHTCrawlerStats {
    /// 已发现的节点数
    pub nodes_discovered: u64,
    /// 已爬取的节点数
    pub nodes_crawled: u64,
    /// 已发现的信息哈希数
    pub infohashes_discovered: u64,
    /// 已处理的请求数
    pub requests_processed: u64,
    /// 请求成功数
    pub requests_succeeded: u64,
    /// 请求失败数
    pub requests_failed: u64,
    /// 请求超时数
    pub requests_timeout: u64,
    /// 开始时间
    pub start_time: Instant,
    /// 每秒发现的信息哈希数
    pub infohashes_per_second: f64,
    /// 每秒发现的节点数
    pub nodes_per_second: f64,
}

impl Default for DHTCrawlerStats {
    fn default() -> Self {
        Self {
            nodes_discovered: 0,
            nodes_crawled: 0,
            infohashes_discovered: 0,
            requests_processed: 0,
            requests_succeeded: 0,
            requests_failed: 0,
            requests_timeout: 0,
            start_time: Instant::now(),
            infohashes_per_second: 0.0,
            nodes_per_second: 0.0,
        }
    }
}

impl DHTCrawlerStats {
    /// 更新统计
    pub fn update(&mut self) {
        let elapsed = self.start_time.elapsed().as_secs_f64();
        if elapsed > 0.0 {
            self.infohashes_per_second = self.infohashes_discovered as f64 / elapsed;
            self.nodes_per_second = self.nodes_discovered as f64 / elapsed;
        }
    }
    
    /// 获取运行时间
    pub fn uptime(&self) -> Duration {
        self.start_time.elapsed()
    }
    
    /// 获取运行时间（格式化）
    pub fn uptime_formatted(&self) -> String {
        let secs = self.start_time.elapsed().as_secs();
        let hours = secs / 3600;
        let minutes = (secs % 3600) / 60;
        let seconds = secs % 60;
        
        format!("{}h {}m {}s", hours, minutes, seconds)
    }
}

/// 线程安全的DHT爬虫统计
pub struct SafeDHTCrawlerStats {
    /// 内部统计
    stats: RwLock<DHTCrawlerStats>,
}

impl SafeDHTCrawlerStats {
    /// 创建新的线程安全统计
    pub fn new() -> Self {
        Self {
            stats: RwLock::new(DHTCrawlerStats::default()),
        }
    }
    
    /// 获取统计快照
    pub async fn get_stats(&self) -> DHTCrawlerStats {
        let mut stats = self.stats.write().await;
        stats.update();
        stats.clone()
    }
    
    /// 增加已发现的节点数
    pub async fn increment_nodes_discovered(&self, count: u64) {
        let mut stats = self.stats.write().await;
        stats.nodes_discovered += count;
    }
    
    /// 增加已爬取的节点数
    pub async fn increment_nodes_crawled(&self, count: u64) {
        let mut stats = self.stats.write().await;
        stats.nodes_crawled += count;
    }
    
    /// 增加已发现的信息哈希数
    pub async fn increment_infohashes_discovered(&self, count: u64) {
        let mut stats = self.stats.write().await;
        stats.infohashes_discovered += count;
    }
    
    /// 增加已处理的请求数
    pub async fn increment_requests_processed(&self, count: u64) {
        let mut stats = self.stats.write().await;
        stats.requests_processed += count;
    }
    
    /// 增加请求成功数
    pub async fn increment_requests_succeeded(&self, count: u64) {
        let mut stats = self.stats.write().await;
        stats.requests_succeeded += count;
    }
    
    /// 增加请求失败数
    pub async fn increment_requests_failed(&self, count: u64) {
        let mut stats = self.stats.write().await;
        stats.requests_failed += count;
    }
    
    /// 增加请求超时数
    pub async fn increment_requests_timeout(&self, count: u64) {
        let mut stats = self.stats.write().await;
        stats.requests_timeout += count;
    }
    
    /// 重置统计
    pub async fn reset(&self) {
        let mut stats = self.stats.write().await;
        *stats = DHTCrawlerStats::default();
    }
}
