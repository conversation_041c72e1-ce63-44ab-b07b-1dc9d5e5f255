use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::fmt::{Display, Formatter};
use std::any::Any;
use uuid::Uuid;

use crate::core::error::CoreResult;

/// Download protocol type
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ProtocolType {
    Http,
    Https,
    Ftp,
    BitTorrent,
    Magnet,
    P2P,
    R2, // 将在未来整合
    Custom(u8),
}

impl Display for ProtocolType {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            ProtocolType::Http => write!(f, "HTTP"),
            ProtocolType::Https => write!(f, "HTTPS"),
            ProtocolType::Ftp => write!(f, "FTP"),
            ProtocolType::BitTorrent => write!(f, "BitTorrent"),
            ProtocolType::Magnet => write!(f, "Magnet"),
            ProtocolType::P2P => write!(f, "P2P"),
            ProtocolType::R2 => write!(f, "R2"), // 将在未来整合
            ProtocolType::Custom(id) => write!(f, "Custom({})", id),
        }
    }
}

/// Download status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum DownloadStatus {
    Pending,
    Initializing,
    Downloading,
    Paused,
    Completed,
    Failed,
    Cancelled,
}

impl Display for DownloadStatus {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            DownloadStatus::Pending => write!(f, "Pending"),
            DownloadStatus::Initializing => write!(f, "Initializing"),
            DownloadStatus::Downloading => write!(f, "Downloading"),
            DownloadStatus::Paused => write!(f, "Paused"),
            DownloadStatus::Completed => write!(f, "Completed"),
            DownloadStatus::Failed => write!(f, "Failed"),
            DownloadStatus::Cancelled => write!(f, "Cancelled"),
        }
    }
}

/// Download progress information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadProgress {
    pub total_size: Option<u64>,
    pub downloaded_size: u64,
    pub progress_percentage: f64,
    pub speed: u64,  // bytes per second
    pub eta: Option<u64>,  // estimated time remaining in seconds
}

/// Download options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadOptions {
    pub connections: Option<u32>,
    pub speed_limit: Option<u64>,
    pub headers: Option<std::collections::HashMap<String, String>>,
    pub timeout: Option<u64>,
    pub retry_count: Option<u32>,
    pub use_mirrors: Option<bool>,
    pub verify_checksum: Option<bool>,
    pub checksum: Option<String>,
    pub checksum_algorithm: Option<String>,
    pub custom_options: Option<std::collections::HashMap<String, String>>,
}

impl Default for DownloadOptions {
    fn default() -> Self {
        Self {
            connections: None,
            speed_limit: None,
            headers: None,
            timeout: None,
            retry_count: None,
            use_mirrors: None,
            verify_checksum: None,
            checksum: None,
            checksum_algorithm: None,
            custom_options: None,
        }
    }
}

/// Downloader interface
/// This is the main interface for all download protocols
#[async_trait]
pub trait Downloader: Send + Sync {
    /// Get the protocol type of the downloader
    /// 
    /// This method returns the protocol type that this downloader instance handles.
    /// It's used by the download manager to route download requests to the appropriate
    /// downloader implementation based on the URL protocol.
    /// 
    /// # Returns
    /// 
    /// A `ProtocolType` enum value indicating which protocol this downloader handles
    /// 
    /// # Example Implementation
    /// 
    /// ```rust
    /// fn protocol_type(&self) -> ProtocolType {
    ///     ProtocolType::Http // or other protocol type
    /// }
    /// ```
    fn protocol_type(&self) -> ProtocolType;
    
    /// Check if the downloader supports the given URL
    fn supports_url(&self, url: &str) -> bool;
    
    /// Initialize the download
    async fn init(&mut self, url: &str, output_path: &str, options: &DownloadOptions) -> CoreResult<()>;
    
    /// Start the download
    async fn start(&mut self) -> CoreResult<()>;
    
    /// Pause the download
    async fn pause(&mut self) -> CoreResult<()>;
    
    /// Resume the download
    async fn resume(&mut self) -> CoreResult<()>;
    
    /// Cancel the download
    async fn cancel(&mut self) -> CoreResult<()>;
    
    /// Get the download progress
    async fn progress(&self) -> CoreResult<DownloadProgress>;
    
    /// Get the download status
    async fn status(&self) -> CoreResult<DownloadStatus>;
    
    /// Get the unique download task ID
    /// 
    /// This method returns the unique identifier for this download task.
    /// The ID is used by the download manager to track and manage download tasks,
    /// and is typically assigned when the downloader is created.
    /// 
    /// # Returns
    /// 
    /// A `Uuid` that uniquely identifies this download task
    /// 
    /// # Example Implementation
    /// 
    /// ```rust
    /// fn id(&self) -> Uuid {
    ///     self.task_id // Return the stored task ID
    /// }
    /// ```
    fn id(&self) -> Uuid;
    
    /// Get the download URL
    fn url(&self) -> &str;
    
    /// Get the output path
    fn output_path(&self) -> &str;
    
    /// Clone the downloader and return it as a boxed trait object
    fn clone_box(&self) -> Box<dyn Downloader>;
    
    /// 获取下载速度（字节/秒）
    async fn speed(&self) -> CoreResult<u64> {
        // 默认实现：从 progress 中获取速度
        let progress = self.progress().await?;
        Ok(progress.speed)
    }
    
    /// 获取总数据大小
    async fn get_total_size(&self) -> CoreResult<Option<u64>> {
        // 默认实现：从 progress 中获取总大小
        let progress = self.progress().await?;
        Ok(progress.total_size)
    }
    
    /// 获取已下载数据大小
    async fn get_downloaded_size(&self) -> CoreResult<u64> {
        // 默认实现：从 progress 中获取已下载大小
        let progress = self.progress().await?;
        Ok(progress.downloaded_size)
    }
    
    /// 将自身转换为 Any 类型，用于类型转换
    fn as_any(&self) -> &dyn Any;
    
    /// 设置下载速度限制
    async fn set_download_limit(&mut self, _limit: Option<u64>) -> CoreResult<()> {
        // 默认实现：不做任何事情
        Ok(())
    }
    
    /// 设置上传速度限制
    async fn set_upload_limit(&mut self, _limit: Option<u64>) -> CoreResult<()> {
        // 默认实现：不做任何事情
        Ok(())
    }
}

/// Downloader factory interface
/// This is used to create downloaders for different protocols
#[async_trait]
pub trait DownloaderFactory: Send + Sync {
    /// Create a downloader for the given URL
    async fn create_downloader(&self, url: &str, output_path: &str, task_id: Uuid) -> CoreResult<Box<dyn Downloader>>;
    
    /// Get the supported protocols
    fn supported_protocols(&self) -> Vec<ProtocolType>;
}
