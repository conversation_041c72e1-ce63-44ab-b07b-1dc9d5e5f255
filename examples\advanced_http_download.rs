//! 高级HTTP下载功能示例
//! 
//! 本示例展示了如何使用HTTP下载器的高级功能：
//! - 多线程分片下载
//! - 增强的元数据存储
//! - 性能优化策略

use std::sync::Arc;
use anyhow::Result;
use tokio;
use uuid::Uuid;

use tonitru_downloader_rust::config::{ConfigManager, Settings};
use tonitru_downloader_rust::protocols::http::HttpDownloader;
use tonitru_downloader_rust::download::resume::ResumeManagerImpl;
use tonitru_downloader_rust::storage::storage_impl::LocalStorage;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::init();

    println!("🚀 高级HTTP下载功能演示");
    println!("========================");

    // 示例1: 多线程下载大文件
    println!("\n📦 示例1: 多线程下载大文件");
    multi_thread_download_example().await?;

    // 示例2: 性能优化和自适应调整
    println!("\n⚡ 示例2: 性能优化和自适应调整");
    performance_optimization_example().await?;

    // 示例3: 元数据管理和下载报告
    println!("\n📊 示例3: 元数据管理和下载报告");
    metadata_management_example().await?;

    // 示例4: 智能重试和错误处理
    println!("\n🔄 示例4: 智能重试和错误处理");
    smart_retry_example().await?;

    println!("\n✅ 所有示例执行完成！");
    Ok(())
}

/// 示例1: 多线程下载大文件
async fn multi_thread_download_example() -> Result<()> {
    // 创建配置
    let mut settings = Settings::default();
    settings.download.max_concurrent_chunks = Some(8); // 8个并发块
    settings.download.path = "./downloads".to_string();
    
    let config_manager = Arc::new(ConfigManager::with_settings(settings.clone()));
    let storage = Arc::new(LocalStorage::new(settings.download.path.clone()));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings, storage));

    // 创建下载器
    let task_id = Uuid::new_v4();
    let url = "https://releases.ubuntu.com/20.04/ubuntu-20.04.6-desktop-amd64.iso".to_string();
    let output_path = "ubuntu-20.04.6-desktop-amd64.iso".to_string();

    let mut downloader = HttpDownloader::new(url.clone(), output_path.clone(), config_manager, task_id)
        .with_resume_manager(resume_manager)
        .with_multi_thread(true, Some(8)) // 启用多线程，最大8个并发
        .with_buffer_size(16 * 1024 * 1024); // 16MB缓冲区

    println!("📥 开始多线程下载: {}", url);
    println!("📁 保存路径: {}", output_path);
    println!("🧵 并发数: 8");

    // 初始化下载器
    downloader.init().await?;

    // 检查是否支持多线程下载
    if downloader.supports_range_requests() {
        println!("✅ 服务器支持范围请求，将使用多线程下载");
    } else {
        println!("⚠️  服务器不支持范围请求，将使用单线程下载");
    }

    // 开始下载
    match downloader.download().await {
        Ok(_) => println!("✅ 下载完成！"),
        Err(e) => println!("❌ 下载失败: {}", e),
    }

    Ok(())
}

/// 示例2: 性能优化和自适应调整
async fn performance_optimization_example() -> Result<()> {
    let settings = Settings::default();
    let config_manager = Arc::new(ConfigManager::with_settings(settings.clone()));
    let storage = Arc::new(LocalStorage::new(settings.download.path.clone()));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings, storage));

    let task_id = Uuid::new_v4();
    let url = "https://httpbin.org/bytes/52428800".to_string(); // 50MB测试文件
    let output_path = "performance_test.bin".to_string();

    let mut downloader = HttpDownloader::new(url.clone(), output_path, config_manager, task_id)
        .with_resume_manager(resume_manager)
        .with_multi_thread(true, Some(4));

    println!("📥 开始性能优化下载测试");
    
    // 初始化下载器
    downloader.init().await?;

    // 模拟性能监控
    let start_time = std::time::Instant::now();
    
    // 开始下载
    match downloader.download().await {
        Ok(_) => {
            let elapsed = start_time.elapsed();
            println!("✅ 下载完成！");
            println!("⏱️  总耗时: {:.2}秒", elapsed.as_secs_f64());
            
            // 获取下载报告
            if let Some(report) = downloader.get_download_report().await {
                println!("📊 下载报告:");
                println!("{}", report);
            }
        },
        Err(e) => println!("❌ 下载失败: {}", e),
    }

    Ok(())
}

/// 示例3: 元数据管理和下载报告
async fn metadata_management_example() -> Result<()> {
    let settings = Settings::default();
    let config_manager = Arc::new(ConfigManager::with_settings(settings.clone()));
    let storage = Arc::new(LocalStorage::new(settings.download.path.clone()));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings, storage));

    let task_id = Uuid::new_v4();
    let url = "https://httpbin.org/json".to_string();
    let output_path = "metadata_test.json".to_string();

    let mut downloader = HttpDownloader::new(url.clone(), output_path, config_manager, task_id)
        .with_resume_manager(resume_manager);

    println!("📥 开始元数据管理示例");

    // 初始化下载器
    downloader.init().await?;

    // 设置自定义元数据
    downloader.set_custom_metadata("user_agent".to_string(), "AdvancedDownloader/1.0".to_string()).await?;
    downloader.set_custom_metadata("download_source".to_string(), "example_app".to_string()).await?;
    downloader.set_custom_metadata("priority".to_string(), "high".to_string()).await?;

    println!("📝 已设置自定义元数据");

    // 开始下载
    match downloader.download().await {
        Ok(_) => {
            println!("✅ 下载完成！");
            
            // 获取详细的下载报告
            if let Some(report) = downloader.get_download_report().await {
                println!("\n📊 详细下载报告:");
                println!("{}", report);
            }
        },
        Err(e) => println!("❌ 下载失败: {}", e),
    }

    Ok(())
}

/// 示例4: 智能重试和错误处理
async fn smart_retry_example() -> Result<()> {
    let settings = Settings::default();
    let config_manager = Arc::new(ConfigManager::with_settings(settings.clone()));
    let storage = Arc::new(LocalStorage::new(settings.download.path.clone()));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings, storage));

    println!("🔄 智能重试策略演示");

    // 测试不同类型的错误重试策略
    let test_cases = vec![
        ("timeout", "连接超时"),
        ("connection", "连接错误"),
        ("404", "文件不存在"),
        ("network", "网络错误"),
    ];

    for (error_type, description) in test_cases {
        println!("\n🧪 测试错误类型: {} ({})", error_type, description);
        
        let task_id = Uuid::new_v4();
        let downloader = HttpDownloader::new(
            "https://example.com/test".to_string(),
            "test.bin".to_string(),
            config_manager.clone(),
            task_id
        );

        // 模拟不同重试次数的策略
        for retry_count in 1..=5 {
            let (delay, should_retry) = downloader.smart_retry_strategy(retry_count, error_type).await;
            
            println!("  重试 {}: 延迟 {:?}, 是否继续重试: {}", 
                    retry_count, delay, should_retry);
            
            if !should_retry {
                println!("  ❌ 达到最大重试次数或不可重试错误");
                break;
            }
        }
    }

    Ok(())
}

/// 网络质量评估示例
#[allow(dead_code)]
async fn network_quality_assessment_example() -> Result<()> {
    use tonitru_downloader_rust::protocols::http::performance_optimizer::{PerformanceStats, NetworkQuality};
    
    let settings = Settings::default();
    let config_manager = Arc::new(ConfigManager::with_settings(settings));
    let task_id = Uuid::new_v4();
    
    let downloader = HttpDownloader::new(
        "https://example.com/test".to_string(),
        "test.bin".to_string(),
        config_manager,
        task_id
    );

    println!("🌐 网络质量评估示例");

    // 模拟不同网络条件
    let test_scenarios = vec![
        ("优秀网络", 20 * 1024 * 1024, 30, 0.005), // 20MB/s, 30ms, 0.5%错误率
        ("良好网络", 8 * 1024 * 1024, 80, 0.02),   // 8MB/s, 80ms, 2%错误率
        ("一般网络", 2 * 1024 * 1024, 150, 0.08),  // 2MB/s, 150ms, 8%错误率
        ("较差网络", 800 * 1024, 300, 0.15),       // 800KB/s, 300ms, 15%错误率
        ("很差网络", 200 * 1024, 800, 0.30),       // 200KB/s, 800ms, 30%错误率
    ];

    for (scenario_name, speed, latency_ms, error_rate) in test_scenarios {
        let mut stats = PerformanceStats::default();
        
        // 添加模拟数据
        for _ in 0..10 {
            stats.add_speed(speed);
            stats.add_latency(std::time::Duration::from_millis(latency_ms));
            stats.add_error_rate(error_rate);
        }

        let quality = downloader.assess_network_quality(&stats).await;
        
        println!("📊 {} -> 评估结果: {:?}", scenario_name, quality);
        
        // 根据网络质量显示建议的优化参数
        match quality {
            NetworkQuality::Excellent => println!("  💡 建议: 使用10MB块大小, 32MB缓冲区, 8个并发"),
            NetworkQuality::Good => println!("  💡 建议: 使用5MB块大小, 16MB缓冲区, 6个并发"),
            NetworkQuality::Fair => println!("  💡 建议: 使用2MB块大小, 8MB缓冲区, 4个并发"),
            NetworkQuality::Poor => println!("  💡 建议: 使用1MB块大小, 4MB缓冲区, 2个并发"),
            NetworkQuality::VeryPoor => println!("  💡 建议: 使用512KB块大小, 2MB缓冲区, 1个并发"),
        }
    }

    Ok(())
}
