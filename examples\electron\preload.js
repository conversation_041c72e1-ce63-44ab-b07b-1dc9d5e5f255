const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 在window对象上暴露API给渲染进程使用
contextBridge.exposeInMainWorld('electronAPI', {
  // 通知主进程渲染进程已准备好
  notifyAppReady: () => ipcRenderer.send('app-ready'),
  
  // 监听下载URL事件
  onAddDownloadUrl: (callback) => {
    ipcRenderer.on('add-download-url', (event, url) => {
      callback(url);
    });
  }
});

// 当DOM加载完成时
window.addEventListener('DOMContentLoaded', () => {
  console.log('DOM加载完成，预加载脚本执行');
});