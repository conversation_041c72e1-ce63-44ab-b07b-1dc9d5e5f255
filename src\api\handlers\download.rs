use axum::{
    extract::{Path, State},
    Json,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error, warn};
use uuid::Uuid;

use crate::api::response::{ApiResponse, error_response};
use crate::api::ErrorCode;
use crate::download::manager::{TaskStatus, TaskInfo};

/// Add download request
#[derive(Debug, Deserialize)]
pub struct AddDownloadParams {
    pub url: String,
    pub output_path: Option<String>,
}

/// Add download response
#[derive(Debug, Serialize)]
pub struct AddDownloadResponse {
    pub task_id: Uuid,
}

/// Add a new download
use crate::api::state::AppState;

pub async fn add_download(
    State(app_state): State<AppState>,
    Json(params): Json<AddDownloadParams>,
) -> ApiResponse<AddDownloadResponse> {
    let download_manager = app_state.download_manager;

    info!("Adding download: url={}", params.url);

    // 创建一个新的TaskInfo对象
    let task = TaskInfo {
        id: Uuid::new_v4(),
        url: params.url,
        output_path: params.output_path.unwrap_or_else(|| "./downloads".to_string()),
        status: TaskStatus::Pending,
        progress: 0.0,
        speed: 0,
        total_size: None,
        downloaded_size: 0,
        uploaded_bytes: 0, // 添加上传字节数字段，初始值为0
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        error_message: None,
    };

    match download_manager.add_task(task).await {
        Ok(task_id) => {
            info!("Download added: task_id={}", task_id);
            ApiResponse::success(AddDownloadResponse { task_id })
        },
        Err(e) => {
            error!("Failed to add download: {}", e);
            error_response(e)
        },
    }
}

/// Start a download
pub async fn start_download(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
) -> ApiResponse<()> {
    let download_manager = app_state.download_manager;

    info!("Starting download: task_id={}", task_id);

    match download_manager.start_task(task_id).await {
        Ok(_) => {
            info!("Download started: task_id={}", task_id);
            ApiResponse::<()>::success_no_data()
        },
        Err(e) => {
            error!("Failed to start download: task_id={}, error={}", task_id, e);
            error_response(e)
        },
    }
}

/// Pause a download
pub async fn pause_download(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
) -> ApiResponse<()> {
    let download_manager = app_state.download_manager;

    info!("Pausing download: task_id={}", task_id);

    match download_manager.pause_task(task_id).await {
        Ok(_) => {
            info!("Download paused: task_id={}", task_id);
            ApiResponse::<()>::success_no_data()
        },
        Err(e) => {
            error!("Failed to pause download: task_id={}, error={}", task_id, e);
            error_response(e)
        },
    }
}

/// Resume a download
pub async fn resume_download(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
) -> ApiResponse<()> {
    let download_manager = app_state.download_manager;

    info!("Resuming download: task_id={}", task_id);

    // 先获取任务信息，检查任务是否存在
    match download_manager.get_task(task_id).await {
        Ok(task) => {
            // 检查任务状态
            if task.status != TaskStatus::Paused && task.status != TaskStatus::Pending {
                let status_str = format!("{}", task.status);
                warn!("Cannot resume task {}: current status is {}", task_id, status_str);
                return ApiResponse::<()>::error(
                    ErrorCode::InvalidState,
                    format!("任务当前状态为 {}，无法恢复。只有暂停或等待状态的任务可以恢复。", status_str)
                );
            }

            // 尝试恢复任务
            match download_manager.resume_task(task_id).await {
                Ok(_) => {
                    info!("Download resumed: task_id={}", task_id);
                    ApiResponse::<()>::success_no_data()
                },
                Err(e) => {
                    error!("Failed to resume download: task_id={}, error={}", task_id, e);
                    error_response(e)
                },
            }
        },
        Err(e) => {
            error!("Failed to get task info: task_id={}, error={}", task_id, e);
            error_response(e)
        }
    }
}

/// Cancel a download
pub async fn cancel_download(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
) -> ApiResponse<()> {
    let download_manager = app_state.download_manager;

    info!("Cancelling download: task_id={}", task_id);

    match download_manager.cancel_task(task_id).await {
        Ok(_) => {
            info!("Download cancelled: task_id={}", task_id);
            ApiResponse::<()>::success_no_data()
        },
        Err(e) => {
            error!("Failed to cancel download: task_id={}, error={}", task_id, e);
            error_response(e)
        },
    }
}

/// Download statistics response
#[derive(Debug, Serialize)]
pub struct DownloadStatsResponse {
    pub total_downloads: usize,
    pub active_downloads: usize,
    pub completed_downloads: usize,
    pub failed_downloads: usize,
    pub total_downloaded_bytes: u64,
    pub total_uploaded_bytes: u64,
}

/// Get download statistics
pub async fn get_download_stats(
    State(app_state): State<AppState>,
) -> ApiResponse<DownloadStatsResponse> {
    let download_manager = app_state.download_manager;

    let stats = download_manager.get_download_stats().await;

    ApiResponse::success(DownloadStatsResponse {
        total_downloads: stats.total_downloads,
        active_downloads: stats.active_downloads,
        completed_downloads: stats.completed_downloads,
        failed_downloads: stats.failed_downloads,
        total_downloaded_bytes: stats.total_downloaded_bytes,
        total_uploaded_bytes: stats.total_uploaded_bytes,
    })
}
