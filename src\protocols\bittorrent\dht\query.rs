use std::collections::{HashMap, HashSet};
use std::net::SocketAddr;
use std::time::Instant;
use anyhow::{Result, anyhow};
use tokio::sync::oneshot;

use super::node::{DHTNode, NodeId};
use super::message::{DHTMessageType, DHTResponse};

/// DHT查询
#[derive(Debug, Clone)]
pub struct DHTQuery {
    /// 查询ID（事务ID）
    pub query_id: Vec<u8>,
    /// 目标ID
    pub target_id: NodeId,
    /// 查询类型
    pub query_type: DHTMessageType,
    /// 已查询的节点
    pub queried_nodes: HashSet<NodeId>,
    /// 已响应的节点
    pub responded_nodes: HashSet<NodeId>,
    /// 找到的节点
    pub found_nodes: Vec<DHTNode>,
    /// 找到的对等点
    pub found_peers: Vec<SocketAddr>,
    /// 查询开始时间
    pub start_time: Instant,
}

impl DHTQuery {
    /// 创建新的DHT查询
    pub fn new(query_id: Vec<u8>, target_id: NodeId, query_type: DHTMessageType) -> Self {
        Self {
            query_id,
            target_id,
            query_type,
            queried_nodes: HashSet::new(),
            responded_nodes: HashSet::new(),
            found_nodes: Vec::new(),
            found_peers: Vec::new(),
            start_time: Instant::now(),
        }
    }

    /// 添加已查询的节点
    pub fn add_queried_node(&mut self, node_id: NodeId) {
        self.queried_nodes.insert(node_id);
    }

    /// 添加已响应的节点
    pub fn add_responded_node(&mut self, node_id: NodeId) {
        self.responded_nodes.insert(node_id);
    }

    /// 添加找到的节点
    pub fn add_found_node(&mut self, node: DHTNode) {
        self.found_nodes.push(node);
    }

    /// 添加找到的对等点
    pub fn add_found_peer(&mut self, peer: SocketAddr) {
        self.found_peers.push(peer);
    }

    /// 检查节点是否已查询
    pub fn is_node_queried(&self, node_id: &NodeId) -> bool {
        self.queried_nodes.contains(node_id)
    }

    /// 检查节点是否已响应
    pub fn is_node_responded(&self, node_id: &NodeId) -> bool {
        self.responded_nodes.contains(node_id)
    }

    /// 获取查询持续时间
    pub fn duration(&self) -> std::time::Duration {
        self.start_time.elapsed()
    }
}

/// 查询信息
struct QueryInfo {
    /// 查询
    query: DHTQuery,
    /// 响应通道发送端
    response_tx: Option<oneshot::Sender<Result<DHTResponse>>>,
}

/// DHT查询管理器
pub struct QueryManager {
    /// 活跃查询
    active_queries: HashMap<Vec<u8>, QueryInfo>,
    /// 最大并行查询数
    max_parallel_queries: usize,
    /// 查询超时时间（秒）
    pub query_timeout: u64,
}

impl QueryManager {
    /// 创建新的查询管理器
    pub fn new(max_parallel_queries: usize, query_timeout: u64) -> Self {
        Self {
            active_queries: HashMap::new(),
            max_parallel_queries,
            query_timeout,
        }
    }

    /// 添加查询
    pub fn add_query(&mut self, query: DHTQuery) -> Result<()> {
        if self.active_queries.len() >= self.max_parallel_queries {
            return Err(anyhow!("Too many active queries"));
        }

        let query_info = QueryInfo {
            query: query.clone(),
            response_tx: None,
        };

        self.active_queries.insert(query.query_id.clone(), query_info);
        Ok(())
    }

    /// 添加带响应通道的查询
    pub fn add_query_with_response_channel(
        &mut self,
        query: DHTQuery,
        response_tx: oneshot::Sender<Result<DHTResponse>>
    ) -> Result<()> {
        if self.active_queries.len() >= self.max_parallel_queries {
            return Err(anyhow!("Too many active queries"));
        }

        let query_info = QueryInfo {
            query: query.clone(),
            response_tx: Some(response_tx),
        };

        self.active_queries.insert(query.query_id.clone(), query_info);
        Ok(())
    }

    /// 获取查询
    pub fn get_query(&self, query_id: &[u8]) -> Option<&DHTQuery> {
        self.active_queries.get(query_id).map(|info| &info.query)
    }

    /// 获取可变查询
    pub fn get_query_mut(&mut self, query_id: &[u8]) -> Option<&mut DHTQuery> {
        self.active_queries.get_mut(query_id).map(|info| &mut info.query)
    }

    /// 发送响应
    pub fn send_response(&mut self, query_id: &[u8], response: Result<DHTResponse>) -> Result<()> {
        if let Some(query_info) = self.active_queries.remove(query_id) {
            if let Some(tx) = query_info.response_tx {
                let _ = tx.send(response);
            }
            Ok(())
        } else {
            Err(anyhow!("Query not found"))
        }
    }

    /// 移除查询
    pub fn remove_query(&mut self, query_id: &[u8]) -> Option<DHTQuery> {
        self.active_queries.remove(query_id).map(|info| info.query)
    }

    /// 获取所有活跃查询
    pub fn get_all_queries(&self) -> HashMap<Vec<u8>, DHTQuery> {
        self.active_queries.iter()
            .map(|(k, v)| (k.clone(), v.query.clone()))
            .collect()
    }

    /// 获取活跃查询数量
    pub fn count(&self) -> usize {
        self.active_queries.len()
    }

    /// 清理超时查询
    pub fn cleanup_expired_queries(&mut self) -> Vec<DHTQuery> {
        let mut expired = Vec::new();
        let now = Instant::now();
        let timeout = std::time::Duration::from_secs(self.query_timeout);

        let expired_keys: Vec<Vec<u8>> = self.active_queries.iter()
            .filter(|(_, info)| now.duration_since(info.query.start_time) > timeout)
            .map(|(k, _)| k.clone())
            .collect();

        for key in expired_keys {
            if let Some(info) = self.active_queries.remove(&key) {
                // 发送超时错误
                if let Some(tx) = info.response_tx {
                    let _ = tx.send(Err(anyhow!("Query timeout")));
                }
                expired.push(info.query);
            }
        }

        expired
    }
}
