use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::error;
use uuid::Uuid;

use crate::download::event_notifier::EventNotifier;
use super::models::{TaskInfo, TaskStatus};

/// 事件处理器，负责处理下载任务的事件通知
pub struct EventHandler {
    event_notifier: Arc<dyn EventNotifier>,
}

impl EventHandler {
    /// 创建一个新的事件处理器
    pub fn new(event_notifier: Arc<dyn EventNotifier>) -> Self {
        Self { event_notifier }
    }

    /// 通知任务开始
    pub async fn notify_task_started(&self, task: &TaskInfo) -> Result<()> {
        if let Err(e) = self.event_notifier.notify_task_started(task).await {
            error!("Failed to notify task started: {}", e);
            return Err(e);
        }
        Ok(())
    }

    /// 通知任务状态变更
    pub async fn notify_task_status_changed(&self, task: &TaskInfo, old_status: TaskStatus) -> Result<()> {
        let event_notifier_clone = self.event_notifier.clone();
        let task_clone = task.clone();
        
        tokio::spawn(async move {
            if let Err(e) = event_notifier_clone.notify_task_status_changed(&task_clone, old_status).await {
                error!("Failed to notify task status changed: {}", e);
            }
        });
        
        Ok(())
    }

    /// 通知任务失败
    pub async fn notify_task_failed(&self, task: &TaskInfo) -> Result<()> {
        let event_notifier_clone = self.event_notifier.clone();
        let task_clone = task.clone();
        
        tokio::spawn(async move {
            if let Err(e) = event_notifier_clone.notify_task_failed(&task_clone).await {
                error!("Failed to notify task failed: {}", e);
            }
        });
        
        Ok(())
    }

    /// 通知任务进度
    pub async fn notify_task_progress(&self, task: &TaskInfo) -> Result<()> {
        let event_notifier_clone = self.event_notifier.clone();
        let task_clone = task.clone();
        
        tokio::spawn(async move {
            if let Err(e) = event_notifier_clone.notify_task_progress(&task_clone).await {
                error!("Failed to notify task progress: {}", e);
            }
        });
        
        Ok(())
    }

    /// 通知任务速度
    pub async fn notify_task_speed(&self, task_id: Uuid, speed: u64) -> Result<()> {
        let event_notifier_clone = self.event_notifier.clone();
        
        tokio::spawn(async move {
            if let Err(e) = event_notifier_clone.notify_task_speed(task_id, speed).await {
                error!("Failed to notify task speed: {}", e);
            }
        });
        
        Ok(())
    }

    /// 通知任务完成
    pub async fn notify_task_completed(&self, task: &TaskInfo) -> Result<()> {
        let event_notifier_clone = self.event_notifier.clone();
        let task_clone = task.clone();
        
        tokio::spawn(async move {
            if let Err(e) = event_notifier_clone.notify_task_completed(&task_clone).await {
                error!("Failed to notify task completed: {}", e);
            }
        });
        
        Ok(())
    }
}