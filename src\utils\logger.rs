use crate::core::error::CoreResult;
use crate::utils::logger_impl::TracingLogger;

/// Initialize the application logger
pub fn init_logger() -> CoreResult<()> {
    TracingLogger::init()
}

/// Log an error and return it
pub fn log_error<E: std::fmt::Display>(err: E) -> E {
    tracing::error!("{}", err);
    err
}

/// Create a logger instance
pub async fn create_logger(name: &str) -> CoreResult<TracingLogger> {
    let logger = TracingLogger::new(name);
    Ok(logger)
}
