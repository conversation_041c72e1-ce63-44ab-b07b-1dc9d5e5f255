use serde::Serialize;
use tokio::sync::broadcast::Sender;
use tracing::{debug, error};
use uuid::Uuid;

use crate::api::websocket::WsMessage;
use crate::core::interfaces::task::TaskInfo;
use crate::core::interfaces::downloader::DownloadStatus;

/// 事件管理器
#[derive(Debug, Clone)]
pub struct EventManager {
    sender: Sender<WsMessage>,
}

impl EventManager {
    /// 创建新的事件管理器
    pub fn new(sender: Sender<WsMessage>) -> Self {
        Self { sender }
    }

    /// 发送任务添加事件
    pub fn task_added(&self, task_info: &TaskInfo) {
        self.send_event("task.add", task_info);
    }

    /// 发送任务开始事件
    pub fn task_started(&self, task_info: &TaskInfo) {
        self.send_event("task.start", task_info);
    }

    /// 发送任务暂停事件
    pub fn task_paused(&self, task_info: &TaskInfo) {
        self.send_event("task.pause", task_info);
    }

    /// 发送任务恢复事件
    pub fn task_resumed(&self, task_info: &TaskInfo) {
        self.send_event("task.resume", task_info);
    }

    /// 发送任务取消事件
    pub fn task_cancelled(&self, task_info: &TaskInfo) {
        self.send_event("task.cancel", task_info);
    }

    /// 发送任务完成事件
    pub fn task_completed(&self, task_info: &TaskInfo) {
        self.send_event("task.complete", task_info);
    }

    /// 发送任务错误事件
    pub fn task_error(&self, task_info: &TaskInfo) {
        self.send_event("task.error", task_info);
    }

    /// 发送任务进度事件
    pub fn task_progress(&self, task_info: &TaskInfo) {
        self.send_event("task.progress", task_info);
    }

    /// 发送任务速度事件
    pub fn task_speed(&self, task_id: Uuid, download_speed: u64, upload_speed: u64) {
        self.send_event("task.speed", serde_json::json!({
            "task_id": task_id,
            "download_speed": download_speed,
            "upload_speed": upload_speed,
        }));
    }

    /// 发送任务状态变更事件
    pub fn task_status_changed(&self, task_info: &TaskInfo, old_status: DownloadStatus) {
        self.send_event("task.status", serde_json::json!({
            "task_id": task_info.id,
            "old_status": old_status,
            "new_status": task_info.status,
        }));
    }

    /// 发送全局状态事件
    pub fn global_status(&self, active_count: usize, waiting_count: usize, stopped_count: usize) {
        self.send_event("global.status", serde_json::json!({
            "active_count": active_count,
            "waiting_count": waiting_count,
            "stopped_count": stopped_count,
        }));
    }

    /// 发送全局速度事件
    pub fn global_speed(&self, download_speed: u64, upload_speed: u64) {
        self.send_event("global.speed", serde_json::json!({
            "download_speed": download_speed,
            "upload_speed": upload_speed,
        }));
    }

    /// 发送任务删除事件
    pub fn task_removed(&self, task_info: &TaskInfo) {
        self.send_event("task.remove", task_info);
    }

    /// 发送事件
    fn send_event<T: Serialize>(&self, event: &str, payload: T) {
        let payload = match serde_json::to_value(payload) {
            Ok(value) => value,
            Err(e) => {
                error!("Failed to serialize payload for event {}: {}", event, e);
                return;
            }
        };

        let message = WsMessage {
            event: event.to_string(),
            payload,
            version: "1.0".to_string(),
        };

        // 尝试发送事件，最多重试3次
        let mut retry_count = 0;
        let max_retries = 3;

        loop {
            match self.sender.send(message.clone()) {
                Ok(receiver_count) => {
                    debug!("Event sent: {} to {} receivers", event, receiver_count);
                    break;
                },
                Err(e) => {
                    if retry_count >= max_retries {
                        if e.0.event.is_empty() {
                            debug!("No receivers for event: {} after {} retries", event, max_retries);
                        } else {
                            // 降低日志级别，避免大量错误日志
                            debug!("Failed to send event {} after {} retries: {}", event, max_retries, e);
                        }
                        break;
                    }

                    // 短暂延迟后重试
                    std::thread::sleep(std::time::Duration::from_millis(10));
                    retry_count += 1;
                    debug!("Retrying to send event {} (attempt {}/{})", event, retry_count, max_retries);
                }
            }
        }
    }
}
