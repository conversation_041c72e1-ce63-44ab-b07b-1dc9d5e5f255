use anyhow::Result;
use std::collections::HashSet;
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio::time::Duration;
use tracing::debug;
use tokio::io::AsyncWriteExt;

use crate::core::p2p::piece::PieceManager;
use crate::protocols::bittorrent::message::BitTorrentMessage;

/// 上传请求管理器
pub struct UploadManager {
    /// 我方是否阻塞对方
    pub am_choking: bool,
    /// 对方是否对我方感兴趣
    pub peer_interested: bool,
    /// 我方允许对方快速请求的分片列表
    pub allowed_fast_pieces: HashSet<u32>,
    /// 对方请求的分片队列
    pub upload_requests: Vec<(u32, u32, u32)>, // (index, begin, length)
    /// 上传限制（字节/秒）
    pub upload_rate_limit: Option<u64>,
    /// 是否支持Fast Extension
    pub supports_fast: bool,
    /// 已上传的总字节数
    pub total_bytes_uploaded: u64,
}

impl UploadManager {
    /// 创建新的上传请求管理器
    pub fn new(supports_fast: bool) -> Self {
        Self {
            am_choking: true,
            peer_interested: false,
            allowed_fast_pieces: HashSet::new(),
            upload_requests: Vec::new(),
            upload_rate_limit: None,
            supports_fast,
            total_bytes_uploaded: 0,
        }
    }

    /// 设置上传速率限制
    pub fn set_upload_rate_limit(&mut self, limit: Option<u64>) {
        self.upload_rate_limit = limit;
    }

    /// 添加允许快速请求的分片
    pub fn add_allowed_fast_piece(&mut self, index: u32) {
        self.allowed_fast_pieces.insert(index);
    }

    /// 设置阻塞状态
    pub fn set_choking(&mut self, choking: bool) {
        self.am_choking = choking;
    }

    /// 设置对方兴趣状态
    pub fn set_peer_interested(&mut self, interested: bool) {
        self.peer_interested = interested;
    }

    /// 处理请求消息
    pub async fn handle_request(&mut self, index: u32, begin: u32, length: u32) -> Result<Option<BitTorrentMessage>> {
        // 如果我们阻塞了对方，并且这个分片不在允许快速请求的列表中，拒绝请求
        if self.am_choking && !self.allowed_fast_pieces.contains(&index) {
            debug!("Rejecting request for piece {} because we are choking the peer", index);

            // 如果支持Fast Extension，发送拒绝消息
            if self.supports_fast {
                return Ok(Some(BitTorrentMessage::Reject(index, begin, length)));
            }

            return Ok(None);
        }

        // 将请求添加到上传队列
        self.upload_requests.push((index, begin, length));
        debug!("Added request for piece {} to upload queue", index);

        Ok(None)
    }

    /// 取消上传请求
    pub async fn cancel_request(&mut self, index: u32, begin: u32, length: u32) {
        self.upload_requests.retain(|&(i, b, l)| !(i == index && b == begin && l == length));
        debug!("取消上传请求: piece={}, begin={}, length={}", index, begin, length);
    }

    /// 处理上传请求
    pub async fn process_uploads(&mut self, piece_manager: &Arc<Mutex<dyn PieceManager>>) -> Result<Vec<BitTorrentMessage>> {
        let mut messages = Vec::new();

        // 如果没有上传请求，直接返回
        if self.upload_requests.is_empty() {
            return Ok(messages);
        }

        // 如果我们阻塞了对方，并且对方不在allowed_fast列表中，则拒绝所有请求
        if self.am_choking {
            debug!("We are choking the peer, rejecting all requests");
            return Ok(messages);
        }

        // 处理所有上传请求
        let mut requests_to_process = Vec::new();
        std::mem::swap(&mut requests_to_process, &mut self.upload_requests);

        for (index, begin, length) in requests_to_process {
            // 获取分片数据
            let piece_manager_guard = piece_manager.lock().await;
            let piece_info_opt = piece_manager_guard.get_piece_info(index).await?;

            // 如果没有找到分片信息，拒绝请求
            let piece_info = match piece_info_opt {
                Some(info) => info,
                None => {
                    debug!("Piece info not found for piece {}, rejecting request", index);

                    // 如果支持Fast Extension，发送拒绝消息
                    if self.supports_fast {
                        messages.push(BitTorrentMessage::Reject(index, begin, length));
                    }

                    continue;
                }
            };

            // 检查请求的范围是否有效
            if begin as u64 + length as u64 > piece_info.size {
                debug!("Invalid request range: begin={}, length={}, piece_size={}", begin, length, piece_info.size);

                // 如果支持Fast Extension，发送拒绝消息
                if self.supports_fast {
                    messages.push(BitTorrentMessage::Reject(index, begin, length));
                }

                continue;
            }

            // 检查我们是否有这个分片
            let piece_state = piece_manager_guard.piece_state(index).await?;
            if piece_state != crate::core::p2p::piece::PieceState::Verified {
                debug!("We don't have verified piece {}, rejecting request. State: {:?}", index, piece_state);

                // 如果支持Fast Extension，发送拒绝消息
                if self.supports_fast {
                    messages.push(BitTorrentMessage::Reject(index, begin, length));
                }

                continue;
            }

            // 读取块数据
            debug!("Attempting to read block: index={}, begin={}, length={}", index, begin, length);
            let block_data = piece_manager_guard.read_block(index, begin, length).await?;
            debug!("Successfully read block data of length: {}", block_data.len());

            // 释放锁，以便其他线程可以访问PieceManager
            drop(piece_manager_guard);

            // 应用上传速率限制（如果有）
            if let Some(rate_limit) = self.upload_rate_limit {
                let bytes_to_upload = block_data.len();
                let upload_time = bytes_to_upload as f64 / rate_limit as f64;
                let upload_delay = Duration::from_secs_f64(upload_time);

                // 等待一段时间以限制上传速率
                tokio::time::sleep(upload_delay).await;
            }

            // 发送分片数据
            self.total_bytes_uploaded += block_data.len() as u64;
            messages.push(BitTorrentMessage::Piece(index, begin, block_data));
        }

        Ok(messages)
    }

    /// 处理上传请求
    pub async fn process_request(&mut self, request: BitTorrentMessage, stream: &mut Option<tokio::net::TcpStream>) -> Result<()> {
        match request {
            BitTorrentMessage::Request(index, begin, length) => {
                // 处理请求消息
                if let Some(response) = self.handle_request(index, begin, length).await? {
                    // 如果有响应消息，发送它
                    if let Some(s) = stream {
                        let message_bytes = response.encode(true)?;
                        s.write_all(&message_bytes).await?
                    }
                }
            }
            _ => {
                // 忽略非请求消息
                debug!("Ignoring non-request message in upload queue");
            }
        }
        
        Ok(())
    }

    /// 获取已上传字节数
    pub fn bytes_uploaded(&self) -> u64 {
        self.total_bytes_uploaded
    }
}
