use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use anyhow::Result;
use async_trait::async_trait;
use tokio::sync::Mutex;
use tracing::{debug, info, warn};

use crate::protocols::bittorrent::peer::PeerInfo;

use super::UploadConfig;

/// 上传策略接口
/// 定义上传策略的行为
#[async_trait]
pub trait UploadStrategy: Send + Sync {
    /// 选择要解除阻塞的对等点
    async fn select_unchoked_peers(&self, peers: &HashMap<SocketAddr, PeerInfo>) -> Result<Vec<SocketAddr>>;
    
    /// 选择要乐观解除阻塞的对等点
    async fn select_optimistic_unchoked_peer(&self, peers: &HashMap<SocketAddr, PeerInfo>) -> Result<Option<SocketAddr>>;
    
    /// 更新对等点统计信息
    async fn update_peer_stats(&self, addr: SocketAddr, uploaded: u64, download_rate: u64) -> Result<()>;
    
    /// 重置统计信息
    async fn reset_stats(&self) -> Result<()>;
}

/// 标准上传策略
/// 实现基于上传速率的阻塞算法
pub struct StandardUploadStrategy {
    /// 上传配置
    config: UploadConfig,
    
    /// 对等点上传统计信息
    peer_stats: Arc<Mutex<HashMap<SocketAddr, PeerStats>>>,
    
    /// 当前解除阻塞的对等点
    unchoked_peers: Arc<Mutex<Vec<SocketAddr>>>,
    
    /// 当前乐观解除阻塞的对等点
    optimistic_unchoked_peer: Arc<Mutex<Option<SocketAddr>>>,
    
    /// 上次乐观解除阻塞时间
    last_optimistic_unchoke: Arc<Mutex<std::time::Instant>>,
}

/// 对等点统计信息
#[derive(Debug, Clone)]
struct PeerStats {
    /// 上传字节数
    uploaded: u64,
    
    /// 下载速率（字节/秒）
    download_rate: u64,
    
    /// 最后更新时间
    last_update: std::time::Instant,
    
    /// 连续解除阻塞次数
    unchoke_count: u32,
}

impl StandardUploadStrategy {
    /// 创建新的标准上传策略
    pub fn new(config: UploadConfig) -> Self {
        Self {
            config,
            peer_stats: Arc::new(Mutex::new(HashMap::new())),
            unchoked_peers: Arc::new(Mutex::new(Vec::new())),
            optimistic_unchoked_peer: Arc::new(Mutex::new(None)),
            last_optimistic_unchoke: Arc::new(Mutex::new(std::time::Instant::now())),
        }
    }
    
    /// 计算对等点得分
    /// 得分基于下载速率和上传历史
    fn calculate_peer_score(&self, stats: &PeerStats) -> f64 {
        // 基础得分是下载速率
        let mut score = stats.download_rate as f64;
        
        // 如果对等点有上传历史，增加得分
        if stats.uploaded > 0 {
            score *= 1.2;
        }
        
        // 如果对等点连续解除阻塞次数过多，降低得分
        if stats.unchoke_count > 3 {
            score *= 0.8;
        }
        
        score
    }
    
    /// 是否需要进行乐观解除阻塞
    async fn need_optimistic_unchoke(&self) -> bool {
        let last_optimistic_unchoke = *self.last_optimistic_unchoke.lock().await;
        let interval = std::time::Duration::from_secs(self.config.optimistic_unchoke_interval);
        
        last_optimistic_unchoke.elapsed() >= interval
    }
    
    /// 更新乐观解除阻塞时间
    async fn update_optimistic_unchoke_time(&self) {
        let mut last_optimistic_unchoke = self.last_optimistic_unchoke.lock().await;
        *last_optimistic_unchoke = std::time::Instant::now();
    }
}

#[async_trait]
impl UploadStrategy for StandardUploadStrategy {
    async fn select_unchoked_peers(&self, peers: &HashMap<SocketAddr, PeerInfo>) -> Result<Vec<SocketAddr>> {
        let mut unchoked_peers = Vec::new();
        
        // 如果没有对等点，返回空列表
        if peers.is_empty() {
            return Ok(unchoked_peers);
        }
        
        // 获取对等点统计信息
        let peer_stats = self.peer_stats.lock().await;
        
        // 创建对等点得分列表
        let mut peer_scores = Vec::new();
        
        for (addr, peer_info) in peers {
            // 只考虑对我们感兴趣的对等点
            if !peer_info.peer_interested {
                continue;
            }
            
            // 获取对等点统计信息
            let stats = peer_stats.get(addr).cloned().unwrap_or_else(|| {
                PeerStats {
                    uploaded: 0,
                    download_rate: 0,
                    last_update: std::time::Instant::now(),
                    unchoke_count: 0,
                }
            });
            
            // 计算得分
            let score = self.calculate_peer_score(&stats);
            
            // 添加到得分列表
            peer_scores.push((*addr, score));
        }
        
        // 按得分排序（从高到低）
        peer_scores.sort_by(|(_, score1), (_, score2)| {
            score2.partial_cmp(score1).unwrap_or(std::cmp::Ordering::Equal)
        });
        
        // 选择得分最高的N个对等点
        let max_unchoked = self.config.max_unchoked_peers;
        
        for (i, (addr, _)) in peer_scores.iter().enumerate() {
            if i < max_unchoked {
                unchoked_peers.push(*addr);
            } else {
                break;
            }
        }
        
        // 更新当前解除阻塞的对等点
        *self.unchoked_peers.lock().await = unchoked_peers.clone();
        
        Ok(unchoked_peers)
    }
    
    async fn select_optimistic_unchoked_peer(&self, peers: &HashMap<SocketAddr, PeerInfo>) -> Result<Option<SocketAddr>> {
        // 检查是否需要进行乐观解除阻塞
        if !self.need_optimistic_unchoke().await {
            return Ok(*self.optimistic_unchoked_peer.lock().await);
        }
        
        // 获取当前解除阻塞的对等点
        let unchoked_peers = self.unchoked_peers.lock().await;
        
        // 创建候选对等点列表
        let mut candidates = Vec::new();
        
        for (addr, peer_info) in peers {
            // 只考虑对我们感兴趣的对等点
            if !peer_info.peer_interested {
                continue;
            }
            
            // 排除已经解除阻塞的对等点
            if unchoked_peers.contains(addr) {
                continue;
            }
            
            candidates.push(*addr);
        }
        
        // 如果没有候选对等点，返回None
        if candidates.is_empty() {
            return Ok(None);
        }
        
        // 随机选择一个候选对等点
        let index = rand::random::<usize>() % candidates.len();
        let selected = candidates[index];
        
        // 更新乐观解除阻塞时间
        self.update_optimistic_unchoke_time().await;
        
        // 更新当前乐观解除阻塞的对等点
        *self.optimistic_unchoked_peer.lock().await = Some(selected);
        
        Ok(Some(selected))
    }
    
    async fn update_peer_stats(&self, addr: SocketAddr, uploaded: u64, download_rate: u64) -> Result<()> {
        let mut peer_stats = self.peer_stats.lock().await;
        
        // 获取或创建对等点统计信息
        let stats = peer_stats.entry(addr).or_insert_with(|| {
            PeerStats {
                uploaded: 0,
                download_rate: 0,
                last_update: std::time::Instant::now(),
                unchoke_count: 0,
            }
        });
        
        // 更新统计信息
        stats.uploaded = uploaded;
        stats.download_rate = download_rate;
        stats.last_update = std::time::Instant::now();
        
        // 如果对等点在当前解除阻塞列表中，增加连续解除阻塞次数
        let unchoked_peers = self.unchoked_peers.lock().await;
        if unchoked_peers.contains(&addr) {
            stats.unchoke_count += 1;
        } else {
            stats.unchoke_count = 0;
        }
        
        Ok(())
    }
    
    async fn reset_stats(&self) -> Result<()> {
        let mut peer_stats = self.peer_stats.lock().await;
        peer_stats.clear();
        
        Ok(())
    }
}
