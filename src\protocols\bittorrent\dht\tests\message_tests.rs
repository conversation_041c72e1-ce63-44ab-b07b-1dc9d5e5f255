#[cfg(test)]
mod message_tests {
    use std::net::{IpAddr, Ipv4Addr, SocketAddr};
    use crate::protocols::bittorrent::dht::node::{NodeId, DHTNode};
    use crate::protocols::bittorrent::dht::message::{DHTMessage, DHTMessageType, TokenManager, MessageCodec};

    #[test]
    fn test_token_manager_new() {
        let token_manager = TokenManager::new();

        // 验证初始化成功 - 只能测试公共接口
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
        let token = token_manager.generate_token(&addr);
        assert!(!token.is_empty());
    }

    #[test]
    fn test_token_manager_generate_token() {
        let token_manager = TokenManager::new();
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        let token = token_manager.generate_token(&addr);

        // 验证生成的令牌不为空
        assert!(!token.is_empty());

        // 同一地址生成的令牌应该相同
        let token2 = token_manager.generate_token(&addr);
        assert_eq!(token, token2);

        // 不同地址生成的令牌应该不同
        let addr2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);
        let token3 = token_manager.generate_token(&addr2);
        assert_ne!(token, token3);
    }

    #[test]
    fn test_token_manager_verify_token() {
        let token_manager = TokenManager::new();
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        let token = token_manager.generate_token(&addr);

        // 验证正确的令牌
        assert!(token_manager.verify_token(&token, &addr));

        // 验证错误的令牌
        let wrong_token = vec![0u8; token.len()];
        assert!(!token_manager.verify_token(&wrong_token, &addr));

        // 验证错误的地址
        let addr2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);
        assert!(!token_manager.verify_token(&token, &addr2));
    }

    #[test]
    fn test_token_manager_refresh_keys() {
        let mut token_manager = TokenManager::new();
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        let token = token_manager.generate_token(&addr);

        // 刷新密钥
        token_manager.refresh_keys();

        // 由于时间间隔短，令牌应该仍然有效
        assert!(token_manager.verify_token(&token, &addr));

        // 模拟密钥刷新 - 创建一个新的TokenManager
        let new_token_manager = TokenManager::new();

        // 旧令牌不应该在新的TokenManager中有效
        assert!(!new_token_manager.verify_token(&token, &addr));
    }

    #[test]
    fn test_message_codec_encode_ping() {
        let node_id = NodeId::from_bytes([1u8; 20]);
        let transaction_id = vec![1, 2, 3, 4];

        let message = DHTMessage {
            message_type: DHTMessageType::Ping,
            transaction_id: transaction_id.clone(),
            sender_id: node_id,
            target_id: None,
            nodes: None,
            peers: None,
            token: None,
            port: None,
            error_code: None,
            error_message: None,
        };

        let encoded = MessageCodec::encode(&message).unwrap();

        // 验证编码成功
        assert!(!encoded.is_empty());

        // 验证编码结果是有效的bencode
        let decoded = serde_bencode::from_bytes::<serde_bencode::value::Value>(&encoded);
        assert!(decoded.is_ok());

        let value = decoded.unwrap();
        if let serde_bencode::value::Value::Dict(dict) = value {
            // 验证基本字段
            assert!(dict.contains_key(&b"t".to_vec()));
            assert!(dict.contains_key(&b"y".to_vec()));
            assert!(dict.contains_key(&b"q".to_vec()));
            assert!(dict.contains_key(&b"a".to_vec()));

            // 验证事务ID
            if let serde_bencode::value::Value::Bytes(t) = &dict[&b"t".to_vec()] {
                assert_eq!(t, &transaction_id);
            } else {
                panic!("Transaction ID not found or has wrong type");
            }

            // 验证消息类型
            if let serde_bencode::value::Value::Bytes(y) = &dict[&b"y".to_vec()] {
                assert_eq!(y, &b"q".to_vec());
            } else {
                panic!("Message type not found or has wrong type");
            }

            // 验证查询类型
            if let serde_bencode::value::Value::Bytes(q) = &dict[&b"q".to_vec()] {
                assert_eq!(q, &b"ping".to_vec());
            } else {
                panic!("Query type not found or has wrong type");
            }
        } else {
            panic!("Encoded message is not a dictionary");
        }
    }

    #[test]
    fn test_message_codec_encode_find_node() {
        let sender_id = NodeId::from_bytes([1u8; 20]);
        let target_id = NodeId::from_bytes([2u8; 20]);
        let transaction_id = vec![1, 2, 3, 4];

        let message = DHTMessage {
            message_type: DHTMessageType::FindNode,
            transaction_id: transaction_id.clone(),
            sender_id,
            target_id: Some(target_id),
            nodes: None,
            peers: None,
            token: None,
            port: None,
            error_code: None,
            error_message: None,
        };

        let encoded = MessageCodec::encode(&message).unwrap();
        let decoded = serde_bencode::from_bytes::<serde_bencode::value::Value>(&encoded).unwrap();

        if let serde_bencode::value::Value::Dict(dict) = decoded {
            // 验证查询类型
            if let serde_bencode::value::Value::Bytes(q) = &dict[&b"q".to_vec()] {
                assert_eq!(q, &b"find_node".to_vec());
            } else {
                panic!("Query type not found or has wrong type");
            }

            // 验证参数
            if let serde_bencode::value::Value::Dict(args) = &dict[&b"a".to_vec()] {
                assert!(args.contains_key(&b"id".to_vec()));
                assert!(args.contains_key(&b"target".to_vec()));
            } else {
                panic!("Arguments not found or have wrong type");
            }
        } else {
            panic!("Encoded message is not a dictionary");
        }
    }

    #[test]
    fn test_message_codec_encode_get_peers() {
        let sender_id = NodeId::from_bytes([1u8; 20]);
        let info_hash = NodeId::from_bytes([2u8; 20]);
        let transaction_id = vec![1, 2, 3, 4];

        let message = DHTMessage {
            message_type: DHTMessageType::GetPeers,
            transaction_id: transaction_id.clone(),
            sender_id,
            target_id: Some(info_hash),
            nodes: None,
            peers: None,
            token: None,
            port: None,
            error_code: None,
            error_message: None,
        };

        let encoded = MessageCodec::encode(&message).unwrap();
        let decoded = serde_bencode::from_bytes::<serde_bencode::value::Value>(&encoded).unwrap();

        if let serde_bencode::value::Value::Dict(dict) = decoded {
            // 验证查询类型
            if let serde_bencode::value::Value::Bytes(q) = &dict[&b"q".to_vec()] {
                assert_eq!(q, &b"get_peers".to_vec());
            } else {
                panic!("Query type not found or has wrong type");
            }

            // 验证参数
            if let serde_bencode::value::Value::Dict(args) = &dict[&b"a".to_vec()] {
                assert!(args.contains_key(&b"id".to_vec()));
                assert!(args.contains_key(&b"info_hash".to_vec()));
            } else {
                panic!("Arguments not found or have wrong type");
            }
        } else {
            panic!("Encoded message is not a dictionary");
        }
    }
}
