use async_trait::async_trait;
use std::path::Path;

use tokio::sync::RwLock;
use tracing::{info};
use tracing_subscriber::{layer::SubscriberExt, EnvFilter, fmt, Registry, registry};
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use tracing_appender::non_blocking::WorkerGuard;

use crate::core::error::{CoreError, CoreResult};
use crate::core::interfaces::logger::{LogLevel, LogRecord, Logger};

/// Logger implementation using tracing
pub struct TracingLogger {
    name: String,
    level: RwLock<LogLevel>,
    file_path: RwLock<Option<String>>,
    console_enabled: RwLock<bool>,
    file_guard: RwLock<Option<WorkerGuard>>,
}

impl TracingLogger {
    /// Create a new tracing logger
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            level: RwLock::new(LogLevel::Info),
            file_path: RwLock::new(None),
            console_enabled: RwLock::new(true),
            file_guard: RwLock::new(None),
        }
    }

    /// Initialize the logger
    pub fn init() -> CoreResult<()> {
        let env_filter = EnvFilter::try_from_default_env()
            .unwrap_or_else(|_| EnvFilter::new("tonitru_downloader=debug,tower_http=debug"));

        let formatting_layer = fmt::layer()
            .with_target(true)
            .with_level(true)
            .with_thread_ids(true)
            .with_thread_names(true);

        let subscriber = Registry::default()
            .with(env_filter)
            .with(formatting_layer);

        tracing::subscriber::set_global_default(subscriber)
            .map_err(|e| CoreError::Internal(format!("Failed to set global default subscriber: {}", e)))?;

        info!("Logger initialized");

        Ok(())
    }

    /// Convert LogLevel to tracing::Level
    fn to_tracing_level(level: LogLevel) -> tracing::Level {
        match level {
            LogLevel::Trace => tracing::Level::TRACE,
            LogLevel::Debug => tracing::Level::DEBUG,
            LogLevel::Info => tracing::Level::INFO,
            LogLevel::Warn => tracing::Level::WARN,
            LogLevel::Error => tracing::Level::ERROR,
        }
    }

    /// Convert tracing::Level to LogLevel
    fn from_tracing_level(level: tracing::Level) -> LogLevel {
        match level {
            tracing::Level::TRACE => LogLevel::Trace,
            tracing::Level::DEBUG => LogLevel::Debug,
            tracing::Level::INFO => LogLevel::Info,
            tracing::Level::WARN => LogLevel::Warn,
            tracing::Level::ERROR => LogLevel::Error,
        }
    }
}

#[async_trait]
impl Logger for TracingLogger {
    async fn log(&self, record: LogRecord) -> CoreResult<()> {
        let level = Self::to_tracing_level(record.level);

        // Create event with fields - 使用 String 类型作为值
        let mut fields = std::collections::HashMap::<&str, String>::new();
        for (key, value) in &record.fields {
            fields.insert(key.as_str(), value.clone());
        }

        // Add standard fields
        if let Some(module_path) = &record.module_path {
            fields.insert("module_path", module_path.clone());
        }
        if let Some(file) = &record.file {
            fields.insert("file", file.clone());
        }
        // 将 line 转换为字符串并存储在 HashMap 中
        if let Some(line) = record.line {
            // 使用 to_string 方法将 line 转换为字符串
            fields.insert("line", line.to_string());
        }
        if let Some(thread_id) = &record.thread_id {
            fields.insert("thread_id", thread_id.clone());
        }
        if let Some(thread_name) = &record.thread_name {
            fields.insert("thread_name", thread_name.clone());
        }

        // Log the message with the appropriate level
        // 由于 tracing 宏不能接受动态的 target，我们需要使用硬编码的 target
        let message = record.message.clone();
        match level {
            tracing::Level::TRACE => tracing::trace!("{}", message),
            tracing::Level::DEBUG => tracing::debug!("{}", message),
            tracing::Level::INFO => tracing::info!("{}", message),
            tracing::Level::WARN => tracing::warn!("{}", message),
            tracing::Level::ERROR => tracing::error!("{}", message),
        }

        Ok(())
    }

    async fn set_level(&self, level: LogLevel) -> CoreResult<()> {
        let mut current_level = self.level.write().await;
        *current_level = level;

        // This is a simplified implementation
        // In a real implementation, we would update the tracing filter
        info!("Logger '{}' level set to {:?}", self.name, level);

        Ok(())
    }

    async fn get_level(&self) -> CoreResult<LogLevel> {
        let level = self.level.read().await;
        Ok(*level)
    }

    async fn enable_file_logging(&self, path: &str) -> CoreResult<()> {
        let mut file_path = self.file_path.write().await;
        let mut file_guard = self.file_guard.write().await;

        // Create the directory if it doesn't exist
        if let Some(parent) = Path::new(path).parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| CoreError::Io(e))?;
        }

        // Set up file appender with daily rotation
        let file_appender = RollingFileAppender::new(
            Rotation::DAILY,
            Path::new(path).parent().unwrap_or(Path::new(".")),
            Path::new(path).file_name().unwrap_or_default(),
        );

        let (non_blocking, guard) = tracing_appender::non_blocking(file_appender);

        let file_layer = fmt::layer()
            .with_ansi(false)
            .with_writer(non_blocking);

        // Get the current registry
        let subscriber = registry::Registry::default()
            .with(file_layer);

        // Set the global default
        tracing::subscriber::set_global_default(subscriber)
            .map_err(|e| CoreError::Internal(format!("Failed to set global default subscriber: {}", e)))?;

        *file_path = Some(path.to_string());
        *file_guard = Some(guard);

        info!("File logging enabled for '{}' at {}", self.name, path);

        Ok(())
    }

    async fn disable_file_logging(&self) -> CoreResult<()> {
        let mut file_path = self.file_path.write().await;
        let mut file_guard = self.file_guard.write().await;

        // Drop the file guard to close the file
        if file_guard.is_some() {
            *file_guard = None;

            // Reinitialize the console-only logger
            let env_filter = EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| EnvFilter::new("tonitru_downloader=debug,tower_http=debug"));

            let formatting_layer = fmt::layer()
                .with_target(true)
                .with_level(true)
                .with_thread_ids(true)
                .with_thread_names(true);

            let subscriber = Registry::default()
                .with(env_filter)
                .with(formatting_layer);

            tracing::subscriber::set_global_default(subscriber)
                .map_err(|e| CoreError::Internal(format!("Failed to set global default subscriber: {}", e)))?;
        }

        *file_path = None;

        info!("File logging disabled for '{}'", self.name);

        Ok(())
    }

    async fn enable_console_logging(&self) -> CoreResult<()> {
        let mut console_enabled = self.console_enabled.write().await;

        if !*console_enabled {
            // Reinitialize with console logging
            let env_filter = EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| EnvFilter::new("tonitru_downloader=debug,tower_http=debug"));

            let formatting_layer = fmt::layer()
                .with_target(true)
                .with_level(true)
                .with_thread_ids(true)
                .with_thread_names(true);

            let subscriber = Registry::default()
                .with(env_filter)
                .with(formatting_layer);

            tracing::subscriber::set_global_default(subscriber)
                .map_err(|e| CoreError::Internal(format!("Failed to set global default subscriber: {}", e)))?;

            *console_enabled = true;
            info!("Console logging enabled for '{}'", self.name);
        }

        Ok(())
    }

    async fn disable_console_logging(&self) -> CoreResult<()> {
        let mut console_enabled = self.console_enabled.write().await;

        if *console_enabled {
            // Check if file logging is enabled
            let file_path = self.file_path.read().await;

            if let Some(path) = file_path.as_ref() {
                // Reinitialize with only file logging
                let file_appender = RollingFileAppender::new(
                    Rotation::DAILY,
                    Path::new(path).parent().unwrap_or(Path::new(".")),
                    Path::new(path).file_name().unwrap_or_default(),
                );

                let (non_blocking, guard) = tracing_appender::non_blocking(file_appender);

                let file_layer = fmt::layer()
                    .with_ansi(false)
                    .with_writer(non_blocking);

                let subscriber = registry::Registry::default()
                    .with(file_layer);

                tracing::subscriber::set_global_default(subscriber)
                    .map_err(|e| CoreError::Internal(format!("Failed to set global default subscriber: {}", e)))?;

                // Update the guard
                let mut file_guard = self.file_guard.write().await;
                *file_guard = Some(guard);
            } else {
                // No logging at all - create a silent subscriber
                let subscriber = Registry::default();
                tracing::subscriber::set_global_default(subscriber)
                    .map_err(|e| CoreError::Internal(format!("Failed to set global default subscriber: {}", e)))?;
            }

            *console_enabled = false;
            info!("Console logging disabled for '{}'", self.name);
        }

        Ok(())
    }

    async fn flush(&self) -> CoreResult<()> {
        // Force a flush by dropping and recreating the guard
        let file_path = self.file_path.read().await;

        if let Some(path) = file_path.as_ref() {
            let mut file_guard = self.file_guard.write().await;
            *file_guard = None;

            // Recreate the guard
            let file_appender = RollingFileAppender::new(
                Rotation::DAILY,
                Path::new(path).parent().unwrap_or(Path::new(".")),
                Path::new(path).file_name().unwrap_or_default(),
            );

            let (_non_blocking, guard) = tracing_appender::non_blocking(file_appender);
            *file_guard = Some(guard);
        }

        Ok(())
    }
}

/// Logger factory implementation
pub struct TracingLoggerFactory;

#[async_trait]
impl crate::core::interfaces::logger::LoggerFactory for TracingLoggerFactory {
    async fn create_logger(&self, name: &str) -> CoreResult<Box<dyn Logger>> {
        Ok(Box::new(TracingLogger::new(name)))
    }
}
