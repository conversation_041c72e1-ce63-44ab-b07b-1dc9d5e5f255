use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::Result;
use tokio::sync::{Mutex, RwLock};
use tokio::time::interval;
use tracing::{debug, info};

/// 速率限制配置
#[derive(Debug, Clone)]
pub struct RateLimiterConfig {
    /// 全局速率限制（每秒请求数）
    pub global_rate: u32,
    /// 每个IP的速率限制（每秒请求数）
    pub per_ip_rate: u32,
    /// 窗口大小（秒）
    pub window_size: u64,
    /// 清理间隔（秒）
    pub cleanup_interval: u64,
    /// 最大IP数
    pub max_ips: usize,
}

impl Default for RateLimiterConfig {
    fn default() -> Self {
        Self {
            global_rate: 1000,
            per_ip_rate: 20,
            window_size: 1,
            cleanup_interval: 60,
            max_ips: 10000,
        }
    }
}

/// IP计数器
struct IPCounter {
    /// 计数
    count: u32,
    /// 上次更新时间
    last_update: Instant,
}

impl IPCounter {
    /// 创建新的IP计数器
    fn new() -> Self {
        Self {
            count: 0,
            last_update: Instant::now(),
        }
    }

    /// 增加计数
    fn increment(&mut self) {
        self.count += 1;
        self.last_update = Instant::now();
    }

    /// 检查是否过期
    fn is_expired(&self, ttl: Duration) -> bool {
        self.last_update.elapsed() > ttl
    }
}

/// DHT速率限制器
pub struct DHTRateLimiter {
    /// 全局计数器
    global_counter: RwLock<u32>,
    /// 上次全局重置时间
    last_global_reset: RwLock<Instant>,
    /// IP计数器
    ip_counters: Arc<RwLock<HashMap<IpAddr, IPCounter>>>,
    /// 配置
    config: RateLimiterConfig,
    /// 是否正在运行
    running: RwLock<bool>,
    /// 任务句柄
    task_handle: Mutex<Option<tokio::task::JoinHandle<()>>>,
}

impl DHTRateLimiter {
    /// 创建新的DHT速率限制器
    pub fn new(config: RateLimiterConfig) -> Self {
        Self {
            global_counter: RwLock::new(0),
            last_global_reset: RwLock::new(Instant::now()),
            ip_counters: Arc::new(RwLock::new(HashMap::new())),
            config,
            running: RwLock::new(false),
            task_handle: Mutex::new(None),
        }
    }

    /// 启动速率限制器
    pub async fn start(&self) -> Result<()> {
        let mut running = self.running.write().await;

        if *running {
            return Ok(());
        }

        *running = true;

        // 创建清理任务
        let config = self.config.clone();
        let ip_counters = Arc::clone(&self.ip_counters);

        let handle = tokio::spawn(async move {
            let mut interval_timer = interval(Duration::from_secs(config.cleanup_interval));
            let ttl = Duration::from_secs(config.window_size * 2);

            loop {
                interval_timer.tick().await;

                // 清理IP计数器
                let mut counters = ip_counters.write().await;
                let before_count = counters.len();

                counters.retain(|_, counter| !counter.is_expired(ttl));

                let cleaned = before_count - counters.len();
                if cleaned > 0 {
                    debug!("Cleaned {} expired IP counters", cleaned);
                }
            }
        });

        // 保存任务句柄
        let mut task_handle = self.task_handle.lock().await;
        *task_handle = Some(handle);

        info!("DHT rate limiter started");

        Ok(())
    }

    /// 停止速率限制器
    pub async fn stop(&self) -> Result<()> {
        let mut running = self.running.write().await;

        if !*running {
            return Ok(());
        }

        *running = false;

        // 取消任务
        let mut task_handle = self.task_handle.lock().await;
        if let Some(handle) = task_handle.take() {
            handle.abort();
        }

        // 重置计数器
        {
            let mut global_counter = self.global_counter.write().await;
            *global_counter = 0;
        }

        {
            let mut ip_counters = self.ip_counters.write().await;
            ip_counters.clear();
        }

        info!("DHT rate limiter stopped");

        Ok(())
    }

    /// 检查是否允许请求
    pub async fn check(&self, ip: IpAddr) -> bool {
        // 检查全局速率
        {
            let mut global_counter = self.global_counter.write().await;
            let mut last_reset = self.last_global_reset.write().await;

            // 检查是否需要重置
            if last_reset.elapsed() >= Duration::from_secs(self.config.window_size) {
                *global_counter = 0;
                *last_reset = Instant::now();
            }

            // 检查是否超过全局速率
            if *global_counter >= self.config.global_rate {
                return false;
            }

            // 增加全局计数
            *global_counter += 1;
        }

        // 检查IP速率
        {
            let mut ip_counters = self.ip_counters.write().await;

            // 获取IP计数器
            let counter = ip_counters.entry(ip).or_insert_with(IPCounter::new);

            // 检查是否需要重置
            if counter.last_update.elapsed() >= Duration::from_secs(self.config.window_size) {
                counter.count = 0;
            }

            // 检查是否超过IP速率
            if counter.count >= self.config.per_ip_rate {
                return false;
            }

            // 增加IP计数
            counter.increment();

            // 检查IP数量
            if ip_counters.len() > self.config.max_ips {
                // 移除最旧的IP
                let oldest_ip = ip_counters.iter()
                    .min_by_key(|(_, counter)| counter.last_update)
                    .map(|(ip, _)| *ip);

                if let Some(oldest_ip) = oldest_ip {
                    ip_counters.remove(&oldest_ip);
                }
            }
        }

        true
    }

    /// 获取全局计数
    pub async fn get_global_count(&self) -> u32 {
        *self.global_counter.read().await
    }

    /// 获取IP计数
    pub async fn get_ip_count(&self, ip: IpAddr) -> u32 {
        let ip_counters = self.ip_counters.read().await;

        if let Some(counter) = ip_counters.get(&ip) {
            counter.count
        } else {
            0
        }
    }

    /// 获取IP数量
    pub async fn get_ip_count_total(&self) -> usize {
        let ip_counters = self.ip_counters.read().await;
        ip_counters.len()
    }

    /// 重置计数器
    pub async fn reset(&self) {
        {
            let mut global_counter = self.global_counter.write().await;
            *global_counter = 0;
        }

        {
            let mut last_reset = self.last_global_reset.write().await;
            *last_reset = Instant::now();
        }

        {
            let mut ip_counters = self.ip_counters.write().await;
            ip_counters.clear();
        }
    }
}
