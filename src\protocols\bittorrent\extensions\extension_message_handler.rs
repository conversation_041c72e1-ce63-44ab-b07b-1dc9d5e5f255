use std::net::SocketAddr;
use std::sync::{Arc, Weak};
use tokio::sync::Mutex;
use tracing::debug;

use super::extension_protocol::ExtensionProtocol;
use super::pex::{PEXExtension, PEXMessage};
use super::message::ExtensionMessage;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

type Result<T> = std::result::Result<T, BitTorrentError>;

/// PEX对等点处理回调
#[async_trait::async_trait]
pub trait PeerManagerCallback: Send + Sync {
    /// 检查对等点是否已连接
    fn is_peer_connected(&self, addr: &SocketAddr) -> bool;
    
    /// 添加对等点到待连接队列
    async fn add_peer_to_queue(&self, addr: SocketAddr) -> Result<()>;
}

/// 扩展消息处理器
pub struct ExtensionMessageHandler {
    /// 扩展协议处理器
    extension_protocol: Arc<Mutex<ExtensionProtocol>>,
    /// 对等点管理器回调
    peer_manager_callback: Option<Weak<dyn PeerManagerCallback>>,
}

impl ExtensionMessageHandler {
    /// 创建新的扩展消息处理器
    pub fn new(extension_protocol: Arc<Mutex<ExtensionProtocol>>) -> Self {
        Self {
            extension_protocol,
            peer_manager_callback: None,
        }
    }
    
    /// 设置对等点管理器回调
    pub fn set_peer_manager_callback(&mut self, callback: Weak<dyn PeerManagerCallback>) {
        self.peer_manager_callback = Some(callback);
    }

    /// 处理扩展消息
    pub async fn handle_message(&self, peer_addr: &str, message_id: u8, payload: &[u8]) -> Result<()> {
        // 使用try_lock而不是await
        if let Ok(protocol) = self.extension_protocol.try_lock() {
            // 处理扩展消息
            let result = protocol.handle_message(message_id, payload).await;

            // 如果是PEX消息，处理新发现的对等点
            if message_id != 0 { // 不是握手消息
                if let Some(pex_id) = protocol.get_message_id("ut_pex") {
                    if message_id == pex_id {
                        // 解析PEX消息
                        if let Ok(pex_message) = PEXMessage::decode(payload) {
                            // 处理新增的IPv4对等点
                            self.process_pex_added_peers(peer_addr, &pex_message.added).await?;

                            // 处理新增的IPv6对等点
                            self.process_pex_added_peers6(peer_addr, &pex_message.added6).await?;

                            debug!("Processed PEX message from {}: added {} IPv4 peers and {} IPv6 peers",
                                   peer_addr, pex_message.added.len() / 6, pex_message.added6.len() / 18);
                        }
                    }
                }
            }

            return result;
        }

        debug!("Extension protocol not initialized or locked, ignoring extension message from {}", peer_addr);
        Ok(())
    }

    /// 处理PEX消息中的IPv4对等点
    async fn process_pex_added_peers(&self, peer_addr: &str, added: &[u8]) -> Result<()> {
        // 每个IPv4对等点占6字节：4字节IP + 2字节端口
        let peer_count = added.len() / 6;

        for i in 0..peer_count {
            let offset = i * 6;

            // 解析IP地址（4字节）
            let ip_bytes = [added[offset], added[offset + 1], added[offset + 2], added[offset + 3]];
            let ip = std::net::Ipv4Addr::from(ip_bytes);

            // 解析端口（2字节，大端序）
            let port = u16::from_be_bytes([added[offset + 4], added[offset + 5]]);

            // 创建SocketAddr
            let addr = SocketAddr::new(std::net::IpAddr::V4(ip), port);

            // 添加对等点
            self.add_peer_from_pex(peer_addr, addr).await?;
        }

        Ok(())
    }

    /// 处理PEX消息中的IPv6对等点
    async fn process_pex_added_peers6(&self, peer_addr: &str, added6: &[u8]) -> Result<()> {
        // 每个IPv6对等点占18字节：16字节IP + 2字节端口
        let peer_count = added6.len() / 18;

        for i in 0..peer_count {
            let offset = i * 18;

            // 解析IP地址（16字节）
            let mut ip_bytes = [0u8; 16];
            ip_bytes.copy_from_slice(&added6[offset..offset + 16]);
            let ip = std::net::Ipv6Addr::from(ip_bytes);

            // 解析端口（2字节，大端序）
            let port = u16::from_be_bytes([added6[offset + 16], added6[offset + 17]]);

            // 创建SocketAddr
            let addr = SocketAddr::new(std::net::IpAddr::V6(ip), port);

            // 添加对等点
            self.add_peer_from_pex(peer_addr, addr).await?;
        }

        Ok(())
    }

    /// 从PEX添加对等点
    async fn add_peer_from_pex(&self, peer_addr: &str, addr: SocketAddr) -> Result<()> {
        // 检查是否已经连接到该对等点
        if let Some(callback) = &self.peer_manager_callback {
            if let Some(callback) = callback.upgrade() {
                if callback.is_peer_connected(&addr) {
                    return Ok(());
                }
                
                // 添加对等点到待连接队列
                debug!("New peer discovered via PEX from {}: {}", peer_addr, addr);
                
                // 尝试通过回调添加对等点（异步调用）
                if let Err(e) = callback.add_peer_to_queue(addr.clone()).await {
                    debug!("Failed to add peer {} to queue: {}", addr, e);
                }
            }
        }

        // 将新发现的对等点添加到PEX扩展的待处理队列
        if let Ok(protocol) = self.extension_protocol.try_lock() {
            // 获取PEX扩展处理器
            if let Some(pex_extension) = protocol.get_handler_as::<PEXExtension>("ut_pex") {
                // 获取待添加的对等点队列
                if let Some(pending_peers) = pex_extension.get_pending_peers() {
                    // 使用try_lock而不是await
                    if let Ok(mut peers) = pending_peers.try_lock() {
                        // 检查是否已经在队列中
                        if !peers.contains(&addr) {
                            debug!("Adding peer {} to PEX pending queue", addr);
                            peers.push(addr);
                        }
                    }
                }
            }
        }

        Ok(())
    }
}