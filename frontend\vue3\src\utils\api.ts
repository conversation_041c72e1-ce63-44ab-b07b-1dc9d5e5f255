/**
 * API 服务
 * 用于处理与后端的 HTTP 通信
 */
import axios from 'axios';
import { initWebSocket, addEventListener, removeEventListener, sendMessage } from './websocket';
import { DownloadAPI, TaskAPI, StatusAPI, SpeedAPI } from './apiConstants';

// 设置 axios 默认配置
axios.defaults.baseURL = 'http://localhost:8080/api/v1';

/**
 * 将 Motrix 的 aria2 RPC 请求转换为我们的 Rust 后端 API 请求
 *
 * @param {string} method - aria2 方法名
 * @param {Array} params - aria2 参数
 * @returns {Promise} - 返回 Promise
 */
export async function convertAria2Request(method: string, params: any[]) {
  // 方法映射
  const methodMap: Record<string, string> = {
    'aria2.addUri': DownloadAPI.ADD,
    'aria2.remove': DownloadAPI.CANCEL(':id'),
    'aria2.pause': DownloadAPI.PAUSE(':id'),
    'aria2.unpause': DownloadAPI.RESUME(':id'),
    'aria2.tellStatus': TaskAPI.GET(':id'),
    'aria2.tellActive': `${TaskAPI.BASE}?status=downloading`,
    'aria2.tellWaiting': `${TaskAPI.BASE}?status=pending`,
    'aria2.tellStopped': `${TaskAPI.BASE}?status=completed`,
    'aria2.getGlobalStat': StatusAPI.GET,
    'aria2.changeGlobalOption': SpeedAPI.DOWNLOAD,
    'aria2.getGlobalOption': SpeedAPI.DOWNLOAD
  };

  // 获取对应的 Rust 后端 API 路径
  let apiPath = methodMap[method] || '/fallback';

  // 转换参数
  const convertedParams = convertParams(method, params);

  // 确定请求方法
  let requestMethod = 'POST';
  let url = apiPath;
  let requestOptions: any = {
    method: requestMethod,
    headers: {
      'Content-Type': 'application/json'
    }
  };

  // 根据方法确定是 GET 还是 POST 请求
  if (method === 'aria2.tellActive' ||
      method === 'aria2.tellWaiting' ||
      method === 'aria2.tellStopped' ||
      method === 'aria2.getGlobalStat' ||
      method === 'aria2.getGlobalOption') {
    requestMethod = 'GET';
    requestOptions.method = 'GET';
    delete requestOptions.headers['Content-Type'];
  } else if (method === 'aria2.tellStatus') {
    requestMethod = 'GET';
    requestOptions.method = 'GET';
    // 替换URL中的ID参数
    url = apiPath.replace(':id', convertedParams.id);
    delete requestOptions.headers['Content-Type'];
  } else if (method === 'aria2.remove' || method === 'aria2.pause' || method === 'aria2.unpause') {
    // 替换URL中的ID参数
    url = apiPath.replace(':id', convertedParams.id || params[0]);
    requestOptions.data = {}; // 这些操作不需要请求体
  } else {
    // POST 请求需要添加 body
    requestOptions.data = convertedParams;
  }

  // 发送请求到 Rust 后端
  const response = await axios({
    url,
    ...requestOptions
  });

  // 转换响应为 aria2 格式
  return convertResponse(method, response.data);
}

/**
 * 转换参数
 *
 * @param {string} method - aria2 方法名
 * @param {Array} params - aria2 参数
 * @returns {Object} - 转换后的参数
 */
function convertParams(method: string, params: any[]) {
  switch (method) {
    case 'aria2.addUri':
      // params[0] 是 URI 数组，params[1] 是选项
      return {
        url: params[0][0],
        output_path: params[1]?.dir ? `${params[1].dir}/${getFileNameFromUrl(params[0][0])}` : undefined
      };
    case 'aria2.remove':
    case 'aria2.pause':
    case 'aria2.unpause':
    case 'aria2.tellStatus':
      // params[0] 是 GID
      return {
        id: params[0]
      };
    case 'aria2.tellActive':
    case 'aria2.tellWaiting':
    case 'aria2.tellStopped':
      // 不需要参数，使用查询参数
      return {};
    case 'aria2.getGlobalStat':
      // 不需要参数
      return {};
    case 'aria2.changeGlobalOption':
      // 转换速度限制参数
      if (params[0]['max-overall-download-limit'] !== undefined) {
        return {
          limit: parseInt(params[0]['max-overall-download-limit'])
        };
      }
      return params[0];
    case 'aria2.getGlobalOption':
      // 不需要参数
      return {};
    default:
      return {};
  }
}

/**
 * 从URL中提取文件名
 *
 * @param {string} url - URL
 * @returns {string} - 文件名
 */
function getFileNameFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const segments = pathname.split('/');
    const lastSegment = segments[segments.length - 1];

    if (lastSegment) {
      return lastSegment;
    }
  } catch (e) {
    // URL解析失败，尝试简单提取
    const segments = url.split('/');
    const lastSegment = segments[segments.length - 1];
    if (lastSegment) {
      return lastSegment;
    }
  }

  // 无法提取文件名，返回默认名称
  return 'download';
}

/**
 * 转换响应
 *
 * @param {string} method - aria2 方法名
 * @param {Object} data - Rust 后端响应数据
 * @returns {Object} - 转换后的响应
 */
function convertResponse(method: string, data: any) {
  // 如果响应包含success字段，提取data部分
  const responseData = data.success !== undefined ? data.data : data;

  if (!responseData && data.error) {
    console.error('API错误:', data.error);
    throw new Error(data.error.message || '未知错误');
  }

  switch (method) {
    case 'aria2.addUri':
      // 返回 GID
      return responseData.task_id;
    case 'aria2.tellStatus':
      // 转换任务状态
      return convertTaskStatus(responseData);
    case 'aria2.tellActive':
    case 'aria2.tellWaiting':
    case 'aria2.tellStopped':
      // 转换任务列表
      return Array.isArray(responseData) ? responseData.map(convertTaskStatus) : [];
    case 'aria2.getGlobalStat':
      // 转换全局统计
      return {
        downloadSpeed: responseData.download_speed?.toString() || '0',
        uploadSpeed: responseData.upload_speed?.toString() || '0',
        numActive: responseData.active_count?.toString() || '0',
        numWaiting: responseData.waiting_count?.toString() || '0',
        numStopped: responseData.stopped_count?.toString() || '0'
      };
    case 'aria2.getGlobalOption':
      // 转换全局选项
      return responseData;
    default:
      return responseData;
  }
}

/**
 * 转换任务状态
 *
 * @param {Object} task - 任务数据
 * @returns {Object} - 转换后的任务状态
 */
function convertTaskStatus(task: any) {
  if (!task) return {};

  // 状态映射
  const statusMap: Record<string, string> = {
    'pending': 'waiting',
    'initializing': 'waiting',
    'downloading': 'active',
    'paused': 'paused',
    'completed': 'complete',
    'failed': 'error',
    'cancelled': 'removed'
  };

  // 提取文件名
  const fileName = task.output_path ?
    task.output_path.split('/').pop() || task.output_path.split('\\').pop() || '' :
    '';

  return {
    gid: task.id,
    status: statusMap[task.status.toLowerCase()] || task.status,
    totalLength: task.total_size?.toString() || '0',
    completedLength: task.downloaded_size?.toString() || '0',
    downloadSpeed: task.speed?.toString() || '0',
    uploadSpeed: '0', // 后端暂未提供上传速度
    connections: '1', // 后端暂未提供连接数
    dir: task.output_path ? task.output_path.substring(0, task.output_path.lastIndexOf('/')) : '',
    files: [],
    fileName: fileName,
    errorCode: '0',
    errorMessage: task.error_message || '',
    bittorrent: null,
    infoHash: '',
    numPieces: '0',
    pieceLength: '0',
    bitfield: '',
    seeders: '0',
    peers: []
  };
}

/**
 * API 类
 * 提供与后端通信的方法
 */
class Api {
  /**
   * 初始化
   */
  constructor() {
    // 初始化 WebSocket 连接
    initWebSocket();
  }

  /**
   * 调用 API
   * @param {string} method - 方法名
   * @param  {...any} args - 参数
   * @returns {Promise} - 返回 Promise
   */
  async call(method: string, ...args: any[]) {
    return convertAria2Request(method, args);
  }

  /**
   * 批量调用 API
   * @param {Array} methods - 方法列表
   * @returns {Promise} - 返回 Promise
   */
  async multicall(methods: [string, ...any[]][]) {
    return Promise.all(methods.map(([method, ...args]) => {
      return this.call(method, ...args);
    }));
  }

  /**
   * 批量调用 API
   * @param {Array} methods - 方法列表
   * @returns {Promise} - 返回 Promise
   */
  async batch(methods: [string, ...any[]][]) {
    return this.multicall(methods);
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名
   * @param {Function} callback - 回调函数
   */
  on(event: string, callback: (payload: any) => void) {
    addEventListener(event, callback);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名
   * @param {Function} callback - 回调函数
   */
  off(event: string, callback: (payload: any) => void) {
    removeEventListener(event, callback);
  }

  /**
   * 发送消息
   * @param {string} event - 事件名
   * @param {Object} payload - 事件数据
   */
  send(event: string, payload: any) {
    sendMessage(event, payload);
  }
}

// 创建 API 实例
const api = new Api();

export default api;
