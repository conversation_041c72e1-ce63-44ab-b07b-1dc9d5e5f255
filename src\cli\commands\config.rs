//! 配置命令模块
//!
//! 实现与配置相关的命令。

use anyhow::Result;
use clap::{Args, Subcommand};
use serde_json::Value;
use std::fs;
use std::path::PathBuf;
use std::sync::Arc;
use log::{debug, trace, info, warn};

use crate::cli::backend::backend::CliBackend;
use crate::cli::backend::models::Config;
use crate::cli::utils::formatter::OutputFormat;
use crate::cli::utils::formatter::format_output;
use crate::cli::utils::debug::{print_debug_json, DebugLevel, DebugTimer};
use serde_json::json;

/// 配置命令
#[derive(Subcommand, Debug)]
pub enum ConfigCommands {
    /// 获取配置
    Get(GetConfigArgs),
    /// 更新配置
    Update(UpdateConfigArgs),
    /// 导出配置
    Export(ExportConfigArgs),
    /// 导入配置
    Import(ImportConfigArgs),
    /// 保存配置到默认路径
    Save,
    /// 重置配置到默认值
    Reset,
}

/// 获取配置参数
#[derive(Args, Debug)]
pub struct GetConfigArgs {
    /// 配置项路径（例如：server.host）
    pub path: Option<String>,
}

/// 更新配置参数
#[derive(Args, Debug)]
pub struct UpdateConfigArgs {
    /// 配置项路径（例如：server.host）
    pub path: String,
    /// 配置项值（JSON格式）
    pub value: String,
}

/// 导出配置参数
#[derive(Args, Debug)]
pub struct ExportConfigArgs {
    /// 导出文件路径
    pub file: PathBuf,
}

/// 导入配置参数
#[derive(Args, Debug)]
pub struct ImportConfigArgs {
    /// 导入文件路径
    pub file: PathBuf,
}

/// 处理配置命令
pub async fn handle_command(
    command: &ConfigCommands,
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("处理配置命令: {:?}", command);
    
    let result = match command {
        ConfigCommands::Get(args) => handle_get_config(args, backend, format).await,
        ConfigCommands::Update(args) => handle_update_config(args, backend, format).await,
        ConfigCommands::Export(args) => handle_export_config(args, backend, format).await,
        ConfigCommands::Import(args) => handle_import_config(args, backend, format).await,
        ConfigCommands::Save => handle_save_config(backend, format).await,
        ConfigCommands::Reset => handle_reset_config(backend, format).await,
    };
    
    if let Err(ref e) = result {
        warn!("配置命令执行失败: {}", e);
    } else {
        debug!("配置命令执行成功");
    }
    
    result
}

/// 从配置中提取特定路径的值
fn extract_config_value(config: &Config, path: &str) -> Value {
    debug!("提取配置值: path={}", path);
    
    // 将配置转换为JSON值
    let config_json = json!(config);
    
    // 按点分割路径
    let parts: Vec<&str> = path.split('.').collect();
    trace!("路径分割: {:?}", parts);
    
    // 递归提取值
    let mut current = &config_json;
    for part in parts {
        if let Some(value) = current.get(part) {
            current = value;
            trace!("找到路径部分: {} -> {:?}", part, current);
        } else {
            // 如果路径不存在，返回空值
            debug!("路径部分不存在: {}", part);
            return Value::Null;
        }
    }
    
    // 返回找到的值
    trace!("提取的最终值: {:?}", current);
    current.clone()
}

/// 处理获取配置命令
async fn handle_get_config(
    args: &GetConfigArgs,
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行获取配置: path={:?}", args.path);
    
    let mut timer = DebugTimer::new("获取配置");
    
    if let Some(path) = &args.path {
        // 获取特定配置项
        debug!("获取特定配置项: {}", path);
        let config = backend.get_config().await?;
        timer.checkpoint("获取配置对象");
        
        // 从配置中提取特定路径的值
        let value = extract_config_value(&config, path);
        timer.checkpoint("提取配置值");
        
        debug!("获取到配置值: path={}, value={:?}", path, value);
        print_debug_json("配置值", &value, DebugLevel::Verbose);
        format_output(value, format.unwrap_or(OutputFormat::Text));
    } else {
        // 获取全部配置
        debug!("获取全部配置");
        let config = backend.get_config().await?;
        timer.checkpoint("获取配置对象");
        
        let json_config = serde_json::json!(config);
        debug!("获取到完整配置，包含 {} 个顶级项", json_config.as_object().map_or(0, |obj| obj.len()));
        print_debug_json("完整配置", &json_config, DebugLevel::Full);
        format_output(json_config, format.unwrap_or(OutputFormat::Yaml));
    }
    
    let elapsed = timer.stop();
    debug!("获取配置总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理更新配置命令
async fn handle_update_config(
    args: &UpdateConfigArgs,
    backend: &Arc<dyn CliBackend>,
    _format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行更新配置: path={}, value={}", args.path, args.value);
    
    let mut timer = DebugTimer::new("更新配置");
    
    // 解析配置值
    let value: Value = serde_json::from_str(&args.value)?;
    timer.checkpoint("解析JSON值");
    debug!("解析JSON值: {:?}", value);
    
    // 更新配置项
    backend.update_config_item(&args.path, &value.to_string()).await?;
    timer.checkpoint("更新配置项");
    
    info!("配置已更新: path={}, value={}", args.path, args.value);
    println!("配置项 {} 已更新为 {}", args.path, args.value);
    
    let elapsed = timer.stop();
    debug!("更新配置总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理导出配置命令
async fn handle_export_config(
    args: &ExportConfigArgs,
    backend: &Arc<dyn CliBackend>,
    _format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行导出配置: file={}", args.file.display());
    
    let mut timer = DebugTimer::new("导出配置");
    
    // 获取配置
    let config = backend.get_config().await?;
    timer.checkpoint("获取配置对象");
    
    // 序列化为JSON
    let json = serde_json::to_string_pretty(&config)?;
    timer.checkpoint("序列化为JSON");
    debug!("配置序列化为JSON，长度: {} 字节", json.len());
    
    // 写入文件
    fs::write(&args.file, &json)?;
    timer.checkpoint("写入文件");
    
    info!("配置已导出到: {}", args.file.display());
    println!("配置已导出到: {}", args.file.display());
    
    let elapsed = timer.stop();
    debug!("导出配置总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理导入配置命令
async fn handle_import_config(
    args: &ImportConfigArgs,
    backend: &Arc<dyn CliBackend>,
    _format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行导入配置: file={}", args.file.display());
    
    let mut timer = DebugTimer::new("导入配置");
    
    // 读取文件
    let json = fs::read_to_string(&args.file)?;
    timer.checkpoint("读取文件");
    debug!("读取配置文件，长度: {} 字节", json.len());
    
    // 反序列化
    let config: Config = serde_json::from_str(&json)?;
    timer.checkpoint("反序列化配置");
    
    // 更新配置
    backend.update_config(config).await?;
    timer.checkpoint("更新配置");
    
    info!("已从 {} 导入配置", args.file.display());
    println!("已从 {} 导入配置", args.file.display());
    
    let elapsed = timer.stop();
    debug!("导入配置总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理保存配置命令
async fn handle_save_config(
    backend: &Arc<dyn CliBackend>,
    _format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行保存配置");
    
    let mut timer = DebugTimer::new("保存配置");
    
    // 保存配置到默认路径
    backend.save_config().await?;
    timer.checkpoint("保存配置");
    
    info!("配置已保存到默认路径");
    println!("配置已保存到默认路径");
    
    let elapsed = timer.stop();
    debug!("保存配置总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理重置配置命令
async fn handle_reset_config(
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行重置配置");
    
    let mut timer = DebugTimer::new("重置配置");
    
    // 重置配置到默认值
    backend.reset_config().await?;
    timer.checkpoint("重置配置");
    
    info!("配置已重置为默认值");
    
    // 输出结果
    let result = json!({"status": "success", "message": "Config reset to default values successfully"});
    print_debug_json("重置结果", &result, DebugLevel::Verbose);
    format_output(result, format.unwrap_or(OutputFormat::Json));
    
    let elapsed = timer.stop();
    debug!("重置配置总耗时: {:?}", elapsed);
    
    Ok(())
}