import { defineStore } from 'pinia';
import axios from 'axios';

// 应用状态类型
interface AppState {
  appName: string;
  appVersion: string;
  engineInfo: {
    version: string;
    enabledFeatures: string[];
  };
  engineOptions: Record<string, any>;
  stat: {
    downloadSpeed: number;
    uploadSpeed: number;
    numActive: number;
    numWaiting: number;
    numStopped: number;
  };
  addTaskVisible: boolean;
  addTaskType: string;
  addTaskUrl: string;
  progress: number;
  aboutPanelVisible: boolean;
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    appName: 'Lumen',
    appVersion: '1.0.0',
    engineInfo: {
      version: '',
      enabledFeatures: []
    },
    engineOptions: {},
    stat: {
      downloadSpeed: 0,
      uploadSpeed: 0,
      numActive: 0,
      numWaiting: 0,
      numStopped: 0
    },
    addTaskVisible: false,
    addTaskType: 'uri',
    addTaskUrl: '',
    progress: 0,
    aboutPanelVisible: false
  }),
  
  getters: {
    // 下载速度（格式化）
    formattedDownloadSpeed: (state) => {
      return formatSpeed(state.stat.downloadSpeed);
    },
    
    // 上传速度（格式化）
    formattedUploadSpeed: (state) => {
      return formatSpeed(state.stat.uploadSpeed);
    }
  },
  
  actions: {
    // 获取应用信息
    async fetchAppInfo() {
      try {
        // 获取版本信息
        const versionResponse = await axios.get('/version');
        this.engineInfo = versionResponse.data;
        
        // 获取全局选项
        const optionsResponse = await axios.get('/download/settings');
        this.engineOptions = optionsResponse.data;
        
        // 开始定时获取统计信息
        this.startFetchingStat();
      } catch (error) {
        console.error('获取应用信息失败:', error);
      }
    },
    
    // 开始定时获取统计信息
    startFetchingStat() {
      // 每秒获取一次统计信息
      setInterval(() => {
        this.fetchGlobalStat();
      }, 1000);
    },
    
    // 获取全局统计信息
    async fetchGlobalStat() {
      try {
        const response = await axios.get('/download/stats');
        const data = response.data;
        
        // 更新统计信息
        this.stat = {
          downloadSpeed: Number(data.downloadSpeed || 0),
          uploadSpeed: Number(data.uploadSpeed || 0),
          numActive: Number(data.numActive || 0),
          numWaiting: Number(data.numWaiting || 0),
          numStopped: Number(data.numStopped || 0)
        };
        
        // 更新进度
        this.fetchProgress();
      } catch (error) {
        console.error('获取统计信息失败:', error);
      }
    },
    
    // 获取下载进度
    async fetchProgress() {
      try {
        const response = await axios.get('/download/list?status=active');
        const data = response.data.tasks || [];
        
        let progress = -1;
        if (data.length !== 0) {
          const tasks = data.filter((task: any) => task.totalLength !== 0);
          if (tasks.length > 0) {
            const completed = tasks.reduce((total: number, task: any) => total + Number(task.completedLength), 0);
            const total = tasks.reduce((total: number, task: any) => total + Number(task.totalLength), 0);
            progress = completed / total;
          } else {
            progress = 2; // 表示无法计算进度
          }
        }
        
        this.progress = progress;
      } catch (error) {
        console.error('获取进度信息失败:', error);
      }
    },
    
    // 显示添加任务对话框
    showAddTaskDialog(taskType: string) {
      this.addTaskType = taskType;
      this.addTaskVisible = true;
    },
    
    // 隐藏添加任务对话框
    hideAddTaskDialog() {
      this.addTaskVisible = false;
    },
    
    // 设置添加任务的URL
    updateAddTaskUrl(url: string) {
      this.addTaskUrl = url;
    },
    
    // 显示关于面板
    showAboutPanel() {
      this.aboutPanelVisible = true;
    },
    
    // 隐藏关于面板
    hideAboutPanel() {
      this.aboutPanelVisible = false;
    }
  }
});

// 格式化速度
function formatSpeed(bytes: number): string {
  if (bytes === 0) return '0 B/s';
  
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s', 'TB/s'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
}
