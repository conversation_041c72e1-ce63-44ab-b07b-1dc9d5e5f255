use anyhow::Result;
use std::sync::Arc;
use uuid::Uuid;

use tonitru_downloader::core::p2p::piece::{PieceState, PieceManager};
use tonitru_downloader::protocols::bittorrent::torrent::TorrentInfo;
use tonitru_downloader::protocols::bittorrent::piece_manager::BitTorrentPieceManager;
use tonitru_downloader::storage::storage_impl::LocalStorage;

/// Create a mock TorrentInfo for testing
fn create_mock_torrent_info() -> TorrentInfo {
    let piece_hashes = vec![
        vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
        vec![21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40],
        vec![41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60],
    ];

    TorrentInfo {
        name: "test_torrent".to_string(),
        announce: Some("http://tracker.example.com/announce".to_string()),
        announce_list: Some(vec![
            vec!["http://tracker.example.com/announce".to_string()],
        ]),
        creation_date: None,
        comment: None,
        created_by: None,
        encoding: None,
        piece_length: 16384, // 16 KB
        pieces: piece_hashes,
        files: None,
        length: 49152, // 48 KB (3 pieces)
        total_size: 49152,
        info_hash: vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
        info_hash_hex: "0102030405060708090a0b0c0d0e0f1011121314".to_string(),
        info_hash_encoded: "%01%02%03%04%05%06%07%08%09%0A%0B%0C%0D%0E%0F%10%11%12%13%14".to_string(),
        url_list: None,
        http_seeds: None,
    }
}

/// Test creating a PieceManager
#[tokio::test]
async fn test_piece_manager_creation() -> Result<()> {
    let torrent_info = create_mock_torrent_info();
    let output_path = format!("./test_output_{}", Uuid::new_v4());
    let storage = Arc::new(LocalStorage::new(&output_path));

    let piece_manager = BitTorrentPieceManager::new(
        torrent_info.clone(),
        output_path.clone()
    ).await?;

    // Verify the piece manager was created correctly
    assert_eq!(piece_manager.total_size, torrent_info.total_size);
    assert_eq!(piece_manager.pieces_total, 3); // 3 pieces in our mock torrent

    // Clean up
    std::fs::remove_dir_all(&output_path).ok();

    Ok(())
}

/// Test initializing the PieceManager
#[tokio::test]
async fn test_piece_manager_init() -> Result<()> {
    let torrent_info = create_mock_torrent_info();
    let output_path = format!("./test_output_{}", Uuid::new_v4());
    let storage = Arc::new(LocalStorage::new(&output_path));

    let mut piece_manager = BitTorrentPieceManager::new(
        torrent_info.clone(),
        output_path.clone()
    ).await?;

    // Initialize the piece manager
    piece_manager.init().await?;

    // Verify pieces were initialized
    assert_eq!(piece_manager.pieces.len(), 3);

    // Verify piece states
    for piece in &piece_manager.pieces {
        assert_eq!(piece.state, PieceState::Missing);
        assert_eq!(piece.size, torrent_info.piece_length);
    }

    // Verify the last piece has the correct length
    let last_piece = &piece_manager.pieces[2];
    assert_eq!(last_piece.size, torrent_info.piece_length);

    // Clean up
    std::fs::remove_dir_all(&output_path).ok();

    Ok(())
}

/// Test getting piece info
#[tokio::test]
async fn test_get_piece_info() -> Result<()> {
    let torrent_info = create_mock_torrent_info();
    let output_path = format!("./test_output_{}", Uuid::new_v4());
    let storage = Arc::new(LocalStorage::new(&output_path));

    let mut piece_manager = BitTorrentPieceManager::new(
        torrent_info.clone(),
        output_path.clone()
    ).await?;

    // Initialize the piece manager
    piece_manager.init().await?;

    // Get piece info for each piece
    for i in 0..3 {
        let piece_info = piece_manager.get_piece_info(i as u32).await?;

        if let Some(info) = piece_info {
            assert_eq!(info.index, i as u32);
            assert_eq!(info.size, torrent_info.piece_length);
            assert_eq!(info.state, PieceState::Missing);
            assert_eq!(info.hash.unwrap(), torrent_info.pieces[i]);
        } else {
            panic!("Expected piece info for piece {}", i);
        }
    }

    // Test getting info for a non-existent piece
    let result = piece_manager.get_piece_info(3).await?;
    assert!(result.is_none());

    // Clean up
    std::fs::remove_dir_all(&output_path).ok();

    Ok(())
}

/// Test updating piece state
#[tokio::test]
async fn test_update_piece_state() -> Result<()> {
    let torrent_info = create_mock_torrent_info();
    let output_path = format!("./test_output_{}", Uuid::new_v4());
    let storage = Arc::new(LocalStorage::new(&output_path));

    let mut piece_manager = BitTorrentPieceManager::new(
        torrent_info.clone(),
        output_path.clone()
    ).await?;

    // Initialize the piece manager
    piece_manager.init().await?;

    // Update piece state
    piece_manager.update_piece_state(0, PieceState::Requested).await?;

    // Verify the state was updated
    let piece_info = piece_manager.get_piece_info(0).await?;
    if let Some(info) = piece_info {
        assert_eq!(info.state, PieceState::Requested);
    } else {
        panic!("Expected piece info for piece 0");
    }

    // Update to another state
    piece_manager.update_piece_state(0, PieceState::Downloaded).await?;

    // Verify the state was updated again
    let piece_info = piece_manager.get_piece_info(0).await?;
    if let Some(info) = piece_info {
        assert_eq!(info.state, PieceState::Downloaded);
    } else {
        panic!("Expected piece info for piece 0");
    }

    // Test updating a non-existent piece
    let result = piece_manager.update_piece_state(3, PieceState::Requested).await;
    assert!(result.is_err());

    // Clean up
    std::fs::remove_dir_all(&output_path).ok();

    Ok(())
}

/// Test getting download progress
#[tokio::test]
async fn test_get_download_progress() -> Result<()> {
    let torrent_info = create_mock_torrent_info();
    let output_path = format!("./test_output_{}", Uuid::new_v4());
    let storage = Arc::new(LocalStorage::new(&output_path));

    let mut piece_manager = BitTorrentPieceManager::new(
        torrent_info.clone(),
        output_path.clone()
    ).await?;

    // Initialize the piece manager
    piece_manager.init().await?;

    // Initially, progress should be 0
    let progress = piece_manager.progress().await?;
    assert_eq!(progress, 0.0);

    // Mark one piece as verified
    piece_manager.update_piece_state(0, PieceState::Verified).await?;

    // Progress should now be 1/3
    let progress = piece_manager.progress().await?;
    assert_eq!(progress, 1.0 / 3.0);

    // Mark another piece as verified
    piece_manager.update_piece_state(1, PieceState::Verified).await?;

    // Progress should now be 2/3
    let progress = piece_manager.progress().await?;
    assert_eq!(progress, 2.0 / 3.0);

    // Mark the last piece as verified
    piece_manager.update_piece_state(2, PieceState::Verified).await?;

    // Progress should now be 1.0 (100%)
    let progress = piece_manager.progress().await?;
    assert_eq!(progress, 1.0);

    // Clean up
    std::fs::remove_dir_all(&output_path).ok();

    Ok(())
}
