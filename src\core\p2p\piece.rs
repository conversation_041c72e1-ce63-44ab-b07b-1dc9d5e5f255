use async_trait::async_trait;
use anyhow::{Result, anyhow};
use bytes::Bytes;
use std::collections::HashSet;
use tokio::fs;
use tokio::io::{AsyncReadExt, AsyncSeekExt};
use tracing::debug;
use crate::protocols::bittorrent::error::BitTorrentError;
use super::piece_cache::{PieceCache, PieceCacheConfig};

// 创建全局分片缓存实例
lazy_static::lazy_static! {
    pub static ref PIECE_CACHE: PieceCache = {
        let config = PieceCacheConfig::default();
        PieceCache::new(config)
    };
}

/// 分片状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PieceState {
    /// 缺失
    Missing,
    /// 已请求
    Requested,
    /// 下载中
    Downloading,
    /// 已下载
    Downloaded,
    /// 已验证
    Verified,
    /// 验证失败
    Failed,
}

/// 分片信息
#[derive(Debug, Clone)]
pub struct PieceInfo {
    /// 分片索引
    pub index: u32,
    /// 分片大小
    pub size: u64,
    /// 分片哈希
    pub hash: Option<Vec<u8>>,
    /// 分片状态
    pub state: PieceState,
    /// 分片优先级
    pub priority: u8,
    /// 下载进度
    pub progress: f64,
}

/// 分片管理接口
/// 定义分片下载和管理的基本操作
#[async_trait]
pub trait PieceManager: Send + Sync {
    /// 初始化分片管理器
    async fn init(&mut self) -> Result<(), BitTorrentError>;

    /// 获取下一个要下载的分片
    async fn next_piece(&mut self) -> Result<Option<PieceInfo>, BitTorrentError>;

    /// 添加分片数据
    async fn add_piece_data(&mut self, index: u32, data: Vec<u8>) -> Result<bool, BitTorrentError>;
    
    /// 验证分片
    async fn verify_piece(&mut self, index: u32) -> Result<bool, BitTorrentError>;

    /// 获取分片状态
    async fn piece_state(&self, index: u32) -> Result<PieceState, BitTorrentError>;

    /// 获取所有分片状态
    async fn all_piece_states(&self) -> Result<Vec<PieceState>, BitTorrentError>;
    
    /// 获取下载进度
    async fn progress(&self) -> Result<f64, BitTorrentError>;

    /// 获取已下载数据大小
    async fn downloaded_size(&self) -> Result<u64, BitTorrentError>;

    /// 获取总数据大小
    async fn total_size(&self) -> Result<u64, BitTorrentError>;

    /// 保存下载的数据
    async fn save_data(&self) -> Result<(), BitTorrentError>;

    /// 检查是否所有分片都已完成
    async fn is_complete(&self) -> Result<bool, BitTorrentError>;

    /// 将自身转换为 Any 类型，用于类型转换
    fn as_any(&self) -> &dyn std::any::Any;
    
    /// 设置分片状态
    ///
    /// 将指定索引的分片状态设置为新状态。
    /// 这个方法提供了一个通用的状态转换机制，可以用于任何状态转换。
    async fn set_piece_state(&mut self, index: u32, state: PieceState) -> Result<(), BitTorrentError> {
        // 获取当前分片状态
        let current_state = self.piece_state(index).await?;

        // 检查状态转换是否有效
        match (current_state, state) {
            // 允许从Missing转换到Requested
            (PieceState::Missing, PieceState::Requested) => {},

            // 允许从Requested转换到Downloading
            (PieceState::Requested, PieceState::Downloading) => {},

            // 允许从Downloading转换到Downloaded
            (PieceState::Downloading, PieceState::Downloaded) => {},

            // 允许从Downloaded转换到Verified或Failed
            (PieceState::Downloaded, PieceState::Verified) => {},
            (PieceState::Downloaded, PieceState::Failed) => {},

            // 允许从Failed转换到Missing（重试）
            (PieceState::Failed, PieceState::Missing) => {},

            // 允许从任何状态转换到Missing（重置）
            (_, PieceState::Missing) => {},

            // 其他转换不允许
            _ => {
                return Err(anyhow!(
                    "Invalid state transition: {} -> {}: index={}",
                    format!("{:?}", current_state),
                    format!("{:?}", state),
                    index
                ));
            }
        }

        // 获取所有分片状态的可变引用
        let mut states = self.all_piece_states().await?;

        // 检查索引是否有效
        if index as usize >= states.len() {
            return Err(anyhow!("Invalid piece index: {}", index));
        }

        // 更新分片状态
        states[index as usize] = state;

        // 将更新后的状态保存到持久化存储
        self.update_piece_state(index, state).await?;

        debug!("Set piece {} state from {:?} to {:?}", index, current_state, state);

        Ok(())
    }

    /// 获取分片大小
    ///
    /// 默认实现返回0，子类应该覆盖此方法提供实际大小
    async fn get_piece_size(&self, index: u32) -> Result<u64> {
        // 获取总大小和分片数量，计算平均分片大小
        let total_size = self.total_size().await?;
        let all_states = self.all_piece_states().await?;
        let piece_count = all_states.len();

        if piece_count == 0 {
            return Ok(0);
        }

        // 如果是最后一个分片，可能大小不同
        if index as usize == piece_count - 1 {
            let regular_piece_size = total_size / piece_count as u64;
            let remainder = total_size % piece_count as u64;

            if remainder > 0 {
                return Ok(remainder);
            }

            return Ok(regular_piece_size);
        }

        // 常规分片大小
        Ok(total_size / piece_count as u64)
    }

    /// 获取分片信息
    async fn get_piece_info(&self, index: u32) -> Result<Option<PieceInfo>, BitTorrentError> {
        // 获取分片状态
        let state = self.piece_state(index).await?;

        // 获取分片大小
        let size = self.get_piece_size(index).await?;

        // 创建基本的分片信息
        let piece_info = PieceInfo {
            index,
            size,
            hash: None,
            state,
            priority: 1,
            progress: 0.0,
        };

        Ok(Some(piece_info))
    }

    /// 获取分片稀有度
    async fn get_piece_rarities(&self) -> Result<Vec<usize>, BitTorrentError> {
        // 获取所有分片状态
        let states = self.all_piece_states().await?;

        // 创建默认稀有度向量，所有分片稀有度默认为1
        // 这表示每个分片至少有一个来源（本地）
        let mut rarities = vec![1; states.len()];

        // 对于已验证的分片，稀有度设为最大值，因为我们已经有了
        for (index, state) in states.iter().enumerate() {
            if *state == PieceState::Verified {
                rarities[index] = usize::MAX;
            }
        }

        Ok(rarities)
    }

    /// 获取已请求的分片
    async fn get_requested_pieces(&self) -> Result<HashSet<u32>, BitTorrentError> {
        // 获取所有分片状态
        let states = self.all_piece_states().await?;

        // 收集所有状态为Requested的分片索引
        let mut requested_pieces = HashSet::new();
        for (index, state) in states.iter().enumerate() {
            if *state == PieceState::Requested {
                requested_pieces.insert(index as u32);
            }
        }

        Ok(requested_pieces)
    }

    /// 标记分片为已请求
    async fn mark_piece_requested(&mut self, index: u32) -> Result<()> {
        // 使用通用的状态转换方法将状态设置为Requested
        // 这会自动检查当前状态是否为Missing，并执行状态转换
        self.set_piece_state(index, PieceState::Requested).await
    }

    /// 更新分片状态
    ///
    /// 这是一个内部方法，用于更新分片状态。默认实现只记录日志，
    /// 具体实现类应该覆盖此方法以提供实际的状态更新逻辑。
    async fn update_piece_state(&mut self, index: u32, state: PieceState) -> Result<()> {
        debug!("Updated piece {} state to {:?}", index, state);
        Ok(())
    }

    /// 读取块数据
    ///
    /// # 参数
    /// * `index` - 分片索引
    /// * `begin` - 块在分片中的起始偏移量
    /// * `length` - 要读取的数据长度
    ///
    /// # 返回值
    /// 返回读取的数据块。如果读取失败，返回错误。
    ///
    /// # 错误
    /// 如果分片索引无效、分片未验证、请求范围无效或读取操作失败，将返回错误。
    async fn read_block(&self, index: u32, begin: u32, length: u32) -> Result<Vec<u8>> {
        // 获取分片状态
        let state = self.piece_state(index).await?;

        // 检查分片状态，只有已验证的分片才能读取
        if state != PieceState::Verified {
            return Err(anyhow!("Cannot read from unverified piece: index={}, state={:?}", index, state));
        }

        // 获取分片信息
        let piece_info = match self.get_piece_info(index).await? {
            Some(info) => info,
            None => return Err(anyhow!("Piece info not found for index={}", index)),
        };

        // 检查请求范围是否有效
        if begin as u64 + length as u64 > piece_info.size {
            return Err(anyhow!(
                "Invalid block range: index={}, begin={}, length={}, piece_size={}",
                index, begin, length, piece_info.size
            ));
        }

        // 尝试从存储中读取数据
        // 首先尝试从缓存中读取，因为这是最快的
        let mut data = vec![0u8; length as usize];

        // 1. 首先尝试从块缓存中读取
        let block_cache_key = format!("block:{}:{}:{}", index, begin, length);
        if let Some(cached_data) = PIECE_CACHE.get(&block_cache_key).await {
            debug!("Cache hit for block: index={}, begin={}, length={}",
                   index, begin, length);

            // 将 Bytes 转换为 Vec<u8>
            data.copy_from_slice(&cached_data);
            return Ok(data);
        }

        // 2. 尝试从整个分片缓存中读取
        let piece_cache_key = format!("piece:{}", index);
        if let Some(piece_data) = PIECE_CACHE.get(&piece_cache_key).await {
            if begin as usize + length as usize <= piece_data.len() {
                debug!("Found piece in cache, extracting block: index={}, begin={}, length={}",
                       index, begin, length);
                data.copy_from_slice(&piece_data.slice(begin as usize..(begin as usize + length as usize)));

                // 缓存提取的块，以便下次直接命中
                let block_data = piece_data.slice(begin as usize..(begin as usize + length as usize));
                let _ = PIECE_CACHE.set(block_cache_key, block_data).await;

                return Ok(data);
            }
        }

        // 3. 尝试从标准下载目录读取
        let download_dir = std::env::current_dir()
            .unwrap_or_else(|_| std::path::PathBuf::from("."))
            .join("downloads");

        // 构建文件路径 - 使用分片索引作为文件名
        let file_path = download_dir.join(format!("piece_{}.dat", index));

        // 尝试从文件中读取数据
        if let Ok(mut file) = fs::File::open(&file_path).await {
            // 设置文件读取位置
            if file.seek(std::io::SeekFrom::Start(begin as u64)).await.is_ok() {
                // 读取数据
                if file.read_exact(&mut data).await.is_ok() {
                    // 数据读取成功
                    debug!("Read block data from file: {:?}, index={}, begin={}, length={}",
                           file_path, index, begin, length);

                    // 缓存读取的块
                    let block_data = Bytes::from(data.clone());
                    let _ = PIECE_CACHE.set(block_cache_key, block_data).await;

                    return Ok(data);
                }
            }
        }

        // 4. 尝试从临时目录读取
        let temp_dir = std::env::temp_dir().join("tonitru_downloader");
        let temp_file_path = temp_dir.join(format!("piece_{}.dat", index));

        if let Ok(mut file) = fs::File::open(&temp_file_path).await {
            // 设置文件读取位置
            if file.seek(std::io::SeekFrom::Start(begin as u64)).await.is_ok() {
                // 读取数据
                if file.read_exact(&mut data).await.is_ok() {
                    // 数据读取成功
                    debug!("Read block data from temp file: {:?}, index={}, begin={}, length={}",
                           temp_file_path, index, begin, length);

                    // 缓存读取的块
                    let block_data = Bytes::from(data.clone());
                    let _ = PIECE_CACHE.set(block_cache_key, block_data).await;

                    return Ok(data);
                }
            }
        }

        // 如果所有尝试都失败，返回错误
        return Err(anyhow!(
            "Block data not available: index={}, begin={}, length={}",
            index, begin, length
        ));
    }
}

/// 分片管理器工厂接口
/// 用于创建分片管理器
#[async_trait]
pub trait PieceManagerFactory: Send + Sync {
    /// 创建分片管理器
    async fn create_piece_manager(&self, file_path: &str) -> Result<Box<dyn PieceManager>>;
}
