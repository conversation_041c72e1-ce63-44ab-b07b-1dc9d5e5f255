#![cfg(feature = "integration_tests")]

use anyhow::Result;
use std::sync::Arc;
use tokio::fs;

use uuid::Uuid;
use chrono::Utc;

use tonitru_downloader::config::{Settings, ConfigManager};
use tonitru_downloader::download::manager::DownloadManagerImpl;
use tonitru_downloader::download::manager::DownloadManager;
use tonitru_downloader::download::resume::ResumeManagerImpl;
use tonitru_downloader::download::bandwidth_scheduler::BandwidthSchedulerImpl;
use tonitru_downloader::analyzer::link_analyzer::LinkAnalyzerImpl;
use tonitru_downloader::storage::storage_impl::LocalStorage;
use tonitru_downloader::download::downloader_factory_impl::DownloaderFactoryImpl;
use tonitru_downloader::download::manager::{TaskStatus, TaskInfo}; // Corrected imports for TaskStatus and TaskInfo
use tonitru_downloader::download::event_notifier::NoopEventNotifier;
use tonitru_downloader::protocols::custom_p2p::CustomP2PFactory;

mod common;
use crate::common::start_test_http_server; 

/// Test the complete download flow with HTTP protocol using a real external URL
#[tokio::test]
async fn test_http_download_flow() -> Result<()> {
    println!("Starting test_http_download_flow");
    // User provided URL
    let url = "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png".to_string();
    println!("Debug: Using URL {}", url);
    
    // Create a temporary directory for downloads
    let base_temp_dir = std::env::temp_dir();
    let temp_dir = base_temp_dir.join("tonitru_tests").join("test_downloads_http_flow_external");
    println!("Using temp directory: {}", temp_dir.display());
    if temp_dir.exists() {
        println!("Removing existing temp directory");
        match fs::remove_dir_all(&temp_dir).await {
            Ok(_) => println!("Successfully removed existing temp directory"),
            Err(e) => println!("Error removing temp directory: {}", e),
        }
    }
    println!("Creating temp directory");
    match fs::create_dir_all(&temp_dir).await {
        Ok(_) => println!("Successfully created temp directory"),
        Err(e) => {
            println!("Error creating temp directory: {}", e);
            return Err(anyhow::anyhow!("Failed to create temp directory: {}", e));
        }
    }
    
    // Create a settings object
    println!("Creating settings");
    let mut settings = Settings::default();
    settings.download.path = temp_dir.to_string_lossy().to_string();
    println!("Download path set to: {}", settings.download.path);

    // Create a storage
    println!("Creating storage");
    let storage = Arc::new(LocalStorage::new(&settings.download.path));
    
    // Create a resume manager
    println!("Creating resume manager");
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage.clone()));
    
    // Initialize the resume manager
    println!("Initializing resume manager");
    match resume_manager.init().await {
        Ok(_) => println!("Resume manager initialized successfully"),
        Err(e) => {
            println!("Error initializing resume manager: {}", e);
            return Err(anyhow::anyhow!("Failed to initialize resume manager: {}", e));
        }
    }
    
    // Create a bandwidth scheduler
    println!("Creating bandwidth scheduler");
    let bandwidth_scheduler = Arc::new(BandwidthSchedulerImpl::new());
    
    // Create a link analyzer
    println!("Creating link analyzer");
    let link_analyzer = Arc::new(LinkAnalyzerImpl::new());
    
    // Create a config manager and downloader factory
    println!("Creating config manager and downloader factory");
    let config_manager = Arc::new(ConfigManager::new_with_settings(settings.clone()));
    let downloader_factory = Arc::new(
        DownloaderFactoryImpl::new(
            config_manager.clone(),
            link_analyzer.clone(),
            resume_manager.clone(),
        )
        .with_bandwidth_scheduler(bandwidth_scheduler.clone()),
    );

    // Create a download manager
    let event_notifier = Arc::new(NoopEventNotifier {});
    let p2p_factory = CustomP2PFactory::new(config_manager.clone());
    let download_manager = DownloadManagerImpl::new(
        event_notifier,
        settings.clone(),
        link_analyzer.clone(),
        p2p_factory,
        resume_manager.clone(),
        downloader_factory
    );
    
    // Add a download task
    println!("Adding download task");
    // 定义输出路径和文件名
    let output_filename = "downloaded_file.png";
    let output_path_str = temp_dir.join(output_filename).to_string_lossy().to_string();
    let task_id = Uuid::new_v4();
    let task = TaskInfo {
        id: task_id,
        url: url.clone(),
        output_path: output_path_str.clone(),
        status: TaskStatus::Pending,
        progress: 0.0,
        speed: 0,
        downloaded_size: 0,
        uploaded_bytes: 0,  // 添加缺少的字段
        total_size: None,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        error_message: None,
    };
    match download_manager.add_task(task).await {
        Ok(id) => {
            println!("Task added successfully with ID: {}", id);
            // 确保task_id与返回的id一致
            assert_eq!(task_id, id, "Task ID mismatch");
        },
        Err(e) => {
            println!("Error adding task: {}", e);
            return Err(anyhow::anyhow!("Failed to add task: {}", e));
        }
    };
    
    // Start the download
    println!("Starting download");
    match download_manager.start_task(task_id).await {
        Ok(_) => println!("Download started successfully"),
        Err(e) => {
            eprintln!("Failed to start download: {}", e);
            return Err(e.into());
        }
    }
    
    // Wait for the download to complete
    let mut status = TaskStatus::Initializing;
    // Increased timeout for potentially slower real-world downloads and larger files
    for i in 0..120 { 
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        
        let task_info_option = download_manager.get_task(task_id).await;
        if task_info_option.is_err() {
            eprintln!("Error getting task info: {:?}", task_info_option.err());
            status = TaskStatus::Failed; // Assume failure if we can't get task info
            break;
        }
        let task_info = task_info_option.unwrap();
        status = task_info.status;
        println!("Download progress: {:.2}%, Status: {:?}, Iteration: {}, Speed: {} bytes/s, Downloaded: {} bytes, Error: {:?}", 
                 task_info.progress, status, i, task_info.speed, task_info.downloaded_size, task_info.error_message);

        if status == TaskStatus::Completed || status == TaskStatus::Failed {
            if status == TaskStatus::Failed {
                eprintln!("Download failed with error: {:?}", task_info.error_message);
            }
            break;
        }
    }
    
    // Verify the download was successful
    assert_eq!(status, TaskStatus::Completed, "Download failed or timed out. Final status: {:?}. Check logs for details.", status);
    
    // Verify the downloaded file exists and is not empty
    println!("Verifying downloaded file: {}", output_path_str);
    match fs::metadata(&output_path_str).await {
        Ok(metadata) => {
            println!("File metadata: is_file={}, size={} bytes", metadata.is_file(), metadata.len());
            assert!(metadata.is_file(), "Output is not a file");
            assert!(metadata.len() > 0, "Downloaded file is empty");
            println!("Downloaded file {} successfully, size: {} bytes", output_filename, metadata.len());
        },
        Err(e) => {
            eprintln!("Error getting file metadata: {}", e);
            panic!("Failed to get file metadata: {}", e);
        }
    }

    // Clean up
    // fs::remove_dir_all(&temp_dir).await?; // Optionally keep files for inspection
    
    Ok(())
}

#[tokio::test]
async fn test_very_simple_print() -> Result<()> {
    println!("SIMPLE TEST: This is a very simple test printing to stdout.");
    eprintln!("SIMPLE TEST: This is a very simple test printing to stderr.");
    assert_eq!(1, 1);
    Ok(())
}

/// Test pausing and resuming a download
#[tokio::test]
async fn test_pause_resume_download() -> Result<()> {
    // Start a test server
    let (server_url, shutdown_tx) = start_test_http_server().await?;
    
    // Create a test file (large enough to test pause/resume)
    let test_content = "This is a test file for HTTP downloader pause/resume test.\n".repeat(1000);
    let current_dir = std::env::current_dir().unwrap();
    let test_file_path = current_dir.join("tests").join("files").join("pause_resume_test.txt");
    fs::write(&test_file_path, &test_content).await?;
    
    // Create a temporary directory for downloads
    let base_temp_dir = std::env::temp_dir();
    let temp_dir = base_temp_dir.join("tonitru_tests").join("test_downloads_pause_resume");
    if temp_dir.exists() {
        fs::remove_dir_all(&temp_dir).await?;
    }
    fs::create_dir_all(&temp_dir).await?;
    
    // Create a settings object
    let mut settings = Settings::default();
    settings.download.path = temp_dir.to_string_lossy().to_string();
    let config_manager = Arc::new(ConfigManager::new_with_settings(settings.clone()));

    // Create a storage
    let storage = Arc::new(LocalStorage::new(&settings.download.path));
    
    // Create a resume manager
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage.clone()));

    // Initialize the resume manager
    resume_manager.init().await?;
    
    // Create a bandwidth scheduler
    let bandwidth_scheduler = Arc::new(BandwidthSchedulerImpl::new());
    
    // Create a link analyzer
    let link_analyzer = Arc::new(LinkAnalyzerImpl::new());
    
    // Create a BitTorrent factory
    let downloader_factory = Arc::new(
        DownloaderFactoryImpl::new(
            config_manager.clone(),
            link_analyzer.clone(),
            resume_manager.clone(),
        )
        .with_bandwidth_scheduler(bandwidth_scheduler.clone()),
    );

    // Create a download manager
    let event_notifier = Arc::new(NoopEventNotifier {});
    let p2p_factory = CustomP2PFactory::new(config_manager.clone());
    let download_manager = DownloadManagerImpl::new(
        event_notifier,
        settings.clone(),
        link_analyzer.clone(),
        p2p_factory,
        resume_manager.clone(),
        downloader_factory
    );
    
    // Add a download task
    let url = format!("{}/files/pause_resume_test.txt", server_url);
    let output_path = temp_dir.join("downloaded_file.txt").to_string_lossy().to_string();
    
    let task_id = Uuid::new_v4();
    let task = TaskInfo {
        id: task_id,
        url: url.clone(),
        output_path: output_path.clone(),
        status: TaskStatus::Pending,
        progress: 0.0,
        speed: 0,
        downloaded_size: 0,
        uploaded_bytes: 0,  // 添加缺少的字段
        total_size: None,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        error_message: None,
    };
    let task_id = download_manager.add_task(task).await?;
    
    // Start the download
    download_manager.start_task(task_id).await?;
    
    // Wait a bit for the download to start
    tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
    
    // Pause the download
    download_manager.pause_task(task_id).await?;
    
    // Verify the task is paused
    let task_info = download_manager.get_task(task_id).await?;
    assert_eq!(task_info.status, TaskStatus::Paused, "Task should be paused");
    
    // Resume the download
    download_manager.resume_task(task_id).await?;
    
    // Wait for the download to complete
    let mut status = TaskStatus::Initializing;
    for _ in 0..30 {
        // Check the task status every second
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        
        let task_info = download_manager.get_task(task_id).await?;
        status = task_info.status;
        
        if status == TaskStatus::Completed || status == TaskStatus::Failed {
            break;
        }
    }
    
    // Verify the download was successful
    assert_eq!(status, TaskStatus::Completed, "Download failed or timed out");
    
    // Verify the downloaded file
    let downloaded_content = fs::read_to_string(output_path).await?;
    assert_eq!(downloaded_content, test_content, "Downloaded content does not match");
    
    // Clean up
    fs::remove_dir_all(&temp_dir).await?;
    shutdown_tx.send(()).unwrap();
    
    Ok(())
}

/// Test canceling a download in progress
#[tokio::test]
async fn test_cancel_download() -> Result<()> {
    // Start a test server
    let (server_url, shutdown_tx) = start_test_http_server().await?;
    
    // Create a test file
    let test_content = "This is a test file for HTTP downloader cancel test.\n".repeat(10000); // Large file
    let current_dir = std::env::current_dir().unwrap();
    let test_file_path = current_dir.join("tests").join("files").join("cancel_test.txt");
    fs::write(&test_file_path, &test_content).await?;
    
    // Create a temporary directory for downloads
    let base_temp_dir = std::env::temp_dir();
    let temp_dir = base_temp_dir.join("tonitru_tests").join("test_downloads_cancel");
    if temp_dir.exists() {
        fs::remove_dir_all(&temp_dir).await?;
    }
    fs::create_dir_all(&temp_dir).await?;
    
    // Setup download manager
    let mut settings = Settings::default();
    settings.download.path = temp_dir.to_string_lossy().to_string();
    let config_manager = Arc::new(ConfigManager::new_with_settings(settings.clone()));
    let storage = Arc::new(LocalStorage::new(&settings.download.path));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage.clone()));
    resume_manager.init().await?;
    let bandwidth_scheduler = Arc::new(BandwidthSchedulerImpl::new());
    let link_analyzer = Arc::new(LinkAnalyzerImpl::new());
    let downloader_factory = Arc::new(
        DownloaderFactoryImpl::new(
            config_manager.clone(),
            link_analyzer.clone(),
            resume_manager.clone(),
        )
        .with_bandwidth_scheduler(bandwidth_scheduler.clone()),
    );
    let event_notifier = Arc::new(NoopEventNotifier {});
    let p2p_factory = CustomP2PFactory::new(config_manager.clone());
    let download_manager = DownloadManagerImpl::new(
        event_notifier,
        settings.clone(),
        link_analyzer.clone(),
        p2p_factory,
        resume_manager.clone(),
        downloader_factory
    );
    
    // Add download task
    let url = format!("{}/files/cancel_test.txt", server_url);
    let output_path = temp_dir.join("cancel_test.txt").to_string_lossy().to_string();
    let task_id = Uuid::new_v4();
    let task = TaskInfo {
        id: task_id,
        url: url.clone(),
        output_path: output_path.clone(),
        status: TaskStatus::Pending,
        progress: 0.0,
        speed: 0,
        downloaded_size: 0,
        uploaded_bytes: 0,  // 添加缺少的字段
        total_size: None,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        error_message: None,
    };
    let task_id = download_manager.add_task(task).await?;
    
    // Start download
    download_manager.start_task(task_id).await?;
    
    // Wait for download to start
    tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
    
    // Cancel the download
    download_manager.cancel_task(task_id).await?;
    
    // Verify task is cancelled
    let task_info = download_manager.get_task(task_id).await?;
    assert_eq!(task_info.status, TaskStatus::Cancelled, "Task should be cancelled");
    
    // Clean up
    fs::remove_dir_all(&temp_dir).await?;
    shutdown_tx.send(()).unwrap();
    
    Ok(())
}

/// Test concurrent downloads
#[tokio::test]
async fn test_concurrent_downloads() -> Result<()> {
    // Start a test server
    let (server_url, shutdown_tx) = start_test_http_server().await?;
    
    // Create test files
    let test_files = vec!["file1.txt", "file2.txt", "file3.txt"];
    let current_dir = std::env::current_dir().unwrap();
    for (i, file) in test_files.iter().enumerate() {
        let content = format!("This is test file {} content\n", i + 1).repeat(1000);
        let test_file_path = current_dir.join("tests").join("files").join(file);
        fs::write(&test_file_path, &content).await?;
    }
    
    // Create a temporary directory for downloads
    let base_temp_dir = std::env::temp_dir();
    let temp_dir = base_temp_dir.join("tonitru_tests").join("test_downloads_concurrent");
    if temp_dir.exists() {
        fs::remove_dir_all(&temp_dir).await?;
    }
    fs::create_dir_all(&temp_dir).await?;
    
    // Setup download manager
    let mut settings = Settings::default();
    settings.download.path = temp_dir.to_string_lossy().to_string();
    let config_manager = Arc::new(ConfigManager::new_with_settings(settings.clone()));
    let storage = Arc::new(LocalStorage::new(&settings.download.path));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage.clone()));
    resume_manager.init().await?;
    let bandwidth_scheduler = Arc::new(BandwidthSchedulerImpl::new());
    let link_analyzer = Arc::new(LinkAnalyzerImpl::new());
    let downloader_factory = Arc::new(
        DownloaderFactoryImpl::new(
            config_manager.clone(),
            link_analyzer.clone(),
            resume_manager.clone(),
        )
        .with_bandwidth_scheduler(bandwidth_scheduler.clone()),
    );
    let event_notifier = Arc::new(NoopEventNotifier {});
    let p2p_factory = CustomP2PFactory::new(config_manager.clone());
    let download_manager = DownloadManagerImpl::new(
        event_notifier,
        settings.clone(),
        link_analyzer.clone(),
        p2p_factory,
        resume_manager.clone(),
        downloader_factory
    );
    
    // Start multiple downloads
    let mut task_ids = Vec::new();
    for file in &test_files {
        let url = format!("{}/files/{}", server_url, file);
        let output_path = temp_dir.join(file).to_string_lossy().to_string();
        let task_id = Uuid::new_v4();
        let task = TaskInfo {
            id: task_id,
            url: url.clone(),
            output_path: output_path.clone(),
            status: TaskStatus::Pending,
            progress: 0.0,
            speed: 0,
            downloaded_size: 0,
            uploaded_bytes: 0,  // 添加缺少的字段
            total_size: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            error_message: None,
        };
        let task_id = download_manager.add_task(task).await?;
        download_manager.start_task(task_id).await?;
        task_ids.push(task_id);
    }
    
    // Wait for all downloads to complete
    for _ in 0..60 {
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        
        let mut all_completed = true;
        for task_id in &task_ids {
            let task_info = download_manager.get_task(*task_id).await?;
            if task_info.status != TaskStatus::Completed {
                all_completed = false;
                break;
            }
        }
        
        if all_completed {
            break;
        }
    }
    
    // Verify all downloads completed successfully
    for task_id in &task_ids {
        let task_info = download_manager.get_task(*task_id).await?;
        assert_eq!(task_info.status, TaskStatus::Completed, "Task should be completed");
    }
    
    // Clean up
    fs::remove_dir_all(&temp_dir).await?;
    shutdown_tx.send(()).unwrap();
    
    Ok(())
}

/// Test error handling for invalid URLs
#[tokio::test]
async fn test_invalid_url_handling() -> Result<()> {
    // Create a temporary directory for downloads
    let base_temp_dir = std::env::temp_dir();
    let temp_dir = base_temp_dir.join("tonitru_tests").join("test_downloads_error");
    if temp_dir.exists() {
        fs::remove_dir_all(&temp_dir).await?;
    }
    fs::create_dir_all(&temp_dir).await?;
    
    // Setup download manager
    let mut settings = Settings::default();
    settings.download.path = temp_dir.to_string_lossy().to_string();
    let config_manager = Arc::new(ConfigManager::new_with_settings(settings.clone()));
    let storage = Arc::new(LocalStorage::new(&settings.download.path));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage.clone()));
    resume_manager.init().await?;
    let bandwidth_scheduler = Arc::new(BandwidthSchedulerImpl::new());
    let link_analyzer = Arc::new(LinkAnalyzerImpl::new());
    let downloader_factory = Arc::new(
        DownloaderFactoryImpl::new(
            config_manager.clone(),
            link_analyzer.clone(),
            resume_manager.clone(),
        )
        .with_bandwidth_scheduler(bandwidth_scheduler.clone()),
    );
    let event_notifier = Arc::new(NoopEventNotifier {});
    let p2p_factory = CustomP2PFactory::new(config_manager.clone());
    let download_manager = DownloadManagerImpl::new(
        event_notifier,
        settings.clone(),
        link_analyzer.clone(),
        p2p_factory,
        resume_manager.clone(),
        downloader_factory
    );
    
    // Test with invalid URL
    let invalid_url = "http://invalid.url.that.does.not.exist/file.txt".to_string();
    let output_path = temp_dir.join("error_test.txt").to_string_lossy().to_string();
    let task_id = Uuid::new_v4();
    let task = TaskInfo {
        id: task_id,
        url: invalid_url.clone(),
        output_path: output_path.clone(),
        status: TaskStatus::Pending,
        progress: 0.0,
        speed: 0,
        downloaded_size: 0,
        uploaded_bytes: 0,  // 添加缺少的字段
        total_size: None,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        error_message: None,
    };
    let task_id = download_manager.add_task(task).await?;
    
    // Start download
    download_manager.start_task(task_id).await?;
    
    // Wait for error status
    for _ in 0..30 {
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        let task_info = download_manager.get_task(task_id).await?;
        if task_info.status == TaskStatus::Failed {
            break;
        }
    }
    
    // Verify task failed
    let task_info = download_manager.get_task(task_id).await?;
    assert_eq!(task_info.status, TaskStatus::Failed, "Task should have failed");
    assert!(task_info.error_message.is_some(), "Error message should be present");
    
    // Clean up
    fs::remove_dir_all(&temp_dir).await?;
    
    Ok(())
}
