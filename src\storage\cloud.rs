use anyhow::{Result, anyhow};
use async_trait::async_trait;
use bytes::Bytes;
use reqwest::{Client, header};
use std::collections::HashMap;
use tracing::debug;

use crate::storage::local::Storage;

/// Cloud storage implementation for Cloudflare R2
pub struct CloudStorage {
    client: Client,
    account_id: String,
    access_key_id: String,
    access_key_secret: String,
    bucket_name: String,
}

impl CloudStorage {
    /// Create a new cloud storage
    pub fn new(
        account_id: String,
        access_key_id: String,
        access_key_secret: String,
        bucket_name: String,
    ) -> Result<Self> {
        let client = Client::builder()
            .build()?;

        Ok(Self {
            client,
            account_id,
            access_key_id,
            access_key_secret,
            bucket_name,
        })
    }

    /// Get the R2 endpoint URL
    fn get_endpoint(&self) -> String {
        format!("https://{}.r2.cloudflarestorage.com", self.account_id)
    }

    /// Get the URL for an object
    fn get_object_url(&self, path: &str) -> String {
        format!("{}/{}/{}", self.get_endpoint(), self.bucket_name, path)
    }

    /// Get the authorization headers
    fn get_auth_headers(&self) -> HashMap<String, String> {
        let mut headers = HashMap::new();
        headers.insert("X-Auth-Key".to_string(), self.access_key_secret.clone());
        headers.insert("X-Auth-Email".to_string(), self.access_key_id.clone());
        headers
    }
}

#[async_trait]
impl Storage for CloudStorage {
    async fn write(&self, path: &str, data: &[u8]) -> Result<()> {
        let url = self.get_object_url(path);

        // Create a request with the data
        let mut request = self.client.put(&url)
            .body(data.to_vec());

        // Add authorization headers
        for (key, value) in self.get_auth_headers() {
            request = request.header(key, value);
        }

        // Send the request
        let response = request.send().await?;

        if !response.status().is_success() {
            return Err(anyhow!("Failed to write to cloud storage: HTTP {}", response.status()));
        }

        debug!("Wrote {} bytes to cloud storage: {}", data.len(), path);

        Ok(())
    }

    async fn write_at(&self, _path: &str, _data: &[u8], _offset: u64) -> Result<()> {
        // R2 doesn't support writing at an offset directly
        // We would need to download the file, modify it, and upload it again
        // This is not efficient for large files

        Err(anyhow!("Writing at an offset is not supported for cloud storage"))
    }

    async fn read(&self, path: &str) -> Result<Bytes> {
        let url = self.get_object_url(path);

        // Create a request
        let mut request = self.client.get(&url);

        // Add authorization headers
        for (key, value) in self.get_auth_headers() {
            request = request.header(key, value);
        }

        // Send the request
        let response = request.send().await?;

        if !response.status().is_success() {
            return Err(anyhow!("Failed to read from cloud storage: HTTP {}", response.status()));
        }

        // Get the data
        let data = response.bytes().await?;

        debug!("Read {} bytes from cloud storage: {}", data.len(), path);

        Ok(data)
    }

    async fn read_at(&self, path: &str, offset: u64, length: usize) -> Result<Bytes> {
        let url = self.get_object_url(path);

        // Create a request with a range header
        let range = format!("bytes={}-{}", offset, offset + length as u64 - 1);

        let mut request = self.client.get(&url)
            .header(header::RANGE, range);

        // Add authorization headers
        for (key, value) in self.get_auth_headers() {
            request = request.header(key, value);
        }

        // Send the request
        let response = request.send().await?;

        if !response.status().is_success() && response.status() != reqwest::StatusCode::PARTIAL_CONTENT {
            return Err(anyhow!("Failed to read from cloud storage: HTTP {}", response.status()));
        }

        // Get the data
        let data = response.bytes().await?;

        debug!("Read {} bytes at offset {} from cloud storage: {}", data.len(), offset, path);

        Ok(data)
    }

    async fn delete(&self, path: &str) -> Result<()> {
        let url = self.get_object_url(path);

        // Create a request
        let mut request = self.client.delete(&url);

        // Add authorization headers
        for (key, value) in self.get_auth_headers() {
            request = request.header(key, value);
        }

        // Send the request
        let response = request.send().await?;

        if !response.status().is_success() {
            return Err(anyhow!("Failed to delete from cloud storage: HTTP {}", response.status()));
        }

        debug!("Deleted from cloud storage: {}", path);

        Ok(())
    }

    async fn exists(&self, path: &str) -> Result<bool> {
        let url = self.get_object_url(path);

        // Create a request
        let mut request = self.client.head(&url);

        // Add authorization headers
        for (key, value) in self.get_auth_headers() {
            request = request.header(key, value);
        }

        // Send the request
        let response = request.send().await?;

        Ok(response.status().is_success())
    }

    async fn size(&self, path: &str) -> Result<u64> {
        let url = self.get_object_url(path);

        // Create a request
        let mut request = self.client.head(&url);

        // Add authorization headers
        for (key, value) in self.get_auth_headers() {
            request = request.header(key, value);
        }

        // Send the request
        let response = request.send().await?;

        if !response.status().is_success() {
            return Err(anyhow!("Failed to get size from cloud storage: HTTP {}", response.status()));
        }

        // Get the content length
        let content_length = response
            .headers()
            .get(header::CONTENT_LENGTH)
            .and_then(|h| h.to_str().ok())
            .and_then(|s| s.parse::<u64>().ok())
            .ok_or_else(|| anyhow!("Content-Length header not found"))?;

        Ok(content_length)
    }

    async fn create_dir(&self, _path: &str) -> Result<()> {
        // R2 doesn't have directories, it uses a flat namespace with prefixes
        // So this is a no-op
        Ok(())
    }
}
