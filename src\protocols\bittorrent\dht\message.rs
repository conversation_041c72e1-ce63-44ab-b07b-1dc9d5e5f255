use anyhow::{Result, anyhow};
use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::net::{SocketAddr, IpAddr, Ipv4Addr};
use rand::Rng;
use serde_bencode::to_bytes;
use sha1::Digest;

use super::node::{DHTNode, NodeId};

/// DHT消息类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum DHTMessageType {
    /// Ping请求
    Ping,
    /// 查找节点请求
    FindNode,
    /// 获取对等点请求
    GetPeers,
    /// 宣布对等点请求
    AnnouncePeer,
    /// 响应消息
    Response,
    /// 错误消息
    Error,
}

/// DHT消息
#[derive(Debug, Clone)]
pub struct DHTMessage {
    /// 消息类型
    pub message_type: DHTMessageType,
    /// 事务ID
    pub transaction_id: Vec<u8>,
    /// 发送者节点ID
    pub sender_id: NodeId,
    /// 目标节点ID或info_hash
    pub target_id: Option<NodeId>,
    /// 节点列表（用于响应）
    pub nodes: Option<Vec<DHTNode>>,
    /// 对等点列表（用于GetPeers响应）
    pub peers: Option<Vec<SocketAddr>>,
    /// 令牌（用于AnnouncePeer）
    pub token: Option<Vec<u8>>,
    /// 端口（用于AnnouncePeer）
    pub port: Option<u16>,
    /// 错误代码
    pub error_code: Option<i32>,
    /// 错误消息
    pub error_message: Option<String>,
}

/// 令牌管理器
pub struct TokenManager {
    /// 当前密钥
    current_key: [u8; 4],
    /// 上一个密钥
    previous_key: [u8; 4],
    /// 上次密钥更新时间
    last_key_refresh: std::time::Instant,
}

impl TokenManager {
    /// 创建新的令牌管理器
    pub fn new() -> Self {
        let mut rng = rand::thread_rng();
        let mut current_key = [0u8; 4];
        let mut previous_key = [0u8; 4];
        rng.fill(&mut current_key);
        rng.fill(&mut previous_key);

        Self {
            current_key,
            previous_key,
            last_key_refresh: std::time::Instant::now(),
        }
    }

    /// 生成令牌
    pub fn generate_token(&self, addr: &SocketAddr) -> Vec<u8> {
        let mut hasher = sha1::Sha1::new();
        hasher.update(&self.current_key);
        hasher.update(&addr.to_string().as_bytes());
        hasher.finalize().to_vec()
    }

    /// 验证令牌
    pub fn verify_token(&self, token: &[u8], addr: &SocketAddr) -> bool {
        // 使用当前密钥验证
        let current_token = self.generate_token(addr);
        if token == current_token {
            return true;
        }

        // 使用上一个密钥验证
        let mut hasher = sha1::Sha1::new();
        hasher.update(&self.previous_key);
        hasher.update(&addr.to_string().as_bytes());
        let previous_token = hasher.finalize().to_vec();

        token == previous_token
    }

    /// 刷新密钥
    pub fn refresh_keys(&mut self) {
        if self.last_key_refresh.elapsed() > std::time::Duration::from_secs(300) { // 5分钟
            let mut rng = rand::thread_rng();
            self.previous_key = self.current_key;
            rng.fill(&mut self.current_key);
            self.last_key_refresh = std::time::Instant::now();
        }
    }
}

/// DHT响应类型
#[derive(Debug, Clone)]
pub enum DHTResponse {
    /// Ping响应
    Ping {
        /// 节点ID
        id: Option<NodeId>,
    },
    /// FindNode响应
    FindNode {
        /// 节点ID
        id: Option<NodeId>,
        /// 节点列表
        nodes: Option<Vec<DHTNode>>,
    },
    /// GetPeers响应
    GetPeers {
        /// 节点ID
        id: Option<NodeId>,
        /// 令牌
        token: Option<Vec<u8>>,
        /// 对等点列表
        values: Option<Vec<SocketAddr>>,
        /// 节点列表
        nodes: Option<Vec<DHTNode>>,
    },
    /// AnnouncePeer响应
    AnnouncePeer {
        /// 节点ID
        id: Option<NodeId>,
    },
    /// SampleInfohashes响应
    SampleInfohashes {
        /// 节点ID
        id: Option<NodeId>,
        /// 样本列表
        samples: Option<Vec<[u8; 20]>>,
        /// 节点列表
        nodes: Option<Vec<DHTNode>>,
        /// 间隔
        interval: Option<u32>,
    },
    /// 错误响应
    Error {
        /// 错误代码
        code: i32,
        /// 错误消息
        message: String,
    },
}

impl DHTResponse {
    /// 从DHT消息创建响应
    pub fn from_message(message: &DHTMessage) -> Result<Self> {
        match message.message_type {
            DHTMessageType::Response => {
                // 根据事务ID和消息内容确定响应类型
                if message.peers.is_some() || message.token.is_some() {
                    // GetPeers响应
                    Ok(DHTResponse::GetPeers {
                        id: Some(message.sender_id),
                        token: message.token.clone(),
                        values: message.peers.clone(),
                        nodes: message.nodes.clone(),
                    })
                } else if message.nodes.is_some() {
                    // FindNode响应
                    Ok(DHTResponse::FindNode {
                        id: Some(message.sender_id),
                        nodes: message.nodes.clone(),
                    })
                } else {
                    // Ping或AnnouncePeer响应
                    Ok(DHTResponse::Ping {
                        id: Some(message.sender_id),
                    })
                }
            },
            DHTMessageType::Error => {
                // 错误响应
                Ok(DHTResponse::Error {
                    code: message.error_code.unwrap_or(0),
                    message: message.error_message.clone().unwrap_or_else(|| "Unknown error".to_string()),
                })
            },
            _ => Err(anyhow!("Not a response message")),
        }
    }
}

/// DHT消息处理器接口
#[async_trait]
pub trait MessageHandler: Send + Sync {
    /// 处理DHT消息
    async fn handle_message(&self, message: DHTMessage, addr: SocketAddr) -> Result<Option<DHTMessage>>;
}

/// DHT消息编码器/解码器
pub struct MessageCodec;

impl MessageCodec {
    /// 将DHT消息编码为Bencode格式
    pub fn encode(message: &DHTMessage) -> Result<Vec<u8>> {
        // 使用Vec<u8>作为键的HashMap
        let mut dict = HashMap::<Vec<u8>, serde_bencode::value::Value>::new();

        // 添加事务ID
        dict.insert(b"t".to_vec(), serde_bencode::value::Value::Bytes(message.transaction_id.clone()));

        match message.message_type {
            DHTMessageType::Ping | DHTMessageType::FindNode | DHTMessageType::GetPeers | DHTMessageType::AnnouncePeer => {
                // 请求消息
                dict.insert(b"y".to_vec(), serde_bencode::value::Value::Bytes(b"q".to_vec()));

                // 添加请求类型
                let query_type = match message.message_type {
                    DHTMessageType::Ping => "ping",
                    DHTMessageType::FindNode => "find_node",
                    DHTMessageType::GetPeers => "get_peers",
                    DHTMessageType::AnnouncePeer => "announce_peer",
                    _ => unreachable!(),
                };
                dict.insert(b"q".to_vec(), serde_bencode::value::Value::Bytes(query_type.as_bytes().to_vec()));

                // 添加参数
                let mut args = HashMap::<Vec<u8>, serde_bencode::value::Value>::new();
                args.insert(b"id".to_vec(), serde_bencode::value::Value::Bytes(message.sender_id.as_bytes().to_vec()));

                if let Some(target_id) = &message.target_id {
                    let key = match message.message_type {
                        DHTMessageType::FindNode => "target",
                        DHTMessageType::GetPeers | DHTMessageType::AnnouncePeer => "info_hash",
                        _ => "",
                    };

                    if !key.is_empty() {
                        args.insert(key.as_bytes().to_vec(), serde_bencode::value::Value::Bytes(target_id.as_bytes().to_vec()));
                    }
                }

                if let (Some(token), Some(port)) = (&message.token, &message.port) {
                    if message.message_type == DHTMessageType::AnnouncePeer {
                        args.insert(b"token".to_vec(), serde_bencode::value::Value::Bytes(token.clone()));
                        args.insert(b"port".to_vec(), serde_bencode::value::Value::Int(*port as i64));
                        args.insert(b"implied_port".to_vec(), serde_bencode::value::Value::Int(0));
                    }
                }

                dict.insert(b"a".to_vec(), serde_bencode::value::Value::Dict(args));
            },
            DHTMessageType::Response => {
                // 响应消息
                dict.insert(b"y".to_vec(), serde_bencode::value::Value::Bytes(b"r".to_vec()));

                // 添加响应参数
                let mut response = HashMap::<Vec<u8>, serde_bencode::value::Value>::new();
                response.insert(b"id".to_vec(), serde_bencode::value::Value::Bytes(message.sender_id.as_bytes().to_vec()));

                if let Some(nodes) = &message.nodes {
                    // 编码节点列表
                    let mut nodes_data = Vec::new();
                    for node in nodes {
                        nodes_data.extend_from_slice(&node.encode_compact());
                    }
                    response.insert(b"nodes".to_vec(), serde_bencode::value::Value::Bytes(nodes_data));
                }

                if let Some(peers) = &message.peers {
                    // 编码对等点列表
                    let mut values = Vec::new();
                    for peer in peers {
                        let mut peer_data = Vec::new();
                        match peer.ip() {
                            std::net::IpAddr::V4(ipv4) => {
                                peer_data.extend_from_slice(&ipv4.octets());
                            },
                            std::net::IpAddr::V6(_) => {
                                // DHT通常不使用IPv6
                                continue;
                            },
                        }
                        peer_data.extend_from_slice(&peer.port().to_be_bytes());
                        values.push(serde_bencode::value::Value::Bytes(peer_data));
                    }
                    response.insert(b"values".to_vec(), serde_bencode::value::Value::List(values));
                }

                if let Some(token) = &message.token {
                    response.insert(b"token".to_vec(), serde_bencode::value::Value::Bytes(token.clone()));
                }

                dict.insert(b"r".to_vec(), serde_bencode::value::Value::Dict(response));
            },
            DHTMessageType::Error => {
                // 错误消息
                dict.insert(b"y".to_vec(), serde_bencode::value::Value::Bytes(b"e".to_vec()));

                // 添加错误信息
                let mut error_list = Vec::new();
                error_list.push(serde_bencode::value::Value::Int(message.error_code.unwrap_or(0) as i64));
                error_list.push(serde_bencode::value::Value::Bytes(message.error_message.clone().unwrap_or_default().into_bytes()));

                dict.insert(b"e".to_vec(), serde_bencode::value::Value::List(error_list));
            },
        }

        // 编码为Bencode格式
        to_bytes(&serde_bencode::value::Value::Dict(dict))
            .map_err(|e| anyhow!("Failed to encode DHT message: {}", e))
    }

    /// 从Bencode格式解码DHT消息
    pub fn decode(data: &[u8]) -> Result<DHTMessage> {
        // 解析Bencode数据
        let value = serde_bencode::from_bytes::<serde_bencode::value::Value>(data)
            .map_err(|e| anyhow!("Failed to decode DHT message: {}", e))?;

        // 获取字典
        let dict = match value {
            serde_bencode::value::Value::Dict(dict) => dict,
            _ => return Err(anyhow!("DHT message is not a dictionary")),
        };

        // 获取消息类型
        let message_type = if let Some(serde_bencode::value::Value::Bytes(y)) = dict.get(&b"y".to_vec()) {
            match y.as_slice() {
                b"q" => {
                    // 请求消息
                    if let Some(serde_bencode::value::Value::Bytes(q)) = dict.get(&b"q".to_vec()) {
                        match q.as_slice() {
                            b"ping" => DHTMessageType::Ping,
                            b"find_node" => DHTMessageType::FindNode,
                            b"get_peers" => DHTMessageType::GetPeers,
                            b"announce_peer" => DHTMessageType::AnnouncePeer,
                            _ => return Err(anyhow!("Unknown query type: {:?}", q)),
                        }
                    } else {
                        return Err(anyhow!("Missing query type"));
                    }
                },
                b"r" => DHTMessageType::Response,
                b"e" => DHTMessageType::Error,
                _ => return Err(anyhow!("Unknown message type: {:?}", y)),
            }
        } else {
            return Err(anyhow!("Missing message type"));
        };

        // 获取事务ID
        let transaction_id = if let Some(serde_bencode::value::Value::Bytes(t)) = dict.get(&b"t".to_vec()) {
            t.clone()
        } else {
            return Err(anyhow!("Missing transaction ID"));
        };

        // 初始化消息
        let mut message = DHTMessage {
            message_type: message_type.clone(),
            transaction_id,
            sender_id: NodeId::new_random(), // 临时值，将在下面更新
            target_id: None,
            nodes: None,
            peers: None,
            token: None,
            port: None,
            error_code: None,
            error_message: None,
        };

        // 根据消息类型解析其他字段
        match message_type {
            DHTMessageType::Ping | DHTMessageType::FindNode | DHTMessageType::GetPeers | DHTMessageType::AnnouncePeer => {
                // 请求消息
                if let Some(serde_bencode::value::Value::Dict(args)) = dict.get(&b"a".to_vec()) {
                    // 获取发送者ID
                    if let Some(serde_bencode::value::Value::Bytes(id)) = args.get(&b"id".to_vec()) {
                        if id.len() == 20 {
                            message.sender_id = NodeId::from_bytes(id.as_slice().try_into().unwrap());
                        } else {
                            return Err(anyhow!("Invalid sender ID length"));
                        }
                    } else {
                        return Err(anyhow!("Missing sender ID"));
                    }

                    // 获取目标ID或info_hash
                    let target_key = match message_type {
                        DHTMessageType::FindNode => b"target".to_vec(),
                        DHTMessageType::GetPeers | DHTMessageType::AnnouncePeer => b"info_hash".to_vec(),
                        _ => vec![],
                    };

                    if !target_key.is_empty() {
                        if let Some(serde_bencode::value::Value::Bytes(target)) = args.get(&target_key) {
                            if target.len() == 20 {
                                message.target_id = Some(NodeId::from_bytes(target.as_slice().try_into().unwrap()));
                            } else {
                                return Err(anyhow!("Invalid target ID length"));
                            }
                        }
                    }

                    // 获取AnnouncePeer特有字段
                    if message_type == DHTMessageType::AnnouncePeer {
                        if let Some(serde_bencode::value::Value::Bytes(token)) = args.get(&b"token".to_vec()) {
                            message.token = Some(token.clone());
                        }

                        if let Some(serde_bencode::value::Value::Int(port)) = args.get(&b"port".to_vec()) {
                            message.port = Some(*port as u16);
                        }
                    }
                } else {
                    return Err(anyhow!("Missing arguments"));
                }
            },
            DHTMessageType::Response => {
                // 响应消息
                if let Some(serde_bencode::value::Value::Dict(response)) = dict.get(&b"r".to_vec()) {
                    // 获取发送者ID
                    if let Some(serde_bencode::value::Value::Bytes(id)) = response.get(&b"id".to_vec()) {
                        if id.len() == 20 {
                            message.sender_id = NodeId::from_bytes(id.as_slice().try_into().unwrap());
                        } else {
                            return Err(anyhow!("Invalid sender ID length"));
                        }
                    } else {
                        return Err(anyhow!("Missing sender ID"));
                    }

                    // 获取节点列表
                    if let Some(serde_bencode::value::Value::Bytes(nodes_data)) = response.get(&b"nodes".to_vec()) {
                        let mut nodes = Vec::new();
                        let mut i = 0;
                        while i + 26 <= nodes_data.len() {
                            if let Some(node) = DHTNode::decode_compact(&nodes_data[i..i+26]) {
                                nodes.push(node);
                            }
                            i += 26;
                        }
                        if !nodes.is_empty() {
                            message.nodes = Some(nodes);
                        }
                    }

                    // 获取对等点列表
                    if let Some(serde_bencode::value::Value::List(values)) = response.get(&b"values".to_vec()) {
                        let mut peers = Vec::new();
                        for value in values {
                            if let serde_bencode::value::Value::Bytes(peer_data) = value {
                                if peer_data.len() == 6 {
                                    let ip = Ipv4Addr::new(peer_data[0], peer_data[1], peer_data[2], peer_data[3]);
                                    let port = u16::from_be_bytes([peer_data[4], peer_data[5]]);
                                    peers.push(SocketAddr::new(IpAddr::V4(ip), port));
                                }
                            }
                        }
                        if !peers.is_empty() {
                            message.peers = Some(peers);
                        }
                    }

                    // 获取令牌
                    if let Some(serde_bencode::value::Value::Bytes(token)) = response.get(&b"token".to_vec()) {
                        message.token = Some(token.clone());
                    }
                } else {
                    return Err(anyhow!("Missing response"));
                }
            },
            DHTMessageType::Error => {
                // 错误消息
                if let Some(serde_bencode::value::Value::List(error)) = dict.get(&b"e".to_vec()) {
                    if error.len() >= 2 {
                        if let serde_bencode::value::Value::Int(code) = error[0] {
                            message.error_code = Some(code as i32);
                        }

                        if let serde_bencode::value::Value::Bytes(msg) = &error[1] {
                            message.error_message = Some(String::from_utf8_lossy(msg).to_string());
                        }
                    }
                } else {
                    return Err(anyhow!("Missing error"));
                }
            },
        }

        Ok(message)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr, SocketAddr};

    #[test]
    fn test_token_manager_new() {
        let token_manager = TokenManager::new();

        // 验证初始化成功 - 只能测试公共接口
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
        let token = token_manager.generate_token(&addr);
        assert!(!token.is_empty());
    }

    #[test]
    fn test_token_manager_generate_token() {
        let token_manager = TokenManager::new();
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        let token = token_manager.generate_token(&addr);

        // 验证生成的令牌不为空
        assert!(!token.is_empty());

        // 同一地址生成的令牌应该相同
        let token2 = token_manager.generate_token(&addr);
        assert_eq!(token, token2);

        // 不同地址生成的令牌应该不同
        let addr2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);
        let token3 = token_manager.generate_token(&addr2);
        assert_ne!(token, token3);
    }

    #[test]
    fn test_token_manager_verify_token() {
        let token_manager = TokenManager::new();
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        let token = token_manager.generate_token(&addr);

        // 验证正确的令牌
        assert!(token_manager.verify_token(&token, &addr));

        // 验证错误的令牌
        let wrong_token = vec![0u8; token.len()];
        assert!(!token_manager.verify_token(&wrong_token, &addr));

        // 验证错误的地址
        let addr2 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);
        assert!(!token_manager.verify_token(&token, &addr2));
    }

    #[test]
    fn test_message_codec_encode_ping() {
        let node_id = NodeId::new_random();
        let transaction_id = vec![1, 2, 3, 4];

        let message = DHTMessage {
            message_type: DHTMessageType::Ping,
            transaction_id: transaction_id.clone(),
            sender_id: node_id,
            target_id: None,
            nodes: None,
            peers: None,
            token: None,
            port: None,
            error_code: None,
            error_message: None,
        };

        let encoded = MessageCodec::encode(&message).unwrap();

        // 验证编码成功
        assert!(!encoded.is_empty());

        // 验证编码结果是有效的bencode
        let decoded = serde_bencode::from_bytes::<serde_bencode::value::Value>(&encoded);
        assert!(decoded.is_ok());
    }
}