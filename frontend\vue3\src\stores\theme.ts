import { defineStore } from 'pinia';

// 主题类型
export type ThemeType = 'light' | 'dark' | 'auto';

export const useThemeStore = defineStore('theme', {
  state: () => ({
    theme: 'auto' as ThemeType,
    systemTheme: 'light' as 'light' | 'dark',
  }),
  
  getters: {
    currentTheme: (state) => {
      if (state.theme === 'auto') {
        return state.systemTheme;
      }
      return state.theme;
    },
  },
  
  actions: {
    // 初始化主题
    initTheme() {
      // 从本地存储获取主题设置
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
        this.theme = savedTheme as ThemeType;
      }
      
      // 检测系统主题
      this.detectSystemTheme();
      
      // 监听系统主题变化
      this.listenForThemeChanges();
    },
    
    // 设置主题
    setTheme(theme: ThemeType) {
      this.theme = theme;
      localStorage.setItem('theme', theme);
    },
    
    // 检测系统主题
    detectSystemTheme() {
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        this.systemTheme = 'dark';
      } else {
        this.systemTheme = 'light';
      }
    },
    
    // 监听系统主题变化
    listenForThemeChanges() {
      if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
          this.systemTheme = e.matches ? 'dark' : 'light';
        });
      }
    },
  },
});
