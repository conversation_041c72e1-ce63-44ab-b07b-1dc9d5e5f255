// Neutralino应用主脚本

// 等待Neutralino API准备就绪
Neutralino.events.on('ready', async () => {
  console.log('应用已启动');
  
  // 初始化应用
  await initApp();
  
  // 注册URL协议处理器
  await registerProtocolHandler();
  
  // 解析URL参数
  parseUrlParams();
});

// 初始化应用
async function initApp() {
  try {
    // 设置窗口标题
    await Neutralino.window.setTitle('Lumen 下载管理器');
    
    // 监听URL协议扩展消息
    Neutralino.events.on('urlProtocolReady', () => {
      console.log('URL协议扩展已准备就绪');
    });
    
    // 监听窗口关闭事件
    Neutralino.events.on('windowClose', () => {
      Neutralino.app.exit();
    });
  } catch (error) {
    console.error('初始化应用失败:', error);
  }
}

// 注册URL协议处理器
async function registerProtocolHandler() {
  try {
    // 获取应用路径
    const appInfo = await Neutralino.app.getConfig();
    const appPath = await Neutralino.os.getPath('executable');
    
    // 检查协议是否已注册
    const checkResult = await Neutralino.extensions.dispatch('js.neutralino.urlprotocol', 'isRegistered', {
      protocol: 'lumen'
    });
    
    if (!checkResult.registered) {
      // 注册协议
      const result = await Neutralino.extensions.dispatch('js.neutralino.urlprotocol', 'register', {
        protocol: 'lumen',
        appPath: appPath
      });
      
      if (result.success) {
        console.log('URL协议注册成功');
      } else {
        console.error('URL协议注册失败:', result.error);
      }
    } else {
      console.log('URL协议已注册');
    }
  } catch (error) {
    console.error('注册URL协议处理器失败:', error);
  }
}

// 解析URL参数
function parseUrlParams() {
  // 获取URL参数
  const urlParams = new URLSearchParams(window.location.search);
  
  // 检查是否有下载链接参数
  const downloadUrl = urlParams.get('url');
  if (downloadUrl) {
    console.log('接收到下载链接:', downloadUrl);
    // 更新应用状态中的下载链接
    updateAddTaskUrl(downloadUrl);
    // 显示添加任务对话框
    showAddTaskDialog('uri');
  }
}

// 更新添加任务的URL
function updateAddTaskUrl(url) {
  // 这里应该调用应用的状态管理函数
  // 在实际应用中，这可能是调用Vue或React的状态更新函数
  window.addTaskUrl = url;
  
  // 触发自定义事件
  const event = new CustomEvent('addTaskUrlUpdated', { detail: { url } });
  window.dispatchEvent(event);
}

// 显示添加任务对话框
function showAddTaskDialog(taskType) {
  // 这里应该调用应用的UI更新函数
  // 在实际应用中，这可能是调用Vue或React的UI更新函数
  window.addTaskVisible = true;
  window.addTaskType = taskType;
  
  // 触发自定义事件
  const event = new CustomEvent('showAddTaskDialog', { detail: { taskType } });
  window.dispatchEvent(event);
}

// 处理来自命令行的URL协议
Neutralino.events.on('appClientConnect', async (e) => {
  // 检查是否有命令行参数
  const cmdArgs = await Neutralino.os.getEnv('NL_ARGS');
  if (cmdArgs) {
    const args = JSON.parse(cmdArgs);
    
    // 查找以lumen://开头的参数
    const protocolUrl = args.find(arg => arg.startsWith('lumen://'));
    if (protocolUrl) {
      handleProtocolUrl(protocolUrl);
    }
  }
});

// 处理协议URL
function handleProtocolUrl(protocolUrl) {
  if (!protocolUrl) return;
  
  try {
    // 处理URL格式 (lumen://download?url=https://example.com/file.zip)
    let urlStr = protocolUrl;
    if (urlStr.startsWith('lumen://')) {
      // 将lumen://转换为标准URL格式以便解析
      urlStr = urlStr.replace('lumen://', 'http://');
    }
    
    const parsedUrl = new URL(urlStr);
    
    // 提取下载链接
    let downloadUrl = '';
    
    // 尝试从查询参数中获取URL
    if (parsedUrl.searchParams.has('url')) {
      downloadUrl = parsedUrl.searchParams.get('url');
    } 
    // 如果查询参数中没有URL，则使用路径部分作为URL
    else if (parsedUrl.pathname && parsedUrl.pathname !== '/') {
      // 移除开头的斜杠
      downloadUrl = parsedUrl.pathname.startsWith('/') 
        ? parsedUrl.pathname.substring(1) 
        : parsedUrl.pathname;
      
      // 确保下载URL包含协议
      if (!downloadUrl.includes('://')) {
        downloadUrl = 'http://' + downloadUrl;
      }
    }
    
    if (downloadUrl) {
      console.log('从协议URL中提取的下载链接:', downloadUrl);
      // 更新应用状态中的下载链接
      updateAddTaskUrl(downloadUrl);
      // 显示添加任务对话框
      showAddTaskDialog('uri');
    }
  } catch (error) {
    console.error('解析协议URL失败:', error);
  }
}