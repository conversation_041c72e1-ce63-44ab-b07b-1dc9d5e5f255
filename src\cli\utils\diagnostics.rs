//! CLI 诊断信息工具模块
//!
//! 提供收集系统和环境信息的功能，用于调试和诊断问题。

use std::collections::HashMap;
use std::env;
use std::time::{SystemTime, UNIX_EPOCH};
use log::{debug, info, trace};
use serde_json::{json, Value};
use colored::Colorize;

/// 收集系统信息
pub fn collect_system_info() -> HashMap<String, String> {
    let mut info = HashMap::new();
    
    // 操作系统信息
    #[cfg(target_os = "windows")]
    {
        info.insert("os".to_string(), "Windows".to_string());
        // 获取Windows版本信息
        use std::process::Command;
        if let Ok(output) = Command::new("powershell")
            .args(["-Command", "(Get-CimInstance -ClassName Win32_OperatingSystem).Caption"])
            .output() 
        {
            if let Ok(os_version) = String::from_utf8(output.stdout) {
                info.insert("os_version".to_string(), os_version.trim().to_string());
            }
        }
        
        // 获取CPU信息
        if let Ok(output) = Command::new("powershell")
            .args(["-Command", "(Get-CimInstance -ClassName Win32_Processor).Name"])
            .output() 
        {
            if let Ok(cpu_info) = String::from_utf8(output.stdout) {
                info.insert("cpu_info".to_string(), cpu_info.trim().to_string());
            }
        }
        
        // 获取总内存大小
        if let Ok(output) = Command::new("powershell")
            .args(["-Command", "(Get-CimInstance -ClassName Win32_ComputerSystem).TotalPhysicalMemory"])
            .output() 
        {
            if let Ok(total_memory) = String::from_utf8(output.stdout) {
                if let Ok(memory_bytes) = total_memory.trim().parse::<u64>() {
                    let memory_gb = memory_bytes as f64 / 1_073_741_824.0; // 转换为GB
                    info.insert("total_memory_gb".to_string(), format!("{:.2}", memory_gb));
                }
            }
        }
    }
    
    #[cfg(target_os = "macos")]
    info.insert("os".to_string(), "macOS".to_string());
    
    #[cfg(target_os = "linux")]
    info.insert("os".to_string(), "Linux".to_string());
    
    // 架构信息
    info.insert("arch".to_string(), env::consts::ARCH.to_string());
    
    // Rust版本信息
    info.insert("rust_version".to_string(), env::var("RUSTC_VERSION").unwrap_or_else(|_| env!("CARGO_PKG_RUST_VERSION", "unknown").to_string()));
    
    // 当前时间戳
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();
    info.insert("timestamp".to_string(), timestamp.to_string());
    
    // 可执行文件路径
    if let Ok(exe_path) = env::current_exe() {
        if let Some(exe_str) = exe_path.to_str() {
            info.insert("executable".to_string(), exe_str.to_string());
        }
    }
    
    // 当前工作目录
    if let Ok(current_dir) = env::current_dir() {
        if let Some(dir_str) = current_dir.to_str() {
            info.insert("current_dir".to_string(), dir_str.to_string());
        }
    }
    
    info
}

/// 收集环境变量信息
pub fn collect_env_vars() -> HashMap<String, String> {
    let mut env_vars = HashMap::new();
    
    // 只收集与应用相关的环境变量，避免泄露敏感信息
    let relevant_prefixes = ["TONITRU_", "RUST_", "CARGO_"];
    
    for (key, value) in env::vars() {
        if relevant_prefixes.iter().any(|prefix| key.starts_with(prefix)) {
            env_vars.insert(key, value);
        }
    }
    
    env_vars
}

/// 打印诊断信息
pub fn print_diagnostics(include_env: bool) {
    let system_info = collect_system_info();
    
    info!("{}", "系统诊断信息".green().bold());
    for (key, value) in &system_info {
        info!("{}: {}", key.blue(), value);
    }
    
    if include_env {
        let env_vars = collect_env_vars();
        
        info!("{}", "环境变量".green().bold());
        for (key, value) in &env_vars {
            info!("{}: {}", key.blue(), value);
        }
    }
}

/// 生成诊断报告
pub fn generate_diagnostic_report(include_env: bool) -> Value {
    let system_info = collect_system_info();
    let mut report = json!({
        "system_info": system_info,
        "timestamp": SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs(),
    });
    
    if include_env {
        let env_vars = collect_env_vars();
        report["environment_variables"] = json!(env_vars);
    }
    
    report
}

/// 检查系统兼容性
pub fn check_system_compatibility() -> Vec<String> {
    let issues = Vec::new();
    
    // 检查操作系统
    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        issues.push(format!("不支持的操作系统: {}", env::consts::OS));
    }
    
    // 检查架构
    #[cfg(not(any(target_arch = "x86_64", target_arch = "aarch64")))]
    {
        issues.push(format!("不支持的架构: {}", env::consts::ARCH));
    }
    
    // 检查是否有足够的磁盘空间
    // 这里只是一个示例，实际实现可能需要使用特定的库
    
    // 返回发现的问题
    issues
}

/// 打印系统兼容性检查结果
pub fn print_compatibility_check() {
    let issues = check_system_compatibility();
    
    if issues.is_empty() {
        info!("{}", "系统兼容性检查: 通过".green());
    } else {
        info!("{}", "系统兼容性检查: 发现问题".yellow());
        for issue in &issues {
            info!("{} {}", "问题:".yellow(), issue);
        }
    }
}

/// 将诊断报告保存到文件
pub fn save_diagnostic_report_to_file(report: Value, message: &str) -> Result<String, std::io::Error> {
    use std::fs;
    use std::path::Path;
    use std::time::SystemTime;
    
    // 创建报告目录（如果不存在）
    let report_dir = Path::new("logs");
    if !report_dir.exists() {
        fs::create_dir_all(report_dir)?;
    }
    
    // 生成带有时间戳的文件名
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();
    
    // 创建文件名，包含时间戳和简短的消息摘要
    let message_digest = message.chars().take(20).collect::<String>().replace(|c: char| !c.is_alphanumeric(), "_");
    let filename = format!("diagnostic_report_{}_{}.json", timestamp, message_digest);
    let file_path = report_dir.join(filename);
    
    // 添加消息到报告
    let mut report_with_message = report.clone();
    report_with_message["message"] = json!(message);
    
    // 将报告写入文件
    let report_json = serde_json::to_string_pretty(&report_with_message)
        .map_err(|_| std::io::Error::new(std::io::ErrorKind::Other, "Failed to serialize report"))?;
    
    fs::write(&file_path, report_json)?;
    
    // 返回文件路径
    Ok(file_path.to_string_lossy().to_string())
}