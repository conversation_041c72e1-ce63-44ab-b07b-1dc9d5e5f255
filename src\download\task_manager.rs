use anyhow::{Result, anyhow};
use async_trait::async_trait;
use chrono::Utc;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{<PERSON>te<PERSON>, RwLock};
use tracing::{info, error};
use uuid::Uuid;

use crate::config::Settings;
use crate::download::manager::{TaskInfo, TaskStatus};
use crate::core::interfaces::{Downloader, DownloaderFactory};

/// 任务管理器接口
#[async_trait]
pub trait TaskManager: Send + Sync {
    /// 添加任务
    async fn add_task(&self, url: String, output_path: Option<String>) -> Result<Uuid>;

    /// 启动任务
    async fn start_task(&self, task_id: Uuid) -> Result<()>;

    /// 暂停任务
    async fn pause_task(&self, task_id: Uuid) -> Result<()>;

    /// 恢复任务
    async fn resume_task(&self, task_id: Uuid) -> Result<()>;

    /// 取消任务
    async fn cancel_task(&self, task_id: Uuid) -> Result<()>;

    /// 获取任务信息
    async fn get_task_info(&self, task_id: Uuid) -> Result<TaskInfo>;

    /// 获取所有任务
    async fn get_all_tasks(&self) -> Result<Vec<TaskInfo>>;

    /// 删除任务
    async fn remove_task(&self, task_id: Uuid) -> Result<()>;

    /// 获取下载统计信息
    async fn get_download_stats(&self) -> crate::download::manager::DownloadStats;
    
    /// 完成任务
    async fn complete_task(&self, task_id: Uuid) -> Result<()>;

    /// 失败任务
    async fn fail_task(&self, task_id: Uuid, error_message: String) -> Result<()>;

    /// 获取设置
    fn get_settings(&self) -> &Settings;
}

/// 任务管理器实现
#[derive(Clone)]
pub struct TaskManagerImpl {
    settings: Settings,
    tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
    downloaders: Arc<Mutex<HashMap<Uuid, Box<dyn Downloader>>>>,
    downloader_factory: Arc<dyn DownloaderFactory>,
}

impl TaskManagerImpl {
    /// 创建新的任务管理器
    pub fn new(
        settings: Settings,
        downloader_factory: Arc<dyn DownloaderFactory>,
    ) -> Self {
        Self {
            settings,
            tasks: Arc::new(RwLock::new(HashMap::new())),
            downloaders: Arc::new(Mutex::new(HashMap::new())),
            downloader_factory,
        }
    }
    
    /// 获取设置
    pub fn get_settings(&self) -> &Settings {
        &self.settings
    }

    /// 更新任务状态
    pub async fn update_task_status(&self, task_id: Uuid, status: TaskStatus, error_message: Option<String>) -> Result<TaskInfo> {
        let mut tasks = self.tasks.write().await;

        let task = tasks.get_mut(&task_id)
            .ok_or_else(|| anyhow!("Task not found: {}", task_id))?;

        task.status = status;
        task.updated_at = Utc::now();

        if let Some(error) = error_message {
            task.error_message = Some(error);
        }

        Ok(task.clone())
    }

    /// 更新任务进度
    pub async fn update_task_progress(&self, task_id: Uuid, progress: f64, speed: u64, total_size: Option<u64>, downloaded_size: u64) -> Result<TaskInfo> {
        let mut tasks = self.tasks.write().await;

        let task = tasks.get_mut(&task_id)
            .ok_or_else(|| anyhow!("Task not found: {}", task_id))?;

        task.progress = progress;
        task.speed = speed;
        task.total_size = total_size;
        task.downloaded_size = downloaded_size;
        task.updated_at = Utc::now();

        Ok(task.clone())
    }
}

#[async_trait]
impl TaskManager for TaskManagerImpl {
    fn get_settings(&self) -> &Settings {
        &self.settings
    }
    async fn add_task(&self, url: String, output_path: Option<String>) -> Result<Uuid> {
        let task_id = Uuid::new_v4();

        // 确定输出路径
        let file_name = std::path::Path::new(&url)
            .file_name()
            .and_then(|name| name.to_str())
            .unwrap_or("download")
            .to_string();

        let output_path = match output_path {
            Some(path) => path,
            None => format!("{}/{}", self.settings.download.path, file_name),
        };

        // 创建新任务
        let task = TaskInfo {
            id: task_id,
            url: url.clone(),
            output_path: output_path.clone(),
            status: TaskStatus::Pending,
            progress: 0.0,
            speed: 0,
            total_size: None,
            downloaded_size: 0,
            uploaded_bytes: 0,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            error_message: None,
        };

        // 添加任务到列表
        self.tasks.write().await.insert(task_id, task);

        // 创建下载器
        let downloader = self.downloader_factory.create_downloader(&url, &output_path, task_id).await?;
        // 插入到downloaders集合中
        self.downloaders.lock().await.insert(task_id, downloader);

        info!("Added download task: id={}, url={}", task_id, url);

        Ok(task_id)
    }

    async fn start_task(&self, task_id: Uuid) -> Result<()> {
        // 更新任务状态
        self.update_task_status(task_id, TaskStatus::Initializing, None).await?;

        // 获取下载器
        let mut downloaders = self.downloaders.lock().await;
        let downloader = downloaders.get_mut(&task_id)
            .ok_or_else(|| anyhow!("Downloader not found for task: {}", task_id))?;

        // 获取任务信息以获取URL和输出路径
        let tasks = self.tasks.read().await;
        let task = tasks.get(&task_id)
            .ok_or_else(|| anyhow!("Task not found: {}", task_id))?;

        // 初始化下载器
        if let Err(e) = downloader.init(&task.url, &task.output_path, &crate::core::interfaces::downloader::DownloadOptions::default()).await {
            self.update_task_status(task_id, TaskStatus::Failed, Some(e.to_string())).await?;
            return Err(e.into());
        }

        // 更新任务状态
        self.update_task_status(task_id, TaskStatus::Downloading, None).await?;

        // 在单独的任务中启动下载
        let downloader_clone = Arc::clone(&self.downloaders);
        let tasks_clone = Arc::clone(&self.tasks);

        tokio::spawn(async move {
            let result = {
                let mut downloaders = downloader_clone.lock().await;
                let downloader = match downloaders.get_mut(&task_id) {
                    Some(d) => d,
                    None => {
                        error!("Downloader not found for task: {}", task_id);
                        return;
                    }
                };

                downloader.start().await
            };

            match result {
                Ok(_) => {
                    let mut tasks = tasks_clone.write().await;
                    if let Some(task) = tasks.get_mut(&task_id) {
                        task.status = TaskStatus::Completed;
                        task.progress = 1.0;
                        task.updated_at = Utc::now();
                        info!("Download completed: id={}", task_id);
                    }
                },
                Err(e) => {
                    let mut tasks = tasks_clone.write().await;
                    if let Some(task) = tasks.get_mut(&task_id) {
                        if task.status != TaskStatus::Cancelled {
                            task.status = TaskStatus::Failed;
                            task.error_message = Some(e.to_string());
                            task.updated_at = Utc::now();
                            error!("Download failed: id={}, error={}", task_id, e);
                        }
                    }
                }
            }
        });

        Ok(())
    }

    async fn pause_task(&self, task_id: Uuid) -> Result<()> {
        // 获取任务
        let task = self.get_task_info(task_id).await?;

        // 检查任务是否可暂停
        if task.status != TaskStatus::Downloading && task.status != TaskStatus::Initializing {
            return Err(anyhow!("Task is not in a pausable state: {}", task.status));
        }

        // 获取下载器
        let mut downloaders = self.downloaders.lock().await;
        let downloader = downloaders.get_mut(&task_id)
            .ok_or_else(|| anyhow!("Downloader not found for task: {}", task_id))?;

        // 暂停下载
        downloader.pause().await?;

        // 更新任务状态
        self.update_task_status(task_id, TaskStatus::Paused, None).await?;

        info!("Download paused: id={}", task_id);

        Ok(())
    }

    async fn resume_task(&self, task_id: Uuid) -> Result<()> {
        // 获取任务
        let task = self.get_task_info(task_id).await?;

        // 检查任务状态并采取相应操作
        match task.status {
            // 如果任务已暂停，则恢复下载
            TaskStatus::Paused => {
                // 获取下载器
                let mut downloaders = self.downloaders.lock().await;
                let downloader = downloaders.get_mut(&task_id)
                    .ok_or_else(|| anyhow!("Downloader not found for task: {}", task_id))?;

                // 恢复下载
                downloader.resume().await?;

                // 更新任务状态
                self.update_task_status(task_id, TaskStatus::Downloading, None).await?;

                info!("Download resumed: id={}", task_id);
            },
            // 如果任务处于等待状态，则启动任务
            TaskStatus::Pending => {
                info!("Task is in pending state, starting task: id={}", task_id);
                return self.start_task(task_id).await;
            },
            // 如果任务已经在下载中，则不做任何操作
            TaskStatus::Downloading => {
                info!("Task is already downloading: id={}", task_id);
            },
            // 其他状态不可恢复
            _ => {
                return Err(anyhow!("Task is not in a resumable state: {}", task.status));
            }
        }

        Ok(())
    }

    async fn cancel_task(&self, task_id: Uuid) -> Result<()> {
        // 获取任务
        let task = self.get_task_info(task_id).await?;

        // 检查任务是否可取消
        if task.status == TaskStatus::Completed || task.status == TaskStatus::Failed || task.status == TaskStatus::Cancelled {
            return Err(anyhow!("Task is not in a cancellable state: {}", task.status));
        }

        // 获取下载器
        let mut downloaders = self.downloaders.lock().await;
        let downloader = downloaders.get_mut(&task_id)
            .ok_or_else(|| anyhow!("Downloader not found for task: {}", task_id))?;

        // 取消下载
        downloader.cancel().await?;

        // 更新任务状态
        self.update_task_status(task_id, TaskStatus::Cancelled, None).await?;

        info!("Download cancelled: id={}", task_id);

        Ok(())
    }

    async fn get_task_info(&self, task_id: Uuid) -> Result<TaskInfo> {
        let tasks = self.tasks.read().await;

        let task = tasks.get(&task_id)
            .ok_or_else(|| anyhow!("Task not found: {}", task_id))?
            .clone();

        Ok(task)
    }

    async fn get_all_tasks(&self) -> Result<Vec<TaskInfo>> {
        let tasks = self.tasks.read().await;

        let task_list = tasks.values().cloned().collect::<Vec<_>>();

        Ok(task_list)
    }

    async fn remove_task(&self, task_id: Uuid) -> Result<()> {
        // 取消任务（如果活动）
        let task = self.get_task_info(task_id).await?;

        if task.status == TaskStatus::Downloading || task.status == TaskStatus::Initializing {
            self.cancel_task(task_id).await?;
        }

        // 移除任务
        self.tasks.write().await.remove(&task_id);
        self.downloaders.lock().await.remove(&task_id);

        info!("Removed download task: id={}", task_id);

        Ok(())
    }

    async fn get_download_stats(&self) -> crate::download::manager::DownloadStats {
        let tasks = self.tasks.read().await;
        let mut stats = crate::download::manager::DownloadStats::default();

        stats.total_downloads = tasks.len();

        for (_, task) in tasks.iter() {
            match task.status {
                crate::download::manager::TaskStatus::Downloading => stats.active_downloads += 1,
                crate::download::manager::TaskStatus::Completed => stats.completed_downloads += 1,
                crate::download::manager::TaskStatus::Failed => stats.failed_downloads += 1,
                _ => {},
            }
            stats.total_downloaded_bytes += task.downloaded_size;
            // TODO: Add total_uploaded_bytes if applicable
        }
        stats
    }

    async fn complete_task(&self, task_id: Uuid) -> Result<()> {
        self.update_task_status(task_id, TaskStatus::Completed, None).await?;
        Ok(())
    }

    async fn fail_task(&self, task_id: Uuid, error_message: String) -> Result<()> {
        self.update_task_status(task_id, TaskStatus::Failed, Some(error_message)).await?;
        Ok(())
    }
}
