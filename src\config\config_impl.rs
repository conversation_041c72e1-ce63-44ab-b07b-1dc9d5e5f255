use async_trait::async_trait;
use config::{Config as ConfigLib, File as ConfigFile};
use serde::{de::DeserializeOwned, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::fs;
use tokio::sync::RwLock;

use crate::core::error::{CoreError, CoreResult};
use crate::core::interfaces::config::{Config, ConfigOps, ConfigFactory as CoreConfigFactory};

/// Configuration implementation
pub struct ConfigImpl {
    config: RwLock<ConfigLib>,
    config_path: Option<PathBuf>,
    defaults: HashMap<String, String>,
    listeners: RwLock<Vec<Arc<dyn ConfigChangeListener>>>,
}

/// 配置变更监听器 trait
pub trait ConfigChangeListener: Send + Sync {
    /// 当配置项变更时调用
    fn on_config_change(&self, key: &str, value: &str);
}

impl ConfigImpl {
    /// Create a new configuration
    pub async fn new() -> CoreResult<Self> {
        let config = ConfigLib::builder()
            .build()
            .map_err(|e| CoreError::Config(format!("Failed to build config: {}", e)))?;

        Ok(Self {
            config: RwLock::new(config),
            config_path: None,
            defaults: HashMap::new(),
            listeners: RwLock::new(Vec::new()),
        })
    }

    pub async fn with_defaults(defaults: HashMap<String, String>) -> CoreResult<Self> {
        let mut builder = ConfigLib::builder();

        // Add defaults
        for (key, value) in &defaults {
            builder = builder.set_default(key, value.clone())
                .map_err(|e| CoreError::Config(format!("Failed to set default for key '{}': {}", key, e)))?;
        }

        let config = builder.build()
            .map_err(|e| CoreError::Config(format!("Failed to build config: {}", e)))?;

        Ok(Self {
            config: RwLock::new(config),
            config_path: None,
            defaults,
            listeners: RwLock::new(Vec::new()),
        })
    }

    /// Load configuration from a file
    pub async fn from_file(path: impl Into<PathBuf>) -> CoreResult<Self> {
        let path = path.into();

        // Check if the file exists
        if !path.exists() {
            return Err(CoreError::NotFound(format!("Configuration file not found: {}", path.display())));
        }

        let config = ConfigLib::builder()
            .add_source(ConfigFile::from(path.clone()))
            .build()
            .map_err(|e| CoreError::Config(format!("Failed to load config from file '{}': {}", path.display(), e)))?;

        Ok(Self {
            config: RwLock::new(config),
            config_path: Some(path),
            defaults: HashMap::new(),
            listeners: RwLock::new(Vec::new()),
        })
    }

    /// Save configuration to the file it was loaded from
    pub async fn save(&self) -> CoreResult<()> {
        if let Some(config_path) = &self.config_path {
            self.save_to_file(config_path.as_path()).await
        } else {
            Err(CoreError::Config("No config path set. Use save_to_file with a path instead.".to_string()))
        }
    }

    /// Get the current configuration path
    pub fn get_config_path(&self) -> Option<&PathBuf> {
        self.config_path.as_ref()
    }
}

#[async_trait]
impl ConfigOps for ConfigImpl {
    async fn get<T: DeserializeOwned + Send + Sync>(&self, key: &str) -> CoreResult<Option<T>> {
        let config = self.config.read().await;

        match config.get::<T>(key) {
            Ok(value) => Ok(Some(value)),
            Err(config::ConfigError::NotFound(_)) => Ok(None),
            Err(e) => Err(CoreError::Config(e.to_string())),
        }
    }

    async fn set<T: Serialize + Send + Sync>(&self, key: &str, value: T) -> CoreResult<()> {
        let mut config = self.config.write().await;

        // 将值序列化为 JSON，然后转换为 config::Value
        let json_value = serde_json::to_value(value)
            .map_err(|e| CoreError::Serialization(format!("Failed to serialize value for key '{}': {}", key, e)))?;
        
        // 如果是字符串类型，先进行验证
        if let serde_json::Value::String(ref s) = json_value {
            self.validate_value(key, s).await?
        }

        // 将 JSON 值转换为 config::Value
        let config_value = match &json_value {
            serde_json::Value::Null => config::Value::from("null"), // 使用字符串代替 null
            serde_json::Value::Bool(b) => config::Value::from(*b),
            serde_json::Value::Number(n) => {
                if let Some(i) = n.as_i64() {
                    config::Value::from(i)
                } else if let Some(f) = n.as_f64() {
                    config::Value::from(f)
                } else {
                    return Err(CoreError::Serialization("Unsupported number type".to_string()));
                }
            },
            serde_json::Value::String(s) => config::Value::from(s.clone()),
            serde_json::Value::Array(arr) => {
                let values: Vec<config::Value> = arr.iter()
                    .map(|v| self.json_to_config_value(v.clone()))
                    .collect::<Result<Vec<_>, _>>()
                    .map_err(|e| CoreError::Serialization(e))?;
                config::Value::from(values)
            },
            serde_json::Value::Object(obj) => {
                let mut map = std::collections::HashMap::new();
                for (k, v) in obj.iter() {
                    map.insert(k.clone(), self.json_to_config_value(v.clone())
                        .map_err(|e| CoreError::Serialization(e))?); 
                }
                config::Value::from(map)
            },
        };

        // 使用 ConfigBuilder 设置配置
        let mut builder = ConfigLib::builder();
        builder = builder.add_source(config.clone());
        builder = builder.set_override(key, config_value)
            .map_err(|e| CoreError::Config(format!("Failed to set override for key '{}': {}", key, e)))?;

        // 重新构建配置
        *config = builder.build()
            .map_err(|e| CoreError::Config(format!("Failed to rebuild config after setting key '{}': {}", key, e)))?;

        log::debug!("Config value set: {} = {:?}", key, json_value);
        
        // 通知监听器配置已变更
        if let serde_json::Value::String(s) = &json_value {
            self.notify_listeners(key, s).await;
        } else {
            // 对于非字符串值，将其转换为字符串后通知
            let value_str = json_value.to_string();
            self.notify_listeners(key, &value_str).await;
        }
        
        Ok(())
    }
}

#[async_trait]
impl Config for ConfigImpl {

    async fn remove(&self, key: &str) -> CoreResult<()> {
        // 由于 config 库没有直接的 remove 方法，我们需要创建一个新的配置
        // 但不包含要删除的键

        // 获取所有键值对
        let keys = self.get_keys().await?;
        let config = self.config.read().await;

        // 创建一个新的配置
        let mut builder = ConfigLib::builder();

        // 添加所有键值对，除了要删除的键
        for k in keys {
            if k != key && !k.starts_with(&format!("{}.", key)) {
                // 只有当键不是要删除的键，且不是要删除键的子键时，才添加
                if let Ok(value) = config.get::<serde_json::Value>(&k) {
                    // 将 serde_json::Value 转换为 config::Value
                    let config_value = self.json_to_config_value(value)
                        .map_err(|e| CoreError::Serialization(e))?;

                    builder = builder.set_default(&k, config_value)
                        .map_err(|e| CoreError::Config(e.to_string()))?;
                }
            }
        }

        // 构建新配置
        let new_config = builder.build()
            .map_err(|e| CoreError::Config(e.to_string()))?;

        // 替换旧配置
        let mut config_write = self.config.write().await;
        *config_write = new_config;

        Ok(())
    }

    async fn exists(&self, key: &str) -> CoreResult<bool> {
        let config = self.config.read().await;
        Ok(config.get::<String>(key).is_ok())
    }

    async fn get_keys(&self) -> CoreResult<Vec<String>> {
        // 由于 config 库没有直接的方法获取所有键，
        // 我们使用预定义的键列表

        let mut keys = Vec::new();

        // 服务器配置
        keys.push("server.host".to_string());
        keys.push("server.port".to_string());

        // 下载配置
        keys.push("download.path".to_string());
        keys.push("download.concurrent_downloads".to_string());
        keys.push("download.connections_per_download".to_string());
        keys.push("download.chunk_size".to_string());
        keys.push("download.buffer_size".to_string());
        keys.push("download.speed_limit".to_string());

        // 数据库配置
        keys.push("database.url".to_string());

        // 镜像配置
        keys.push("mirror.enabled".to_string());
        keys.push("mirror.max_mirrors".to_string());
        keys.push("mirror.health_check_interval_secs".to_string());

        Ok(keys)
    }

    async fn get_all_as_json(&self) -> CoreResult<String> {
        let config = self.config.read().await;
        let keys = self.get_keys().await?;

        let mut result = serde_json::Map::new();

        // 获取所有键值对并构建 JSON 对象
        for key in keys {
            if let Ok(value) = config.get::<serde_json::Value>(&key) {
                // 处理嵌套键
                let parts: Vec<&str> = key.split('.').collect();

                // 递归构建嵌套对象
                self.insert_nested_value(&mut result, &parts, value.clone());
            }
        }

        serde_json::to_string(&serde_json::Value::Object(result))
            .map_err(|e| CoreError::Serialization(e.to_string()))
    }

    async fn load_from_file(&self, path: &Path) -> CoreResult<()> {
        // Check if the file exists
        if !path.exists() {
            return Err(CoreError::NotFound(format!("Configuration file not found: {}", path.display())));
        }

        let mut config = self.config.write().await;

        // 创建一个新的配置构建器
        let mut builder = ConfigLib::builder();

        // 添加默认值
        for (key, value) in &self.defaults {
            builder = builder.set_default(key, value.clone())
                .map_err(|e| CoreError::Config(e.to_string()))?;
        }

        // 添加文件作为配置源
        builder = builder.add_source(ConfigFile::from(path));

        // 构建新配置
        let new_config = builder.build()
            .map_err(|e| CoreError::Config(e.to_string()))?;

        // 替换旧配置
        *config = new_config;

        Ok(())
    }

    async fn save_to_file(&self, path: &Path) -> CoreResult<()> {
        // 如果提供了路径，使用提供的路径；否则，尝试使用存储的配置路径
        let effective_path = if let Some(config_path) = &self.config_path {
            config_path.as_path()
        } else {
            path
        };

        // 创建备份（如果文件已存在）
        if effective_path.exists() {
            let backup_path = effective_path.with_extension("bak");
            fs::copy(effective_path, &backup_path).await
                .map_err(|e| CoreError::Io(e.into()))?;
            log::info!("Created backup of configuration file at: {}", backup_path.display());
        }

        // 获取配置的 JSON 表示
        let config_json = self.get_all_as_json().await?;

        // 美化 JSON
        let value: serde_json::Value = serde_json::from_str(&config_json)
            .map_err(|e| CoreError::Serialization(format!("Failed to parse config JSON: {}", e)))?;

        let pretty_json = serde_json::to_string_pretty(&value)
            .map_err(|e| CoreError::Serialization(format!("Failed to format config JSON: {}", e)))?;

        // 创建父目录（如果不存在）
        if let Some(parent) = effective_path.parent() {
            fs::create_dir_all(parent).await
                .map_err(|e| CoreError::Io(e.into()))?;
        }

        // 写入文件
        fs::write(effective_path, pretty_json).await
            .map_err(|e| CoreError::Io(e.into()))?;

        log::info!("Configuration saved to: {}", effective_path.display());
        Ok(())
    }

    async fn reset_to_default(&self) -> CoreResult<()> {
        let mut config = self.config.write().await;

        // Create a new builder
        let mut builder = ConfigLib::builder();

        // Add defaults
        for (key, value) in &self.defaults {
            builder = builder.set_default(key, value.clone())
                .map_err(|e| CoreError::Config(e.to_string()))?;
        }

        // Build the new config
        let new_config = builder.build()
            .map_err(|e| CoreError::Config(e.to_string()))?;

        // Replace the old config
        *config = new_config;

        Ok(())
    }
}

impl ConfigImpl {
    /// 递归插入嵌套值到 JSON 对象
    fn insert_nested_value(&self, map: &mut serde_json::Map<String, serde_json::Value>, parts: &[&str], value: serde_json::Value) {
        if parts.is_empty() {
            return;
        }

        if parts.len() == 1 {
            // 最后一个部分，直接插入值
            map.insert(parts[0].to_string(), value);
        } else {
            // 创建或获取嵌套对象
            let part = parts[0];

            if !map.contains_key(part) {
                map.insert(part.to_string(), serde_json::Value::Object(serde_json::Map::new()));
            }

            // 获取嵌套对象的可变引用
            if let Some(serde_json::Value::Object(next_map)) = map.get_mut(part) {
                // 递归处理剩余部分
                self.insert_nested_value(next_map, &parts[1..], value);
            }
        }
    }

    /// 将 serde_json::Value 转换为 config::Value
    fn json_to_config_value(&self, json_value: serde_json::Value) -> Result<config::Value, String> {
        match json_value {
            serde_json::Value::Null => Ok(config::Value::from("null")), // 使用字符串代替 null
            serde_json::Value::Bool(b) => Ok(config::Value::from(b)),
            serde_json::Value::Number(n) => {
                if let Some(i) = n.as_i64() {
                    Ok(config::Value::from(i))
                } else if let Some(f) = n.as_f64() {
                    Ok(config::Value::from(f))
                } else {
                    Err("Unsupported number type".to_string())
                }
            },
            serde_json::Value::String(s) => Ok(config::Value::from(s)),
            serde_json::Value::Array(arr) => {
                let values: Result<Vec<config::Value>, String> = arr.into_iter()
                    .map(|v| self.json_to_config_value(v))
                    .collect();
                Ok(config::Value::from(values?))
            },
            serde_json::Value::Object(obj) => {
                let mut map = std::collections::HashMap::new();
                for (k, v) in obj {
                    map.insert(k, self.json_to_config_value(v)?);
                }
                Ok(config::Value::from(map))
            },
        }
    }
}

/// Configuration factory implementation
pub struct ConfigFactory;

#[async_trait]
impl CoreConfigFactory for ConfigFactory {
    async fn create_config(&self) -> CoreResult<ConfigImpl> {
        ConfigImpl::new().await
    }
}

impl ConfigImpl {
    /// 验证配置值是否符合预期格式和范围
    async fn validate_value(&self, key: &str, value: &str) -> CoreResult<()> {
        // 根据键名进行特定验证
        match key {
            // 服务器端口验证
            "server.port" => {
                let port = value.parse::<u16>()
                    .map_err(|_| CoreError::Validation(format!("Invalid port number: {}", value)))?;
                if port == 0 {
                    return Err(CoreError::Validation("Port cannot be 0".to_string()));
                }
            },
            // 下载路径验证
            "download.path" => {
                if value.is_empty() {
                    return Err(CoreError::Validation("Download path cannot be empty".to_string()));
                }
                let path = Path::new(value);
                if !path.exists() {
                    return Err(CoreError::Validation(format!("Download path does not exist: {}", value)));
                }
                if !path.is_dir() {
                    return Err(CoreError::Validation(format!("Download path is not a directory: {}", value)));
                }
            },
            // 并发下载数验证
            "download.concurrent_downloads" => {
                let count = value.parse::<u32>()
                    .map_err(|_| CoreError::Validation(format!("Invalid concurrent downloads count: {}", value)))?;
                if count == 0 || count > 100 {
                    return Err(CoreError::Validation("Concurrent downloads must be between 1 and 100".to_string()));
                }
            },
            // 每个下载的连接数验证
            "download.connections_per_download" => {
                let count = value.parse::<u32>()
                    .map_err(|_| CoreError::Validation(format!("Invalid connections per download count: {}", value)))?;
                if count == 0 || count > 16 {
                    return Err(CoreError::Validation("Connections per download must be between 1 and 16".to_string()));
                }
            },
            // 数据库URL验证
            "database.url" => {
                if !value.starts_with("sqlite:") && !value.starts_with("mysql:") && !value.starts_with("postgres:") {
                    return Err(CoreError::Validation("Database URL must start with sqlite:, mysql: or postgres:".to_string()));
                }
            },
            // 代理URL验证
            "proxy.url" => {
                if !value.is_empty() && !value.starts_with("http:") && !value.starts_with("https:") && !value.starts_with("socks5:") {
                    return Err(CoreError::Validation("Proxy URL must start with http:, https: or socks5:".to_string()));
                }
            },
            _ => {}
        }
        
        Ok(())
    }

    /// 添加配置变更监听器
    pub async fn add_listener(&self, listener: Arc<dyn ConfigChangeListener>) {
        let mut listeners = self.listeners.write().await;
        listeners.push(listener);
    }
    
    /// 移除配置变更监听器
    pub async fn remove_listener(&self, listener: &Arc<dyn ConfigChangeListener>) {
        let mut listeners = self.listeners.write().await;
        listeners.retain(|l| !Arc::ptr_eq(l, listener));
    }
    
    /// 通知所有监听器配置已变更
    async fn notify_listeners(&self, key: &str, value: &str) {
        let listeners = self.listeners.read().await;
        for listener in listeners.iter() {
            listener.on_config_change(key, value);
        }
    }
}
