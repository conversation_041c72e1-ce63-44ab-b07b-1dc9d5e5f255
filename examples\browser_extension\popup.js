// 浏览器扩展弹出窗口脚本

// 当DOM加载完成时执行
document.addEventListener('DOMContentLoaded', () => {
  // 获取DOM元素
  const lumenAppUrlInput = document.getElementById('lumenAppUrl');
  const useProtocolCheckbox = document.getElementById('useProtocol');
  const saveButton = document.getElementById('saveBtn');
  const statusDiv = document.getElementById('status');
  
  // 加载保存的设置
  loadSettings();
  
  // 添加保存按钮点击事件
  saveButton.addEventListener('click', saveSettings);
  
  // 加载设置
  function loadSettings() {
    chrome.storage.sync.get({
      useProtocol: true, // 默认使用协议
      lumenAppUrl: "http://localhost:9080" // 默认应用URL
    }, (items) => {
      lumenAppUrlInput.value = items.lumenAppUrl;
      useProtocolCheckbox.checked = items.useProtocol;
    });
  }
  
  // 保存设置
  function saveSettings() {
    // 获取输入值
    const lumenAppUrl = lumenAppUrlInput.value.trim();
    const useProtocol = useProtocolCheckbox.checked;
    
    // 验证URL格式
    if (lumenAppUrl && !isValidUrl(lumenAppUrl)) {
      showStatus('请输入有效的URL地址', 'error');
      return;
    }
    
    // 保存设置
    chrome.storage.sync.set({
      lumenAppUrl: lumenAppUrl || "http://localhost:9080", // 如果为空则使用默认值
      useProtocol: useProtocol
    }, () => {
      // 显示保存成功消息
      showStatus('设置已保存', 'success');
    });
  }
  
  // 显示状态消息
  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
    
    // 3秒后隐藏消息
    setTimeout(() => {
      statusDiv.style.display = 'none';
    }, 3000);
  }
  
  // 验证URL格式
  function isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }
});