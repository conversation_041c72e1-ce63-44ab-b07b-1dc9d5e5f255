use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::Result;
use tokio::sync::{Mutex, RwLock};
use tokio::time::interval;
use tracing::{debug, info, warn};

use super::{Protocol, MappingInfo, PortMapper};

/// 映射生命周期管理器配置
#[derive(Debug, Clone)]
pub struct MappingLifetimeManagerConfig {
    /// 检查间隔（秒）
    pub check_interval: u64,
    /// 提前续期时间（秒）
    pub renew_ahead: u64,
    /// 默认生命周期（秒）
    pub default_lifetime: u64,
}

impl Default for MappingLifetimeManagerConfig {
    fn default() -> Self {
        Self {
            check_interval: 60, // 1分钟
            renew_ahead: 120, // 2分钟
            default_lifetime: 7200, // 2小时
        }
    }
}

/// 映射生命周期管理器
pub struct MappingLifetimeManager {
    /// 端口映射器
    mappers: Vec<Arc<dyn PortMapper>>,
    /// 映射信息
    mappings: RwLock<HashMap<String, MappingInfo>>,
    /// 配置
    config: MappingLifetimeManagerConfig,
    /// 是否正在运行
    running: Mutex<bool>,
    /// 任务句柄
    task_handle: Mutex<Option<tokio::task::JoinHandle<()>>>,
}

impl MappingLifetimeManager {
    /// 创建新的映射生命周期管理器
    pub fn new(mappers: Vec<Arc<dyn PortMapper>>, config: MappingLifetimeManagerConfig) -> Self {
        Self {
            mappers,
            mappings: RwLock::new(HashMap::new()),
            config,
            running: Mutex::new(false),
            task_handle: Mutex::new(None),
        }
    }
    
    /// 启动管理器
    pub async fn start(&self) -> Result<()> {
        let mut running = self.running.lock().await;
        
        if *running {
            return Ok(());
        }
        
        *running = true;
        
        // 创建检查任务
        let mappers = self.mappers.clone();
        let mappings = self.mappings.clone();
        let config = self.config.clone();
        
        let handle = tokio::spawn(async move {
            let mut check_interval = interval(Duration::from_secs(config.check_interval));
            
            loop {
                check_interval.tick().await;
                
                // 检查映射
                let mut mappings_guard = mappings.write().await;
                let mut expired_keys = Vec::new();
                
                for (key, mapping) in mappings_guard.iter_mut() {
                    // 计算剩余时间
                    let elapsed = mapping.created_at.elapsed().as_secs();
                    
                    if elapsed >= mapping.lifetime {
                        // 映射已过期，标记为删除
                        expired_keys.push(key.clone());
                    } else if mapping.lifetime - elapsed <= config.renew_ahead {
                        // 需要续期
                        for mapper in &mappers {
                            if let Ok(()) = mapper.renew_mapping(
                                mapping,
                                Duration::from_secs(config.default_lifetime)
                            ).await {
                                debug!("Renewed mapping: {}:{} ({})",
                                    mapping.external_addr.ip(), mapping.external_addr.port(), mapping.protocol);
                                break;
                            }
                        }
                    }
                }
                
                // 删除过期映射
                for key in expired_keys {
                    if let Some(mapping) = mappings_guard.remove(&key) {
                        debug!("Removed expired mapping: {}:{} ({})",
                            mapping.external_addr.ip(), mapping.external_addr.port(), mapping.protocol);
                    }
                }
            }
        });
        
        // 保存任务句柄
        let mut task_handle = self.task_handle.lock().await;
        *task_handle = Some(handle);
        
        info!("Started mapping lifetime manager");
        
        Ok(())
    }
    
    /// 停止管理器
    pub async fn stop(&self) -> Result<()> {
        let mut running = self.running.lock().await;
        
        if !*running {
            return Ok(());
        }
        
        *running = false;
        
        // 取消任务
        let mut task_handle = self.task_handle.lock().await;
        if let Some(handle) = task_handle.take() {
            handle.abort();
        }
        
        // 删除所有映射
        let mappings = self.mappings.read().await;
        
        for mapping in mappings.values() {
            for mapper in &self.mappers {
                if let Err(e) = mapper.delete_mapping(mapping).await {
                    warn!("Failed to delete mapping: {}", e);
                } else {
                    break;
                }
            }
        }
        
        info!("Stopped mapping lifetime manager");
        
        Ok(())
    }
    
    /// 添加映射
    pub async fn add_mapping(&self, mapping: MappingInfo) -> Result<()> {
        let key = format!("{}:{}:{}",
            mapping.external_addr.ip(),
            mapping.external_addr.port(),
            mapping.protocol);
        
        let mut mappings = self.mappings.write().await;
        mappings.insert(key, mapping);
        
        Ok(())
    }
    
    /// 删除映射
    pub async fn remove_mapping(&self, protocol: Protocol, external_port: u16) -> Result<()> {
        let mut mappings = self.mappings.write().await;
        let mut remove_key = None;
        
        for (key, mapping) in mappings.iter() {
            if mapping.protocol == protocol && mapping.external_addr.port() == external_port {
                remove_key = Some(key.clone());
                
                // 删除映射
                for mapper in &self.mappers {
                    if let Err(e) = mapper.delete_mapping(mapping).await {
                        warn!("Failed to delete mapping: {}", e);
                    } else {
                        break;
                    }
                }
                
                break;
            }
        }
        
        if let Some(key) = remove_key {
            mappings.remove(&key);
        }
        
        Ok(())
    }
    
    /// 获取所有映射
    pub async fn get_all_mappings(&self) -> Vec<MappingInfo> {
        let mappings = self.mappings.read().await;
        mappings.values().cloned().collect()
    }
    
    /// 创建新的映射
    pub async fn create_mapping(&self, protocol: Protocol, internal_port: u16, external_port: u16) -> Result<MappingInfo> {
        // 尝试使用每个映射器创建映射
        for mapper in &self.mappers {
            match mapper.create_mapping(
                protocol,
                internal_port,
                external_port,
                Duration::from_secs(self.config.default_lifetime)
            ).await {
                Ok(mapping) => {
                    // 添加到管理器
                    self.add_mapping(mapping.clone()).await?;
                    return Ok(mapping);
                },
                Err(e) => {
                    debug!("Failed to create mapping with mapper: {}", e);
                    continue;
                }
            }
        }
        
        Err(anyhow::anyhow!("Failed to create mapping with any mapper"))
    }
}
