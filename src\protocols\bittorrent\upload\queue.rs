use std::collections::VecDeque;
use anyhow::{Result, anyhow};
use tracing::{debug, warn};

use super::UploadError;

/// 上传请求
/// 表示一个分片上传请求
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct UploadRequest {
    /// 分片索引
    pub index: u32,
    
    /// 分片内偏移量
    pub begin: u32,
    
    /// 请求长度
    pub length: u32,
    
    /// 请求时间戳
    pub timestamp: std::time::Instant,
    
    /// 请求优先级
    pub priority: u8,
}

impl UploadRequest {
    /// 创建新的上传请求
    pub fn new(index: u32, begin: u32, length: u32) -> Self {
        Self {
            index,
            begin,
            length,
            timestamp: std::time::Instant::now(),
            priority: 0, // 默认优先级
        }
    }
    
    /// 创建带优先级的上传请求
    pub fn with_priority(index: u32, begin: u32, length: u32, priority: u8) -> Self {
        Self {
            index,
            begin,
            length,
            timestamp: std::time::Instant::now(),
            priority,
        }
    }
    
    /// 获取请求的唯一标识符
    pub fn id(&self) -> String {
        format!("{}:{}:{}", self.index, self.begin, self.length)
    }
    
    /// 获取请求的年龄（秒）
    pub fn age(&self) -> f64 {
        self.timestamp.elapsed().as_secs_f64()
    }
}

/// 上传请求队列
/// 管理上传请求的队列
pub struct RequestQueue {
    /// 请求队列
    queue: VecDeque<UploadRequest>,
    
    /// 最大队列长度
    max_length: usize,
    
    /// 是否按优先级排序
    sort_by_priority: bool,
}

impl RequestQueue {
    /// 创建新的请求队列
    pub fn new(max_length: usize) -> Self {
        Self {
            queue: VecDeque::with_capacity(max_length),
            max_length,
            sort_by_priority: true,
        }
    }
    
    /// 添加请求到队列
    pub fn add_request(&mut self, request: UploadRequest) -> Result<()> {
        // 检查队列是否已满
        if self.queue.len() >= self.max_length {
            return Err(UploadError::LimitExceeded.into());
        }
        
        // 检查请求是否已存在
        let request_id = request.id();
        for existing in &self.queue {
            if existing.id() == request_id {
                return Err(UploadError::InvalidRequest("Duplicate request".to_string()).into());
            }
        }
        
        // 添加请求到队列
        self.queue.push_back(request);
        
        // 如果需要，按优先级排序
        if self.sort_by_priority {
            self.sort_queue();
        }
        
        Ok(())
    }
    
    /// 获取请求
    pub fn get_requests(&mut self, count: usize) -> Vec<UploadRequest> {
        let mut requests = Vec::new();
        
        // 获取指定数量的请求
        for _ in 0..count.min(self.queue.len()) {
            if let Some(request) = self.queue.pop_front() {
                requests.push(request);
            }
        }
        
        requests
    }
    
    /// 清空队列
    pub fn clear(&mut self) {
        self.queue.clear();
    }
    
    /// 获取队列长度
    pub fn len(&self) -> usize {
        self.queue.len()
    }
    
    /// 检查队列是否为空
    pub fn is_empty(&self) -> bool {
        self.queue.is_empty()
    }
    
    /// 设置是否按优先级排序
    pub fn set_sort_by_priority(&mut self, sort: bool) {
        self.sort_by_priority = sort;
        
        if sort {
            self.sort_queue();
        }
    }
    
    /// 按优先级排序队列
    fn sort_queue(&mut self) {
        // 将队列转换为向量
        let mut vec: Vec<_> = self.queue.drain(..).collect();
        
        // 按优先级排序（优先级高的在前）
        vec.sort_by(|a, b| b.priority.cmp(&a.priority));
        
        // 将排序后的向量转换回队列
        self.queue = vec.into_iter().collect();
    }
    
    /// 移除过期的请求
    pub fn remove_expired(&mut self, max_age: f64) -> usize {
        let mut expired_count = 0;
        
        // 移除过期的请求
        self.queue.retain(|request| {
            let expired = request.age() > max_age;
            if expired {
                expired_count += 1;
            }
            !expired
        });
        
        expired_count
    }
    
    /// 获取队列中的所有请求
    pub fn get_all_requests(&self) -> Vec<UploadRequest> {
        self.queue.iter().cloned().collect()
    }
}
