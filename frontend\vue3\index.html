<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 修改CSP配置，确保unsafe-eval被正确应用，Vue3内部使用了Function构造函数需要此权限 -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self' ws: wss: http://localhost:8080 ws://localhost:8080">
    <title>Lumen - 下载管理器</title>
    <style>
      .skeleton-aside {
        background-color: rgba(0, 0, 0, 0.8);
        width: 78px;
      }
      .skeleton-subnav {
        background-color: #f4f5f7;
        width: 200px;
      }
      .skeleton-main {
        background-color: #fff;
      }

      @media (prefers-color-scheme: dark) {
        .skeleton-aside {
          background-color: rgba(0, 0, 0, 0.9);
        }
        .skeleton-subnav {
          background-color: #2D2D2D;
        }
        .skeleton-main {
          background-color: #343434;
        }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="title-bar"></div>
      <section class="el-container" id="container">
        <aside class="el-aside skeleton-aside hidden-sm-and-down">
        </aside>
        <aside class="el-aside skeleton-subnav hidden-xs-only">
        </aside>
        <section class="el-container skeleton-main">
        </section>
      </section>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
