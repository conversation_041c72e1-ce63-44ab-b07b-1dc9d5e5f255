use async_trait::async_trait;
use std::sync::Arc;
use tracing::{debug, error};
use uuid::Uuid;
use std::any::Any;

use crate::config::ConfigManager;
use crate::config::Settings;
use crate::core::interfaces::Downloader;
use crate::core::interfaces::{ProtocolType, DownloadStatus, DownloadProgress, DownloadOptions};
use crate::core::error::CoreResult;
use crate::download::resume::ResumeManager;
use crate::download::bandwidth_scheduler::BandwidthScheduler;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

use super::protocol::BitTorrentProtocol;
use super::torrent::{TorrentInfo, parse_torrent_file, parse_magnet_link};

/// BitTorrent downloader implementation
pub struct BitTorrentDownloader {
    /// Torrent file path or magnet link
    pub torrent_path: String,
    /// Output path
    pub output_path: String,
    /// Config Manager
    pub config_manager: Arc<ConfigManager>,
    /// Task ID
    pub task_id: Uuid,
    /// Resume manager
    pub resume_manager: Option<Arc<dyn ResumeManager>>,
    /// Bandwidth scheduler
    pub bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
    /// BitTorrent protocol implementation
    protocol: Option<BitTorrentProtocol>,
    /// Torrent information
    torrent_info: Option<TorrentInfo>,
    /// 自定义设置
    custom_settings: Option<Settings>,
}

impl BitTorrentDownloader {
    /// Create a new BitTorrent downloader
    pub fn new(
        torrent_path: String,
        output_path: String,
        config_manager: Arc<ConfigManager>,
        task_id: Uuid,
    ) -> Self {
        Self {
            torrent_path,
            output_path,
            config_manager,
            task_id,
            resume_manager: None,
            bandwidth_scheduler: None,
            protocol: None,
            torrent_info: None,
            custom_settings: None,
        }
    }

    /// Set resume manager
    pub fn with_resume_manager(
        mut self,
        resume_manager: Arc<dyn ResumeManager>,
    ) -> Self {
        self.resume_manager = Some(resume_manager);
        self
    }

    /// Set bandwidth scheduler
    pub fn with_bandwidth_scheduler(
        mut self,
        bandwidth_scheduler: Arc<dyn BandwidthScheduler>,
    ) -> Self {
        self.bandwidth_scheduler = Some(bandwidth_scheduler);
        self
    }

    /// Parse torrent file or magnet link
    async fn parse_torrent(&mut self) -> Result<TorrentInfo, BitTorrentError> {
        if self.torrent_path.starts_with("magnet:") {
            // Parse magnet link
            parse_magnet_link(&self.torrent_path).map_err(|e| BitTorrentError::Other(format!("parse_magnet_link error: {}", e)))
        } else {
            // Parse torrent file
            parse_torrent_file(&self.torrent_path).await.map_err(|e| BitTorrentError::Other(format!("parse_torrent_file error: {}", e)))
        }
    }
}

#[async_trait]
impl Downloader for BitTorrentDownloader {
    fn protocol_type(&self) -> ProtocolType {
        ProtocolType::BitTorrent
    }
    
    fn supports_url(&self, url: &str) -> bool {
        url.starts_with("magnet:") || url.ends_with(".torrent")
    }
    
    async fn init(&mut self, url: &str, output_path: &str, options: &DownloadOptions) -> CoreResult<()> {
        debug!("Initializing BitTorrent downloader for task {}", self.task_id);

        // Update paths if provided
        if !url.is_empty() {
            self.torrent_path = url.to_string();
        }
        if !output_path.is_empty() {
            self.output_path = output_path.to_string();
        }

        // 保存下载选项，用于后续创建BitTorrentProtocol时使用
        // 从options中提取相关参数
        let mut custom_settings = None;
        
        // 如果有自定义选项，处理BitTorrent特定的设置
        if let Some(custom_options) = &options.custom_options {
            // 创建一个Settings结构体来保存BitTorrent特定的设置
            let mut settings = self.config_manager.get_settings().await.clone();
            
            // 处理连接数
            if let Some(connections) = options.connections {
                settings.webseed_max_connections = Some(connections as usize);
            }
            
            // 处理速度限制
            if let Some(speed_limit) = options.speed_limit {
                settings.download.speed_limit = Some(speed_limit);
            }
            
            // 处理超时设置
            if let Some(timeout) = options.timeout {
                settings.webseed_connection_timeout = Some(timeout);
                settings.webseed_read_timeout = Some(timeout);
            }
            
            // 处理重试次数
            if let Some(retry_count) = options.retry_count {
                settings.webseed_retry_count = Some(retry_count);
            }
            
            // 处理自定义BitTorrent选项
            if let Some(value) = custom_options.get("enable_dht") {
                settings.enable_dht = Some(value.to_lowercase() == "true");
            }
            
            if let Some(value) = custom_options.get("webseed_enabled") {
                settings.webseed_enabled = Some(value.to_lowercase() == "true");
            }
            
            if let Some(value) = custom_options.get("upload_enabled") {
                settings.upload_enabled = Some(value.to_lowercase() == "true");
            }
            
            if let Some(value) = custom_options.get("piece_selection_strategy") {
                settings.piece_selection_strategy = Some(value.clone());
            }
            
            custom_settings = Some(settings);
        }
        
        // 保存自定义设置，用于后续创建BitTorrentProtocol
        self.custom_settings = custom_settings;

        // Parse torrent file or magnet link
        match self.parse_torrent().await {
            Ok(torrent_info) => {
                self.torrent_info = Some(torrent_info);
                Ok(())
            },
            Err(e) => {
                error!("Failed to parse torrent file or magnet link: {}", e);
                Err(BitTorrentError::Other(format!("Failed to parse torrent file or magnet link: {}", e)))
            }
        }
    }
    
    async fn start(&mut self) -> CoreResult<()> {
        debug!("Starting BitTorrent download for task {}", self.task_id);
        
        // Initialize BitTorrent protocol if not already done
        if self.protocol.is_none() {
            let torrent_info = match &self.torrent_info {
                Some(info) => info.clone(),
                None => {
                    // Try to parse torrent if not already done
                    match self.parse_torrent().await {
                        Ok(info) => {
                            self.torrent_info = Some(info.clone());
                            info
                        },
                        Err(e) => return Err(BitTorrentError::Other(format!("Failed to parse torrent: {}", e))),
                    }
                }
            };
            
            // Get settings - 使用自定义设置或默认设置
            let settings = if let Some(custom_settings) = &self.custom_settings {
                custom_settings.clone()
            } else {
                self.config_manager.get_settings().await
            };
            
            // Create BitTorrent protocol
            let mut protocol = BitTorrentProtocol::with_config_manager(
                self.task_id,
                self.torrent_path.clone(),
                self.output_path.clone(),
                settings,
                self.config_manager.clone(),
            );
            
            // Set torrent info
            protocol.set_torrent_info(torrent_info);
            
            // Set resume manager if available
            if let Some(resume_manager) = &self.resume_manager {
                protocol.resume_manager = Some(Arc::clone(resume_manager));
            }
            
            // Set bandwidth scheduler if available
            if let Some(bandwidth_scheduler) = &self.bandwidth_scheduler {
                protocol.bandwidth_scheduler = Some(Arc::clone(bandwidth_scheduler));
            }
            
            self.protocol = Some(protocol);
        }
        
        // Start the protocol
        if let Some(protocol) = &mut self.protocol {
            protocol.start().await.map_err(|e| BitTorrentError::Other(format!("Failed to start BitTorrent protocol: {}", e)))
        } else {
            Err(BitTorrentError::Other("BitTorrent protocol not initialized".into()))
        }
    }
    
    async fn pause(&mut self) -> CoreResult<()> {
        if let Some(protocol) = &mut self.protocol {
            protocol.pause().await.map_err(|e| BitTorrentError::Other(format!("Failed to pause BitTorrent protocol: {}", e)))
        } else {
            Err(BitTorrentError::Other("BitTorrent protocol not initialized".into()))
        }
    }
    
    async fn resume(&mut self) -> CoreResult<()> {
        if let Some(protocol) = &mut self.protocol {
            protocol.resume().await.map_err(|e| BitTorrentError::Other(format!("Failed to resume BitTorrent protocol: {}", e)))
        } else {
            Err(BitTorrentError::Other("BitTorrent protocol not initialized".into()))
        }
    }
    
    async fn cancel(&mut self) -> CoreResult<()> {
        if let Some(protocol) = &mut self.protocol {
            protocol.cancel().await.map_err(|e| BitTorrentError::Other(format!("Failed to cancel BitTorrent protocol: {}", e)))
        } else {
            // If protocol is not initialized, just return success
            Ok(())
        }
    }
    
    async fn progress(&self) -> CoreResult<DownloadProgress> {
        if let Some(protocol) = &self.protocol {
            // 获取各项下载信息
            let progress_percentage = protocol.progress_percentage().await.map_err(|e| BitTorrentError::Other(format!("Failed to get progress percentage: {}", e)))?;
            let speed = protocol.speed().await.map_err(|e| BitTorrentError::Other(format!("Failed to get speed: {}", e)))?;
            let total_size = protocol.get_total_size().await.map_err(|e| BitTorrentError::Other(format!("Failed to get total size: {}", e)))?;
            let downloaded_size = protocol.get_downloaded_size().await.map_err(|e| BitTorrentError::Other(format!("Failed to get downloaded size: {}", e)))?;
            
            // 计算预计完成时间（ETA）
            let eta = if speed > 0 && total_size.is_some() {
                let remaining = total_size.unwrap().saturating_sub(downloaded_size);
                Some(remaining / speed)
            } else {
                None
            };
            
            Ok(DownloadProgress {
                total_size,
                downloaded_size,
                progress_percentage,
                speed,
                eta,
            })
        } else {
            // Return default progress if protocol is not initialized
            Ok(DownloadProgress {
                total_size: None,
                downloaded_size: 0,
                progress_percentage: 0.0,
                speed: 0,
                eta: None,
            })
        }
    }
    
    async fn status(&self) -> CoreResult<DownloadStatus> {
        if let Some(protocol) = &self.protocol {
            // Map protocol status to download status
            let status = protocol.status().await?;
            
            // Return the status directly since it's already a DownloadStatus
            Ok(status)
        } else {
            // Return pending status if protocol is not initialized
            Ok(DownloadStatus::Pending)
        }
    }
    
    fn id(&self) -> Uuid {
        self.task_id
    }
    
    fn url(&self) -> &str {
        &self.torrent_path
    }
    
    fn output_path(&self) -> &str {
        &self.output_path
    }
    
    fn clone_box(&self) -> Box<dyn Downloader> {
        Box::new(Self {
            torrent_path: self.torrent_path.clone(),
            output_path: self.output_path.clone(),
            config_manager: self.config_manager.clone(),
            task_id: self.task_id,
            resume_manager: self.resume_manager.clone(),
            bandwidth_scheduler: self.bandwidth_scheduler.clone(),
            protocol: None, // Protocol cannot be cloned, will be recreated when needed
            torrent_info: self.torrent_info.clone(),
            custom_settings: self.custom_settings.clone(),
        })
    }
    
    async fn speed(&self) -> CoreResult<u64> {
        if let Some(protocol) = &self.protocol {
            protocol.speed().await.map_err(|e| BitTorrentError::Other(format!("Failed to get speed: {}", e)))
        } else {
            Ok(0)
        }
    }
    
    async fn get_total_size(&self) -> CoreResult<Option<u64>> {
        if let Some(protocol) = &self.protocol {
            protocol.get_total_size().await.map_err(|e| BitTorrentError::Other(format!("Failed to get total size: {}", e)))
        } else if let Some(info) = &self.torrent_info {
            Ok(Some(info.total_size))
        } else {
            Ok(None)
        }
    }
    
    async fn get_downloaded_size(&self) -> CoreResult<u64> {
        if let Some(protocol) = &self.protocol {
            protocol.get_downloaded_size().await.map_err(|e| BitTorrentError::Other(format!("Failed to get downloaded size: {}", e)))
        } else {
            Ok(0)
        }
    }
    
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    async fn set_download_limit(&mut self, limit: Option<u64>) -> CoreResult<()> {
        if let Some(protocol) = &mut self.protocol {
            protocol.set_download_limit(limit).await.map_err(|e| BitTorrentError::Other(format!("Failed to set download limit: {}", e)))
        } else {
            // Store the limit and apply it when protocol is initialized
            Ok(())
        }
    }
    
    async fn set_upload_limit(&mut self, limit: Option<u64>) -> CoreResult<()> {
        if let Some(protocol) = &mut self.protocol {
            protocol.set_upload_limit(limit).await.map_err(|e| BitTorrentError::Other(format!("Failed to set upload limit: {}", e)))
        } else {
            // Store the limit and apply it when protocol is initialized
            Ok(())
        }
    }
}
