//! HTTP下载器性能优化模块

use std::collections::VecDeque;
use std::time::{Duration, Instant};
use anyhow::Result;
use tracing::{debug, info, warn};

use super::downloader::HttpDownloader;

/// 性能统计数据
#[derive(Debug, Clone)]
pub struct PerformanceStats {
    /// 下载速度历史（最近10个数据点）
    pub speed_history: VecDeque<u64>,
    /// 延迟历史（最近10个数据点）
    pub latency_history: VecDeque<Duration>,
    /// 错误率历史
    pub error_rate_history: VecDeque<f64>,
    /// 最后更新时间
    pub last_update: Instant,
    /// 连接建立时间
    pub connection_time: Option<Duration>,
    /// 首字节时间
    pub first_byte_time: Option<Duration>,
    /// 平均块大小
    pub average_chunk_size: u64,
    /// 最优块大小
    pub optimal_chunk_size: u64,
}

impl Default for PerformanceStats {
    fn default() -> Self {
        Self {
            speed_history: VecDeque::with_capacity(10),
            latency_history: VecDeque::with_capacity(10),
            error_rate_history: VecDeque::with_capacity(10),
            last_update: Instant::now(),
            connection_time: None,
            first_byte_time: None,
            average_chunk_size: 1024 * 1024, // 1MB 默认
            optimal_chunk_size: 1024 * 1024, // 1MB 默认
        }
    }
}

impl PerformanceStats {
    /// 添加速度数据点
    pub fn add_speed(&mut self, speed: u64) {
        if self.speed_history.len() >= 10 {
            self.speed_history.pop_front();
        }
        self.speed_history.push_back(speed);
        self.last_update = Instant::now();
    }

    /// 添加延迟数据点
    pub fn add_latency(&mut self, latency: Duration) {
        if self.latency_history.len() >= 10 {
            self.latency_history.pop_front();
        }
        self.latency_history.push_back(latency);
    }

    /// 添加错误率数据点
    pub fn add_error_rate(&mut self, error_rate: f64) {
        if self.error_rate_history.len() >= 10 {
            self.error_rate_history.pop_front();
        }
        self.error_rate_history.push_back(error_rate);
    }

    /// 获取平均速度
    pub fn average_speed(&self) -> u64 {
        if self.speed_history.is_empty() {
            return 0;
        }
        self.speed_history.iter().sum::<u64>() / self.speed_history.len() as u64
    }

    /// 获取平均延迟
    pub fn average_latency(&self) -> Duration {
        if self.latency_history.is_empty() {
            return Duration::from_millis(0);
        }
        let total_ms: u64 = self.latency_history.iter()
            .map(|d| d.as_millis() as u64)
            .sum();
        Duration::from_millis(total_ms / self.latency_history.len() as u64)
    }

    /// 获取平均错误率
    pub fn average_error_rate(&self) -> f64 {
        if self.error_rate_history.is_empty() {
            return 0.0;
        }
        self.error_rate_history.iter().sum::<f64>() / self.error_rate_history.len() as f64
    }

    /// 检查性能是否稳定
    pub fn is_performance_stable(&self) -> bool {
        if self.speed_history.len() < 5 {
            return false;
        }

        // 计算速度变异系数
        let avg_speed = self.average_speed() as f64;
        if avg_speed == 0.0 {
            return false;
        }

        let variance: f64 = self.speed_history.iter()
            .map(|&speed| {
                let diff = speed as f64 - avg_speed;
                diff * diff
            })
            .sum::<f64>() / self.speed_history.len() as f64;

        let std_dev = variance.sqrt();
        let coefficient_of_variation = std_dev / avg_speed;

        // 如果变异系数小于0.3，认为性能稳定
        coefficient_of_variation < 0.3
    }
}

/// 自适应配置
#[derive(Debug, Clone)]
pub struct AdaptiveConfig {
    /// 当前块大小
    pub current_chunk_size: u64,
    /// 当前缓冲区大小
    pub current_buffer_size: usize,
    /// 当前并发数
    pub current_concurrency: usize,
    /// 最后调整时间
    pub last_adjustment: Instant,
    /// 调整间隔
    pub adjustment_interval: Duration,
}

impl Default for AdaptiveConfig {
    fn default() -> Self {
        Self {
            current_chunk_size: 1024 * 1024, // 1MB
            current_buffer_size: 8 * 1024 * 1024, // 8MB
            current_concurrency: 4,
            last_adjustment: Instant::now(),
            adjustment_interval: Duration::from_secs(30),
        }
    }
}

impl HttpDownloader {
    /// 初始化性能优化器
    pub fn initialize_performance_optimizer(&mut self) {
        // 这里可以根据系统资源和网络条件初始化性能参数
        debug!("Initialized performance optimizer for task {}", self.task_id);
    }

    /// 自适应调整块大小
    pub async fn adaptive_chunk_size_adjustment(&mut self, stats: &PerformanceStats) -> Result<()> {
        if !stats.is_performance_stable() {
            return Ok(()); // 性能不稳定时不调整
        }

        let avg_speed = stats.average_speed();
        let avg_latency = stats.average_latency();

        // 基于速度和延迟调整块大小
        let new_chunk_size = if avg_speed > 10 * 1024 * 1024 { // > 10MB/s
            // 高速网络，使用较大的块
            std::cmp::min(self.chunk_size * 2, 10 * 1024 * 1024) // 最大10MB
        } else if avg_speed < 1024 * 1024 { // < 1MB/s
            // 低速网络，使用较小的块
            std::cmp::max(self.chunk_size / 2, 256 * 1024) // 最小256KB
        } else if avg_latency > Duration::from_millis(500) {
            // 高延迟网络，使用较大的块减少请求次数
            std::cmp::min(self.chunk_size * 3 / 2, 5 * 1024 * 1024) // 最大5MB
        } else {
            self.chunk_size // 保持当前大小
        };

        if new_chunk_size != self.chunk_size {
            debug!("Adjusting chunk size from {} to {} bytes based on performance", 
                   self.chunk_size, new_chunk_size);
            self.chunk_size = new_chunk_size;
        }

        Ok(())
    }

    /// 自适应调整缓冲区大小
    pub async fn adaptive_buffer_size_adjustment(&mut self, stats: &PerformanceStats) -> Result<()> {
        let avg_speed = stats.average_speed();
        
        // 基于下载速度调整缓冲区大小
        let new_buffer_size = if avg_speed > 50 * 1024 * 1024 { // > 50MB/s
            // 超高速网络，使用大缓冲区
            32 * 1024 * 1024 // 32MB
        } else if avg_speed > 10 * 1024 * 1024 { // > 10MB/s
            // 高速网络
            16 * 1024 * 1024 // 16MB
        } else if avg_speed > 1024 * 1024 { // > 1MB/s
            // 中速网络
            8 * 1024 * 1024 // 8MB
        } else {
            // 低速网络，使用小缓冲区减少内存占用
            4 * 1024 * 1024 // 4MB
        };

        if new_buffer_size != self.buffer_size {
            debug!("Adjusting buffer size from {} to {} bytes based on performance", 
                   self.buffer_size, new_buffer_size);
            self.buffer_size = new_buffer_size;
            
            // 重新分配缓冲区
            self.buffer.clear();
            self.buffer.reserve(new_buffer_size);
        }

        Ok(())
    }

    /// 智能重试策略
    pub async fn smart_retry_strategy(&self, error_count: u32, last_error: &str) -> (Duration, bool) {
        // 基于错误类型和次数决定重试策略
        let base_delay = Duration::from_secs(1);
        
        let delay = match error_count {
            1..=3 => base_delay * error_count, // 线性增长
            4..=6 => base_delay * (error_count * error_count), // 指数增长
            _ => Duration::from_secs(60), // 最大1分钟
        };

        // 基于错误类型决定是否继续重试
        let should_retry = match last_error {
            e if e.contains("timeout") => error_count < 5,
            e if e.contains("connection") => error_count < 3,
            e if e.contains("404") || e.contains("403") => false, // 不重试客户端错误
            _ => error_count < 3,
        };

        debug!("Smart retry strategy: delay={:?}, should_retry={}, error_count={}", 
               delay, should_retry, error_count);

        (delay, should_retry)
    }

    /// 网络质量评估
    pub async fn assess_network_quality(&self, stats: &PerformanceStats) -> NetworkQuality {
        let avg_speed = stats.average_speed();
        let avg_latency = stats.average_latency();
        let avg_error_rate = stats.average_error_rate();

        // 综合评估网络质量
        let speed_score = match avg_speed {
            s if s > 10 * 1024 * 1024 => 5, // > 10MB/s: 优秀
            s if s > 5 * 1024 * 1024 => 4,  // > 5MB/s: 良好
            s if s > 1024 * 1024 => 3,      // > 1MB/s: 一般
            s if s > 512 * 1024 => 2,       // > 512KB/s: 较差
            _ => 1,                         // <= 512KB/s: 很差
        };

        let latency_score = match avg_latency.as_millis() {
            l if l < 50 => 5,   // < 50ms: 优秀
            l if l < 100 => 4,  // < 100ms: 良好
            l if l < 200 => 3,  // < 200ms: 一般
            l if l < 500 => 2,  // < 500ms: 较差
            _ => 1,             // >= 500ms: 很差
        };

        let error_score = match avg_error_rate {
            e if e < 0.01 => 5, // < 1%: 优秀
            e if e < 0.05 => 4, // < 5%: 良好
            e if e < 0.10 => 3, // < 10%: 一般
            e if e < 0.20 => 2, // < 20%: 较差
            _ => 1,             // >= 20%: 很差
        };

        let overall_score = (speed_score + latency_score + error_score) as f64 / 3.0;

        match overall_score {
            s if s >= 4.5 => NetworkQuality::Excellent,
            s if s >= 3.5 => NetworkQuality::Good,
            s if s >= 2.5 => NetworkQuality::Fair,
            s if s >= 1.5 => NetworkQuality::Poor,
            _ => NetworkQuality::VeryPoor,
        }
    }

    /// 优化下载参数
    pub async fn optimize_download_parameters(&mut self, network_quality: NetworkQuality) -> Result<()> {
        match network_quality {
            NetworkQuality::Excellent => {
                // 优秀网络：使用最大性能参数
                self.chunk_size = 10 * 1024 * 1024; // 10MB
                self.buffer_size = 32 * 1024 * 1024; // 32MB
                info!("Optimized for excellent network quality");
            },
            NetworkQuality::Good => {
                // 良好网络：使用高性能参数
                self.chunk_size = 5 * 1024 * 1024; // 5MB
                self.buffer_size = 16 * 1024 * 1024; // 16MB
                info!("Optimized for good network quality");
            },
            NetworkQuality::Fair => {
                // 一般网络：使用平衡参数
                self.chunk_size = 2 * 1024 * 1024; // 2MB
                self.buffer_size = 8 * 1024 * 1024; // 8MB
                info!("Optimized for fair network quality");
            },
            NetworkQuality::Poor => {
                // 较差网络：使用保守参数
                self.chunk_size = 1024 * 1024; // 1MB
                self.buffer_size = 4 * 1024 * 1024; // 4MB
                warn!("Optimized for poor network quality");
            },
            NetworkQuality::VeryPoor => {
                // 很差网络：使用最小参数
                self.chunk_size = 512 * 1024; // 512KB
                self.buffer_size = 2 * 1024 * 1024; // 2MB
                warn!("Optimized for very poor network quality");
            },
        }

        // 重新分配缓冲区
        self.buffer.clear();
        self.buffer.reserve(self.buffer_size);

        Ok(())
    }
}

/// 网络质量等级
#[derive(Debug, Clone, PartialEq)]
pub enum NetworkQuality {
    Excellent,
    Good,
    Fair,
    Poor,
    VeryPoor,
}
