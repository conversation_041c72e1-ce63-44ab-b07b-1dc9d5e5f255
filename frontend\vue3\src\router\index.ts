import { createRouter, createWebHistory } from 'vue-router'
import Main from '../components/Main.vue'
import TaskView from '../views/TaskView.vue'
import PreferenceView from '../views/PreferenceView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'main',
      component: Main,
      children: [
        {
          path: '',
          name: 'main-redirect',
          redirect: '/task/active'
        },
        {
          path: '/task',
          redirect: '/task/active'
        },
        {
          path: '/task/:status',
          name: 'task',
          component: TaskView,
          props: true
        },
        {
          path: '/preference',
          name: 'preference',
          component: PreferenceView
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
})

export default router
