use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio::time::Duration;
use tracing::{debug, warn};
use uuid::Uuid;

use crate::core::p2p::protocol::{DownloadStatus, DownloadStats};
use crate::core::p2p::peer::Peer;
use crate::core::p2p::piece::PieceManager;

/// 通用协议实现
pub struct CommonProtocol {
    /// 任务ID
    pub task_id: Uuid,
    /// 下载URL
    pub url: String,
    /// 输出路径
    pub output_path: String,
    /// 下载状态
    pub status: DownloadStatus,
    /// 下载统计
    pub stats: DownloadStats,
    /// 是否已初始化
    pub initialized: bool,
    /// 是否已取消
    pub cancelled: bool,
    /// 对等点列表
    pub peers: HashMap<String, Arc<Mutex<dyn Peer>>>,
    /// 分片管理器
    pub piece_manager: Option<Arc<Mutex<dyn PieceManager>>>,
    /// 最后更新时间
    pub last_update: std::time::Instant,
}

impl CommonProtocol {
    /// 创建新的通用协议实现
    pub fn new(task_id: Uuid, url: String, output_path: String) -> Self {
        Self {
            task_id,
            url,
            output_path,
            status: DownloadStatus::Pending,
            stats: DownloadStats {
                downloaded: 0,
                uploaded: 0,
                download_speed: 0,
                upload_speed: 0,
                connected_peers: 0,
                total_peers: 0,
                pieces_downloaded: 0,
                pieces_total: 0,
                progress: 0.0,
                estimated_time: None,
            },
            initialized: false,
            cancelled: false,
            peers: HashMap::new(),
            piece_manager: None,
            last_update: std::time::Instant::now(),
        }
    }

    /// 更新下载统计
    pub async fn update_stats(&mut self) -> Result<()> {
        let mut _downloaded = 0;
        let mut uploaded = 0;
        let mut download_speed = 0;
        let mut upload_speed = 0;
        let mut connected_peers = 0;

        // 统计对等点数据
        for (_, peer) in &self.peers {
            let peer = peer.lock().await;

            if peer.is_connected() {
                connected_peers += 1;
                _downloaded += peer.downloaded();
                uploaded += peer.uploaded();
                download_speed += peer.download_speed();
                upload_speed += peer.upload_speed();
            }
        }

        // 更新分片统计
        if let Some(piece_manager) = &self.piece_manager {
            let piece_manager = piece_manager.lock().await;
            self.stats.pieces_downloaded = piece_manager.progress().await? as u32 * self.stats.pieces_total;
            self.stats.progress = piece_manager.progress().await?;
            self.stats.downloaded = piece_manager.downloaded_size().await?;
        }

        self.stats.uploaded = uploaded;
        self.stats.download_speed = download_speed;
        self.stats.upload_speed = upload_speed;
        self.stats.connected_peers = connected_peers;

        // 计算预计剩余时间
        if download_speed > 0 {
            if let Some(piece_manager) = &self.piece_manager {
                let piece_manager = piece_manager.lock().await;
                let total_size = piece_manager.total_size().await?;
                let remaining = total_size - self.stats.downloaded;
                let seconds = remaining / download_speed;
                self.stats.estimated_time = Some(Duration::from_secs(seconds));
            }
        } else {
            self.stats.estimated_time = None;
        }

        self.last_update = std::time::Instant::now();

        Ok(())
    }

    /// 下载循环
    pub async fn download_loop(&mut self) -> Result<()> {
        // 检查是否已取消
        if self.cancelled {
            return Ok(());
        }

        // 更新状态
        self.status = DownloadStatus::Downloading;

        // 更新统计信息
        if self.last_update.elapsed() > Duration::from_secs(1) {
            if let Err(e) = self.update_stats().await {
                warn!("Failed to update stats: {}", e);
            }
        }

        // 处理对等点连接
        let mut active_peers = 0;
        let mut peers_to_remove = Vec::new();

        for (addr, peer) in &self.peers {
            let mut peer = peer.lock().await;

            if !peer.is_connected() {
                // 尝试重新连接
                if let Err(e) = peer.connect().await {
                    debug!("Failed to connect to peer {}: {}", addr, e);
                    peers_to_remove.push(addr.clone());
                    continue;
                }
            }

            active_peers += 1;
        }

        // 移除失效的对等点
        for addr in peers_to_remove {
            self.peers.remove(&addr);
        }

        self.stats.connected_peers = active_peers;

        // 检查下载是否完成
        if let Some(piece_manager) = &self.piece_manager {
            let piece_manager = piece_manager.lock().await;
            if piece_manager.is_complete().await? {
                self.status = DownloadStatus::Completed;
                return Ok(());
            }
        }

        Ok(())
    }
}
