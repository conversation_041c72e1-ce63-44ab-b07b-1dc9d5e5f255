// DHT模块入口
// 导出子模块
pub mod node;
pub mod routing;
pub mod message;
pub mod client;
pub mod service;
pub mod config;
pub mod query;
pub mod event;
pub mod bootstrap;
pub mod peer_finder;
pub mod message_handler;
pub mod bep44;
pub mod crawler;
pub mod optimizations;

#[cfg(test)]
mod tests;

// 导出公共接口
pub use node::{DHTNode, NodeId};
pub use routing::{RoutingTable, RoutingTableConfig};
pub use message::{DHTMessage, DHTMessageType, MessageHandler, TokenManager, MessageCodec};
pub use client::{DHTClient};
pub use service::{DHTService, DHTServiceConfig};
pub use config::DHTClientConfig;
pub use query::{DHTQuery, QueryManager};
pub use event::{DHTEvent, DHTEventListener, DHTEventDispatcher};
pub use bootstrap::DHTBootstrapper;
pub use peer_finder::DHTPeerFinder;
pub use message_handler::DHTMessageHandler;

// 导出BEP 44接口
pub use bep44::{DHTStore, DHTValue, ImmutableValue, MutableValue};

// 导出爬虫接口
pub use crawler::{DHTCrawler, DHTCrawlerConfig, DHTCrawlerStats};

// 导出优化接口
pub use optimizations::batch_processor::DHTBatchProcessor;
pub use optimizations::message_cache::DHTMessageCache;
pub use optimizations::rate_limiter::DHTRateLimiter;
pub use optimizations::routing_table_optimizer::DHTRoutingTableOptimizer;

// 重新导出核心接口
pub use crate::core::p2p::dht::{DHT, DHTConfig};
