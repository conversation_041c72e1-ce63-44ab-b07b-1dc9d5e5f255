use std::env;
use tracing::{debug, info};

use crate::config::settings::ProxyConfig;
use super::detector::{ProxyDetector, parse_auth_from_url, parse_no_proxy};
use super::error::ProxyError;

/// 环境变量代理检测器
/// 
/// 负责从系统环境变量中检测代理设置
pub struct EnvProxyDetector;

impl EnvProxyDetector {
    /// 创建新的环境变量代理检测器实例
    pub fn new() -> Self {
        EnvProxyDetector {}
    }
    
    /// 从环境变量中获取 no_proxy 设置
    fn get_no_proxy_from_env(&self) -> Option<String> {
        if let Ok(no_proxy_value) = env::var("no_proxy") {
            if !no_proxy_value.is_empty() {
                return Some(no_proxy_value);
            }
        }
        
        if let Ok(no_proxy_value) = env::var("NO_PROXY") {
            if !no_proxy_value.is_empty() {
                return Some(no_proxy_value);
            }
        }
        
        None
    }
}

impl ProxyDetector for EnvProxyDetector {
    fn detect(&self) -> Result<Option<ProxyConfig>, ProxyError> {
        debug!("检测环境变量代理设置...");
        
        // 检查常见的代理环境变量
        let _proxy_vars = [
            "http_proxy", "HTTP_PROXY",
            "https_proxy", "HTTPS_PROXY",
            "all_proxy", "ALL_PROXY"
        ];

        let mut proxy_url = None;
        let mut proxy_type = "http".to_string();

        // 首先尝试 HTTPS 代理
        for var in ["https_proxy", "HTTPS_PROXY"].iter() {
            if let Ok(proxy_value) = env::var(var) {
                if !proxy_value.is_empty() {
                    proxy_url = Some(proxy_value);
                    proxy_type = "https".to_string();
                    break;
                }
            }
        }

        // 如果没有 HTTPS 代理，尝试 HTTP 代理
        if proxy_url.is_none() {
            for var in ["http_proxy", "HTTP_PROXY"].iter() {
                if let Ok(proxy_value) = env::var(var) {
                    if !proxy_value.is_empty() {
                        proxy_url = Some(proxy_value);
                        proxy_type = "http".to_string();
                        break;
                    }
                }
            }
        }

        // 如果没有 HTTP 代理，尝试通用代理
        if proxy_url.is_none() {
            for var in ["all_proxy", "ALL_PROXY"].iter() {
                if let Ok(proxy_value) = env::var(var) {
                    if !proxy_value.is_empty() {
                        // 根据 URL 前缀确定代理类型
                        let proxy_type_value = if proxy_value.starts_with("socks") || proxy_value.starts_with("SOCKS") {
                            "socks5".to_string()
                        } else if proxy_value.starts_with("https") || proxy_value.starts_with("HTTPS") {
                            "https".to_string()
                        } else {
                            "http".to_string()
                        };

                        proxy_url = Some(proxy_value);
                        proxy_type = proxy_type_value;
                        break;
                    }
                }
            }
        }

        // 检查 no_proxy 环境变量
        let no_proxy_str = self.get_no_proxy_from_env();
        let no_proxy = no_proxy_str.as_deref().map_or_else(Vec::new, parse_no_proxy);

        // 如果找到代理 URL，创建 ProxyConfig
        if let Some(url) = proxy_url {
            // 解析用户名和密码
            match parse_auth_from_url(&url) {
                Ok((clean_url, username, password)) => {
                    info!("从环境变量检测到代理: {}", clean_url);
                    Ok(Some(ProxyConfig {
                        enabled: true,
                        url: clean_url,
                        username,
                        password,
                        no_proxy,
                        proxy_type,
                    }))
                },
                Err(e) => Err(ProxyError::EnvError(format!("解析代理URL时出错: {}", e)))
            }
        } else {
            debug!("未从环境变量检测到代理");
            Ok(None)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;

    #[test]
    fn test_detect_http_proxy() {
        env::set_var("http_proxy", "http://test.com:8080");
        let detector = EnvProxyDetector::new();
        let config = detector.detect().unwrap().unwrap();
        assert_eq!(config.url, "http://test.com:8080/");
        assert_eq!(config.proxy_type, "http");
        env::remove_var("http_proxy");
    }

    #[test]
    fn test_detect_https_proxy() {
        env::set_var("https_proxy", "https://test.com:8443");
        let detector = EnvProxyDetector::new();
        let config = detector.detect().unwrap().unwrap();
        assert_eq!(config.url, "https://test.com:8443/");
        assert_eq!(config.proxy_type, "https");
        env::remove_var("https_proxy");
    }

    #[test]
    fn test_detect_all_proxy_socks() {
        env::set_var("all_proxy", "socks5://test.com:1080");
        let detector = EnvProxyDetector::new();
        let config = detector.detect().unwrap().unwrap();
        assert_eq!(config.url, "socks5://test.com:1080/");
        assert_eq!(config.proxy_type, "socks5");
        env::remove_var("all_proxy");
    }

    #[test]
    fn test_detect_no_proxy() {
        env::set_var("http_proxy", "http://test.com:8080");
        env::set_var("no_proxy", "localhost,127.0.0.1");
        let detector = EnvProxyDetector::new();
        let config = detector.detect().unwrap().unwrap();
        assert_eq!(config.no_proxy, vec!["localhost".to_string(), "127.0.0.1".to_string()]);
        env::remove_var("http_proxy");
        env::remove_var("no_proxy");
    }

    #[test]
    fn test_no_proxy_detected() {
        // 确保环境变量中没有代理设置
        for var in ["http_proxy", "HTTP_PROXY", "https_proxy", "HTTPS_PROXY", "all_proxy", "ALL_PROXY"].iter() {
            env::remove_var(var);
        }
        
        let detector = EnvProxyDetector::new();
        let result = detector.detect().unwrap();
        assert!(result.is_none());
    }
}