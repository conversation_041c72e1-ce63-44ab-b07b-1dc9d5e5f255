use anyhow::Result;
use bytes::Bytes;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tracing::debug;

/// WebSeed缓存项
#[derive(Clone)]
struct CacheItem {
    /// 缓存数据
    data: Bytes,
    /// 缓存时间
    timestamp: Instant,
    /// 访问次数
    access_count: u32,
    /// 最后访问时间
    last_accessed: Instant,
}

/// WebSeed缓存配置
#[derive(Clone, Debug)]
pub struct WebSeedCacheConfig {
    /// 是否启用缓存
    pub enabled: bool,
    /// 最大缓存项数量
    pub max_items: usize,
    /// 缓存项过期时间（秒）
    pub ttl: u64,
    /// 缓存清理间隔（秒）
    pub cleanup_interval: u64,
}

impl Default for WebSeedCacheConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_items: 1000,
            ttl: 3600, // 1小时
            cleanup_interval: 300, // 5分钟
        }
    }
}

/// WebSeed缓存
pub struct WebSeedCache {
    /// 缓存项
    cache: Mutex<HashMap<String, CacheItem>>,
    /// 缓存配置
    config: WebSeedCacheConfig,
    /// 最后清理时间
    last_cleanup: Mutex<Instant>,
    /// 缓存命中次数
    hits: Mutex<u64>,
    /// 缓存未命中次数
    misses: Mutex<u64>,
}

#[allow(dead_code)]
impl WebSeedCache {
    /// 创建新的WebSeed缓存
    pub fn new(config: WebSeedCacheConfig) -> Self {
        Self {
            cache: Mutex::new(HashMap::new()),
            config,
            last_cleanup: Mutex::new(Instant::now()),
            hits: Mutex::new(0),
            misses: Mutex::new(0),
        }
    }

    /// 获取缓存项
    pub async fn get(&self, key: &str) -> Option<Bytes> {
        // 显式使用enabled字段
        let enabled = self.config.enabled;
        if !enabled {
            return None;
        }

        // 检查是否需要清理
        self.check_cleanup().await;

        let mut cache = self.cache.lock().await;

        if let Some(item) = cache.get_mut(key) {
            // 显式使用ttl字段
            let ttl = self.config.ttl;
            // 检查是否过期
            if item.timestamp.elapsed().as_secs() > ttl {
                // 移除过期项
                cache.remove(key);

                // 更新未命中计数
                let mut misses = self.misses.lock().await;
                *misses += 1;

                return None;
            }

            // 更新访问信息
            item.access_count += 1;
            item.last_accessed = Instant::now();

            // 更新命中计数
            let mut hits = self.hits.lock().await;
            *hits += 1;

            // 返回缓存数据
            Some(item.data.clone())
        } else {
            // 更新未命中计数
            let mut misses = self.misses.lock().await;
            *misses += 1;

            None
        }
    }

    /// 设置缓存项
    pub async fn set(&self, key: String, data: Bytes) -> Result<()> {
        // 显式使用enabled字段
        let enabled = self.config.enabled;
        if !enabled {
            return Ok(());
        }

        // 检查是否需要清理
        self.check_cleanup().await;

        let mut cache = self.cache.lock().await;

        // 显式使用max_items字段
        let max_items = self.config.max_items;
        // 检查缓存大小
        if cache.len() >= max_items && !cache.contains_key(&key) {
            // 移除最旧的项
            self.evict_oldest(&mut cache).await;
        }

        // 添加新项
        let item = CacheItem {
            data,
            timestamp: Instant::now(),
            access_count: 1,
            last_accessed: Instant::now(),
        };

        cache.insert(key, item);

        Ok(())
    }

    /// 移除最旧的缓存项
    async fn evict_oldest(&self, cache: &mut HashMap<String, CacheItem>) {
        if cache.is_empty() {
            return;
        }

        // 查找最旧的项
        let oldest_key = cache.iter()
            .min_by_key(|(_, item)| item.last_accessed)
            .map(|(key, _)| key.clone());

        if let Some(key) = oldest_key {
            cache.remove(&key);
            debug!("Evicted oldest cache item: {}", key);
        }
    }

    /// 检查是否需要清理
    async fn check_cleanup(&self) {
        let mut last_cleanup = self.last_cleanup.lock().await;

        // 显式使用cleanup_interval字段
        let cleanup_interval = self.config.cleanup_interval;
        if last_cleanup.elapsed().as_secs() > cleanup_interval {
            // 更新最后清理时间
            *last_cleanup = Instant::now();

            // 清理过期项
            self.cleanup().await;
        }
    }

    /// 清理过期项
    async fn cleanup(&self) {
        let mut cache = self.cache.lock().await;

        let now = Instant::now();
        // 显式使用ttl字段
        let ttl_value = self.config.ttl;
        let ttl = Duration::from_secs(ttl_value);

        // 查找过期项
        let expired_keys: Vec<String> = cache.iter()
            .filter(|(_, item)| now.duration_since(item.timestamp) > ttl)
            .map(|(key, _)| key.clone())
            .collect();

        // 记录过期项数量
        let expired_count = expired_keys.len();

        // 移除过期项
        for key in &expired_keys {
            cache.remove(key);
        }

        debug!("Cleaned up {} expired cache items", expired_count);
    }

    /// 获取缓存统计信息
    pub async fn stats(&self) -> (usize, u64, u64) {
        let cache = self.cache.lock().await;
        let hits = *self.hits.lock().await;
        let misses = *self.misses.lock().await;

        (cache.len(), hits, misses)
    }

    /// 清空缓存
    pub async fn clear(&self) {
        let mut cache = self.cache.lock().await;
        cache.clear();

        debug!("Cache cleared");
    }
}
