use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use sha1::{Sha1, Digest};
use tokio::fs;
use url::Url;

/// Torrent file information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TorrentFile {
    pub path: String,
    pub length: u64,
}

/// Torrent information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TorrentInfo {
    pub name: String,
    pub announce: Option<String>,
    pub announce_list: Option<Vec<Vec<String>>>,
    pub creation_date: Option<i64>,
    pub comment: Option<String>,
    pub created_by: Option<String>,
    pub encoding: Option<String>,
    pub piece_length: u64,
    pub pieces: Vec<Vec<u8>>,
    pub files: Option<Vec<TorrentFile>>,
    pub length: u64,
    pub info_hash: Vec<u8>,
    pub info_hash_hex: String,
    pub info_hash_encoded: String,
    pub total_size: u64,
    /// BEP 19 URL Seed 列表 (GetRight style)
    pub url_list: Option<Vec<String>>,
    /// BEP 17 HTTP Seed 列表 (Hoffman style)
    pub http_seeds: Option<Vec<String>>,
}

/// Torrent file structure for serde
#[derive(Debug, Deserialize, Serialize)]
struct BencodeFile {
    #[serde(rename = "path")]
    path_list: Vec<String>,
    length: u64,
}

/// Torrent info dictionary for serde
#[derive(Debug, Deserialize, Serialize)]
struct BencodeInfoDict {
    name: String,
    #[serde(rename = "piece length")]
    piece_length: u64,
    #[serde(with = "serde_bytes")]
    pieces: Vec<u8>,
    #[serde(default)]
    length: Option<u64>,
    #[serde(default)]
    files: Option<Vec<BencodeFile>>,
}

/// Torrent dictionary for serde
#[derive(Debug, Deserialize, Serialize)]
struct BencodeDict {
    info: BencodeInfoDict,
    announce: String,
    #[serde(default)]
    #[serde(rename = "announce-list")]
    announce_list: Option<Vec<Vec<String>>>,
    #[serde(default)]
    #[serde(rename = "creation date")]
    creation_date: Option<i64>,
    #[serde(default)]
    comment: Option<String>,
    #[serde(default)]
    #[serde(rename = "created by")]
    created_by: Option<String>,
    #[serde(default)]
    encoding: Option<String>,
    #[serde(default)]
    #[serde(rename = "url-list")]
    url_list: Option<Vec<String>>,
    #[serde(default)]
    httpseeds: Option<Vec<String>>,
}

/// Parse a torrent file
pub async fn parse_torrent_file(path: &str) -> Result<TorrentInfo> {
    let torrent_data = fs::read(path).await?;
    parse_torrent_data(&torrent_data)
}

/// Parse torrent data
pub fn parse_torrent_data(data: &[u8]) -> Result<TorrentInfo> {
    let torrent: BencodeDict = serde_bencode::from_bytes(data)?;

    // Calculate info hash
    let info_dict = serde_bencode::to_bytes(&torrent.info)?;
    let mut hasher = Sha1::new();
    hasher.update(&info_dict);
    let info_hash = hasher.finalize().to_vec();

    // Convert info hash to hex
    let info_hash_hex = hex::encode(&info_hash);

    // URL encode info hash for tracker requests
    let info_hash_encoded = url_encode_bytes(&info_hash);

    let mut torrent_files = Vec::new();
    let mut total_size = 0;
    let length;

    if let Some(file_length) = torrent.info.length {
        // Single file torrent
        total_size = file_length;
        length = file_length;
        torrent_files.push(TorrentFile {
            path: torrent.info.name.clone(),
            length: file_length,
        });
    } else if let Some(file_list) = &torrent.info.files {
        // Multi-file torrent
        for file in file_list {
            let path = file.path_list.join("/");
            total_size += file.length;

            torrent_files.push(TorrentFile {
                path,
                length: file.length,
            });
        }
        length = total_size;
    } else {
        return Err(anyhow!("Invalid torrent file: missing both length and files"));
    }

    // Convert pieces from a flat byte array to a vector of 20-byte SHA1 hashes
    let mut pieces_vec = Vec::new();
    let pieces_bytes = &torrent.info.pieces;
    for i in 0..(pieces_bytes.len() / 20) {
        let start = i * 20;
        let end = start + 20;
        if end <= pieces_bytes.len() {
            pieces_vec.push(pieces_bytes[start..end].to_vec());
        }
    }

    Ok(TorrentInfo {
        name: torrent.info.name,
        announce: Some(torrent.announce),
        announce_list: torrent.announce_list,
        creation_date: torrent.creation_date,
        comment: torrent.comment,
        created_by: torrent.created_by,
        encoding: torrent.encoding,
        piece_length: torrent.info.piece_length,
        pieces: pieces_vec,
        files: Some(torrent_files),
        length,
        total_size,
        info_hash,
        info_hash_hex,
        info_hash_encoded,
        url_list: torrent.url_list,
        http_seeds: torrent.httpseeds,
    })
}

/// Parse a magnet link
pub fn parse_magnet_link(magnet_link: &str) -> Result<TorrentInfo> {
    // Validate that it's a magnet link
    if !magnet_link.starts_with("magnet:?") {
        return Err(anyhow!("Not a valid magnet link"));
    }

    // Parse the URL
    let url = Url::parse(magnet_link)?;

    // Extract parameters
    let mut xt = None;
    let mut dn = None;
    let mut tr = Vec::new();

    for (key, value) in url.query_pairs() {
        match key.as_ref() {
            "xt" => {
                // Extract hash from urn:btih:HASH
                if value.starts_with("urn:btih:") {
                    xt = Some(value.strip_prefix("urn:btih:").unwrap().to_string());
                }
            },
            "dn" => {
                dn = Some(value.to_string());
            },
            "tr" => {
                tr.push(value.to_string());
            },
            _ => {}
        }
    }

    // Check if we have the required parameters
    let info_hash_hex = xt.ok_or_else(|| anyhow!("Missing xt parameter in magnet link"))?;
    let name = dn.unwrap_or_else(|| "Unknown".to_string());

    // Convert hex hash to bytes
    let info_hash = hex::decode(&info_hash_hex)?;

    // URL encode info hash for tracker requests
    let info_hash_encoded = url_encode_bytes(&info_hash);

    // Create a minimal TorrentInfo
    Ok(TorrentInfo {
        name,
        announce: tr.first().cloned(),
        announce_list: Some(tr.into_iter().map(|url| vec![url]).collect()),
        creation_date: None,
        comment: None,
        created_by: None,
        encoding: None,
        piece_length: 0, // Unknown until we get metadata
        pieces: Vec::new(), // Unknown until we get metadata
        files: None, // Unknown until we get metadata
        length: 0, // Unknown until we get metadata
        total_size: 0, // Unknown until we get metadata
        info_hash,
        info_hash_hex,
        info_hash_encoded,
        url_list: None, // Unknown until we get metadata
        http_seeds: None, // Unknown until we get metadata
    })
}

/// URL encode bytes for tracker requests
fn url_encode_bytes(bytes: &[u8]) -> String {
    let mut encoded = String::with_capacity(bytes.len() * 3);
    for &byte in bytes {
        encoded.push('%');
        encoded.push_str(&hex::encode(&[byte]));
    }
    encoded
}

/// Create a torrent file from TorrentInfo
pub fn create_torrent_file(path: &str, info: &TorrentInfo) -> Result<()> {
    // Create bencode info dictionary
    let mut files = Vec::new();
    if let Some(torrent_files) = &info.files {
        if torrent_files.len() == 1 {
            // Single file torrent
            // No files field needed
        } else {
            // Multi-file torrent
            for file in torrent_files {
                let path_list = file.path.split('/').map(|s| s.to_string()).collect();
                files.push(BencodeFile {
                    path_list,
                    length: file.length,
                });
            }
        }
    }

    // For single file torrents, use the length field
    // For multi-file torrents, use the files field
    let (length, files_opt) = if info.files.is_none() || info.files.as_ref().unwrap().is_empty() {
        // No files specified, use the length field directly
        (Some(info.length), None)
    } else if info.files.as_ref().map_or(0, |f| f.len()) == 1 {
        // Single file torrent
        (Some(info.files.as_ref().unwrap()[0].length), None)
    } else {
        // Multi-file torrent
        (None, Some(files))
    };

    let info_dict = BencodeInfoDict {
        name: info.name.clone(),
        piece_length: info.piece_length,
        pieces: info.pieces.clone().into_iter().flatten().collect(),
        length,
        files: files_opt,
    };

    // Create bencode dictionary
    let dict = BencodeDict {
        info: info_dict,
        announce: info.announce.clone().unwrap_or_default(),
        announce_list: info.announce_list.clone(),
        creation_date: info.creation_date,
        comment: info.comment.clone(),
        created_by: info.created_by.clone(),
        encoding: info.encoding.clone(),
        url_list: info.url_list.clone(),
        httpseeds: info.http_seeds.clone(),
    };

    // Serialize to bencode
    let data = serde_bencode::to_bytes(&dict)?;

    // Write to file
    std::fs::write(path, data)?;

    Ok(())
}
