use anyhow::{Result, anyhow};
use bytes::Bytes;
use rand::{seq::SliceRandom, Rng};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::debug;

use crate::core::p2p::peer::Peer;
use crate::core::p2p::piece::PieceManager;
use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::protocols::bittorrent::BitTorrentPeer;
use crate::protocols::bittorrent::message::BitTorrentMessage;

use super::core::BitTorrentProtocol;

/// 从对等点读取块数据
async fn read_block_from_peer(
    peer_addr: &std::net::SocketAddr,
    piece_index: u32,
    begin: u32,
    length: u32,
    _piece_manager: &Arc<Mutex<dyn PieceManager>>,
    torrent_info: &TorrentInfo
) -> Result<Vec<u8>> {
    // 创建一个通道，用于接收分片数据
    let (tx, rx) = tokio::sync::oneshot::channel::<Result<Vec<u8>>>();

    // 创建一个任务，尝试从对等点读取块
    let peer_addr_clone = peer_addr.to_string();
    let info_hash_clone = torrent_info.info_hash.clone();

    tokio::spawn(async move {
        use std::net::SocketAddr;

        let addr = *peer_addr;

        // 创建一个新的对等点连接
        match BitTorrentPeer::new(addr, &info_hash_clone, b"-RS0001-000000", 10, 1000, None, Some(torrent_info.clone()), Some(torrent_info.name.clone())).await {
            Ok(mut peer) => {
                // 连接到对等点
                if let Err(e) = peer.connect().await {
                    tx.send(Err(anyhow!("Failed to connect to peer: {}", e))).ok();
                    return;
                }

                // 发送感兴趣消息
                if let Err(e) = peer.send_bt_message(BitTorrentMessage::Interested).await {
                    tx.send(Err(anyhow!("Failed to send interested message: {}", e))).ok();
                    return;
                }

                // 等待解除阻塞
                let timeout = tokio::time::Duration::from_secs(10); // 10秒超时
                let start_time = tokio::time::Instant::now();

                while peer.peer_choking && start_time.elapsed() < timeout {
                    match peer.receive_bt_message().await {
                        Ok(Some(BitTorrentMessage::Unchoke)) => {
                            // peer.peer_choking 会在 receive_bt_message 中自动更新
                            break;
                        },
                        Ok(Some(_)) => {
                            // 忽略其他消息
                        },
                        Ok(None) => {
                            // 没有收到消息，等待一段时间
                            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                        },
                        Err(e) => {
                            tx.send(Err(anyhow!("Failed to receive message: {}", e))).ok();
                            return;
                        }
                    }
                }

                if peer.peer_choking {
                    tx.send(Err(anyhow!("Peer did not unchoke us within timeout"))).ok();
                    return;
                }

                // 发送请求消息
                if let Err(e) = peer.send_bt_message(BitTorrentMessage::Request(piece_index, begin, length)).await {
                    tx.send(Err(anyhow!("Failed to send request message: {}", e))).ok();
                    return;
                }

                // 等待响应
                let timeout = tokio::time::Duration::from_secs(10); // 10秒超时
                let start_time = tokio::time::Instant::now();

                while start_time.elapsed() < timeout {
                    // 接收消息
                    match peer.receive_bt_message().await {
                        Ok(Some(message)) => {
                            match message {
                                BitTorrentMessage::Piece(index, offset, data) => {
                                    // 检查是否是我们请求的分片
                                    if index == piece_index && offset == begin {
                                        tx.send(Ok(data)).ok();
                                        return;
                                    }
                                },
                                BitTorrentMessage::Reject(index, offset, len) => {
                                    // 对等点拒绝了我们的请求
                                    if index == piece_index && offset == begin && len == length {
                                        tx.send(Err(anyhow!("Peer rejected our request"))).ok();
                                        return;
                                    }
                                },
                                BitTorrentMessage::Choke => {
                                    // 对等点阻塞了我们
                                    tx.send(Err(anyhow!("Peer choked us"))).ok();
                                    return;
                                },
                                _ => {
                                    // 忽略其他消息
                                }
                            }
                        },
                        Ok(None) => {
                            // 没有收到消息，等待一段时间
                            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                        },
                        Err(e) => {
                            tx.send(Err(anyhow!("Failed to receive message: {}", e))).ok();
                            return;
                        }
                    }
                }

                // 超时
                tx.send(Err(anyhow!("Timeout waiting for piece data"))).ok();
            },
            Err(e) => {
                tx.send(Err(anyhow!("Failed to create peer: {}", e))).ok();
            }
        }
    });

    // 等待结果
    match rx.await {
        Ok(result) => result,
        Err(_) => Err(anyhow!("Failed to receive result from peer task")),
    }
}

impl BitTorrentProtocol {
    /// 从WebSeed下载分片
    pub(crate) async fn download_piece_from_webseed(&self, piece_index: usize) -> Result<Bytes> {
        if let Some(webseed_manager) = &self.webseed_manager {
            if webseed_manager.has_available_sources().await {
                return webseed_manager.download_piece(piece_index).await;
            }
        }

        Err(anyhow!("No available WebSeed sources"))
    }

    /// 从WebSeed并发下载多个分片
    pub(crate) async fn download_pieces_from_webseed_concurrent(&self, piece_indices: &[usize]) -> Result<Vec<(usize, Bytes)>> {
        if let Some(webseed_manager) = &self.webseed_manager {
            if webseed_manager.has_available_sources().await {
                return webseed_manager.download_pieces_concurrent(piece_indices).await;
            }
        }

        Err(anyhow!("No available WebSeed sources"))
    }

    /// 从对等点下载分片
    pub(crate) async fn download_piece_from_peers(&self, piece_index: usize) -> Result<Bytes> {
        // 检查是否有PeerManager和PieceManager
        let peer_manager = self.peer_manager.as_ref()
            .ok_or_else(|| anyhow!("Peer manager not initialized"))?;

        let piece_manager = self.piece_manager.as_ref()
            .ok_or_else(|| anyhow!("Piece manager not initialized"))?;

        let torrent_info = self.torrent_info.as_ref()
            .ok_or_else(|| anyhow!("Torrent info not available"))?;

        // 获取分片信息
        let piece_info = {
            let piece_manager_guard = piece_manager.lock().await;
            piece_manager_guard.get_piece_info(piece_index as u32).await?
                .ok_or_else(|| anyhow!("Piece info not found for index={}", piece_index))?
        };

        // 获取分片大小和索引
        let piece_size = piece_info.size as usize;
        let piece_index_u32 = piece_index as u32;

        // 获取所有可能拥有该分片的对等点
        let mut potential_peers = Vec::new();
        for (addr, peer_arc) in peer_manager.peers() {
            // 检查对等点是否拥有该分片
            if peer_arc.has_piece(piece_index_u32) && !peer_arc.is_choking() && peer_arc.common.lock().await.is_connected() {
                potential_peers.push(addr.clone());
            }
        }

        if potential_peers.is_empty() {
            return Err(anyhow!("No peers have piece {}", piece_index));
        }

        // 创建一个缓冲区，用于存储分片数据
        let mut piece_data = vec![0u8; piece_size];
        let block_size = 16384; // 16KB块大小
        let num_blocks = (piece_size + block_size - 1) / block_size;

        // 创建一个向量，用于跟踪已下载的块
        let mut downloaded_blocks = vec![false; num_blocks];
        let mut all_blocks_downloaded = false;

        // 设置超时时间
        let timeout = tokio::time::Duration::from_secs(60); // 60秒超时
        let start_time = tokio::time::Instant::now();

        // 尝试下载所有块
        while !all_blocks_downloaded && start_time.elapsed() < timeout {
            // 检查是否所有块都已下载
            all_blocks_downloaded = downloaded_blocks.iter().all(|&downloaded| downloaded);
            if all_blocks_downloaded {
                break;
            }

            // 获取未下载的块索引
            let mut block_indices: Vec<usize> = (0..num_blocks)
                .filter(|&i| !downloaded_blocks[i])
                .collect();

            if block_indices.is_empty() {
                // 所有块都已下载
                all_blocks_downloaded = true;
                break;
            }

            // 随机打乱块索引（在每次迭代中创建新的RNG）
            {
                let mut rng = rand::thread_rng();
                block_indices.shuffle(&mut rng);

                // 为每个未下载的块选择一个对等点
                for &block_index in &block_indices {
                    // 随机选择一个对等点
                    let peer_index = rng.gen_range(0..potential_peers.len());
                    let peer_addr = &potential_peers[peer_index];

                    // 计算块的起始偏移量和长度
                    let begin = (block_index * block_size) as u32;
                    let length = if block_index == num_blocks - 1 && piece_size % block_size != 0 {
                        (piece_size % block_size) as u32
                    } else {
                        block_size as u32
                    };

                    // 尝试从对等点读取块
                    match read_block_from_peer(
                        peer_addr,
                        piece_index_u32,
                        begin,
                        length,
                        piece_manager,
                        torrent_info
                    ).await {
                        Ok(block_data) => {
                            // 将块数据复制到分片数据中
                            let start = begin as usize;
                            let end = start + block_data.len();
                            if end <= piece_data.len() {
                                piece_data[start..end].copy_from_slice(&block_data);
                                downloaded_blocks[block_index] = true;
                            }
                        },
                        Err(e) => {
                            // 读取块失败，尝试下一个块
                            debug!("Failed to read block {} from peer {}: {}", block_index, peer_addr, e);
                        }
                    }
                }
            } // RNG 在这里被丢弃

            // 等待一段时间，避免过于频繁的请求
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        }

        // 检查是否所有块都已下载
        if all_blocks_downloaded {
            // 验证分片数据
            let mut piece_manager_guard = piece_manager.lock().await;

            // 添加分片数据
            match piece_manager_guard.add_piece_data(piece_index_u32, piece_data.clone()).await {
                Ok(true) => {
                    // 分片数据添加成功
                    Ok(Bytes::from(piece_data))
                },
                Ok(false) => {
                    // 分片数据添加失败
                    Err(anyhow!("Failed to add piece data"))
                },
                Err(e) => {
                    // 添加分片数据时发生错误
                    Err(e)
                }
            }
        } else {
            // 超时或无法下载所有块
            Err(anyhow!("Failed to download all blocks for piece {}", piece_index))
        }
    }

    /// 下载分片，优先使用WebSeed（如果启用）
    pub(crate) async fn download_piece(&self, piece_index: usize) -> Result<Bytes> {
        // 如果优先使用WebSeed，先尝试WebSeed下载
        let use_webseed_first = self.webseed_enabled &&
                               self.settings.webseed_prefer.unwrap_or(false);

        let has_webseed = if let Some(webseed_manager) = &self.webseed_manager {
            webseed_manager.has_available_sources().await
        } else {
            false
        };

        if use_webseed_first && has_webseed {
            match self.download_piece_from_webseed(piece_index).await {
                Ok(data) => {
                    debug!("Successfully downloaded piece {} from WebSeed", piece_index);
                    return Ok(data);
                },
                Err(webseed_error) => {
                    debug!("Failed to download piece {} from WebSeed: {}", piece_index, webseed_error);
                    // 继续尝试从对等点下载
                }
            }
        }

        // 尝试从对等点下载
        match self.download_piece_from_peers(piece_index).await {
            Ok(data) => {
                debug!("Successfully downloaded piece {} from peers", piece_index);
                Ok(data)
            },
            Err(peer_error) => {
                // 如果从对等点下载失败，尝试WebSeed
                if self.webseed_enabled && has_webseed {
                    match self.download_piece_from_webseed(piece_index).await {
                        Ok(data) => {
                            debug!("Successfully downloaded piece {} from WebSeed after peer failure", piece_index);
                            Ok(data)
                        },
                        Err(webseed_error) => {
                            // 两种方法都失败
                            Err(anyhow!("Failed to download piece {}: peer error: {}, webseed error: {}",
                                piece_index, peer_error, webseed_error))
                        }
                    }
                } else {
                    // 没有可用的WebSeed，返回原始错误
                    Err(peer_error)
                }
            }
        }
    }
}
