use crate::config::Settings;
use super::cache::WebSeedCacheConfig;

/// WebSeed配置
#[derive(Debug, Clone)]
pub struct WebSeedConfig {
    /// 是否启用WebSeed
    pub enabled: bool,
    /// 最大并发WebSeed连接数
    pub max_connections: usize,
    /// 连接超时（秒）
    pub connection_timeout: u64,
    /// 读取超时（秒）
    pub read_timeout: u64,
    /// 可用性检查间隔（秒）
    pub availability_check_interval: u64,
    /// 最小分片大小（字节）
    pub min_piece_size: usize,
    /// 是否优先使用WebSeed
    pub prefer_webseed: bool,
    /// WebSeed失败后重试次数
    pub retry_count: u32,
    /// WebSeed失败后重试延迟（秒）
    pub retry_delay: u64,
    /// 缓存配置
    pub cache_config: WebSeedCacheConfig,
}

impl Default for WebSeedConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_connections: 4,
            connection_timeout: 30,
            read_timeout: 60,
            availability_check_interval: 300,
            min_piece_size: 16384,
            prefer_webseed: false,
            retry_count: 3,
            retry_delay: 5,
            cache_config: WebSeedCacheConfig::default(),
        }
    }
}

impl WebSeedConfig {
    /// 从Settings创建WebSeedConfig
    pub fn from_settings(settings: &Settings) -> Self {
        // 创建缓存配置
        let cache_config = WebSeedCacheConfig {
            enabled: settings.webseed_cache_enabled.unwrap_or(true),
            max_items: settings.webseed_cache_max_items.unwrap_or(1000),
            ttl: settings.webseed_cache_ttl.unwrap_or(3600),
            cleanup_interval: settings.webseed_cache_cleanup_interval.unwrap_or(300),
        };

        Self {
            enabled: settings.webseed_enabled.unwrap_or(true),
            max_connections: settings.webseed_max_connections.unwrap_or(4),
            connection_timeout: settings.webseed_connection_timeout.unwrap_or(30),
            read_timeout: settings.webseed_read_timeout.unwrap_or(60),
            availability_check_interval: settings.webseed_availability_check_interval.unwrap_or(300),
            min_piece_size: settings.webseed_min_piece_size.unwrap_or(16384),
            prefer_webseed: settings.webseed_prefer.unwrap_or(false),
            retry_count: settings.webseed_retry_count.unwrap_or(3),
            retry_delay: settings.webseed_retry_delay.unwrap_or(5),
            cache_config,
        }
    }
}
