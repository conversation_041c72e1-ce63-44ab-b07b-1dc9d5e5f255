use uuid::Uuid;
use std::sync::Arc;

use tonitru_downloader::config::Settings;
use tonitru_downloader::config::ConfigManager;
use tonitru_downloader::protocols::http::HttpDownloader;

#[tokio::test]
#[ignore]
async fn test_http_client_follows_redirects() {
    // This test verifies that the HTTP client follows redirects
    // Since we're using reqwest, which follows redirects by default,
    // we just need to make sure our code doesn't interfere with that behavior

    // Create a settings object
    let settings = Settings::default();

    // Create a task ID
    let task_id = Uuid::new_v4();

    // Create an HttpDownloader
    let mut downloader = HttpDownloader::new(
        "https://example.com/redirect".to_string(),
        "output.txt".to_string(),
        Arc::new(ConfigManager::with_settings(settings)),
        task_id
    );

    // 注意：在实际环境中，这个测试需要网络连接才能成功
    // 在这里，我们只是验证代码能够编译和运行
    println!("HTTP redirect test would run here if not ignored");
}

#[tokio::test]
#[ignore]
async fn test_http_client_handles_too_many_redirects() {
    // This test verifies that the HTTP client handles too many redirects
    // Since we're using reqwest, which has a default limit of 10 redirects,
    // we just need to make sure our code handles the error properly

    // Create a settings object
    let settings = Settings::default();

    // Create a task ID
    let task_id = Uuid::new_v4();

    // Create an HttpDownloader
    let mut downloader = HttpDownloader::new(
        "https://example.com/too-many-redirects".to_string(),
        "output.txt".to_string(),
        Arc::new(ConfigManager::with_settings(settings)),
        task_id
    );

    // 注意：在实际环境中，这个测试需要网络连接才能成功
    // 在这里，我们只是验证代码能够编译和运行
    println!("HTTP too many redirects test would run here if not ignored");
}

// In a real integration test, we would set up a server that performs redirects
// and verify that our client follows them correctly. However, that's beyond
// the scope of this unit test file.
