use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, warn, info};
use anyhow::{Result, anyhow};

use super::settings::{Settings, ProxyConfig};
use super::constants::*;
use crate::core::interfaces::config::{Config, ConfigOps};

/// Configuration Manager
///
/// Centrally manages all application settings and provides access to them.
/// This follows the dependency injection pattern, allowing components to access
/// configuration without directly depending on the configuration implementation.
#[derive(Clone)]
pub struct ConfigManager {
    /// Application settings
    settings: Arc<RwLock<Settings>>,
}

impl ConfigManager {
    /// Create a new configuration manager with default settings
    pub async fn new() -> Result<Self> {
        let settings = Settings::new().map_err(|e| anyhow!("Failed to load settings: {}", e))?;
        Ok(Self {
            settings: Arc::new(RwLock::new(settings)),
        })
    }

    /// Create a new configuration manager with the provided settings
    pub fn new_with_settings(settings: Settings) -> Self {
        Self {
            settings: Arc::new(RwLock::new(settings)),
        }
    }

    /// Create a new configuration manager with the provided settings
    pub fn with_settings(settings: Settings) -> Self {
        Self {
            settings: Arc::new(RwLock::new(settings)),
        }
    }

    /// Get a reference to the settings
    pub async fn get_settings(&self) -> Settings {
        self.settings.read().await.clone()
    }

    /// Update the settings
    pub async fn update_settings(&self, settings: Settings) {
        let mut current_settings = self.settings.write().await;
        *current_settings = settings;
    }

    /// Get BitTorrent default trackers
    pub async fn get_default_trackers(&self) -> Vec<String> {
        let settings = self.settings.read().await;
        if let Some(bt_config) = &settings.bittorrent {
            bt_config.default_trackers.clone()
        } else {
            // If BitTorrent config is not available, return the default trackers from constants
            DEFAULT_BT_TRACKERS.iter().map(|s| s.to_string()).collect()
        }
    }

    /// Add a tracker to the default trackers list
    pub async fn add_default_tracker(&self, tracker_url: String) -> Result<()> {
        let mut settings = self.settings.write().await;

        // Ensure BitTorrent config exists
        if settings.bittorrent.is_none() {
            settings.bittorrent = Some(super::settings::BitTorrentConfig {
                enabled: DEFAULT_BT_ENABLED,
                port: DEFAULT_BT_PORT,
                max_peers: DEFAULT_BT_MAX_PEERS,
                max_connections: DEFAULT_BT_MAX_CONNECTIONS,
                connection_timeout: DEFAULT_BT_CONNECTION_TIMEOUT,
                request_timeout: DEFAULT_BT_REQUEST_TIMEOUT,
                default_trackers: DEFAULT_BT_TRACKERS.iter().map(|s| s.to_string()).collect(),
                enable_dht: true,
                peer_connect_timeout: DEFAULT_PEER_CONNECT_TIMEOUT,
                tracker_interval: DEFAULT_TRACKER_INTERVAL,
                peer_handshake_timeout_secs: DEFAULT_PEER_HANDSHAKE_TIMEOUT_SECS,
                dht_port: 6881,
                dht_bootstrap_nodes: vec![
                    "router.bittorrent.com:6881".to_string(),
                    "dht.transmissionbt.com:6881".to_string(),
                    "router.utorrent.com:6881".to_string(),
                ],
                dht_routing_table_size: 8,
                dht_node_timeout: 900,
                dht_query_timeout: 30,
                dht_auto_start: true,
                dht_maintenance_interval: 300,
                dht_routing_table_path: Some("data/dht_routing_table.json".to_string()),
                dht_routing_table_save_interval: 600,
                dht_load_routing_table: true,
                enable_ipv6: true,
            });
        }

        // Add the tracker if it doesn't already exist
        if let Some(bt_config) = &mut settings.bittorrent {
            if !bt_config.default_trackers.contains(&tracker_url) {
                bt_config.default_trackers.push(tracker_url);
                debug!("Added tracker to default trackers list");
            } else {
                debug!("Tracker already exists in default trackers list");
            }
        }

        Ok(())
    }

    /// Remove a tracker from the default trackers list
    pub async fn remove_default_tracker(&self, tracker_url: &str) -> Result<()> {
        let mut settings = self.settings.write().await;

        if let Some(bt_config) = &mut settings.bittorrent {
            let original_len = bt_config.default_trackers.len();
            bt_config.default_trackers.retain(|url| url != tracker_url);

            if bt_config.default_trackers.len() < original_len {
                debug!("Removed tracker from default trackers list");
            } else {
                debug!("Tracker not found in default trackers list");
            }
        }

        Ok(())
    }

    /// Get BitTorrent port
    pub async fn get_bittorrent_port(&self) -> u16 {
        let settings = self.settings.read().await;
        if let Some(bt_config) = &settings.bittorrent {
            bt_config.port
        } else {
            DEFAULT_BT_PORT
        }
    }

    /// Get DHT settings
    pub async fn get_dht_settings(&self) -> (bool, u16, Vec<String>) {
        let settings = self.settings.read().await;

        if let Some(bt_config) = &settings.bittorrent {
            (
                bt_config.enable_dht,
                bt_config.dht_port,
                bt_config.dht_bootstrap_nodes.clone(),
            )
        } else if let Some(dht_config) = &settings.dht {
            (
                dht_config.enabled,
                dht_config.port,
                dht_config.bootstrap_nodes.clone(),
            )
        } else {
            // Legacy fallback
            (
                settings.enable_dht.unwrap_or(true),
                settings.dht_port.unwrap_or(6881),
                settings.dht_bootstrap_nodes.clone().unwrap_or_else(|| vec![
                    "router.bittorrent.com:6881".to_string(),
                    "dht.transmissionbt.com:6881".to_string(),
                    "router.utorrent.com:6881".to_string(),
                ]),
            )
        }
    }

    /// Get IPv6 support status
    pub async fn get_ipv6_enabled(&self) -> bool {
        let settings = self.settings.read().await;

        if let Some(bt_config) = &settings.bittorrent {
            bt_config.enable_ipv6
        } else {
            // Fallback to global setting
            settings.enable_ipv6.unwrap_or(true)
        }
    }

    /// Get a configuration value by key
    /// 根据键获取配置值
    /// 
    /// 这个方法返回字符串形式的配置值。如果需要其他类型，需要手动进行类型转换，
    /// 或者使用泛型方法 `get<T>`。
    /// 
    /// # 参数
    /// * `key` - 配置键，使用点号分隔的路径格式，如 "server.port"
    /// 
    /// # 返回值
    /// * `Result<String>` - 成功时返回字符串值，失败时返回错误
    /// 
    /// # 错误
    /// 当配置键不存在时，会返回错误
    /// 
    /// # 示例
    /// ```
    /// let port_str = config_manager.get_value("server.port").await?;
    /// let port = port_str.parse::<u16>().unwrap_or(8080);
    /// ```
    /// 
    /// 对于需要类型转换的情况，建议使用泛型方法 `get<T>`：
    /// ```
    /// let port: u16 = config_manager.get("server.port").await?;
    /// ```
    pub async fn get_value(&self, key: &str) -> Result<String> {
        let settings = self.settings.read().await;
        
        // Split the key by dots to navigate nested structures
        let parts: Vec<&str> = key.split('.').collect();
        
        match parts.as_slice() {
            // Download settings
            ["download", "speed_limit"] => Ok(settings.download.speed_limit.unwrap_or(0).to_string()),
            ["download", "path"] => Ok(settings.download.path.clone()),
            ["download", "concurrent_downloads"] => Ok(settings.download.concurrent_downloads.to_string()),
            ["download", "connections_per_download"] => Ok(settings.download.connections_per_download.to_string()),
            ["download", "chunk_size"] => Ok(settings.download.chunk_size.to_string()),
            ["download", "buffer_size"] => Ok(settings.download.buffer_size.to_string()),
            
            // Upload settings
            ["upload", "speed_limit"] => Ok(settings.download.speed_limit.unwrap_or(0).to_string()),
            
            // Server settings
            ["server", "host"] => Ok(settings.server.host.clone()),
            ["server", "port"] => Ok(settings.server.port.to_string()),
            
            // Database settings
            ["database", "url"] => Ok(settings.database.url.clone()),
            
            // Mirror settings
            ["mirror", "enabled"] => Ok(settings.mirror.enabled.to_string()),
            ["mirror", "max_mirrors"] => Ok(settings.mirror.max_mirrors.to_string()),
            ["mirror", "health_check_interval_secs"] => Ok(settings.mirror.health_check_interval_secs.to_string()),
            
            // Proxy settings
            ["proxy", "enabled"] => Ok(settings.proxy.as_ref().map_or("false".to_string(), |p| p.enabled.to_string())),
            ["proxy", "url"] => Ok(settings.proxy.as_ref().map_or("".to_string(), |p| p.url.clone())),
            ["proxy", "type"] => Ok(settings.proxy.as_ref().map_or("".to_string(), |p| p.proxy_type.clone())),
            ["proxy", "username"] => Ok(settings.proxy.as_ref().map_or("".to_string(), |p| p.username.clone().unwrap_or_default())),
            ["proxy", "password"] => Ok(settings.proxy.as_ref().map_or("".to_string(), |p| p.password.clone().unwrap_or_default())),
            ["proxy", "no_proxy"] => Ok(settings.proxy.as_ref().map_or("".to_string(), |p| p.no_proxy.join(","))),
            
            // Task specific settings
            ["task", task_id, "download_limit"] if task_id.len() > 0 => {
                // For task-specific settings, we would ideally have a task settings map
                // For now, we'll return a default value
                Ok("0".to_string())
            },
            
            // Default case - key not found
            _ => Err(anyhow!("Configuration key not found: {}", key)),
        }
    }
    
    /// 根据键设置配置值
    /// 
    /// 这个方法接受字符串形式的配置值。如果需要设置其他类型，需要手动将其转换为字符串，
    /// 或者使用泛型方法 `set<T>`。
    /// 
    /// # 参数
    /// * `key` - 配置键，使用点号分隔的路径格式，如 "server.port"
    /// * `value` - 字符串形式的配置值
    /// 
    /// # 返回值
    /// * `Result<()>` - 成功时返回 Ok(()), 失败时返回错误
    /// 
    /// # 错误
    /// 当配置键不存在或值无法转换为目标类型时，会返回错误
    /// 
    /// # 示例
    /// ```
    /// config_manager.set_value("server.port", "8080".to_string()).await?;
    /// ```
    /// 
    /// 对于需要类型转换的情况，建议使用泛型方法 `set<T>`：
    /// ```
    /// config_manager.set("server.port", 8080).await?;
    /// ```
    pub async fn set_value(&self, key: &str, value: String) -> Result<()> {
        let mut settings = self.settings.write().await;
        
        // Split the key by dots to navigate nested structures
        let parts: Vec<&str> = key.split('.').collect();
        
        match parts.as_slice() {
            // Download settings
            ["download", "speed_limit"] => {
                settings.download.speed_limit = Some(value.parse::<u64>().map_err(|_| anyhow!("Invalid value for download.speed_limit: {}", value))?);
            },
            ["download", "path"] => {
                settings.download.path = value;
            },
            ["download", "concurrent_downloads"] => {
                settings.download.concurrent_downloads = value.parse::<u32>().map_err(|_| anyhow!("Invalid value for download.concurrent_downloads: {}", value))?.into();
            },
            ["download", "connections_per_download"] => {
                settings.download.connections_per_download = value.parse::<u32>().map_err(|_| anyhow!("Invalid value for download.connections_per_download: {}", value))?.into();
            },
            ["download", "chunk_size"] => {
                settings.download.chunk_size = value.parse::<u64>().map_err(|_| anyhow!("Invalid value for download.chunk_size: {}", value))?.try_into().unwrap();
            },
            ["download", "buffer_size"] => {
                settings.download.buffer_size = value.parse::<u64>().map_err(|_| anyhow!("Invalid value for download.buffer_size: {}", value))?.try_into().unwrap();
            },
            
            // Upload settings
            ["upload", "speed_limit"] => {
                settings.download.speed_limit = Some(value.parse::<u64>().map_err(|_| anyhow!("Invalid value for upload.speed_limit: {}", value))?);
            },
            
            // Server settings
            ["server", "host"] => {
                settings.server.host = value;
            },
            ["server", "port"] => {
                settings.server.port = value.parse::<u16>().map_err(|_| anyhow!("Invalid value for server.port: {}", value))?;
            },
            
            // Database settings
            ["database", "url"] => {
                settings.database.url = value;
            },
            
            // Mirror settings
            ["mirror", "enabled"] => {
                settings.mirror.enabled = value.parse::<bool>().map_err(|_| anyhow!("Invalid value for mirror.enabled: {}", value))?;
            },
            ["mirror", "max_mirrors"] => {
                settings.mirror.max_mirrors = value.parse::<u32>().map_err(|_| anyhow!("Invalid value for mirror.max_mirrors: {}", value))?.into();
            },
            ["mirror", "health_check_interval_secs"] => {
                settings.mirror.health_check_interval_secs = value.parse::<u64>().map_err(|_| anyhow!("Invalid value for mirror.health_check_interval_secs: {}", value))?;
            },
            
            // Proxy settings
            ["proxy", "enabled"] => {
                if settings.proxy.is_none() {
                    settings.proxy = Some(ProxyConfig {
                        enabled: value.parse::<bool>().map_err(|_| anyhow!("Invalid value for proxy.enabled: {}", value))?,
                        url: "".to_string(),
                        username: None,
                        password: None,
                        no_proxy: Vec::new(),
                        proxy_type: "http".to_string(),
                    });
                } else {
                    settings.proxy.as_mut().unwrap().enabled = value.parse::<bool>().map_err(|_| anyhow!("Invalid value for proxy.enabled: {}", value))?;
                }
            },
            ["proxy", "url"] => {
                if settings.proxy.is_none() {
                    settings.proxy = Some(ProxyConfig {
                        enabled: false,
                        url: value,
                        username: None,
                        password: None,
                        no_proxy: Vec::new(),
                        proxy_type: "http".to_string(),
                    });
                } else {
                    settings.proxy.as_mut().unwrap().url = value;
                }
            },
            ["proxy", "type"] => {
                if settings.proxy.is_none() {
                    settings.proxy = Some(ProxyConfig {
                        enabled: false,
                        url: "".to_string(),
                        username: None,
                        password: None,
                        no_proxy: Vec::new(),
                        proxy_type: value,
                    });
                } else {
                    settings.proxy.as_mut().unwrap().proxy_type = value;
                }
            },
            ["proxy", "username"] => {
                if settings.proxy.is_none() {
                    settings.proxy = Some(ProxyConfig {
                        enabled: false,
                        url: "".to_string(),
                        username: Some(value),
                        password: None,
                        no_proxy: Vec::new(),
                        proxy_type: "http".to_string(),
                    });
                } else {
                    settings.proxy.as_mut().unwrap().username = Some(value);
                }
            },
            ["proxy", "password"] => {
                if settings.proxy.is_none() {
                    settings.proxy = Some(ProxyConfig {
                        enabled: false,
                        url: "".to_string(),
                        username: None,
                        password: Some(value),
                        no_proxy: Vec::new(),
                        proxy_type: "http".to_string(),
                    });
                } else {
                    settings.proxy.as_mut().unwrap().password = Some(value);
                }
            },
            ["proxy", "no_proxy"] => {
                let no_proxy_list = value.split(',').map(|s| s.trim().to_string()).collect();
                if settings.proxy.is_none() {
                    settings.proxy = Some(ProxyConfig {
                        enabled: false,
                        url: "".to_string(),
                        username: None,
                        password: None,
                        no_proxy: no_proxy_list,
                        proxy_type: "http".to_string(),
                    });
                } else {
                    settings.proxy.as_mut().unwrap().no_proxy = no_proxy_list;
                }
            },
            
            // Task specific settings
            ["task", task_id, "download_limit"] if task_id.len() > 0 => {
                // For task-specific settings, we would ideally have a task settings map
                // For now, we'll just log that we received the setting
                debug!("Setting task-specific download limit for task {}: {}", task_id, value);
                // In a real implementation, we would store this in a task settings map
            },
            
            // Default case - key not found
            _ => return Err(anyhow!("Configuration key not found: {}", key)),
        }
        
        Ok(())
    }
}

impl ConfigManager {
    /// From specified path load configuration file
    /// 
    /// This method loads configuration from a specified file path.
    /// If no path is provided, it will use the default config path.
    pub async fn load_from_file(&self, path: Option<impl AsRef<std::path::Path>>) -> Result<()> {
        // Create ConfigImpl instance and load config from file
        let config_impl = match path {
            Some(p) => {
                let path_buf = std::path::PathBuf::from(p.as_ref());
                super::config_impl::ConfigImpl::from_file(path_buf).await
                    .map_err(|e| anyhow!("Failed to load config from file: {}", e))?
            },
            None => {
                // 使用默认配置路径
                let config_impl = super::config_impl::ConfigImpl::new().await
                    .map_err(|e| anyhow!("Failed to create config: {}", e))?;
                
                if let Some(path) = config_impl.get_config_path() {
                    if path.exists() {
                        super::config_impl::ConfigImpl::from_file(path).await
                            .map_err(|e| anyhow!("Failed to load config from default path: {}", e))?
                    } else {
                        config_impl
                    }
                } else {
                    config_impl
                }
            }
        };
        
        // Get config JSON representation
        let config_json = config_impl.get_all_as_json().await
            .map_err(|e| anyhow!("Failed to get config as JSON: {}", e))?;
        
        // Deserialize JSON to Settings
        let settings: Settings = serde_json::from_str(&config_json)
            .map_err(|e| anyhow!("Failed to deserialize settings: {}", e))?;
        
        // Update current settings
        self.update_settings(settings).await;
        
        debug!("Configuration loaded from file");
        Ok(())
    }

    /// Save config to specified path
    /// 
    /// This method saves the current configuration to a file.
    /// If no path is provided, it will use the default config path.
    pub async fn save_to_file(&self, path: Option<impl AsRef<std::path::Path>>) -> Result<()> {
        // Get current settings
        let settings = self.get_settings().await;
        
        // Serialize settings to JSON
        let settings_json = serde_json::to_string_pretty(&settings)
            .map_err(|e| anyhow!("Failed to serialize settings: {}", e))?;
        
        // Create ConfigImpl instance
        let config_impl = super::config_impl::ConfigImpl::new().await
            .map_err(|e| anyhow!("Failed to create config: {}", e))?;
        
        // Add JSON settings to ConfigImpl
        let value: serde_json::Value = serde_json::from_str(&settings_json)
            .map_err(|e| anyhow!("Failed to parse settings JSON: {}", e))?;
        
        // Recursively add all key value pairs
        self.add_json_to_config(&config_impl, "", &value).await?;
        
        // Save to file
        match path {
            Some(p) => {
                config_impl.save_to_file(p.as_ref()).await
                    .map_err(|e| anyhow!("Failed to save config to file: {}", e))?
            },
            None => {
                // 使用默认配置路径
                if let Some(default_path) = config_impl.get_config_path() {
                    // 确保目录存在
                    if let Some(parent) = default_path.parent() {
                        std::fs::create_dir_all(parent)
                            .map_err(|e| anyhow!("Failed to create config directory: {}", e))?;
                    }
                    
                    config_impl.save_to_file(default_path).await
                        .map_err(|e| anyhow!("Failed to save config to default path: {}", e))?
                } else {
                    return Err(anyhow!("No default config path available"));
                }
            }
        };
        
        debug!("Configuration saved to file");
        Ok(())
    }

    /// Recursively add JSON value to config
    /// 
    /// This method recursively traverses a JSON value and adds all key-value pairs
    /// to the configuration. Object values are traversed recursively, while primitive
    /// values are added directly with their full path as the key.
    /// 
    /// # Arguments
    /// * `config` - The ConfigImpl instance to add values to
    /// * `prefix` - The current key prefix (empty for root)
    /// * `value` - The JSON value to add
    /// 
    /// # Returns
    /// * `Result<()>` - Success or error
    async fn add_json_to_config(&self, config: &super::config_impl::ConfigImpl, prefix: &str, value: &serde_json::Value) -> Result<()> {
        match value {
            serde_json::Value::Object(map) => {
                // 对象类型，递归处理每个键值对
                for (key, val) in map {
                    let new_prefix = if prefix.is_empty() {
                        key.clone()
                    } else {
                        format!("{}.{}", prefix, key)
                    };
                    
                    // 使用 Box::pin 包装递归调用的 Future
                    Box::pin(self.add_json_to_config(config, &new_prefix, val)).await
                        .map_err(|e| anyhow!("Failed to add JSON value for key '{}': {}", new_prefix, e))?;
                }
                Ok(())
            },
            serde_json::Value::Array(arr) => {
                // 数组类型，直接设置
                if !prefix.is_empty() {
                    debug!("Setting array value for key: {} with {} elements", prefix, arr.len());
                    config.set(prefix, value.clone()).await
                        .map_err(|e| anyhow!("Failed to set array value for key '{}': {}", prefix, e))?;
                }
                Ok(())
            },
            _ => {
                // 其他基本类型值，直接设置
                if !prefix.is_empty() {
                    debug!("Setting primitive value for key: {}", prefix);
                    config.set(prefix, value.clone()).await
                        .map_err(|e| anyhow!("Failed to set primitive value for key '{}': {}", prefix, e))?;
                } else {
                    warn!("Attempted to set a value with empty key, ignoring");
                }
                Ok(())
            }
        }
    }

    /// Get config file path
    /// 
    /// This method returns the path to the configuration file.
    /// If no configuration file exists yet, it returns the default path
    /// where the configuration file would be created.
    /// 
    /// # Returns
    /// * `Result<Option<std::path::PathBuf>>` - The path to the configuration file, or None if not available
    pub async fn get_config_path(&self) -> Result<Option<std::path::PathBuf>> {
        // Create ConfigImpl instance
        let config_impl = super::config_impl::ConfigImpl::new().await
            .map_err(|e| anyhow!("Failed to create config: {}", e))?;
        
        // Get config path
        let path = config_impl.get_config_path().cloned();
        
        if let Some(ref p) = path {
            debug!("Configuration path: {}", p.display());
        } else {
            debug!("No configuration path available");
        }
        
        Ok(path)
    }

    /// Reset configuration to default values
    /// 
    /// This method resets all configuration settings to their default values.
    /// After calling this method, all custom settings will be lost and default settings
    /// will be saved to the configuration file.
    pub async fn reset_to_default(&self) -> Result<()> {
        info!("Resetting configuration to default values");
        
        // 创建默认设置
        let default_settings = Settings::default();
        
        // 更新当前设置
        self.update_settings(default_settings).await;
        
        // 保存默认设置到文件
        self.save_to_file(None::<&str>).await?;
        
        info!("Configuration has been reset to default values");
        Ok(())
    }

    /// Get a configuration value with type conversion
    /// 
    /// 这个方法提供了一种类型安全的方式来获取配置值，它会自动将字符串值转换为请求的类型。
    /// 相比于 get_value 方法，这个方法更加类型安全，不需要手动进行类型转换。
    /// 
    /// # 参数
    /// * `key` - 配置键
    /// 
    /// # 返回值
    /// * `Result<T>` - 成功时返回转换后的值，失败时返回错误
    /// 
    /// # 错误
    /// 当配置不存在或无法转换为请求的类型时，会返回错误
    /// 
    /// # 示例
    /// ```
    /// let port: u16 = config_manager.get("server.port").await?;
    /// let enabled: bool = config_manager.get("feature.enabled").await?;
    /// 
    /// // 也可以获取复杂类型
    /// #[derive(serde::Deserialize)]
    /// struct ProxyConfig {
    ///     enabled: bool,
    ///     url: String,
    /// }
    /// 
    /// let proxy_config: ProxyConfig = config_manager.get("proxy.config").await?;
    /// ```
    pub async fn get<T>(&self, key: &str) -> Result<T> 
    where 
        T: serde::de::DeserializeOwned + Send + Sync
    {
        // 获取字符串值
        let value_str = self.get_value(key).await?;
        
        // 尝试将字符串值转换为请求的类型
        serde_json::from_str::<T>(&value_str)
            .map_err(|e| anyhow!("Failed to convert value '{}' to requested type for key '{}': {}", value_str, key, e))
    }
    
    /// Set a configuration value with type conversion
    /// 
    /// 这个方法提供了一种类型安全的方式来设置配置值，它会自动将值转换为字符串表示。
    /// 相比于 set_value 方法，这个方法更加类型安全，不需要手动将值转换为字符串。
    /// 
    /// # 参数
    /// * `key` - 配置键
    /// * `value` - 要设置的值，可以是任何可序列化的类型
    /// 
    /// # 返回值
    /// * `Result<()>` - 成功时返回 Ok(()), 失败时返回错误
    /// 
    /// # 错误
    /// 当值无法序列化为字符串时，会返回错误
    /// 
    /// # 示例
    /// ```
    /// // 设置基本类型
    /// config_manager.set("server.port", 8080).await?;
    /// config_manager.set("feature.enabled", true).await?;
    /// 
    /// // 也可以设置复杂类型
    /// #[derive(serde::Serialize)]
    /// struct ProxyConfig {
    ///     enabled: bool,
    ///     url: String,
    /// }
    /// 
    /// let proxy_config = ProxyConfig {
    ///     enabled: true,
    ///     url: "http://proxy.example.com:8080".to_string(),
    /// };
    /// 
    /// config_manager.set("proxy.config", &proxy_config).await?;
    /// ```
    pub async fn set<T>(&self, key: &str, value: T) -> Result<()> 
    where 
        T: serde::Serialize + Send + Sync
    {
        // 将值转换为字符串
        let value_str = serde_json::to_string(&value)
            .map_err(|e| anyhow!("Failed to serialize value for key '{}': {}", key, e))?;
        
        // 使用字符串值设置配置
        self.set_value(key, value_str).await
    }
}
