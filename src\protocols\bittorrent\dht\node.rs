use std::fmt;
use std::net::{IpAddr, SocketAddr};
use std::time::{Duration, Instant};
use rand::Rng;
use serde::{Serialize, Deserialize};

/// 节点ID（20字节）
#[derive(<PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash)]
pub struct NodeId([u8; 20]);

impl NodeId {
    /// 创建新的随机节点ID
    pub fn new_random() -> Self {
        let mut rng = rand::thread_rng();
        let mut id = [0u8; 20];
        rng.fill(&mut id);
        Self(id)
    }

    /// 从字节数组创建节点ID
    pub fn from_bytes(bytes: [u8; 20]) -> Self {
        Self(bytes)
    }

    /// 获取内部字节数组
    pub fn as_bytes(&self) -> &[u8; 20] {
        &self.0
    }

    /// 获取节点ID的字节表示（复制）
    pub fn bytes(&self) -> [u8; 20] {
        self.0
    }

    /// 计算与另一个节点ID的XOR距离
    pub fn distance(&self, other: &NodeId) -> NodeId {
        let mut result = [0u8; 20];
        for i in 0..20 {
            result[i] = self.0[i] ^ other.0[i];
        }
        NodeId(result)
    }

    /// 检查指定位是否为1
    pub fn bit_at(&self, index: usize) -> bool {
        if index >= 160 {
            return false;
        }

        let byte_index = index / 8;
        let bit_index = 7 - (index % 8);

        (self.0[byte_index] & (1 << bit_index)) != 0
    }

    /// 获取共同前缀长度
    pub fn common_prefix_len(&self, other: &NodeId) -> usize {
        let mut count = 0;

        for i in 0..20 {
            let xor_byte = self.0[i] ^ other.0[i];
            if xor_byte == 0 {
                count += 8;
            } else {
                count += xor_byte.leading_zeros() as usize;
                break;
            }
        }

        count
    }
}

impl fmt::Debug for NodeId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "NodeId({})", hex::encode(self.0))
    }
}

impl fmt::Display for NodeId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", hex::encode(self.0))
    }
}

impl Serialize for NodeId {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_str(&hex::encode(self.0))
    }
}

impl<'de> Deserialize<'de> for NodeId {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        let bytes = hex::decode(s).map_err(serde::de::Error::custom)?;

        if bytes.len() != 20 {
            return Err(serde::de::Error::custom("NodeId must be 20 bytes"));
        }

        let mut id = [0u8; 20];
        id.copy_from_slice(&bytes);

        Ok(NodeId(id))
    }
}

/// DHT节点信息
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct DHTNode {
    /// 节点ID
    pub id: NodeId,
    /// 节点IP地址
    pub ip: IpAddr,
    /// 节点端口
    pub port: u16,
    /// 最后一次响应时间
    pub last_seen: Option<Instant>,
    /// 是否是可信节点
    pub is_good: bool,
    /// 连续失败次数
    pub failures: u32,
}

/// 用于序列化的DHT节点信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableDHTNode {
    /// 节点ID
    pub id: NodeId,
    /// 节点IP地址（字符串形式）
    pub ip: String,
    /// 节点端口
    pub port: u16,
    /// 是否是可信节点
    pub is_good: bool,
    /// 连续失败次数
    pub failures: u32,
    /// 最后一次响应时间（距离现在的秒数）
    pub last_seen_secs: Option<u64>,
}

impl DHTNode {
    /// 创建新的DHT节点
    pub fn new(id: NodeId, ip: IpAddr, port: u16) -> Self {
        Self {
            id,
            ip,
            port,
            last_seen: None,
            is_good: false,
            failures: 0,
        }
    }

    /// 从套接字地址创建DHT节点
    pub fn from_addr(id: NodeId, addr: SocketAddr) -> Self {
        Self::new(id, addr.ip(), addr.port())
    }

    /// 获取节点的套接字地址
    pub fn addr(&self) -> SocketAddr {
        SocketAddr::new(self.ip, self.port)
    }

    /// 获取节点ID
    pub fn get_id(&self) -> &NodeId {
        &self.id
    }

    /// 更新节点的最后响应时间
    pub fn update_last_seen(&mut self) {
        self.last_seen = Some(Instant::now());
        self.failures = 0;
    }

    /// 标记为可信节点
    pub fn mark_as_good(&mut self) {
        self.is_good = true;
    }

    /// 增加失败次数
    pub fn increment_failures(&mut self) {
        self.failures += 1;
    }

    /// 检查节点是否过期
    pub fn is_expired(&self, timeout_duration: Duration) -> bool {
        if let Some(last_seen) = self.last_seen {
            last_seen.elapsed() > timeout_duration
        } else {
            true
        }
    }

    /// 检查节点是否可疑（多次失败）
    pub fn is_questionable(&self, max_failures: u32) -> bool {
        self.failures >= max_failures
    }

    /// 编码为压缩二进制格式（用于DHT协议）
    ///
    /// 根据BEP 32，IPv4和IPv6地址使用不同的编码格式：
    /// - IPv4: 节点ID(20字节) + IPv4地址(4字节) + 端口(2字节) = 26字节
    /// - IPv6: 节点ID(20字节) + IPv6地址(16字节) + 端口(2字节) = 38字节
    pub fn encode_compact(&self) -> Vec<u8> {
        match self.ip {
            IpAddr::V4(ipv4) => {
                // IPv4格式: 节点ID(20字节) + IPv4地址(4字节) + 端口(2字节)
                let mut result = Vec::with_capacity(26);

                // 添加节点ID (20字节)
                result.extend_from_slice(self.id.as_bytes());

                // 添加IPv4地址 (4字节)
                result.extend_from_slice(&ipv4.octets());

                // 添加端口 (2字节，网络字节序)
                result.extend_from_slice(&self.port.to_be_bytes());

                result
            },
            IpAddr::V6(ipv6) => {
                // IPv6格式: 节点ID(20字节) + IPv6地址(16字节) + 端口(2字节)
                let mut result = Vec::with_capacity(38);

                // 添加节点ID (20字节)
                result.extend_from_slice(self.id.as_bytes());

                // 添加IPv6地址 (16字节)
                result.extend_from_slice(&ipv6.octets());

                // 添加端口 (2字节，网络字节序)
                result.extend_from_slice(&self.port.to_be_bytes());

                result
            },
        }
    }

    /// 从压缩二进制格式解码（用于DHT协议）
    ///
    /// 根据BEP 32，支持两种格式：
    /// - 26字节格式: 节点ID(20字节) + IPv4地址(4字节) + 端口(2字节)
    /// - 38字节格式: 节点ID(20字节) + IPv6地址(16字节) + 端口(2字节)
    pub fn decode_compact(data: &[u8]) -> Option<Self> {
        match data.len() {
            26 => {
                // IPv4格式
                // 提取节点ID (前20字节)
                let mut node_id = [0u8; 20];
                node_id.copy_from_slice(&data[0..20]);

                // 提取IPv4地址 (接下来的4字节)
                let mut ip_bytes = [0u8; 4];
                ip_bytes.copy_from_slice(&data[20..24]);
                let ip = IpAddr::V4(ip_bytes.into());

                // 提取端口 (最后2字节)
                let port = u16::from_be_bytes([data[24], data[25]]);

                Some(Self::new(NodeId::from_bytes(node_id), ip, port))
            },
            38 => {
                // IPv6格式
                // 提取节点ID (前20字节)
                let mut node_id = [0u8; 20];
                node_id.copy_from_slice(&data[0..20]);

                // 提取IPv6地址 (接下来的16字节)
                let mut ip_bytes = [0u8; 16];
                ip_bytes.copy_from_slice(&data[20..36]);
                let ip = IpAddr::V6(ip_bytes.into());

                // 提取端口 (最后2字节)
                let port = u16::from_be_bytes([data[36], data[37]]);

                Some(Self::new(NodeId::from_bytes(node_id), ip, port))
            },
            _ => None,
        }
    }

    /// 转换为可序列化的节点
    pub fn to_serializable(&self) -> SerializableDHTNode {
        SerializableDHTNode {
            id: self.id,
            ip: self.ip.to_string(),
            port: self.port,
            is_good: self.is_good,
            failures: self.failures,
            last_seen_secs: self.last_seen.map(|t| t.elapsed().as_secs()),
        }
    }

    /// 从可序列化的节点创建
    pub fn from_serializable(node: SerializableDHTNode) -> Result<Self, std::net::AddrParseError> {
        let ip = node.ip.parse::<IpAddr>()?;

        let mut dht_node = Self::new(node.id, ip, node.port);
        dht_node.is_good = node.is_good;
        dht_node.failures = node.failures;

        // 如果有最后一次响应时间，设置它
        if let Some(secs) = node.last_seen_secs {
            // 创建一个过去的时间点
            let duration = Duration::from_secs(secs);
            let now = Instant::now();

            // 如果时间太长，就不设置
            if secs < 24 * 60 * 60 { // 不超过一天
                if let Some(time) = now.checked_sub(duration) {
                    dht_node.last_seen = Some(time);
                }
            }
        }

        Ok(dht_node)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};

    #[test]
    fn test_node_id_new_random() {
        let node_id1 = NodeId::new_random();
        let node_id2 = NodeId::new_random();

        // 两个随机ID应该不同
        assert_ne!(node_id1, node_id2);
    }

    #[test]
    fn test_node_id_from_bytes() {
        let bytes = [1u8; 20];
        let node_id = NodeId::from_bytes(bytes);

        assert_eq!(node_id.as_bytes(), &bytes);
    }

    #[test]
    fn test_node_id_distance() {
        // 创建两个ID，只有第一个字节不同
        let mut bytes1 = [0u8; 20];
        let mut bytes2 = [0u8; 20];
        bytes1[0] = 1;
        bytes2[0] = 2;

        let id1 = NodeId::from_bytes(bytes1);
        let id2 = NodeId::from_bytes(bytes2);

        let distance = id1.distance(&id2);

        // XOR距离应该是 1 XOR 2 = 3 在第一个字节，其余为0
        assert_eq!(distance.as_bytes()[0], 3);
        for i in 1..20 {
            assert_eq!(distance.as_bytes()[i], 0);
        }
    }

    #[test]
    fn test_dht_node_new() {
        let node_id = NodeId::new_random();
        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let port = 6881;

        let node = DHTNode::new(node_id, ip, port);

        assert_eq!(node.id, node_id);
        assert_eq!(node.ip, ip);
        assert_eq!(node.port, port);
        assert_eq!(node.failures, 0);
        assert!(!node.is_good);
        assert!(node.last_seen.is_none());
    }

    #[test]
    fn test_dht_node_encode_decode_compact_ipv4() {
        let node_id = NodeId::from_bytes([1u8; 20]);
        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let port = 6881;

        let node = DHTNode::new(node_id, ip, port);

        let encoded = node.encode_compact();
        assert_eq!(encoded.len(), 26); // 20字节ID + 4字节IPv4 + 2字节端口

        let decoded = DHTNode::decode_compact(&encoded).unwrap();
        assert_eq!(decoded.id, node.id);
        assert_eq!(decoded.ip, node.ip);
        assert_eq!(decoded.port, node.port);
    }

    #[test]
    fn test_dht_node_encode_decode_compact_ipv6() {
        let node_id = NodeId::from_bytes([1u8; 20]);
        let ip = IpAddr::V6(Ipv6Addr::new(0x2001, 0xdb8, 0, 0, 0, 0, 0, 1));
        let port = 6881;

        let node = DHTNode::new(node_id, ip, port);

        let encoded = node.encode_compact();
        assert_eq!(encoded.len(), 38); // 20字节ID + 16字节IPv6 + 2字节端口

        let decoded = DHTNode::decode_compact(&encoded).unwrap();
        assert_eq!(decoded.id, node.id);
        assert_eq!(decoded.ip, node.ip);
        assert_eq!(decoded.port, node.port);
    }

    #[test]
    fn test_dht_node_invalid_compact_format() {
        // 测试无效长度的数据
        let data = vec![0u8; 30]; // 既不是26字节也不是38字节
        let result = DHTNode::decode_compact(&data);
        assert!(result.is_none());
    }
}