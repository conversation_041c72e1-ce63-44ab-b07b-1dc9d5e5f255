use crate::config::settings::ProxyConfig;
use super::detector::ProxyDetector;
use super::error::ProxyError;

#[cfg(target_os = "windows")]
pub mod windows;

#[cfg(target_os = "macos")]
pub mod macos;

#[cfg(target_os = "linux")]
pub mod linux;

/// 操作系统代理检测器
/// 
/// 根据当前操作系统选择适当的代理检测实现
pub struct OsProxyDetector;

impl OsProxyDetector {
    /// 创建新的操作系统代理检测器实例
    pub fn new() -> Self {
        OsProxyDetector {}
    }
}

impl ProxyDetector for OsProxyDetector {
    fn detect(&self) -> Result<Option<ProxyConfig>, ProxyError> {
        #[cfg(target_os = "windows")]
        {
            return windows::WindowsProxyDetector::new().detect();
        }

        #[cfg(target_os = "macos")]
        {
            return macos::MacOsProxyDetector::new().detect();
        }

        #[cfg(target_os = "linux")]
        {
            return linux::LinuxProxyDetector::new().detect();
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            return Ok(None);
        }
    }
}