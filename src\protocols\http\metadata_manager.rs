//! HTTP下载器元数据管理模块

use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use anyhow::Result;
use serde::{Serialize, Deserialize};
use tracing::{debug};
use uuid::Uuid;

use super::downloader::HttpDownloader;

/// 下载元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadMetadata {
    /// 任务ID
    pub task_id: Uuid,
    /// 下载URL
    pub url: String,
    /// 文件名
    pub filename: String,
    /// 文件大小
    pub file_size: Option<u64>,
    /// MIME类型
    pub mime_type: Option<String>,
    /// 服务器信息
    pub server_info: Option<String>,
    /// 最后修改时间
    pub last_modified: Option<String>,
    /// ETag
    pub etag: Option<String>,
    /// 内容编码
    pub content_encoding: Option<String>,
    /// 是否支持范围请求
    pub supports_range: bool,
    /// 下载开始时间
    pub start_time: u64,
    /// 预计完成时间
    pub estimated_completion: Option<u64>,
    /// 平均下载速度
    pub average_speed: u64,
    /// 峰值下载速度
    pub peak_speed: u64,
    /// 重试次数
    pub retry_count: u32,
    /// 错误历史
    pub error_history: Vec<String>,
    /// 网络质量指标
    pub network_metrics: NetworkMetrics,
    /// 自定义元数据
    pub custom_metadata: HashMap<String, String>,
}

/// 网络质量指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkMetrics {
    /// 连接延迟（毫秒）
    pub connection_latency: Option<u64>,
    /// 首字节时间（毫秒）
    pub time_to_first_byte: Option<u64>,
    /// 连接重试次数
    pub connection_retries: u32,
    /// 超时次数
    pub timeout_count: u32,
    /// 网络错误次数
    pub network_errors: u32,
    /// 平均响应时间
    pub average_response_time: Option<u64>,
}

impl Default for NetworkMetrics {
    fn default() -> Self {
        Self {
            connection_latency: None,
            time_to_first_byte: None,
            connection_retries: 0,
            timeout_count: 0,
            network_errors: 0,
            average_response_time: None,
        }
    }
}

impl DownloadMetadata {
    /// 创建新的下载元数据
    pub fn new(task_id: Uuid, url: String) -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        Self {
            task_id,
            url,
            filename: String::new(),
            file_size: None,
            mime_type: None,
            server_info: None,
            last_modified: None,
            etag: None,
            content_encoding: None,
            supports_range: false,
            start_time: now,
            estimated_completion: None,
            average_speed: 0,
            peak_speed: 0,
            retry_count: 0,
            error_history: Vec::new(),
            network_metrics: NetworkMetrics::default(),
            custom_metadata: HashMap::new(),
        }
    }

    /// 从HTTP响应头更新元数据
    pub fn update_from_headers(&mut self, headers: &reqwest::header::HeaderMap) {
        // 文件大小
        if let Some(content_length) = headers.get(reqwest::header::CONTENT_LENGTH) {
            if let Ok(size_str) = content_length.to_str() {
                if let Ok(size) = size_str.parse::<u64>() {
                    self.file_size = Some(size);
                }
            }
        }

        // MIME类型
        if let Some(content_type) = headers.get(reqwest::header::CONTENT_TYPE) {
            if let Ok(mime_str) = content_type.to_str() {
                self.mime_type = Some(mime_str.to_string());
            }
        }

        // 服务器信息
        if let Some(server) = headers.get(reqwest::header::SERVER) {
            if let Ok(server_str) = server.to_str() {
                self.server_info = Some(server_str.to_string());
            }
        }

        // 最后修改时间
        if let Some(last_modified) = headers.get(reqwest::header::LAST_MODIFIED) {
            if let Ok(modified_str) = last_modified.to_str() {
                self.last_modified = Some(modified_str.to_string());
            }
        }

        // ETag
        if let Some(etag) = headers.get(reqwest::header::ETAG) {
            if let Ok(etag_str) = etag.to_str() {
                self.etag = Some(etag_str.to_string());
            }
        }

        // 内容编码
        if let Some(encoding) = headers.get(reqwest::header::CONTENT_ENCODING) {
            if let Ok(encoding_str) = encoding.to_str() {
                self.content_encoding = Some(encoding_str.to_string());
            }
        }

        // 范围支持
        if let Some(accept_ranges) = headers.get(reqwest::header::ACCEPT_RANGES) {
            if let Ok(ranges_str) = accept_ranges.to_str() {
                self.supports_range = ranges_str.contains("bytes");
            }
        }

        // 从Content-Disposition头提取文件名
        if let Some(disposition) = headers.get(reqwest::header::CONTENT_DISPOSITION) {
            if let Ok(disposition_str) = disposition.to_str() {
                if let Some(filename) = Self::extract_filename_from_disposition(disposition_str) {
                    self.filename = filename;
                }
            }
        }
    }

    /// 从Content-Disposition头提取文件名
    fn extract_filename_from_disposition(disposition: &str) -> Option<String> {
        // 简单的文件名提取逻辑
        if let Some(filename_start) = disposition.find("filename=") {
            let filename_part = &disposition[filename_start + 9..];
            if let Some(quote_end) = filename_part.find('"') {
                if filename_part.starts_with('"') {
                    return Some(filename_part[1..quote_end].to_string());
                }
            } else if let Some(semicolon) = filename_part.find(';') {
                return Some(filename_part[..semicolon].trim().to_string());
            } else {
                return Some(filename_part.trim().to_string());
            }
        }
        None
    }

    /// 更新速度统计
    pub fn update_speed_stats(&mut self, current_speed: u64) {
        if current_speed > self.peak_speed {
            self.peak_speed = current_speed;
        }

        // 简单的移动平均
        if self.average_speed == 0 {
            self.average_speed = current_speed;
        } else {
            self.average_speed = (self.average_speed * 3 + current_speed) / 4;
        }
    }

    /// 添加错误记录
    pub fn add_error(&mut self, error: String) {
        self.error_history.push(error);
        self.retry_count += 1;
        
        // 限制错误历史记录数量
        if self.error_history.len() > 10 {
            self.error_history.remove(0);
        }
    }

    /// 更新网络指标
    pub fn update_network_metrics(&mut self, latency: Option<u64>, ttfb: Option<u64>) {
        if let Some(lat) = latency {
            self.network_metrics.connection_latency = Some(lat);
        }
        if let Some(ttfb_val) = ttfb {
            self.network_metrics.time_to_first_byte = Some(ttfb_val);
        }
    }

    /// 估算完成时间
    pub fn estimate_completion_time(&mut self, downloaded: u64) -> Option<u64> {
        if let Some(total_size) = self.file_size {
            if self.average_speed > 0 && downloaded < total_size {
                let remaining = total_size - downloaded;
                let eta_seconds = remaining / self.average_speed;
                let completion_time = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs() + eta_seconds;
                self.estimated_completion = Some(completion_time);
                return Some(completion_time);
            }
        }
        None
    }

    /// 设置自定义元数据
    pub fn set_custom_metadata(&mut self, key: String, value: String) {
        self.custom_metadata.insert(key, value);
    }

    /// 获取自定义元数据
    pub fn get_custom_metadata(&self, key: &str) -> Option<&String> {
        self.custom_metadata.get(key)
    }

    /// 生成下载报告
    pub fn generate_report(&self) -> String {
        let mut report = String::new();
        
        report.push_str(&format!("=== Download Report for {} ===\n", self.task_id));
        report.push_str(&format!("URL: {}\n", self.url));
        report.push_str(&format!("Filename: {}\n", self.filename));
        
        if let Some(size) = self.file_size {
            report.push_str(&format!("File Size: {} bytes ({:.2} MB)\n", size, size as f64 / 1024.0 / 1024.0));
        }
        
        if let Some(mime) = &self.mime_type {
            report.push_str(&format!("MIME Type: {}\n", mime));
        }
        
        report.push_str(&format!("Supports Range Requests: {}\n", self.supports_range));
        report.push_str(&format!("Average Speed: {} KB/s\n", self.average_speed / 1024));
        report.push_str(&format!("Peak Speed: {} KB/s\n", self.peak_speed / 1024));
        report.push_str(&format!("Retry Count: {}\n", self.retry_count));
        
        if !self.error_history.is_empty() {
            report.push_str("Error History:\n");
            for (i, error) in self.error_history.iter().enumerate() {
                report.push_str(&format!("  {}. {}\n", i + 1, error));
            }
        }
        
        report
    }
}

impl HttpDownloader {
    /// 初始化元数据
    pub async fn initialize_metadata(&mut self) -> Result<()> {
        let mut metadata = DownloadMetadata::new(self.task_id, self.url.clone());
        
        // 从URL提取文件名
        if let Some(filename) = self.extract_filename_from_url() {
            metadata.filename = filename;
        }
        
        // 保存到恢复点元数据中
        if let Some(resume_point) = &mut self.resume_point {
            let metadata_json = serde_json::to_string(&metadata).unwrap_or_default();
            resume_point.metadata.insert("download_metadata".to_string(), metadata_json);
        }
        
        debug!("Initialized metadata for task {}", self.task_id);
        Ok(())
    }

    /// 更新元数据
    pub async fn update_metadata_from_response(&mut self, response: &reqwest::Response) -> Result<()> {
        if let Some(resume_point) = &mut self.resume_point {
            if let Some(metadata_json) = resume_point.metadata.get("download_metadata") {
                if let Ok(mut metadata) = serde_json::from_str::<DownloadMetadata>(metadata_json) {
                    metadata.update_from_headers(response.headers());
                    
                    let updated_json = serde_json::to_string(&metadata).unwrap_or_default();
                    resume_point.metadata.insert("download_metadata".to_string(), updated_json);
                    
                    debug!("Updated metadata from response headers");
                }
            }
        }
        Ok(())
    }

    /// 更新速度元数据
    pub async fn update_speed_metadata(&mut self) -> Result<()> {
        if let Some(resume_point) = &mut self.resume_point {
            if let Some(metadata_json) = resume_point.metadata.get("download_metadata") {
                if let Ok(mut metadata) = serde_json::from_str::<DownloadMetadata>(metadata_json) {
                    metadata.update_speed_stats(self.current_speed);
                    metadata.estimate_completion_time(self.downloaded_size);
                    
                    let updated_json = serde_json::to_string(&metadata).unwrap_or_default();
                    resume_point.metadata.insert("download_metadata".to_string(), updated_json);
                }
            }
        }
        Ok(())
    }

    /// 添加错误到元数据
    pub async fn add_error_to_metadata(&mut self, error: String) -> Result<()> {
        if let Some(resume_point) = &mut self.resume_point {
            if let Some(metadata_json) = resume_point.metadata.get("download_metadata") {
                if let Ok(mut metadata) = serde_json::from_str::<DownloadMetadata>(metadata_json) {
                    metadata.add_error(error);
                    
                    let updated_json = serde_json::to_string(&metadata).unwrap_or_default();
                    resume_point.metadata.insert("download_metadata".to_string(), updated_json);
                }
            }
        }
        Ok(())
    }

    /// 从URL提取文件名
    fn extract_filename_from_url(&self) -> Option<String> {
        if let Ok(url) = url::Url::parse(&self.url) {
            if let Some(segments) = url.path_segments() {
                if let Some(last_segment) = segments.last() {
                    if !last_segment.is_empty() {
                        return Some(last_segment.to_string());
                    }
                }
            }
        }
        None
    }

    /// 获取下载报告
    pub async fn get_download_report(&self) -> Option<String> {
        if let Some(resume_point) = &self.resume_point {
            if let Some(metadata_json) = resume_point.metadata.get("download_metadata") {
                if let Ok(metadata) = serde_json::from_str::<DownloadMetadata>(metadata_json) {
                    return Some(metadata.generate_report());
                }
            }
        }
        None
    }

    /// 设置自定义元数据
    pub async fn set_custom_metadata(&mut self, key: String, value: String) -> Result<()> {
        if let Some(resume_point) = &mut self.resume_point {
            if let Some(metadata_json) = resume_point.metadata.get("download_metadata") {
                if let Ok(mut metadata) = serde_json::from_str::<DownloadMetadata>(metadata_json) {
                    metadata.set_custom_metadata(key, value);
                    
                    let updated_json = serde_json::to_string(&metadata).unwrap_or_default();
                    resume_point.metadata.insert("download_metadata".to_string(), updated_json);
                }
            }
        }
        Ok(())
    }
}
