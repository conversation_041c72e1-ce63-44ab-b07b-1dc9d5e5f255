use std::path::{Path, PathBuf};
use std::fs;
use async_trait::async_trait;
use libloading::{Library, Symbol};
use tracing::info;
use serde::{Serialize, Deserialize};

use crate::core::error::{CoreResult, CoreError};
use crate::core::interfaces::plugin::Plugin;

/// Plugin library type
pub type PluginLibrary = Library;

/// Plugin create function type
pub type PluginCreateFn = unsafe fn() -> *mut dyn Plugin;

/// Plugin manifest
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginManifest {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub website: Option<String>,
    pub dependencies: Vec<String>,
    pub entry_point: String,
}

/// Plugin loader interface
#[async_trait]
pub trait PluginLoader: Send + Sync {
    /// Load a plugin from a path
    async fn load_plugin(&self, path: &Path) -> CoreResult<(Box<dyn Plugin>, PluginLibrary)>;
    
    /// Read plugin manifest
    async fn read_manifest(&self, path: &Path) -> CoreResult<PluginManifest>;
}

/// Plugin loader implementation
pub struct PluginLoaderImpl {
    /// Plugin manifest file name
    manifest_file: String,
}

impl PluginLoaderImpl {
    /// Create a new plugin loader
    pub fn new() -> Self {
        Self {
            manifest_file: "plugin.json".to_string(),
        }
    }
    
    /// Get the plugin library path
    fn get_library_path(&self, plugin_dir: &Path, manifest: &PluginManifest) -> PathBuf {
        let mut path = plugin_dir.to_path_buf();
        
        #[cfg(target_os = "windows")]
        {
            path.push(format!("{}.dll", manifest.entry_point));
        }
        
        #[cfg(target_os = "linux")]
        {
            path.push(format!("lib{}.so", manifest.entry_point));
        }
        
        #[cfg(target_os = "macos")]
        {
            path.push(format!("lib{}.dylib", manifest.entry_point));
        }
        
        path
    }
}

#[async_trait]
impl PluginLoader for PluginLoaderImpl {
    async fn load_plugin(&self, path: &Path) -> CoreResult<(Box<dyn Plugin>, PluginLibrary)> {
        // Read the manifest
        let manifest = self.read_manifest(path).await?;
        
        // Get the library path
        let library_path = self.get_library_path(path, &manifest);
        
        // Check if the library exists
        if !library_path.exists() {
            return Err(CoreError::NotFound(format!("Plugin library not found: {}", library_path.display())));
        }
        
        // Load the library
        let library = unsafe {
            Library::new(&library_path)
                .map_err(|e| CoreError::Plugin(format!("Failed to load plugin library: {}", e)))?
        };
        
        // Get the create function
        let create_fn: Symbol<PluginCreateFn> = unsafe {
            library.get(b"create_plugin")
                .map_err(|e| CoreError::Plugin(format!("Failed to get plugin create function: {}", e)))?
        };
        
        // Create the plugin
        let plugin_ptr = unsafe { create_fn() };
        
        // Convert to Box
        let plugin = unsafe {
            Box::<dyn Plugin>::from_raw(plugin_ptr)
        };
        
        // Verify plugin metadata
        let metadata = plugin.metadata();
        
        if metadata.id != manifest.id {
            return Err(CoreError::Plugin(format!("Plugin ID mismatch: {} != {}", metadata.id, manifest.id)));
        }
        
        if metadata.version != manifest.version {
            return Err(CoreError::Plugin(format!("Plugin version mismatch: {} != {}", metadata.version, manifest.version)));
        }
        
        info!("Loaded plugin: {} v{}", metadata.name, metadata.version);
        
        Ok((plugin, library))
    }
    
    async fn read_manifest(&self, path: &Path) -> CoreResult<PluginManifest> {
        // Get the manifest path
        let manifest_path = path.join(&self.manifest_file);
        
        // Check if the manifest exists
        if !manifest_path.exists() {
            return Err(CoreError::NotFound(format!("Plugin manifest not found: {}", manifest_path.display())));
        }
        
        // Read the manifest
        let manifest_content = fs::read_to_string(&manifest_path)
            .map_err(|e| CoreError::IO(format!("Failed to read plugin manifest: {}", e)))?;
        
        // Parse the manifest
        let manifest: PluginManifest = serde_json::from_str(&manifest_content)
            .map_err(|e| CoreError::Parse(format!("Failed to parse plugin manifest: {}", e)))?;
        
        Ok(manifest)
    }
}
