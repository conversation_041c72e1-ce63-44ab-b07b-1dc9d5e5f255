use std::collections::HashMap;
use std::sync::Arc;
use std::any::Any;
use tokio::sync::RwLock;
use async_trait::async_trait;
use tracing::debug;
use crate::core::p2p::peer::PeerInfo;
use hex;
use super::message::{ExtensionMessage, ExtensionHandshake};
use crate::protocols::bittorrent::utils::error::BitTorrentError;

type Result<T> = std::result::Result<T, BitTorrentError>;

/// 扩展协议处理器接口
#[async_trait]
pub trait ExtensionHandler: Send + Sync + Any {
    /// 处理扩展消息
    async fn handle_message(&self, message_id: u8, payload: &[u8]) -> Result<()>;
    
    /// 处理扩展消息（带info_hash）
    /// 
    /// # 参数
    /// * `message_id` - 消息ID
    /// * `payload` - 消息负载
    /// * `info_hash` - 种子的信息哈希
    /// 
    /// # 返回
    /// 处理结果
    async fn handle_message_with_info_hash(&self, message_id: u8, payload: &[u8], _info_hash: &[u8]) -> Result<()> {
        // 默认实现调用不带info_hash的方法
        self.handle_message(message_id, payload).await
    }
    
    /// 获取扩展ID
    fn extension_id(&self) -> &str;

    /// 获取扩展消息ID
    fn message_id(&self) -> u8;

    /// 设置扩展消息ID
    fn set_message_id(&mut self, id: u8);

    /// 生成扩展消息
    async fn generate_message(&self) -> Result<Vec<u8>>;

    /// 转换为Any类型，用于类型转换
    fn as_any(&self) -> &dyn Any;
}

/// 扩展协议实现
pub struct ExtensionProtocol {
    /// 扩展处理器映射 (扩展ID -> (消息ID, 处理器))
    handlers: HashMap<String, (u8, Box<dyn ExtensionHandler + Send + Sync>)>,
    /// 支持的扩展列表 (扩展ID -> 消息ID)
    supported_extensions: HashMap<String, u8>,
    /// 对端支持的扩展列表 (扩展ID -> 消息ID)
    peer_extensions: Arc<RwLock<HashMap<String, u8>>>,
    /// 是否已初始化
    initialized: bool,
    /// 客户端版本
    client_version: Arc<RwLock<Option<String>>>,
    /// 元数据大小
    metadata_size: Arc<RwLock<Option<u64>>>,
}

impl ExtensionProtocol {
    /// 创建新的扩展协议处理器
    pub fn new() -> Self {
        Self {
            handlers: HashMap::new(),
            supported_extensions: HashMap::new(),
            peer_extensions: Arc::new(RwLock::new(HashMap::new())),
            initialized: false,
            client_version: Arc::new(RwLock::new(None)),
            metadata_size: Arc::new(RwLock::new(None)),
        }
    }

    /// 注册扩展处理器
    pub fn register_handler(&mut self, handler: Box<dyn ExtensionHandler + Send + Sync>) -> Result<()> {
        let extension_id = handler.extension_id().to_string();
        let message_id = handler.message_id();

        if self.handlers.contains_key(&extension_id) {
            return Err(BitTorrentError::Other(format!("Extension handler already registered: {}", extension_id)));
        }

        self.supported_extensions.insert(extension_id.clone(), message_id);
        self.handlers.insert(extension_id, (message_id, handler));

        Ok(())
    }

    /// 处理扩展消息
    pub async fn handle_message(&self, message_id: u8, payload: &[u8]) -> Result<()> {
        // 处理握手消息
        if message_id == 0 {
            return self.handle_handshake(payload).await;
        }

        // 查找对应的处理器
        for (_, (id, handler)) in &self.handlers {
            if *id == message_id {
                return handler.handle_message(message_id, payload).await;
            }
        }

        Err(BitTorrentError::Other(format!("No handler found for extension message ID: {}", message_id)))
    }
    
    /// 处理扩展消息（带info_hash）
    pub async fn handle_message_with_info_hash(&self, message_id: u8, payload: &[u8], info_hash: &[u8]) -> Result<()> {
        // 处理握手消息
        if message_id == 0 {
            return self.handle_handshake(payload).await;
        }

        // 查找对应的处理器
        for (_, (id, handler)) in &self.handlers {
            if *id == message_id {
                return handler.handle_message_with_info_hash(message_id, payload, info_hash).await;
            }
        }

        Err(BitTorrentError::Other(format!("No handler found for extension message ID: {}", message_id)))
    }

    /// 处理握手消息
    async fn handle_handshake(&self, payload: &[u8]) -> Result<()> {
        let handshake = ExtensionHandshake::decode(payload)?;

        // 更新对端支持的扩展
        let mut peer_extensions_map = HashMap::new();
        for (id, message_id) in handshake.extensions.clone() {
            peer_extensions_map.insert(id, message_id);
        }

        // 使用内部可变性更新对端支持的扩展
        let mut peer_extensions = self.peer_extensions.write().await;
        *peer_extensions = peer_extensions_map.clone();

        // 更新客户端版本
        if let Some(version) = handshake.version.clone() {
            let mut client_version = self.client_version.write().await;
            *client_version = Some(version.clone());
            debug!("Updated client version: {}", version);
        }

        // 更新元数据大小
        if let Some(size) = handshake.metadata_size {
            let mut metadata_size = self.metadata_size.write().await;
            *metadata_size = Some(size);
            debug!("Updated metadata size: {}", size);
        }

        debug!("Received extension handshake: {:?}", peer_extensions_map);

        Ok(())
    }

    /// 生成握手消息
    pub async fn generate_handshake(&self) -> Result<Vec<u8>> {
        let mut handshake = ExtensionHandshake::new();

        // 添加支持的扩展
        for (id, message_id) in &self.supported_extensions {
            handshake.add_extension(id, *message_id);
        }

        // 设置元数据大小
        let metadata_size = self.metadata_size.read().await;
        if let Some(size) = *metadata_size {
            handshake.set_metadata_size(size);
        }

        // 设置客户端版本
        let client_version = self.client_version.read().await;
        if let Some(version) = client_version.clone() {
            handshake.version = Some(version);
        }

        handshake.encode()
    }

    /// 获取扩展消息ID
    pub fn get_message_id(&self, extension_id: &str) -> Option<u8> {
        self.supported_extensions.get(extension_id).copied()
    }

    /// 检查对端是否支持指定扩展
    pub async fn peer_supports(&self, extension_id: &str) -> bool {
        let peer_extensions = self.peer_extensions.read().await;
        peer_extensions.contains_key(extension_id)
    }

    /// 获取对端支持的扩展消息ID
    pub async fn get_peer_message_id(&self, extension_id: &str) -> Option<u8> {
        let peer_extensions = self.peer_extensions.read().await;
        peer_extensions.get(extension_id).copied()
    }

    /// 设置元数据大小
    pub async fn set_metadata_size(&self, size: u64) -> Result<()> {
        let mut metadata_size = self.metadata_size.write().await;
        *metadata_size = Some(size);
        debug!("Set metadata size: {}", size);
        Ok(())
    }

    /// 获取扩展处理器
    pub fn get_handler(&self, extension_id: &str) -> Option<&Box<dyn ExtensionHandler + Send + Sync>> {
        self.handlers.get(extension_id).map(|(_, handler)| handler)
    }

    /// 获取扩展处理器并转换为指定类型
    pub fn get_handler_as<T: 'static>(&self, extension_id: &str) -> Option<&T> {
        self.get_handler(extension_id)
            .and_then(|handler| handler.as_any().downcast_ref::<T>())
    }

    /// 获取客户端版本
    pub async fn get_client_version(&self) -> Option<String> {
        let client_version = self.client_version.read().await;
        client_version.clone()
    }

    /// 设置客户端版本
    pub async fn set_client_version(&self, version: String) -> Result<()> {
        let mut client_version = self.client_version.write().await;
        *client_version = Some(version.clone());
        debug!("Set client version: {}", version);
        Ok(())
    }

    /// 获取元数据大小
    pub async fn get_metadata_size(&self) -> Option<u64> {
        let metadata_size = self.metadata_size.read().await;
        *metadata_size
    }

    /// 获取对端支持的所有扩展
    pub async fn get_peer_extensions(&self) -> HashMap<String, u8> {
        let peer_extensions = self.peer_extensions.read().await;
        peer_extensions.clone()
    }
    
    /// 获取所有扩展处理器
    pub fn get_handlers(&self) -> impl Iterator<Item = (&String, &Box<dyn ExtensionHandler + Send + Sync>)> {
        self.handlers.iter().map(|(id, (_, handler))| (id, handler))
    }

    /// 获取待处理的对等点
    /// 
    /// # 参数
    /// * `info_hash` - 种子的信息哈希
    /// 
    /// # 返回
    /// 与指定info_hash相关的待处理对等点列表
    /// 
    /// # 注意
    /// 此方法尝试根据info_hash过滤对等点。如果找不到与指定info_hash相关的对等点，
    /// 则返回所有待处理的对等点作为备选。
    pub fn get_pending_peers(&self, info_hash: &[u8]) -> Vec<PeerInfo> {
        let mut result = Vec::new();
        debug!("获取待处理对等点，info_hash: {}", hex::encode(info_hash));
        
        // 尝试获取PEX扩展处理器
        if let Some(pex_handler) = self.get_handler_as::<super::pex::PEXExtension>("ut_pex") {
            // 尝试获取与指定info_hash相关的待处理对等点
            if let Some(peers) = pex_handler.get_pending_peers_by_info_hash(info_hash) {
                debug!("找到{}个待处理对等点", peers.len());
                
                // 将SocketAddr转换为PeerInfo
                for addr in peers.iter() {
                    result.push(PeerInfo {
                        id: None,
                        addr: *addr,
                        client_name: None,
                        connected: false,
                        last_seen: std::time::Instant::now(),
                        downloaded: 0,
                        uploaded: 0,
                        download_speed: 0,
                        upload_speed: 0,
                    });
                }
            } else {
                debug!("未找到待处理对等点");
            }
        } else {
            debug!("未找到PEX扩展处理器");
        }
        
        result
    }
}
