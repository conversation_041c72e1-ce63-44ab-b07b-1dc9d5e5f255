use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use serde_json::{json, Value, Map};
use uuid::Uuid;
use log::{debug, trace, info, warn};

use crate::cli::backend::backend::CliBackend;
use crate::cli::commands::download::args::{ListArgs, TaskIdArgs};
use crate::cli::commands::download::formatters::{format_datetime, format_file_size, format_speed, format_status};
use crate::cli::utils::formatter::{format_output, OutputFormat};
use crate::cli::utils::debug::{print_debug_info, print_debug_object, print_debug_json, DebugLevel, DebugTimer};
use crate::download::manager::{TaskInfo, TaskStatus};

/// Handle list command
pub async fn handle_list(backend: &Arc<dyn CliBackend>, args: &ListArgs, format: OutputFormat) -> Result<()> {
    debug!("执行列出下载任务");
    print_debug_object("列表参数", args, DebugLevel::Verbose);
    debug!("输出格式: {:?}", format);
    
    let mut timer = DebugTimer::new("列出下载任务");
    
    let task_strings = backend.get_all_download_tasks().await?;
    timer.checkpoint("获取所有任务");
    
    debug!("获取到 {} 个原始任务数据", task_strings.len());
    
    // 将字符串反序列化为TaskInfo对象
    let tasks: Vec<TaskInfo> = task_strings
        .into_iter()
        .filter_map(|task_str| {
            match serde_json::from_str::<TaskInfo>(&task_str) {
                Ok(task) => Some(task),
                Err(e) => {
                    warn!("反序列化任务失败: {}", e);
                    eprintln!("Error deserializing task: {}", e);
                    None
                }
            }
        })
        .collect();
    
    timer.checkpoint("反序列化任务");
    debug!("成功反序列化 {} 个任务", tasks.len());
    
    // 过滤任务
    let filtered_tasks: Vec<_> = tasks
        .into_iter()
        .filter(|task| {
            if args.status == "all" {
                return true;
            }
            
            let status_str = match task.status {
                TaskStatus::Pending => "pending",
                TaskStatus::Initializing => "initializing",
                TaskStatus::Downloading => "downloading",
                TaskStatus::Paused => "paused",
                TaskStatus::Completed => "completed",
                TaskStatus::Failed => "failed",
                TaskStatus::Cancelled => "cancelled",
                TaskStatus::Error => "error",
            };
            
            status_str == args.status
        })
        .take(args.limit)
        .collect();
    
    timer.checkpoint("过滤任务");
    debug!("过滤后剩余 {} 个任务", filtered_tasks.len());
    
    // 转换为JSON格式
    let mut json_tasks = Vec::new();
    for task in filtered_tasks {
        // 格式化日期时间
        let created_at = format_datetime(task.created_at);
        let updated_at = format_datetime(task.updated_at);
        
        // 格式化文件大小
        let downloaded_size = format_file_size(task.downloaded_size);
        let total_size = task.total_size.map(format_file_size).unwrap_or_else(|| "未知".to_string());
        
        // 格式化下载速度
        let speed = format_speed(task.speed);
        
        // 格式化进度
        let progress = format!("{:.1}%", task.progress * 100.0);
        
        // 创建JSON对象
        let json_task = json!({
            "ID": task.id.to_string(),
            "URL": task.url,
            "状态": format_status(task.status),
            "进度": progress,
            "速度": speed,
            "已下载": downloaded_size,
            "总大小": total_size,
            "创建时间": created_at,
            "更新时间": updated_at,
        });
        
        trace!("处理任务 {}: 状态={}, 进度={}", task.id, format_status(task.status), progress);
        json_tasks.push(json_task);
    }
    
    timer.checkpoint("格式化任务");
    
    // 输出结果
    if json_tasks.is_empty() {
        info!("没有找到符合条件的任务");
        println!("No tasks found.");
    } else {
        let json_value = Value::Array(json_tasks);
        print_debug_json("任务列表JSON", &json_value, DebugLevel::Full);
        format_output(json_value, format);
    }
    
    let elapsed = timer.stop();
    debug!("列出任务总耗时: {:?}", elapsed);
    
    Ok(())
}

/// Handle info command
pub async fn handle_info(backend: &Arc<dyn CliBackend>, args: &TaskIdArgs, format: OutputFormat) -> Result<()> {
    debug!("执行查看任务详情: task_id={}", args.task_id);
    debug!("输出格式: {:?}", format);
    
    let mut timer = DebugTimer::new("查看任务详情");
    
    let task_id = Uuid::parse_str(&args.task_id)?;
    trace!("解析UUID: {} -> {}", args.task_id, task_id);
    
    let task_info = backend.get_download_task_info(task_id).await?;
    timer.checkpoint("获取任务信息");
    
    let task: TaskInfo = serde_json::from_str(&task_info)?;
    timer.checkpoint("反序列化任务");
    
    debug!("获取到任务详情: id={}, url={}, status={:?}", task.id, task.url, task.status);
    
    // 格式化日期时间
    let created_at = format_datetime(task.created_at);
    let updated_at = format_datetime(task.updated_at);
    
    // 格式化文件大小
    let downloaded_size = format_file_size(task.downloaded_size);
    let total_size = task.total_size.map(format_file_size).unwrap_or_else(|| "未知".to_string());
    
    // 格式化下载速度
    let speed = format_speed(task.speed);
    
    // 格式化进度
    let progress = format!("{:.1}%", task.progress * 100.0);
    
    // 创建详细信息JSON对象
    let mut details = HashMap::new();
    details.insert("ID".to_string(), json!(task.id.to_string()));
    details.insert("URL".to_string(), json!(task.url));
    details.insert("输出路径".to_string(), json!(task.output_path));
    details.insert("状态".to_string(), json!(format_status(task.status)));
    details.insert("进度".to_string(), json!(progress));
    details.insert("速度".to_string(), json!(speed));
    details.insert("已下载".to_string(), json!(downloaded_size));
    details.insert("总大小".to_string(), json!(total_size));
    details.insert("创建时间".to_string(), json!(created_at));
    details.insert("更新时间".to_string(), json!(updated_at));
    
    // 如果有错误信息，添加到详细信息中
    if let Some(error) = task.error_message {
        details.insert("错误信息".to_string(), json!(error));
    }
    
    timer.checkpoint("格式化任务详情");
    
    // 输出结果
    let mut json_map = Map::new();
    for (key, value) in details {
        json_map.insert(key, value);
    }
    let json_value = Value::Object(json_map);
    
    print_debug_json("任务详情JSON", &json_value, DebugLevel::Full);
    format_output(json_value, format);
    
    let elapsed = timer.stop();
    debug!("查看任务详情总耗时: {:?}", elapsed);
    
    Ok(())
}