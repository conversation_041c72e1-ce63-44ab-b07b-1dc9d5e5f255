use anyhow::Result;
use async_trait::async_trait;
use std::collections::{HashMap, BTreeMap};
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::{Duration, Instant, interval};
use tracing::warn;
use uuid::Uuid;
use chrono::Utc;

use crate::download::speed_limiter::{SpeedLimiter, SpeedLimiterImpl};
use super::traits::BandwidthScheduler;
use super::models::{TimeBasedSpeedRule, TaskPriority, BandwidthStats};

/// Bandwidth scheduler implementation
pub struct BandwidthSchedulerImpl {
    /// Global download speed limiter
    global_download_limiter: Arc<dyn SpeedLimiter>,
    /// Global upload speed limiter
    global_upload_limiter: Arc<dyn SpeedLimiter>,
    /// Task download speed limiters
    task_download_limiters: Arc<RwLock<HashMap<Uuid, Arc<dyn SpeedLimiter>>>>,
    /// Task upload speed limiters
    task_upload_limiters: Arc<RwLock<HashMap<Uuid, Arc<dyn SpeedLimiter>>>>,
    /// Time-based speed limit rules
    time_based_rules: Arc<RwLock<HashMap<Uuid, TimeBasedSpeedRule>>>,
    /// Last time-based update
    last_time_based_update: Arc<RwLock<Instant>>,
    /// Task priorities
    task_priorities: Arc<RwLock<HashMap<Uuid, TaskPriority>>>,
    /// Global bandwidth statistics
    global_stats: Arc<RwLock<BandwidthStats>>,
    /// Task bandwidth statistics
    task_stats: Arc<RwLock<HashMap<Uuid, BandwidthStats>>>,
    /// Last bandwidth rate calculation time
    last_rate_calc_time: Arc<RwLock<Instant>>,
    /// Recent download bytes for rate calculation
    recent_download_bytes: Arc<RwLock<HashMap<Uuid, Vec<(Instant, u64)>>>>,
    /// Recent upload bytes for rate calculation
    recent_upload_bytes: Arc<RwLock<HashMap<Uuid, Vec<(Instant, u64)>>>>,
}

impl BandwidthSchedulerImpl {
    /// Create a new bandwidth scheduler
    pub fn new() -> Self {
        let scheduler = Self {
            global_download_limiter: Arc::new(SpeedLimiterImpl::new(None)),
            global_upload_limiter: Arc::new(SpeedLimiterImpl::new(None)),
            task_download_limiters: Arc::new(RwLock::new(HashMap::new())),
            task_upload_limiters: Arc::new(RwLock::new(HashMap::new())),
            time_based_rules: Arc::new(RwLock::new(HashMap::new())),
            last_time_based_update: Arc::new(RwLock::new(Instant::now())),
            task_priorities: Arc::new(RwLock::new(HashMap::new())),
            global_stats: Arc::new(RwLock::new(BandwidthStats::default())),
            task_stats: Arc::new(RwLock::new(HashMap::new())),
            last_rate_calc_time: Arc::new(RwLock::new(Instant::now())),
            recent_download_bytes: Arc::new(RwLock::new(HashMap::new())),
            recent_upload_bytes: Arc::new(RwLock::new(HashMap::new())),
        };

        // Start background tasks
        scheduler.start_background_tasks();

        scheduler
    }

    /// Start background tasks for periodic updates
    fn start_background_tasks(&self) {
        // Clone Arc references for the background task
        let scheduler = Arc::new(self.clone());

        // Spawn a background task to periodically update time-based rules and clean up stats
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(60)); // Check every minute

            loop {
                interval.tick().await;

                // Update time-based limits
                if let Err(e) = scheduler.update_time_based_limits().await {
                    warn!("Failed to update time-based limits: {}", e);
                }

                // Calculate rates
                if let Err(e) = scheduler.calculate_rates().await {
                    warn!("Failed to calculate bandwidth rates: {}", e);
                }

                // Clean up old task stats for tasks that haven't been active for a while
                if let Err(e) = scheduler.cleanup_inactive_tasks().await {
                    warn!("Failed to clean up inactive tasks: {}", e);
                }
            }
        });
    }

    /// Clean up stats for inactive tasks
    async fn cleanup_inactive_tasks(&self) -> Result<()> {
        let now = Utc::now();
        let inactive_threshold = Duration::from_secs(3600 * 24); // 24 hours

        // Clean up task stats
        {
            let mut task_stats = self.task_stats.write().await;
            task_stats.retain(|_, stats| {
                // Keep if updated within the threshold
                (now - stats.last_update).to_std().unwrap_or(Duration::from_secs(0)) < inactive_threshold
            });
        }

        // Clean up recent bytes
        {
            let mut recent_downloads = self.recent_download_bytes.write().await;
            // Clean up empty entries
            let empty_keys: Vec<_> = recent_downloads.keys()
                .filter(|k| recent_downloads.get(k).map_or(true, |v| v.is_empty()))
                .cloned()
                .collect();
            for key in empty_keys {
                recent_downloads.remove(&key);
            }
        }

        {
            let mut recent_uploads = self.recent_upload_bytes.write().await;
            // Clean up empty entries
            let empty_keys: Vec<_> = recent_uploads.keys()
                .filter(|k| recent_uploads.get(k).map_or(true, |v| v.is_empty()))
                .cloned()
                .collect();
            for key in empty_keys {
                recent_uploads.remove(&key);
            }
        }

        Ok(())
    }

    /// Clone implementation
    fn clone(&self) -> Self {
        Self {
            global_download_limiter: self.global_download_limiter.clone(),
            global_upload_limiter: self.global_upload_limiter.clone(),
            task_download_limiters: self.task_download_limiters.clone(),
            task_upload_limiters: self.task_upload_limiters.clone(),
            time_based_rules: self.time_based_rules.clone(),
            last_time_based_update: self.last_time_based_update.clone(),
            task_priorities: self.task_priorities.clone(),
            global_stats: self.global_stats.clone(),
            task_stats: self.task_stats.clone(),
            last_rate_calc_time: self.last_rate_calc_time.clone(),
            recent_download_bytes: self.recent_download_bytes.clone(),
            recent_upload_bytes: self.recent_upload_bytes.clone(),
        }
    }

    /// Calculate bandwidth rate
    async fn calculate_rates(&self) -> Result<()> {
        let now = Instant::now();
        let mut last_calc = self.last_rate_calc_time.write().await;

        // Only recalculate rates every second
        if now.duration_since(*last_calc) < Duration::from_secs(1) {
            return Ok(());
        }

        *last_calc = now;

        // Calculate global rates
        let mut global_download_rate = 0;
        let mut global_upload_rate = 0;

        // Calculate download rates
        {
            let mut recent_downloads = self.recent_download_bytes.write().await;

            for (task_id, bytes_list) in recent_downloads.iter_mut() {
                // Remove entries older than 5 seconds
                bytes_list.retain(|(time, _)| now.duration_since(*time) < Duration::from_secs(5));

                // Calculate task download rate (bytes per second)
                let task_bytes: u64 = bytes_list.iter().map(|(_, bytes)| bytes).sum();
                let task_rate = if bytes_list.is_empty() {
                    0
                } else {
                    let oldest = bytes_list.iter().map(|(time, _)| *time).min().unwrap_or(now);
                    let duration = now.duration_since(oldest).as_secs_f64();
                    if duration > 0.0 {
                        (task_bytes as f64 / duration) as u64
                    } else {
                        0
                    }
                };

                // Update task stats
                let mut task_stats_map = self.task_stats.write().await;
                let stats = task_stats_map.entry(*task_id).or_insert_with(BandwidthStats::default);
                stats.download_rate = task_rate;
                drop(task_stats_map);

                global_download_rate += task_rate;
            }
        }

        // Calculate upload rates
        {
            let mut recent_uploads = self.recent_upload_bytes.write().await;

            for (task_id, bytes_list) in recent_uploads.iter_mut() {
                // Remove entries older than 5 seconds
                bytes_list.retain(|(time, _)| now.duration_since(*time) < Duration::from_secs(5));

                // Calculate task upload rate (bytes per second)
                let task_bytes: u64 = bytes_list.iter().map(|(_, bytes)| bytes).sum();
                let task_rate = if bytes_list.is_empty() {
                    0
                } else {
                    let oldest = bytes_list.iter().map(|(time, _)| *time).min().unwrap_or(now);
                    let duration = now.duration_since(oldest).as_secs_f64();
                    if duration > 0.0 {
                        (task_bytes as f64 / duration) as u64
                    } else {
                        0
                    }
                };

                // Update task stats
                let mut task_stats_map = self.task_stats.write().await;
                let stats = task_stats_map.entry(*task_id).or_insert_with(BandwidthStats::default);
                stats.upload_rate = task_rate;
                drop(task_stats_map);

                global_upload_rate += task_rate;
            }
        }

        // Update global stats
        let mut global_stats = self.global_stats.write().await;
        global_stats.download_rate = global_download_rate;
        global_stats.upload_rate = global_upload_rate;
        global_stats.last_update = Utc::now();

        Ok(())
    }

    /// Get or create task download speed limiter
    async fn get_or_create_task_download_limiter(&self, task_id: Uuid) -> Arc<dyn SpeedLimiter> {
        let mut limiters = self.task_download_limiters.write().await;

        if let Some(limiter) = limiters.get(&task_id) {
            limiter.clone()
        } else {
            let limiter = Arc::new(SpeedLimiterImpl::new(None));
            limiters.insert(task_id, limiter.clone());
            limiter
        }
    }

    /// Get or create task upload speed limiter
    async fn get_or_create_task_upload_limiter(&self, task_id: Uuid) -> Arc<dyn SpeedLimiter> {
        let mut limiters = self.task_upload_limiters.write().await;

        if let Some(limiter) = limiters.get(&task_id) {
            limiter.clone()
        } else {
            let limiter = Arc::new(SpeedLimiterImpl::new(None));
            limiters.insert(task_id, limiter.clone());
            limiter
        }
    }

    /// Adjust speed limits based on task priorities
    async fn adjust_limits_by_priority(&self) -> Result<()> {
        // Get global limits
        let global_download_limit = self.get_global_download_limit().await?;
        let global_upload_limit = self.get_global_upload_limit().await?;

        // If no global limits, nothing to adjust
        if global_download_limit.is_none() && global_upload_limit.is_none() {
            return Ok(());
        }

        // Get all tasks with their priorities
        let priorities = self.task_priorities.read().await;

        // Group tasks by priority
        let mut tasks_by_priority: BTreeMap<TaskPriority, Vec<Uuid>> = BTreeMap::new();

        for (task_id, priority) in priorities.iter() {
            tasks_by_priority.entry(*priority).or_default().push(*task_id);
        }

        // Adjust download limits
        if let Some(global_limit) = global_download_limit {
            // Higher priority tasks get more bandwidth
            let total_tasks = priorities.len() as u64;
            if total_tasks > 0 {
                let base_limit = global_limit / total_tasks;

                for (priority, tasks) in tasks_by_priority.iter().rev() {  // Reverse to start with highest priority
                    let priority_factor = match priority {
                        TaskPriority::Low => 0.5,
                        TaskPriority::Normal => 1.0,
                        TaskPriority::High => 2.0,
                        TaskPriority::Critical => 4.0,
                    };

                    let adjusted_limit = (base_limit as f64 * priority_factor) as u64;

                    for task_id in tasks {
                        self.set_task_download_limit(*task_id, Some(adjusted_limit)).await?;
                    }
                }
            }
        }

        // Adjust upload limits
        if let Some(global_limit) = global_upload_limit {
            // Higher priority tasks get more bandwidth
            let total_tasks = priorities.len() as u64;
            if total_tasks > 0 {
                let base_limit = global_limit / total_tasks;

                for (priority, tasks) in tasks_by_priority.iter().rev() {  // Reverse to start with highest priority
                    let priority_factor = match priority {
                        TaskPriority::Low => 0.5,
                        TaskPriority::Normal => 1.0,
                        TaskPriority::High => 2.0,
                        TaskPriority::Critical => 4.0,
                    };

                    let adjusted_limit = (base_limit as f64 * priority_factor) as u64;

                    for task_id in tasks {
                        self.set_task_upload_limit(*task_id, Some(adjusted_limit)).await?;
                    }
                }
            }
        }

        Ok(())
    }
}

#[async_trait]
impl BandwidthScheduler for BandwidthSchedulerImpl {
    async fn set_global_download_limit(&self, bytes_per_second: Option<u64>) -> Result<()> {
        self.global_download_limiter.set_limit(bytes_per_second).await
    }

    async fn get_global_download_limit(&self) -> Result<Option<u64>> {
        self.global_download_limiter.get_limit().await
    }

    async fn set_task_download_limit(&self, task_id: Uuid, bytes_per_second: Option<u64>) -> Result<()> {
        let limiter = self.get_or_create_task_download_limiter(task_id).await;
        limiter.set_limit(bytes_per_second).await
    }

    async fn get_task_download_limit(&self, task_id: Uuid) -> Result<Option<u64>> {
        let limiters = self.task_download_limiters.read().await;

        if let Some(limiter) = limiters.get(&task_id) {
            limiter.get_limit().await
        } else {
            Ok(None)
        }
    }

    async fn wait_for_download_quota(&self, task_id: Uuid, bytes: usize) -> Result<()> {
        // Skip if bytes is 0
        if bytes == 0 {
            return Ok(());
        }

        // Get task priority
        let priority = self.get_task_priority(task_id).await?;

        // Adjust wait time based on priority
        let priority_factor = match priority {
            TaskPriority::Low => 1.5,       // Low priority tasks wait longer
            TaskPriority::Normal => 1.0,    // Normal priority tasks wait the standard time
            TaskPriority::High => 0.7,      // High priority tasks wait less
            TaskPriority::Critical => 0.3,  // Critical priority tasks wait much less
        };

        // First, wait for global quota with priority adjustment
        if priority_factor == 1.0 {
            // Standard wait for normal priority
            self.global_download_limiter.wait_for_quota(bytes).await?;
        } else {
            // Adjusted wait for other priorities
            let adjusted_bytes = (bytes as f64 * priority_factor) as usize;
            self.global_download_limiter.wait_for_quota(adjusted_bytes).await?;
        }

        // Then, wait for task quota
        let limiters = self.task_download_limiters.read().await;

        if let Some(limiter) = limiters.get(&task_id) {
            limiter.wait_for_quota(bytes).await?;
        }

        // Update download statistics
        self.update_download_stats(task_id, bytes as u64).await?;

        Ok(())
    }

    async fn set_global_upload_limit(&self, bytes_per_second: Option<u64>) -> Result<()> {
        self.global_upload_limiter.set_limit(bytes_per_second).await
    }

    async fn get_global_upload_limit(&self) -> Result<Option<u64>> {
        self.global_upload_limiter.get_limit().await
    }

    async fn set_task_upload_limit(&self, task_id: Uuid, bytes_per_second: Option<u64>) -> Result<()> {
        let limiter = self.get_or_create_task_upload_limiter(task_id).await;
        limiter.set_limit(bytes_per_second).await
    }

    async fn get_task_upload_limit(&self, task_id: Uuid) -> Result<Option<u64>> {
        let limiters = self.task_upload_limiters.read().await;

        if let Some(limiter) = limiters.get(&task_id) {
            limiter.get_limit().await
        } else {
            Ok(None)
        }
    }

    async fn wait_for_upload_quota(&self, task_id: Uuid, bytes: usize) -> Result<()> {
        // Skip if bytes is 0
        if bytes == 0 {
            return Ok(());
        }

        // Get task priority
        let priority = self.get_task_priority(task_id).await?;

        // Adjust wait time based on priority
        let priority_factor = match priority {
            TaskPriority::Low => 1.5,       // Low priority tasks wait longer
            TaskPriority::Normal => 1.0,    // Normal priority tasks wait the standard time
            TaskPriority::High => 0.7,      // High priority tasks wait less
            TaskPriority::Critical => 0.3,  // Critical priority tasks wait much less
        };

        // First, wait for global quota with priority adjustment
        if priority_factor == 1.0 {
            // Standard wait for normal priority
            self.global_upload_limiter.wait_for_quota(bytes).await?;
        } else {
            // Adjusted wait for other priorities
            let adjusted_bytes = (bytes as f64 * priority_factor) as usize;
            self.global_upload_limiter.wait_for_quota(adjusted_bytes).await?;
        }

        // Then, wait for task quota
        let limiters = self.task_upload_limiters.read().await;

        if let Some(limiter) = limiters.get(&task_id) {
            limiter.wait_for_quota(bytes).await?;
        }

        Ok(())
    }

    async fn add_time_based_rule(&self, rule: TimeBasedSpeedRule) -> Result<Uuid> {
        let rule_id = rule.id;
        self.time_based_rules.write().await.insert(rule_id, rule);

        // Update limits immediately
        self.update_time_based_limits().await?;

        Ok(rule_id)
    }

    async fn remove_time_based_rule(&self, rule_id: Uuid) -> Result<()> {
        self.time_based_rules.write().await.remove(&rule_id);

        // Update limits immediately
        self.update_time_based_limits().await?;

        Ok(())
    }

    async fn get_time_based_rules(&self) -> Result<Vec<TimeBasedSpeedRule>> {
        let rules = self.time_based_rules.read().await;
        Ok(rules.values().cloned().collect())
    }

    async fn update_time_based_limits(&self) -> Result<()> {
        let now = Instant::now();

        // Check if we need to update
        {
            let last_update = self.last_time_based_update.read().await;
            if now.duration_since(*last_update) < Duration::from_secs(60) {
                return Ok(());
            }
        }

        // Update last update time
        {
            let mut last_update = self.last_time_based_update.write().await;
            *last_update = now;
        }

        // Get current time
        let current_time = Utc::now();

        // Find active rules
        let rules = self.time_based_rules.read().await;
        let active_rules: Vec<&TimeBasedSpeedRule> = rules.values()
            .filter(|rule| rule.is_active_at(&current_time))
            .collect();

        if active_rules.is_empty() {
            // No active rules, reset to default limits
            return Ok(());
        }

        // Find the most restrictive limits
        let mut min_download_limit = None;
        let mut min_upload_limit = None;

        for rule in active_rules {
            // Update download limit
            if let Some(download_limit) = rule.download_limit {
                min_download_limit = match min_download_limit {
                    Some(current_min) => Some(std::cmp::min(current_min, download_limit)),
                    None => Some(download_limit),
                };
            }

            // Update upload limit
            if let Some(upload_limit) = rule.upload_limit {
                min_upload_limit = match min_upload_limit {
                    Some(current_min) => Some(std::cmp::min(current_min, upload_limit)),
                    None => Some(upload_limit),
                };
            }
        }

        // Apply the limits
        if let Some(download_limit) = min_download_limit {
            self.set_global_download_limit(Some(download_limit)).await?;
        }

        if let Some(upload_limit) = min_upload_limit {
            self.set_global_upload_limit(Some(upload_limit)).await?;
        }

        Ok(())
    }

    async fn update_download_stats(&self, task_id: Uuid, bytes: u64) -> Result<()> {
        if bytes == 0 {
            return Ok(());
        }

        // Update global stats
        let mut global_stats = self.global_stats.write().await;
        global_stats.total_downloaded += bytes;
        global_stats.last_update = Utc::now();
        drop(global_stats);

        // Update task stats
        let mut task_stats_map = self.task_stats.write().await;
        let stats = task_stats_map.entry(task_id).or_insert_with(BandwidthStats::default);
        stats.total_downloaded += bytes;
        stats.last_update = Utc::now();
        drop(task_stats_map);

        // Add to recent downloads for rate calculation
        let mut recent_downloads = self.recent_download_bytes.write().await;
        let task_downloads = recent_downloads.entry(task_id).or_insert_with(Vec::new);
        task_downloads.push((Instant::now(), bytes));

        // Recalculate rates
        self.calculate_rates().await?;

        Ok(())
    }

    async fn update_upload_stats(&self, task_id: Uuid, bytes: u64) -> Result<()> {
        if bytes == 0 {
            return Ok(());
        }

        // Apply upload speed limit
        self.wait_for_upload_quota(task_id, bytes as usize).await?;

        // Update global stats
        let mut global_stats = self.global_stats.write().await;
        global_stats.total_uploaded += bytes;
        global_stats.last_update = Utc::now();
        drop(global_stats);

        // Update task stats
        let mut task_stats_map = self.task_stats.write().await;
        let stats = task_stats_map.entry(task_id).or_insert_with(BandwidthStats::default);
        stats.total_uploaded += bytes;
        stats.last_update = Utc::now();
        drop(task_stats_map);

        // Add to recent uploads for rate calculation
        let mut recent_uploads = self.recent_upload_bytes.write().await;
        let task_uploads = recent_uploads.entry(task_id).or_insert_with(Vec::new);
        task_uploads.push((Instant::now(), bytes));

        // Recalculate rates
        self.calculate_rates().await?;

        Ok(())
    }

    async fn get_task_stats(&self, task_id: Uuid) -> Result<BandwidthStats> {
        // Ensure rates are up to date
        self.calculate_rates().await?;

        // Get task stats
        let task_stats_map = self.task_stats.read().await;

        if let Some(stats) = task_stats_map.get(&task_id) {
            Ok(stats.clone())
        } else {
            Ok(BandwidthStats::default())
        }
    }

    async fn get_global_stats(&self) -> Result<BandwidthStats> {
        // Ensure rates are up to date
        self.calculate_rates().await?;

        // Get global stats
        let global_stats = self.global_stats.read().await;
        Ok(global_stats.clone())
    }

    async fn set_task_priority(&self, task_id: Uuid, priority: TaskPriority) -> Result<()> {
        let mut priorities = self.task_priorities.write().await;
        priorities.insert(task_id, priority);

        // Adjust speed limits based on priority
        self.adjust_limits_by_priority().await?;

        Ok(())
    }

    async fn get_task_priority(&self, task_id: Uuid) -> Result<TaskPriority> {
        let priorities = self.task_priorities.read().await;
        Ok(priorities.get(&task_id).copied().unwrap_or_default())
    }
}
