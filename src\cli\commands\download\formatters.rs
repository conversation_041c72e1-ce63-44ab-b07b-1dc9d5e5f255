use chrono::{DateTime, Utc};
use crate::download::manager::TaskStatus;

/// Format datetime
pub fn format_datetime(datetime: DateTime<Utc>) -> String {
    datetime.format("%Y-%m-%d %H:%M:%S").to_string()
}

/// Format file size
pub fn format_file_size(size: u64) -> String {
    const KB: u64 = 1024;
    const MB: u64 = KB * 1024;
    const GB: u64 = MB * 1024;
    const TB: u64 = GB * 1024;
    
    if size < KB {
        format!("{} B", size)
    } else if size < MB {
        format!("{:.2} KB", size as f64 / KB as f64)
    } else if size < GB {
        format!("{:.2} MB", size as f64 / MB as f64)
    } else if size < TB {
        format!("{:.2} GB", size as f64 / GB as f64)
    } else {
        format!("{:.2} TB", size as f64 / TB as f64)
    }
}

/// Format speed
pub fn format_speed(speed: u64) -> String {
    if speed == 0 {
        return "0 B/s".to_string();
    }
    
    format!("{}/s", format_file_size(speed))
}

/// Format status
pub fn format_status(status: TaskStatus) -> String {
    match status {
        TaskStatus::Pending => "等待中".to_string(),
        TaskStatus::Initializing => "初始化中".to_string(),
        TaskStatus::Downloading => "下载中".to_string(),
        TaskStatus::Paused => "已暂停".to_string(),
        TaskStatus::Completed => "已完成".to_string(),
        TaskStatus::Failed => "失败".to_string(),
        TaskStatus::Cancelled => "已取消".to_string(),
        TaskStatus::Error => "错误".to_string(),
    }
}