use std::collections::HashMap;
use tracing::{debug, info};
use winreg::enums::*;
use winreg::RegKey;

use crate::config::settings::ProxyConfig;
use super::super::detector::{ProxyDetector, parse_auth_from_url, parse_no_proxy};
use super::super::error::ProxyError;

/// Windows 代理检测器
/// 
/// 负责从 Windows 注册表中检测代理设置
pub struct WindowsProxyDetector;

impl WindowsProxyDetector {
    /// 创建新的 Windows 代理检测器实例
    pub fn new() -> Self {
        WindowsProxyDetector {}
    }
}

impl ProxyDetector for WindowsProxyDetector {
    fn detect(&self) -> Result<Option<ProxyConfig>, ProxyError> {
        debug!("检查 Windows 注册表中的代理设置");

        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let internet_settings = hkcu.open_subkey("Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings")
            .map_err(|e| ProxyError::RegistryError(format!("无法打开注册表项: {}", e)))?;

        // 检查代理是否启用
        let proxy_enabled: u32 = internet_settings.get_value("ProxyEnable").unwrap_or(0);

        if proxy_enabled == 0 {
            debug!("Windows 代理已禁用");
            return Ok(None);
        }

        // 获取代理服务器地址
        let proxy_server: String = match internet_settings.get_value("ProxyServer") {
            Ok(value) => value,
            Err(e) => {
                return Err(ProxyError::RegistryError(format!("无法获取 ProxyServer 值: {}", e)));
            }
        };

        if proxy_server.is_empty() {
            debug!("Windows 代理服务器地址为空");
            return Ok(None);
        }

        // 解析代理服务器地址
        let final_proxy_url;
        let final_proxy_type;

        // 检查是否有协议前缀
        if proxy_server.contains('=') {
            // 格式可能是 "http=proxy.example.com:8080;https=proxy.example.com:8443"
            let parts: HashMap<String, String> = proxy_server
                .split(';')
                .filter_map(|s| {
                    let mut iter = s.splitn(2, '=');
                    let key = iter.next()?.to_string();
                    let value = iter.next()?.to_string();
                    Some((key, value))
                })
                .collect();

            if let Some(general_proxy) = parts.get("http") {
                final_proxy_url = format!("http://{}", general_proxy);
                final_proxy_type = "http".to_string();
            } else if let Some(general_proxy) = parts.get("https") {
                final_proxy_url = format!("https://{}", general_proxy);
                final_proxy_type = "https".to_string();
            } else if let Some(general_proxy) = parts.get("socks") {
                final_proxy_url = format!("socks5://{}", general_proxy);
                final_proxy_type = "socks5".to_string();
            } else if let Some(general_proxy) = parts.get("ftp") {
                final_proxy_url = format!("ftp://{}", general_proxy);
                final_proxy_type = "ftp".to_string();
            } else {
                debug!("Windows 设置中未找到可识别的代理协议（多协议格式）");
                return Ok(None);
            }
        } else {
            // 格式可能是 "proxy.example.com:8080" 或 "http://proxy.example.com:8080"
            if proxy_server.starts_with("http://") || proxy_server.starts_with("https://") {
                final_proxy_url = proxy_server.clone();
                if proxy_server.starts_with("https://") {
                    final_proxy_type = "https".to_string();
                } else if proxy_server.starts_with("socks://") {
                    final_proxy_type = "socks5".to_string();
                } else {
                    final_proxy_type = "http".to_string();
                }
            } else {
                // 默认认为是 HTTP 代理
                final_proxy_url = format!("http://{}", proxy_server);
                final_proxy_type = "http".to_string();
            }
        }

        // 获取代理例外列表
        let no_proxy_str: Option<String> = internet_settings.get_value("ProxyOverride").ok();
        let no_proxy = no_proxy_str.as_deref().map_or_else(Vec::new, parse_no_proxy);

        // 解析用户名和密码
        match parse_auth_from_url(&final_proxy_url) {
            Ok((url, username, password)) => {
                info!("从 Windows 注册表检测到代理: {}", url);
                Ok(Some(ProxyConfig {
                    enabled: true,
                    url,
                    username,
                    password,
                    no_proxy,
                    proxy_type: final_proxy_type,
                }))
            },
            Err(e) => Err(ProxyError::RegistryError(format!("解析代理URL时出错: {}", e)))
        }
    }
}

#[cfg(test)]
mod tests {
    // Windows 注册表测试需要模拟注册表或在实际 Windows 环境中运行
    // 这里只提供基本的测试框架
    #[test]
    #[cfg(target_os = "windows")]
    fn test_windows_registry_detection() {
        // 这个测试假设一个干净的状态或模拟注册表
        // 对于实际测试，您可能需要设置模拟注册表或在受控环境中运行
        // 这里，我们只确保如果代理被禁用或未找到，它返回 None
        // 注意：这个测试可能会根据实际系统设置而变化
    }
}