use anyhow::Result;

use super::core::BitTorrentProtocol;

impl BitTorrentProtocol {
    /// Update download statistics
    pub(crate) async fn update_stats(&mut self) -> Result<()> {
        // 使用统计信息管理器更新统计信息
        if let Some(peer_manager) = &self.peer_manager {
            self.stats_manager.update_stats(
                self.piece_manager.as_ref(),
                self.torrent_info.as_ref(),
                peer_manager,
                self.downloaded,
                self.uploaded
            ).await?;
        }

        Ok(())
    }

    /// 获取下载进度百分比
    pub async fn progress_percentage(&self) -> Result<f64> {
        Ok(self.stats_manager.progress())
    }

    /// Get download speed
    pub async fn speed(&self) -> Result<u64> {
        Ok(self.stats_manager.download_speed())
    }


}
