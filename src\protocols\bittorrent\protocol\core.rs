// 标准库
use std::clone::Clone;
use std::sync::Arc;

// 三方库
use anyhow::Result;
use hex;
use tokio::sync::Mutex;
use uuid::Uuid;

// 本地配置
use crate::config::{ConfigManager, Settings};
use crate::config::constants::{DEFAULT_BLOCK_SIZE, DEFAULT_BLOCK_TIMEOUT, DEFAULT_STATS_UPDATE_INTERVAL};
use crate::config::DEFAULT_TRACKER_INTERVAL;

// 核心接口
use crate::core::interfaces::{DownloadProgress, DownloadStatus, ProtocolType};
use crate::core::p2p::piece::PieceManager;

// BitTorrent相关模块
use crate::download::bandwidth_scheduler::BandwidthScheduler;
use crate::protocols::bittorrent::block_manager::BlockManager;
use crate::protocols::bittorrent::dht_manager::DHTManager;
use crate::protocols::bittorrent::peer_manager::PeerConnectionManager;
use crate::protocols::bittorrent::stats_manager::StatsManager;
use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::protocols::bittorrent::tracker_manager::TrackerManager;
use crate::protocols::bittorrent::webseed::WebSeedManager;

/// BitTorrent protocol implementation
pub struct BitTorrentProtocol {
    /// Task ID
    pub task_id: Uuid,
    /// Download URL (torrent file or magnet link)
    pub url: String,
    /// Output path
    pub output_path: String,
    /// Settings
    pub settings: Settings,
    /// Configuration manager
    pub config_manager: Option<Arc<ConfigManager>>,
    /// Torrent information
    pub torrent_info: Option<TorrentInfo>,
    /// Tracker管理器
    pub tracker_manager: Option<Arc<TrackerManager>>,
    /// DHT管理器
    pub dht_manager: Option<DHTManager>,
    /// 对等点管理器
    pub peer_manager: Option<crate::protocols::bittorrent::peer_manager::PeerManager>,
    /// 对等点连接管理器
    pub peer_connection_manager: Option<Arc<PeerConnectionManager>>,
    /// Piece manager
    pub piece_manager: Option<Arc<Mutex<dyn PieceManager>>>,
    /// 块管理器
    pub block_manager: BlockManager,
    /// 统计信息管理器
    pub stats_manager: StatsManager,
    /// WebSeed管理器
    pub webseed_manager: Option<WebSeedManager>,
    /// 是否启用WebSeed
    pub webseed_enabled: bool,
    /// 是否启用上传
    pub upload_enabled: bool,
    /// 带宽调度器
    pub bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
    /// 恢复管理器
    pub resume_manager: Option<Arc<dyn crate::download::resume::ResumeManager>>,
    /// Download status
    pub status: DownloadStatus,
    /// Peer ID
    pub peer_id: String,
    /// Is initialized
    pub initialized: bool,
    /// 已下载的数据大小
    pub downloaded: u64,
    /// 已上传的数据大小
    pub uploaded: u64,
    /// 是否需要元数据交换
    pub needs_metadata: bool,
}

impl BitTorrentProtocol {
    /// 启用或禁用上传功能
    pub async fn enable_upload(&mut self, enabled: bool) -> Result<()> {
        self.upload_enabled = enabled;
        Ok(())
    }

    /// 设置种子信息
    pub fn set_torrent_info(&mut self, torrent_info: TorrentInfo) {
        self.torrent_info = Some(torrent_info);
    }

    /// 获取已上传的字节数
    pub fn uploaded_size(&self) -> u64 {
        self.uploaded
    }

    /// Create a new BitTorrent protocol instance
    pub fn new(task_id: Uuid, url: String, output_path: String, settings: Settings, config_manager: Arc<ConfigManager>) -> Self {
        // Generate a peer ID
        let peer_id = format!("-RS0001-{}", hex::encode(&task_id.as_bytes()[0..6]));

        // 检查是否启用DHT
        let _use_dht = settings.enable_dht.unwrap_or(true);

        // 检查是否启用上传
        let upload_enabled = settings.upload_enabled.unwrap_or(true);

        // 获取分片选择策略
        let _piece_selection_strategy = settings.piece_selection_strategy.clone().unwrap_or_else(|| "rarest_first".to_string());

        // 从配置中获取块大小和超时时间
        let block_size = settings.block_size.unwrap_or(DEFAULT_BLOCK_SIZE) as u32; // 默认16KB
        let block_timeout = settings.block_timeout.unwrap_or(DEFAULT_BLOCK_TIMEOUT); // 默认30秒

        // 从配置中获取统计信息更新间隔
        let stats_update_interval = settings.stats_update_interval.unwrap_or(DEFAULT_STATS_UPDATE_INTERVAL); // 默认1秒

        // 先构造 peer_connection_manager
        let peer_connection_manager = Arc::new(crate::protocols::bittorrent::peer_manager::PeerConnectionManager::new(
            peer_id.clone(),
            settings.bittorrent.as_ref().unwrap().peer_connect_timeout,
            settings.bittorrent.as_ref().unwrap().max_connections,
            settings.bittorrent.as_ref().unwrap().peer_handshake_timeout_secs,
            0, // num_pieces, will be set later
            None, // dht_manager, will be set later
        ));
        let tracker_interval = settings.bittorrent.as_ref().map(|b| b.tracker_interval).unwrap_or(DEFAULT_TRACKER_INTERVAL);

        Self {
            task_id,
            url,
            output_path: output_path.clone(),
            settings: settings.clone(),
            config_manager: Some(config_manager),
            torrent_info: None,
            tracker_manager: Some(Arc::new(crate::protocols::bittorrent::tracker_manager::TrackerManager::new(
                self.peer_id.clone(),
                settings.bittorrent.as_ref().map(|b| b.tracker_interval).unwrap_or(DEFAULT_TRACKER_INTERVAL),
                Some(peer_connection_manager.clone()),
            ))),
            dht_manager: None,
            peer_manager: None,
            peer_connection_manager: Some(peer_connection_manager),
            piece_manager: None,
            block_manager: BlockManager::new(block_size, block_timeout), // 使用配置的值
            stats_manager: StatsManager::new(stats_update_interval), // 使用配置的值
            webseed_manager: None,
            webseed_enabled: settings.webseed_enabled.unwrap_or(true),
            upload_enabled,
            bandwidth_scheduler: None,
            resume_manager: None,
            status: DownloadStatus::Pending,
            peer_id,
            initialized: false,
            downloaded: 0,
            uploaded: 0,
            needs_metadata: false,
        }
    }

    /// Create a new BitTorrent protocol instance with a configuration manager
    pub fn with_config_manager(task_id: Uuid, url: String, output_path: String, settings: Settings, config_manager: Arc<ConfigManager>) -> Self {
        // Generate a peer ID
        let peer_id = format!("-RS0001-{}", hex::encode(&task_id.as_bytes()[0..6]));

        // 检查是否启用DHT
        let _use_dht = settings.enable_dht.unwrap_or(true);

        // 检查是否启用上传
        let upload_enabled = settings.upload_enabled.unwrap_or(true);

        // 获取分片选择策略
        let _piece_selection_strategy = settings.piece_selection_strategy.clone().unwrap_or_else(|| "rarest_first".to_string());

        // 从配置中获取块大小和超时时间
        let block_size = settings.block_size.unwrap_or(DEFAULT_BLOCK_SIZE) as u32; // 默认16KB
        let block_timeout = settings.block_timeout.unwrap_or(DEFAULT_BLOCK_TIMEOUT); // 默认30秒

        // 从配置中获取统计信息更新间隔
        let stats_update_interval = settings.stats_update_interval.unwrap_or(DEFAULT_STATS_UPDATE_INTERVAL); // 默认1秒

        // 先构造 peer_connection_manager
        let peer_connection_manager = Arc::new(PeerConnectionManager::new(
            peer_id.clone(),
            settings.bittorrent.as_ref().unwrap().peer_connect_timeout,
            settings.bittorrent.as_ref().unwrap().max_connections,
            settings.bittorrent.as_ref().unwrap().peer_handshake_timeout_secs,
            0, // num_pieces, will be set later
            None, // dht_manager, will be set later
        ));

        Self {
            task_id,
            url,
            output_path: output_path.clone(),
            settings: settings.clone(),
            config_manager: Some(config_manager.clone()),
            torrent_info: None,
            tracker_manager: Some(Arc::new(crate::protocols::bittorrent::tracker_manager::TrackerManager::with_config_manager(
                peer_id.clone(),
                settings.bittorrent.as_ref().map(|b| b.tracker_interval).unwrap_or(DEFAULT_TRACKER_INTERVAL),
                config_manager,
                Some(peer_connection_manager.clone()),
            ))),
            dht_manager: None,
            peer_manager: None,
            peer_connection_manager: Some(peer_connection_manager),
            piece_manager: None,
            block_manager: BlockManager::new(block_size, block_timeout), // 使用配置的值
            stats_manager: StatsManager::new(stats_update_interval), // 使用配置的值
            webseed_manager: None,
            webseed_enabled: settings.webseed_enabled.unwrap_or(true),
            upload_enabled,
            bandwidth_scheduler: None,
            resume_manager: None,
            status: DownloadStatus::Pending,
            peer_id,
            initialized: false,
            downloaded: 0,
            uploaded: 0,
            needs_metadata: false,
        }
    }

    /// Get protocol type
    pub fn protocol_type(&self) -> ProtocolType {
        ProtocolType::BitTorrent
    }

    /// Get download status
    pub async fn get_status(&self) -> DownloadStatus {
        self.status
    }

    /// 获取完整的下载进度信息
    pub async fn get_download_progress(&self) -> Result<DownloadProgress> {
        let progress = self.stats_manager.progress();
        let total_size = if let Some(torrent_info) = &self.torrent_info {
            Some(torrent_info.total_size)
        } else {
            None
        };
        let speed = self.stats_manager.download_speed();
        
        Ok(DownloadProgress {
            progress_percentage: progress,
            downloaded_size: self.downloaded,
            total_size,
            speed,
            eta: None, // 预计完成时间需要在调用处计算
        })
    }

    /// 获取下载文件的总大小（字节）
    pub async fn get_total_size(&self) -> Result<Option<u64>> {
        if let Some(torrent_info) = &self.torrent_info {
            Ok(Some(torrent_info.total_size))
        } else {
            Ok(None)
        }
    }

    /// 获取已下载的数据大小（字节）
    pub async fn get_downloaded_size(&self) -> Result<u64> {
        Ok(self.downloaded)
    }

    /// 获取下载统计信息
    pub async fn stats(&self) -> crate::core::p2p::protocol::DownloadStats {
        let progress = self.stats_manager.progress();
        let download_speed = self.stats_manager.download_speed();
        let upload_speed = self.stats_manager.upload_speed();
        
        let pieces_downloaded = if let Some(piece_manager) = &self.piece_manager {
            let piece_manager = piece_manager.lock().await;
            let states = piece_manager.all_piece_states().await.unwrap_or_default();
            states.iter().filter(|&state| {
                *state == crate::core::p2p::piece::PieceState::Downloaded || 
                *state == crate::core::p2p::piece::PieceState::Verified
            }).count() as u32
        } else {
            0
        };
        
        let pieces_total = if let Some(torrent_info) = &self.torrent_info {
            torrent_info.pieces.len() as u32
        } else {
            0
        };
        
        let connected_peers = if let Some(peer_manager) = &self.peer_manager {
            peer_manager.active_peers().await as usize
        } else {
            0
        };
        
        let total_peers = if let Some(peer_manager) = &self.peer_manager {
            (peer_manager.connected_peer_count() + peer_manager.peer_queue_length()) as usize
        } else {
            0
        };
        
        let estimated_time = if download_speed > 0 && self.downloaded < self.torrent_info.as_ref().map_or(0, |info| info.total_size) {
            let remaining_bytes = self.torrent_info.as_ref().map_or(0, |info| info.total_size) - self.downloaded;
            let seconds = remaining_bytes as f64 / download_speed as f64;
            Some(std::time::Duration::from_secs_f64(seconds))
        } else {
            None
        };
        
        crate::core::p2p::protocol::DownloadStats {
            downloaded: self.downloaded,
            uploaded: self.uploaded,
            download_speed,
            upload_speed,
            connected_peers,
            total_peers,
            pieces_downloaded,
            pieces_total,
            progress,
            estimated_time,
        }
    }
}

// 实现Clone特性
impl Clone for BitTorrentProtocol {
    fn clone(&self) -> Self {
        Self {
            task_id: self.task_id,
            url: self.url.clone(),
            output_path: self.output_path.clone(),
            settings: self.settings.clone(),
            config_manager: self.config_manager.clone(),
            torrent_info: self.torrent_info.clone(),
            tracker_manager: Some(Arc::new(crate::protocols::bittorrent::tracker_manager::TrackerManager::new(
                self.peer_id.clone(),
                settings.bittorrent.as_ref().map(|b| b.tracker_interval).unwrap_or(DEFAULT_TRACKER_INTERVAL),
                self.peer_connection_manager.clone(),
            ))), // 不克隆管理器
            dht_manager: None, // 不克隆管理器
            peer_manager: None,
            peer_connection_manager: None, // 不克隆管理器
            piece_manager: self.piece_manager.clone(),
            block_manager: self.block_manager.clone(),
            stats_manager: self.stats_manager.clone(),
            webseed_manager: None, // 不克隆管理器
            webseed_enabled: self.webseed_enabled,
            upload_enabled: self.upload_enabled,
            bandwidth_scheduler: self.bandwidth_scheduler.clone(),
            resume_manager: self.resume_manager.clone(),
            status: self.status,
            peer_id: self.peer_id.clone(),
            initialized: self.initialized,
            downloaded: self.downloaded,
            uploaded: self.uploaded,
            needs_metadata: self.needs_metadata,
        }
    }
}
