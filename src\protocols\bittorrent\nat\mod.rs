// NAT穿透模块
// 导出子模块
pub mod stun;
pub mod mapping;
pub mod hole_puncher;
pub mod nat_detector;
pub mod coordinator;

// 导出公共接口
pub use stun::client::StunClient;
pub use stun::message::{StunMessage, StunMessageType, StunAttribute};
pub use stun::server_pool::StunServerPool;
pub use mapping::{Protocol, MappingInfo, PortMapper};
pub use mapping::upnp::UPnPMapper;
pub use mapping::natpmp::NATPMPMapper;
pub use mapping::pcp::PCPMapper;
pub use mapping::lifetime_manager::MappingLifetimeManager;
pub use hole_puncher::HolePuncher;
pub use nat_detector::{NATType, NATTypeDetector};
pub use coordinator::{NATTraversalCoordinator, TraversalStrategy, TraversalConfig};
