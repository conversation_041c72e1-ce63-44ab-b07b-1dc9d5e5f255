use clap::Args;

/// Add download task arguments
#[derive(Debug, Args)]
pub struct AddArgs {
    /// URL to download
    pub url: String,
    /// Output path
    #[arg(short, long)]
    pub output: Option<String>,
}

/// Task ID arguments
#[derive(Debug, Args)]
pub struct TaskIdArgs {
    /// Task ID
    pub task_id: String,
}

/// List arguments
#[derive(Debug, Args)]
pub struct ListArgs {
    /// Filter by status (all, pending, downloading, paused, completed, failed, cancelled)
    #[arg(short, long, default_value = "all")]
    pub status: String,
    /// Limit the number of tasks to show
    #[arg(short, long, default_value = "10")]
    pub limit: usize,
}

/// Batch tasks arguments
#[derive(Debug, Args)]
pub struct BatchTasksArgs {
    /// Task IDs (comma-separated)
    #[arg(short, long)]
    pub task_ids: Option<String>,
    
    /// Filter by status (pending, downloading, paused, completed, failed, cancelled)
    #[arg(short, long)]
    pub status: Option<String>,
    
    /// Apply to all tasks
    #[arg(short, long)]
    pub all: bool,
}