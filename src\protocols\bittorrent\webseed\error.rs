use std::fmt;
use std::error::Error;

/// WebSeed错误类型
#[derive(Debug)]
pub enum WebSeedError {
    /// 无可用WebSeed源
    NoAvailableSources,
    /// HTTP请求错误
    HttpError(String),
    /// 无效的URL
    InvalidUrl(String),
    /// 无效的分片索引
    InvalidPieceIndex(usize),
    /// 无效的文件索引
    InvalidFileIndex(usize),
    /// 数据长度不匹配
    DataLengthMismatch { expected: usize, actual: usize },
    /// 其他错误
    Other(String),
}

impl fmt::Display for WebSeedError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            WebSeedError::NoAvailableSources => write!(f, "No available WebSeed sources"),
            WebSeedError::HttpError(msg) => write!(f, "HTTP error: {}", msg),
            WebSeedError::InvalidUrl(url) => write!(f, "Invalid URL: {}", url),
            WebSeedError::InvalidPieceIndex(index) => write!(f, "Invalid piece index: {}", index),
            WebSeedError::InvalidFileIndex(index) => write!(f, "Invalid file index: {}", index),
            WebSeedError::DataLengthMismatch { expected, actual } => 
                write!(f, "Data length mismatch: expected {}, got {}", expected, actual),
            WebSeedError::Other(msg) => write!(f, "WebSeed error: {}", msg),
        }
    }
}

impl Error for WebSeedError {}

/// 从reqwest::Error转换为WebSeedError
impl From<reqwest::Error> for WebSeedError {
    fn from(err: reqwest::Error) -> Self {
        WebSeedError::HttpError(err.to_string())
    }
}

/// 从std::io::Error转换为WebSeedError
impl From<std::io::Error> for WebSeedError {
    fn from(err: std::io::Error) -> Self {
        WebSeedError::Other(format!("IO error: {}", err))
    }
}

/// 从String转换为WebSeedError
impl From<String> for WebSeedError {
    fn from(err: String) -> Self {
        WebSeedError::Other(err)
    }
}

/// 从&str转换为WebSeedError
impl From<&str> for WebSeedError {
    fn from(err: &str) -> Self {
        WebSeedError::Other(err.to_string())
    }
}

/// WebSeed结果类型
pub type WebSeedResult<T> = Result<T, WebSeedError>;
