use std::net::IpAddr;
use sha1::{Sha1, Digest};

/// Fast Extension (BEP 6) 实现
/// 参考: http://bittorrent.org/beps/bep_0006.html

/// 计算允许快速请求的分片列表
///
/// 根据BEP 6规范，这个算法使用以下输入:
/// - 对等点的IP地址
/// - 种子的信息哈希
/// - 分片总数
/// - 允许的分片数量（默认为10）
///
/// 算法:
/// 1. 将IP地址转换为网络字节序的4字节数组
/// 2. 将IP地址与信息哈希连接
/// 3. 计算SHA1哈希
/// 4. 使用哈希的前4个字节作为种子
/// 5. 使用线性同余生成器生成随机数
/// 6. 使用随机数模分片总数得到分片索引
/// 7. 重复步骤5-6直到生成足够数量的不重复分片索引
pub fn calculate_allowed_fast(
    peer_ip: &IpAddr,
    info_hash: &[u8],
    total_pieces: u32,
    allowed_count: usize,
) -> Vec<u32> {
    // 将IP地址转换为网络字节序的4字节数组
    let ip_bytes = match peer_ip {
        IpAddr::V4(ipv4) => ipv4.octets().to_vec(),
        IpAddr::V6(ipv6) => {
            // 对于IPv6，我们使用映射到IPv4的地址（如果可能）
            // 否则使用最后4个字节
            let segments = ipv6.segments();
            if segments[0..5] == [0, 0, 0, 0, 0] && (segments[5] == 0 || segments[5] == 0xffff) {
                // IPv4映射的IPv6地址
                let v4_part = ((segments[6] as u32) << 16) | (segments[7] as u32);
                vec![
                    ((v4_part >> 24) & 0xff) as u8,
                    ((v4_part >> 16) & 0xff) as u8,
                    ((v4_part >> 8) & 0xff) as u8,
                    (v4_part & 0xff) as u8,
                ]
            } else {
                // 使用最后4个字节
                vec![
                    ((segments[6] >> 8) & 0xff) as u8,
                    (segments[6] & 0xff) as u8,
                    ((segments[7] >> 8) & 0xff) as u8,
                    (segments[7] & 0xff) as u8,
                ]
            }
        }
    };

    // 将IP地址与信息哈希连接
    let mut combined = ip_bytes;
    combined.extend_from_slice(info_hash);

    // 计算SHA1哈希
    let mut hasher = Sha1::new();
    hasher.update(&combined);
    let hash = hasher.finalize();

    // 使用哈希的前4个字节作为种子
    let mut x = u32::from_be_bytes([hash[0], hash[1], hash[2], hash[3]]);

    // 使用线性同余生成器生成随机数
    let mut result = Vec::with_capacity(allowed_count);
    let mut count = 0;

    while count < allowed_count && count < total_pieces as usize {
        // 线性同余生成器
        // 使用u64进行计算，避免溢出
        x = ((x as u64 * 0x10a860c1u64) % 0xfffffffbu64) as u32;

        // 计算分片索引
        let piece_index = x % total_pieces;

        // 确保不重复
        if !result.contains(&piece_index) {
            result.push(piece_index);
            count += 1;
        }
    }

    result
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{Ipv4Addr, Ipv6Addr};

    #[test]
    fn test_calculate_allowed_fast_ipv4() {
        let peer_ip = IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1));
        let info_hash = [0u8; 20]; // 全零的信息哈希用于测试
        let total_pieces = 1000;
        let allowed_count = 10;

        let result = calculate_allowed_fast(&peer_ip, &info_hash, total_pieces, allowed_count);

        // 验证结果
        assert_eq!(result.len(), allowed_count);
        for &piece in &result {
            assert!(piece < total_pieces);
        }
    }

    #[test]
    fn test_calculate_allowed_fast_ipv6() {
        let peer_ip = IpAddr::V6(Ipv6Addr::new(0, 0, 0, 0, 0, 0, 0xc0a8, 0x0101)); // 等价于***********
        let info_hash = [0u8; 20]; // 全零的信息哈希用于测试
        let total_pieces = 1000;
        let allowed_count = 10;

        let result = calculate_allowed_fast(&peer_ip, &info_hash, total_pieces, allowed_count);

        // 验证结果
        assert_eq!(result.len(), allowed_count);
        for &piece in &result {
            assert!(piece < total_pieces);
        }
    }

    #[test]
    fn test_calculate_allowed_fast_deterministic() {
        let peer_ip = IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1));
        let info_hash = [0u8; 20]; // 全零的信息哈希用于测试
        let total_pieces = 1000;
        let allowed_count = 10;

        let result1 = calculate_allowed_fast(&peer_ip, &info_hash, total_pieces, allowed_count);
        let result2 = calculate_allowed_fast(&peer_ip, &info_hash, total_pieces, allowed_count);

        // 验证结果是确定性的
        assert_eq!(result1, result2);
    }
}
