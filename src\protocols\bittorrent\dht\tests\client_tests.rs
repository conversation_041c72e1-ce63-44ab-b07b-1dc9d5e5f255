#[cfg(test)]
mod client_tests {
    use std::net::{IpAddr, Ipv4Addr, SocketAddr};
    use std::sync::Arc;
    use std::time::Duration;
    use anyhow::Result;
    use async_trait::async_trait;
    use tokio::sync::Mutex;

    use crate::core::p2p::dht::{DHT, DHTConfig, DHTEvent, DHTEventListener, DHTStatus};
    use crate::protocols::bittorrent::dht::{DHTClient, config::DHTClientConfig, routing::RoutingTableConfig};
    use crate::protocols::bittorrent::dht::node::NodeId;

    // 测试事件监听器
    struct TestDHTEventListener {
        events: Arc<Mutex<Vec<DHTEvent>>>,
    }

    impl TestDHTEventListener {
        fn new() -> Self {
            Self {
                events: Arc::new(Mutex::new(Vec::new())),
            }
        }

        async fn get_events(&self) -> Vec<DHTEvent> {
            self.events.lock().await.clone()
        }
    }

    #[async_trait]
    impl DHTEventListener for TestDHTEventListener {
        async fn on_event(&self, event: DHTEvent) -> Result<()> {
            self.events.lock().await.push(event);
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_dht_client_new() {
        let config = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true,
        };

        let client = DHTClient::new(config).await;
        assert!(client.is_ok());

        let client = client.unwrap();
        // 检查客户端状态
        let status = client.get_status().await.unwrap();
        assert!(!status.initialized);
        assert!(!status.running);
        assert_eq!(status.uptime.as_secs(), 0);
    }

    #[tokio::test]
    async fn test_dht_client_init() {
        let config = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true,
        };

        let mut client = DHTClient::new(config).await.unwrap();

        // 初始化客户端
        let result = client.init().await;
        assert!(result.is_ok());
        let status = client.get_status().await.unwrap();
        assert!(status.initialized);

        // 再次初始化应该也成功
        let result = client.init().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_dht_client_start_stop() {
        let config = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true,
        };

        let mut client = DHTClient::new(config).await.unwrap();

        // 启动客户端
        let result = client.start().await;
        assert!(result.is_ok());
        let status = client.get_status().await.unwrap();
        assert!(status.initialized);
        assert!(status.running);
        // 启动后uptime可能是0，但不应该是None
        assert!(status.uptime.as_secs() >= 0);

        // 停止客户端
        let result = client.stop().await;
        assert!(result.is_ok());
        let status = client.get_status().await.unwrap();
        assert!(!status.running);
    }

    #[tokio::test]
    async fn test_dht_client_add_remove_event_listener() {
        let config = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true,
        };

        let mut client = DHTClient::new(config).await.unwrap();

        // 添加事件监听器
        let listener = Arc::new(TestDHTEventListener::new());
        client.add_event_listener(listener.clone()).await.unwrap();

        // 发送一个事件
        let event = DHTEvent::NodeStateChanged {
            node_count: 10,
            bootstrapped: true,
        };

        // 发送事件
        client.send_event(event.clone()).await.unwrap();

        // 等待一下，让事件处理完成
        tokio::time::sleep(Duration::from_millis(100)).await;

        // 验证事件已接收
        let received_events = listener.get_events().await;
        assert_eq!(received_events.len(), 1);

        // 移除事件监听器
        client.remove_event_listener(0).await.unwrap();

        // 再次发送事件
        client.send_event(event.clone()).await.unwrap();

        // 等待一下，让事件处理完成
        tokio::time::sleep(Duration::from_millis(100)).await;

        // 验证没有新事件接收
        let received_events = listener.get_events().await;
        assert_eq!(received_events.len(), 1);

        // 移除不存在的监听器应该失败
        let result = client.remove_event_listener(0).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_dht_client_get_status() {
        let config = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true,
        };

        let mut client = DHTClient::new(config).await.unwrap();

        // 获取初始状态
        let status = client.get_status().await.unwrap();
        assert!(!status.initialized);
        assert!(!status.running);
        assert_eq!(status.node_count, 0);
        assert_eq!(status.active_queries, 0);
        assert_eq!(status.discovered_peers, 0);
        assert_eq!(status.uptime, Duration::from_secs(0));

        // 初始化并启动客户端
        client.init().await.unwrap();
        client.start().await.unwrap();

        // 获取更新后的状态
        let status = client.get_status().await.unwrap();
        assert!(status.initialized);
        assert!(status.running);
        assert!(status.uptime > Duration::from_secs(0));
    }

    // 这个测试已经在test_dht_client_add_remove_event_listener中覆盖了

    // 这个测试不再适用，因为DHTClientConfig已经移动到config.rs文件中
}
