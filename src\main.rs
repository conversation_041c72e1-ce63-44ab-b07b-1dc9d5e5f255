mod config;
mod api;
mod download;
mod storage;
mod analyzer;
mod blockchain;
mod utils;
mod core;
mod protocols;
mod plugins;
mod examples;

use std::net::SocketAddr;
use std::sync::Arc;
use tokio::signal;
use tracing::{info, warn, error};

use crate::config::{Settings, ConfigManager};
use crate::api::routes::create_router;
use crate::api::websocket::WebSocketManager;
use crate::api::events::EventManager;
use crate::download::manager_ws::WebSocketDownloadManager;
use crate::download::bandwidth_scheduler::{BandwidthScheduler, BandwidthSchedulerImpl};
use crate::analyzer::link_analyzer::LinkAnalyzerImpl;
use crate::storage::storage_impl::LocalStorage;
use crate::utils::logger::init_logger;
use crate::core::error::CoreResult;
use crate::core::interfaces::config::ConfigFactory as CoreConfigFactory;
use crate::plugins::manager::PluginManagerImpl;
use axum::Router;

#[tokio::main]
async fn main() -> CoreResult<()> {
    // 检查命令行参数，如果有--example参数，则运行示例代码
    let args: Vec<String> = std::env::args().collect();
    if args.len() > 1 && args[1] == "--example" {
        if args.len() > 2 {
            match args[2].as_str() {
                "config" => {
                    println!("运行配置管理器示例...");
                    if let Err(e) = examples::config_usage_example::demonstrate_config_usage().await {
                        eprintln!("示例运行失败: {}", e);
                    }
                    return Ok(());
                },
                _ => {
                    println!("未知的示例: {}", args[2]);
                    println!("可用的示例: config");
                    return Ok(());
                }
            }
        } else {
            println!("请指定要运行的示例：");
            println!("  config - 配置管理器示例");
            return Ok(());
        }
    }

    // Initialize the logger
    init_logger()?;

    // Load the settings
    let settings = Settings::new()?;
    info!("Settings loaded");

    // Create configuration manager
    let config_manager = Arc::new(ConfigManager::new_with_settings(settings.clone()));
    info!("Configuration manager created");

    // Create storage
    let storage = Arc::new(LocalStorage::new(&settings.download.path));

    // Create the link analyzer
    let link_analyzer = Arc::new(LinkAnalyzerImpl::new());

    // Create the plugin manager
    let plugin_manager = Arc::new(PluginManagerImpl::new("./plugins"));

    // Load all plugins
    match plugin_manager.load_all_plugins().await {
        Ok(plugins) => {
            info!("Loaded {} plugins", plugins.len());
            for plugin_id in &plugins {
                info!("Loaded plugin: {}", plugin_id);
            }
        },
        Err(e) => {
            warn!("Failed to load plugins: {}", e);
        }
    }

    // Create the bandwidth scheduler
    let bandwidth_scheduler = Arc::new(BandwidthSchedulerImpl::new());

    // Set initial speed limits from settings
    if let Some(limit) = settings.download.speed_limit {
        if limit > 0 {
            bandwidth_scheduler.set_global_download_limit(Some(limit)).await
                .unwrap_or_else(|e| error!("Failed to set global download limit: {}", e));
            info!("Set global download limit to {} bytes/s", limit);
        }
    }

    // Start a background task to update time-based speed limits
    let bandwidth_scheduler_clone = bandwidth_scheduler.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60));
        loop {
            interval.tick().await;
            if let Err(e) = bandwidth_scheduler_clone.update_time_based_limits().await {
                error!("Failed to update time-based speed limits: {}", e);
            }
        }
    });

    // Create WebSocket manager
    let ws_manager = WebSocketManager::new();

    // Send heartbeat messages periodically to keep WebSocket channels alive
    let ws_manager_clone = ws_manager.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(30));
        loop {
            interval.tick().await;
            ws_manager_clone.broadcast("heartbeat", serde_json::json!({
                "timestamp": chrono::Utc::now().to_rfc3339(),
                "server_status": "running"
            }));
        }
    });

    // Create event manager
    let event_manager = EventManager::new(ws_manager.get_sender());

    // Create WebSocket-enabled download manager
    let download_manager = Arc::new(WebSocketDownloadManager::new(
        settings.clone(),
        link_analyzer,
        event_manager.clone(),
        storage.clone(),
        bandwidth_scheduler.clone(),
    ));

    // Create application state
    let app_state = crate::api::state::AppState {
        download_manager: download_manager.clone(),
        bandwidth_scheduler: Some(bandwidth_scheduler.clone()),
        config_manager: Some(config_manager.clone()),
        ws_manager: ws_manager.clone(),
        event_manager: event_manager.clone(),
    };

    // Add CORS middleware
    use tower_http::cors::{Any, CorsLayer};
    let cors = CorsLayer::new()
        .allow_methods(Any)
        .allow_headers(Any)
        .allow_origin(Any);

    // Create the API router with CORS middleware
    let app = Router::new()
        .layer(cors)
        .merge(create_router(app_state));

    // Get the server address
    let ip = settings.server.host.parse::<std::net::IpAddr>()
        .unwrap_or_else(|_| std::net::IpAddr::V4(std::net::Ipv4Addr::new(127, 0, 0, 1)));
    let addr = SocketAddr::from((ip, settings.server.port));

    // Start the server
    info!("Starting server on {}", addr);
    
    let listener = tokio::net::TcpListener::bind(&addr).await
        .map_err(|e| crate::core::error::CoreError::Internal(e.to_string()))?;
    axum::serve(listener, app)
        .with_graceful_shutdown(shutdown_signal())
        .await
        .map_err(|e| crate::core::error::CoreError::Internal(e.to_string()))?;

    info!("Server shutdown");

    Ok(())
}

async fn shutdown_signal() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("Failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("Failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    info!("Shutdown signal received");
}
