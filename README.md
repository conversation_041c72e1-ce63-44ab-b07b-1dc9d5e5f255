# Tonitru Downloader

Tonitru Downloader 是一个高性能的分布式多协议下载工具，支持HTTP(S)、BitTorrent、DHT、自定义P2P协议和Cloudflare R2等多种协议。

## 特性

- **多协议支持**：支持HTTP(S)、BitTorrent、DHT、自定义P2P协议、Cloudflare R2等
- **高性能下载**：利用异步并发、多线程和智能优化实现高速下载
- **智能镜像加速**：自动发现和利用多个镜像源
- **高级下载特性**：支持断点续传、速度控制、动态配置、分块下载等
- **全面任务管理**：提供实时的、全面的下载状态信息和任务管理能力
- **可扩展架构**：模块化设计，便于添加新功能和协议

## 快速开始

### 安装

```bash
# 克隆仓库
git clone https://github.com/your-org/tonitru-downloader.git
cd tonitru-downloader

# 构建项目
cargo build --release
```

### 运行

```bash
# 运行服务器
./target/release/tonitru_downloader
```

默认情况下，服务器将在 http://localhost:8080 上启动。

## 配置

配置文件位于 `config/default.toml`，可以根据需要进行修改：

```toml
[server]
host = "127.0.0.1"
port = 8080

[download]
path = "./downloads"
concurrent_downloads = 5
connections_per_download = 8
chunk_size = 1048576  # 1MB
buffer_size = 8192    # 8KB
speed_limit = 0       # 0 means no limit
```

### 配置管理

```bash
# 获取配置
tonitru config get [--key <key>] [--format <format>]

# 更新配置
tonitru config update --key <key> --value <value> [--format <format>]

# 导出配置
tonitru config export --path <path> [--format <format>]

# 导入配置
tonitru config import --path <path> [--format <format>]

# 保存配置
tonitru config save [--format <format>]

# 重置配置到默认值
tonitru config reset [--format <format>]
```

## API 接口

Tonitru Downloader 提供了一组 RESTful API 接口，用于与前端交互。详细的 API 文档请参阅 [API接口规范](docs/API接口规范.md)。

### 示例

添加下载任务：

```bash
curl -X POST http://localhost:8080/api/v1/downloads \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/file.zip"}'
```

获取所有任务：

```bash
curl -X GET http://localhost:8080/api/v1/tasks
```

## 开发

### 环境要求

- Rust 1.70.0 或更高版本
- Cargo 1.70.0 或更高版本

### 构建

```bash
# 开发构建
cargo build

# 发布构建
cargo build --release
```

### 测试

```bash
# 运行所有测试
cargo test

# 运行特定测试
cargo test test_analyze_http_url
```

### 文档

项目文档位于 `docs` 目录：

- [开发计划](docs/开发计划.md)
- [API接口规范](docs/API接口规范.md)
- [开发文档](docs/开发文档.md)
- [BitTorrent实现文档](docs/bittorrent_implementation.md)
- [BitTorrent下载器文档](docs/bittorrent_downloader.md)
- [BitTorrent与DHT实现文档](docs/bittorrent_dht.md)

### 最近更新

#### 全局速度监控与任务状态通知增强 (2023-09-15)

最近对下载管理系统进行了功能增强，主要改进包括：

- 实现了全局下载/上传速度监控功能，定期收集并通知全局带宽统计信息
- 增强了任务状态变更通知机制，实时反馈任务状态的变化
- 优化了事件通知系统，提高了前端界面的实时性和响应速度
- 改进了 WebSocket 下载管理器，添加了后台任务定期监控全局速度
- 完善了任务状态变更的处理流程，确保状态变更时能够正确通知相关组件

这些改进使得用户界面能够实时显示全局下载和上传速度，并且在任务状态变更时立即得到通知，提升了用户体验和系统的实时性。

#### DHT功能增强与优化 (2023-08-10)

最近对DHT模块进行了全面增强和优化，使其能够在无Tracker的情况下下载种子。主要改进包括：

- 改进了引导过程，实现了并行查询、超时重试和动态等待机制
- 增强了对等点查找功能，添加了自动重试机制，提高了查找成功率
- 优化了错误处理和日志记录，便于调试和问题排查
- 改进了DHT客户端的启动和停止过程，提高了可靠性
- 添加了全面的测试，验证了DHT功能的正确性
- 更新了相关文档，详细说明了DHT的实现和使用方法

这些改进使BitTorrent下载器能够在无Tracker的情况下工作，大大提高了下载的可靠性和可用性。用户现在可以仅通过磁力链接下载种子，而不需要依赖Tracker服务器。

详细信息请参阅[BitTorrent与DHT实现文档](docs/bittorrent_dht.md)。

#### BitTorrent Tracker管理系统增强 (2023-07-15)

最近对BitTorrent的Tracker管理系统进行了全面改进，主要目标是提高下载的可靠性和效率。改进的主要内容包括：

- 实现了多Tracker支持，从种子文件的announce_list中提取多个Tracker
- 添加了智能故障转移机制，在主Tracker失败时自动尝试备用Tracker
- 创建了Tracker状态管理系统，跟踪每个Tracker的状态、成功/失败次数和响应时间
- 实现了基于失败次数的指数退避算法，避免频繁重试无效的Tracker
- 添加了Tracker优先级队列，成功的Tracker会被移到队列前面
- 改进了错误处理和恢复机制，提供详细的错误信息

详细信息请参阅[BitTorrent实现文档](docs/bittorrent_implementation.md)和[BitTorrent下载器文档](docs/bittorrent_downloader.md)。

#### DHT模块重构 (2023-05-18)

最近对BitTorrent DHT模块进行了全面重构，主要目标是提高代码的模块化、可维护性和可测试性。重构的主要内容包括：

- 将原来的单一`client.rs`文件拆分为多个专注于特定功能的模块
- 创建了新的组件如`DHTBootstrapper`、`DHTPeerFinder`和`DHTMessageHandler`
- 采用依赖注入模式，使组件之间的依赖关系更加明确
- 实现了专门的`DHTEventDispatcher`来处理事件分发
- 将配置相关代码移至专门的`config.rs`文件
- 实现了专门的`QueryManager`来管理DHT查询
- 更新了测试以适应新的架构

详细信息请参阅[BitTorrent与DHT实现文档](docs/bittorrent_dht.md)。

## 贡献

欢迎贡献代码、报告问题或提出建议。请参阅 [开发文档](docs/开发文档.md) 了解更多信息。

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。
