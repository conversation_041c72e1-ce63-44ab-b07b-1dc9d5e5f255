use anyhow::{Result, anyhow};
use bytes::{BytesMut, BufMut};
use std::net::IpAddr;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::time::timeout;
use tracing::debug;

use crate::protocols::common::peer::CommonPeer;
use super::fast_extension::calculate_allowed_fast;

/// BitTorrent握手处理器
pub struct HandshakeHandler {
    /// 信息哈希
    info_hash: Vec<u8>,
    /// 本地对等点ID
    local_peer_id: Vec<u8>,
    /// 是否支持Fast Extension
    pub supports_fast: bool,
    /// 是否支持扩展协议
    pub supports_extensions: bool,
    /// 对等点ID
    pub peer_id: Option<Vec<u8>>,
    /// 客户端版本
    pub client_version: Option<String>,
}



impl HandshakeHandler {
    /// 获取信息哈希
    pub fn info_hash(&self) -> &[u8] {
        &self.info_hash
    }

    /// 获取本地对等点ID
    pub fn local_peer_id(&self) -> &[u8] {
        &self.local_peer_id
    }

    /// 创建新的握手处理器
    pub fn new(info_hash: &[u8], local_peer_id: &[u8]) -> Self {
        Self {
            info_hash: info_hash.to_vec(),
            local_peer_id: local_peer_id.to_vec(),
            supports_fast: false,
            supports_extensions: false,
            peer_id: None,
            client_version: None,
        }
    }

    /// 执行BitTorrent握手
    pub async fn perform_handshake(&mut self, common: &mut CommonPeer) -> Result<bool> {
        // 构造握手消息
        let mut handshake = BytesMut::with_capacity(68);
        handshake.put_u8(19); // 协议名长度
        handshake.put_slice(b"BitTorrent protocol"); // 协议名

        // 保留字节 - 设置扩展标志位
        // Fast Extension使用第3位（从0开始）在第7个字节（从0开始）
        // 即 reserved[7] |= 0x04
        // 扩展协议使用第5位（从0开始）在第5个字节（从0开始）
        // 即 reserved[5] |= 0x10
        let mut reserved = [0u8; 8];
        reserved[7] |= 0x04; // 启用Fast Extension
        reserved[5] |= 0x10; // 启用扩展协议
        handshake.put_slice(&reserved);

        handshake.put_slice(&self.info_hash); // 信息哈希
        handshake.put_slice(&self.local_peer_id); // 本地对等点ID

        // 发送握手消息
        if let Some(socket) = &mut common.socket {
            // 发送握手
            socket.write_all(&handshake).await?;

            // 更新上传统计（避免同时借用socket和common）
            let handshake_len = handshake.len();
            drop(socket); // 释放socket的借用
            common.update_upload_stats(handshake_len);

            // 重新获取socket引用
            if let Some(socket) = &mut common.socket {
                // 读取握手响应
                let mut response = vec![0; 68];
                let timeout_duration = common.timeout; // 先获取超时时间

                match timeout(timeout_duration, socket.read_exact(&mut response)).await {
                    Ok(Ok(_)) => {
                        // 释放socket借用，然后更新下载统计
                        let response_len = response.len();
                        drop(socket);
                        common.update_download_stats(response_len);
                    },
                    Ok(Err(e)) => return Err(anyhow!("Failed to read handshake response: {}", e)),
                    Err(_) => return Err(anyhow!("Handshake response timed out")),
                }

                // 验证响应
                if response[0] != 19 || &response[1..20] != b"BitTorrent protocol" {
                    return Err(anyhow!("Invalid handshake response"));
                }

                // 检查信息哈希
                if &response[28..48] != self.info_hash {
                    return Err(anyhow!("Info hash mismatch in handshake response"));
                }

                // 检查Fast Extension支持
                // Fast Extension使用第3位（从0开始）在第7个字节（从0开始）
                // 即 reserved[7] & 0x04
                let reserved_bytes = &response[20..28];
                self.supports_fast = (reserved_bytes[7] & 0x04) != 0;
                debug!("Peer supports Fast Extension: {}", self.supports_fast);

                // 检查扩展协议支持
                // 扩展协议使用第5位（从0开始）在第5个字节（从0开始）
                // 即 reserved[5] & 0x10
                self.supports_extensions = (reserved_bytes[5] & 0x10) != 0;
                debug!("Peer supports Extension Protocol: {}", self.supports_extensions);

                // 提取对等点ID
                self.peer_id = Some(response[48..68].to_vec());
                common.set_peer_id(self.peer_id.clone().unwrap_or_default());

                // 尝试识别客户端
                if let Some(peer_id) = &self.peer_id {
                    if peer_id.len() >= 8 {
                        let client_id = String::from_utf8_lossy(&peer_id[1..9]);
                        self.client_version = Some(client_id.to_string());
                        common.set_client_name(client_id.to_string());
                    }
                }

                debug!("Handshake successful with peer: {:?}", common.peer_info.addr);

                Ok(true)
            } else {
                Err(anyhow!("Socket unexpectedly closed during handshake"))
            }
        } else {
            Err(anyhow!("Not connected to peer"))
        }
    }

    /// 计算并发送allowed_fast消息
    pub fn calculate_allowed_fast(&self, peer_ip: IpAddr, total_pieces: u32, max_allowed: usize) -> Vec<u32> {
        // 如果不支持Fast Extension，返回空列表
        if !self.supports_fast {
            return Vec::new();
        }

        // 计算允许快速请求的分片，最多允许指定数量的分片
        calculate_allowed_fast(&peer_ip, &self.info_hash, total_pieces, max_allowed)
    }
}
