//! DHT 集成模块
//! 
//! 这个模块负责与 DHT 网络集成，获取对等点信息。
//! 它处理 DHT 消息、对等点发现和 DHT 引导等功能。

use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};

use anyhow::{anyhow, Result};

use tracing::{debug, error, info, trace, warn};
use crate::core::p2p::dht::DHT;

use crate::protocols::bittorrent::dht_manager::DHTManager;
use tokio::sync::Mutex;

/// DHT 集成
/// 
/// 负责与 DHT 网络集成，获取对等点信息。
#[derive(Clone)]
pub struct DHTIntegration {
    /// DHT 管理器
    dht_manager: Arc<Mutex<DHTManager>>,
    
    /// 种子信息哈希
    info_hash: Option<Vec<u8>>,
    
    /// 最后一次查询时间
    last_query: Instant,
    
    /// 查询间隔
    query_interval: Duration,
}

impl DHTIntegration {
    /// 创建新的 DHT 集成
    pub fn new(dht_manager: Arc<Mutex<DHTManager>>) -> Self {
        Self {
            dht_manager,
            info_hash: None,
            last_query: Instant::now(),
            query_interval: Duration::from_secs(300), // 5分钟查询一次
        }
    }
    
    /// 设置种子信息哈希
    pub fn set_info_hash(&mut self, info_hash: Vec<u8>) {
        self.info_hash = Some(info_hash);
    }
    
    /// 设置查询间隔
    pub fn set_query_interval(&mut self, interval: Duration) {
        self.query_interval = interval;
    }
    
    /// 查询对等点
    pub async fn query_peers(&mut self) -> Result<Vec<SocketAddr>> {
        // 检查是否设置了info_hash
        let info_hash = match &self.info_hash {
            Some(hash) => hash.clone(),
            None => {
                debug!("尚未设置info_hash，无法查询对等点");
                return Ok(Vec::new());
            }
        };
        
        // 检查是否到达查询间隔
        let now = Instant::now();
        if now.duration_since(self.last_query) < self.query_interval {
            return Ok(Vec::new());
        }
        
        // 更新最后查询时间
        self.last_query = now;
        
        // 查询对等点
        debug!("通过DHT查询info_hash为 {} 的对等点", hex::encode(&info_hash));
        let info_hash_array: [u8; 20] = info_hash.try_into().map_err(|_| anyhow!("Invalid info_hash length"))?;
        
        // 获取DHT管理器的锁
        let dht_manager = self.dht_manager.lock().await;
        let peers = dht_manager.get_peers(info_hash_array).await?;
        
        debug!("DHT查询返回 {} 个对等点", peers.len());
        
        Ok(peers)
    }
    
    /// 宣告对等点
    pub async fn announce_peer(&self, port: u16) -> Result<()> {
        // 检查是否设置了info_hash
        let info_hash = match &self.info_hash {
            Some(hash) => hash.clone(),
            None => {
                debug!("尚未设置info_hash，无法宣告对等点");
                return Ok(());
            }
        };
        
        // 宣告对等点
        debug!("向DHT网络宣告info_hash为 {} 的对等点，端口: {}", hex::encode(&info_hash), port);
        let info_hash_array: [u8; 20] = info_hash.try_into().map_err(|_| anyhow!("Invalid info_hash length"))?;
        
        // 获取DHT管理器的锁
        let dht_manager = self.dht_manager.lock().await;
        dht_manager.announce_peer(info_hash_array, port).await?;
        
        Ok(())
    }
    
    /// 启动 DHT
    pub async fn start_dht(&self) -> Result<()> {
        debug!("启动DHT");
        
        // 获取DHT管理器的锁
        let mut dht_manager = self.dht_manager.lock().await;
        dht_manager.start().await?;
        
        Ok(())
    }
    
    /// 停止 DHT
    pub async fn stop_dht(&self) -> Result<()> {
        debug!("停止DHT");
        
        // 获取DHT管理器的锁
        let mut dht_manager = self.dht_manager.lock().await;
        dht_manager.stop().await?;
        
        Ok(())
    }
    
    /// 添加 DHT 引导节点
    pub async fn add_bootstrap_node(&self, addr: SocketAddr) -> Result<()> {
        debug!("添加DHT引导节点: {}", addr);
        
        // 获取DHT管理器的锁
        let dht_manager = self.dht_manager.lock().await;
        
        // 检查DHT是否启用
        if !dht_manager.is_enabled() {
            debug!("DHT未启用，无法添加引导节点");
            return Err(anyhow::anyhow!("DHT未启用"));
        }
        
        // 获取DHT状态
        let status = dht_manager.get_status().await?;
        if !status.initialized {
            debug!("DHT未初始化，无法添加引导节点");
            return Err(anyhow::anyhow!("DHT未初始化"));
        }
        
        // 记录添加引导节点的信息
        info!("添加DHT引导节点: {}，当前节点数: {}", addr, status.node_count);
        
        // 调用DHT管理器的添加引导节点方法
        dht_manager.add_bootstrap_node(addr).await?;
        
        debug!("成功添加DHT引导节点: {}", addr);
        
        Ok(())
    }
    
    /// 获取 DHT 统计信息
    pub async fn get_dht_stats(&self) -> Result<String> {
        debug!("获取DHT统计信息");
        
        // 获取DHT管理器的锁
        let dht_manager = self.dht_manager.lock().await;
        let status = dht_manager.get_status().await?;
        
        // 将 DHTStatus 转换为字符串
        let stats = format!(
            "DHT状态: 初始化={}, 运行中={}, 节点数={}, 活跃查询={}, 发现的对等点={}, 运行时间={}秒",
            status.initialized,
            status.running,
            status.node_count,
            status.active_queries,
            status.discovered_peers,
            status.uptime.as_secs()
        );
        
        Ok(stats)
    }
}