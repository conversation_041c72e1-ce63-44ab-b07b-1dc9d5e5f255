//! CLI后端实现
//!
//! 实现CLI后端接口。

use std::sync::Arc;

use anyhow::{anyhow, Result};
use async_trait::async_trait;
use log::{error, info};
use serde_json;
use uuid::Uuid;

use crate::config::ConfigManager;
use crate::download::bandwidth_scheduler::BandwidthScheduler;
use crate::download::manager::{DownloadManager, TaskInfo, TaskStatus};
use crate::core::interfaces::storage::Storage;

use super::backend::CliBackend;
use super::models::{SpeedLimitResponse, TimeRule, Config, Status, DownloadStats};

/// CLI后端实现
pub struct Backend {
    download_manager: Arc<dyn DownloadManager>,
    config_manager: Arc<ConfigManager>,
    storage: Arc<dyn Storage>,
}

impl Backend {
    /// 创建CLI后端实例
    pub fn new(
        download_manager: Arc<dyn DownloadManager>,
        config_manager: Arc<ConfigManager>,
        storage: Arc<dyn Storage>,
    ) -> Self {
        Self {
            download_manager,
            config_manager,
            storage,
        }
    }
    
    /// 获取时间规则ID列表 
    async fn get_time_rule_ids(&self) -> Result<Vec<Uuid>> { 
        match self.storage.get("time_rules:index").await { 
            Ok(json) => { 
                let ids: Vec<Uuid> = serde_json::from_str(&json)?;
                Ok(ids) 
            }, 
            Err(_) => Ok(Vec::new()), 
        } 
    } 
    
    /// 获取带宽调度器 
    async fn get_bandwidth_scheduler(&self) -> Result<Option<Arc<dyn BandwidthScheduler>>> { 
        // 尝试从下载管理器获取带宽调度器
        // 检查下载管理器是否是WebSocketDownloadManager类型
        // 由于Rust的类型系统限制，我们无法直接检查类型，但可以尝试通过反射或其他方式获取
        
        // 方法1：尝试通过环境变量或配置获取带宽调度器实例
        // 从配置管理器获取下载速度限制
        let speed_limit_str = self.config_manager.get_value("download.speed_limit").await.unwrap_or_else(|_| "0".to_string());
        let speed_limit = speed_limit_str.parse::<u64>().unwrap_or(0);
        
        // 创建一个新的带宽调度器实例
        use crate::download::bandwidth_scheduler::BandwidthSchedulerImpl;
        let scheduler = Arc::new(BandwidthSchedulerImpl::new());
        
        // 如果配置中有全局下载速度限制，设置它
        if speed_limit > 0 {
            if let Err(e) = scheduler.set_global_download_limit(Some(speed_limit)).await {
                log::warn!("无法设置全局下载速度限制: {}", e);
            }
        }
        
        // 返回创建的带宽调度器实例
        Ok(Some(scheduler))
    }
}

#[async_trait]
impl CliBackend for Backend {
    async fn add_download_task(&self, url: &str, output_path: &str) -> Result<Uuid> {
        let task_id = Uuid::new_v4();
        
        // 创建下载器
        self.download_manager.create_downloader(url, output_path, task_id).await?;
        
        // 创建任务信息
        let task = TaskInfo {
            id: task_id,
            url: url.to_string(),
            output_path: output_path.to_string(),
            status: TaskStatus::Pending,
            progress: 0.0,
            speed: 0,
            downloaded_size: 0,
            uploaded_bytes: 0,
            total_size: None,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            error_message: None,
        };
        
        // 添加任务到下载管理器
        self.download_manager.add_task(task).await?;
        
        Ok(task_id)
    }
    
    async fn start_download_task(&self, task_id: Uuid) -> Result<()> {
        self.download_manager.start_task(task_id).await
    }
    
    async fn pause_download_task(&self, task_id: Uuid) -> Result<()> {
        self.download_manager.pause_task(task_id).await
    }
    
    async fn resume_download_task(&self, task_id: Uuid) -> Result<()> {
        self.download_manager.resume_task(task_id).await
    }
    
    async fn cancel_download_task(&self, task_id: Uuid) -> Result<()> {
        self.download_manager.cancel_task(task_id).await
    }
    
    async fn get_download_task_info(&self, task_id: Uuid) -> Result<String> {
        let info = self.download_manager.get_task(task_id).await?;
        Ok(serde_json::to_string(&info)?)
    }
    
    async fn get_all_download_tasks(&self) -> Result<Vec<String>> {
        let tasks = self.download_manager.get_all_tasks().await?;
        let mut task_strings = Vec::new();
        for task in tasks {
            task_strings.push(serde_json::to_string(&task)?)
        }
        Ok(task_strings)
    }
    
    async fn remove_download_task(&self, task_id: Uuid) -> Result<()> {
        self.download_manager.remove_task(task_id).await
    }
    
    async fn get_download_stats(&self) -> Result<DownloadStats> {
        let manager_stats = self.download_manager.get_download_stats().await;
        
        Ok(DownloadStats {
            active_count: manager_stats.active_downloads,
            paused_count: 0, // 下载管理器没有提供暂停数量，设为0
            completed_count: manager_stats.completed_downloads,
            failed_count: manager_stats.failed_downloads,
            total_count: manager_stats.total_downloads,
            total_downloaded_bytes: manager_stats.total_downloaded_bytes,
            total_uploaded_bytes: manager_stats.total_uploaded_bytes,
        })
    }
    
    async fn set_global_download_limit(&self, limit: u64) -> Result<()> {
        // 尝试获取带宽调度器
        if let Some(scheduler) = self.get_bandwidth_scheduler().await? {
            scheduler.set_global_download_limit(Some(limit)).await?;
            
            // 保存到配置
            self.config_manager.set_value("download.speed_limit", limit.to_string()).await?;
            
            Ok(())
        } else {
            Err(anyhow!("无法获取带宽调度器"))
        }
    }
    
    async fn get_global_download_limit(&self) -> Result<SpeedLimitResponse> {
        // 尝试从配置中获取
        let limit = match self.config_manager.get_value("download.speed_limit").await {
            Ok(value) => value.parse::<u64>().unwrap_or(0),
            Err(_) => 0,
        };
        
        Ok(SpeedLimitResponse { limit })
    }
    
    async fn set_global_upload_limit(&self, limit: u64) -> Result<()> {
        // 尝试获取带宽调度器
        if let Some(scheduler) = self.get_bandwidth_scheduler().await? {
            scheduler.set_global_upload_limit(Some(limit)).await?;
            
            // 保存到配置
            self.config_manager.set_value("upload.speed_limit", limit.to_string()).await?;
            
            Ok(())
        } else {
            Err(anyhow!("无法获取带宽调度器"))
        }
    }
    
    async fn get_global_upload_limit(&self) -> Result<SpeedLimitResponse> {
        // 尝试从配置中获取
        let limit = match self.config_manager.get_value("upload.speed_limit").await {
            Ok(value) => value.parse::<u64>().unwrap_or(0),
            Err(_) => 0,
        };
        
        Ok(SpeedLimitResponse { limit })
    }
    
    async fn set_task_download_limit(&self, task_id: Uuid, limit: u64) -> Result<()> {
        // 尝试获取带宽调度器
        if let Some(scheduler) = self.get_bandwidth_scheduler().await? {
            scheduler.set_task_download_limit(task_id, Some(limit)).await?;
            
            // 保存到存储
            let key = format!("task:{}:download_limit", task_id);
            self.storage.write(&key, limit.to_string().as_bytes()).await?;
            
            Ok(())
        } else {
            Err(anyhow!("无法获取带宽调度器"))
        }
    }
    
    async fn get_task_download_limit(&self, task_id: Uuid) -> Result<SpeedLimitResponse> {
        // 尝试从存储中获取
        let key = format!("task:{}:download_limit", task_id);
        let limit = match self.storage.read(&key).await {
            Ok(bytes) => String::from_utf8_lossy(&bytes).parse::<u64>().unwrap_or(0),
            Err(_) => 0,
        };
        
        Ok(SpeedLimitResponse { limit })
    }
    
    async fn set_task_upload_limit(&self, task_id: Uuid, limit: u64) -> Result<()> {
        // 尝试获取带宽调度器
        if let Some(scheduler) = self.get_bandwidth_scheduler().await? {
            scheduler.set_task_upload_limit(task_id, Some(limit)).await?;
            
            // 保存到存储
            let key = format!("task:{}:upload_limit", task_id);
            self.storage.write(&key, limit.to_string().as_bytes()).await?;
            
            Ok(())
        } else {
            Err(anyhow!("无法获取带宽调度器"))
        }
    }
    
    async fn get_task_upload_limit(&self, task_id: Uuid) -> Result<SpeedLimitResponse> {
        // 尝试从存储中获取
        let key = format!("task:{}:upload_limit", task_id);
        let limit = match self.storage.read(&key).await {
            Ok(bytes) => String::from_utf8_lossy(&bytes).parse::<u64>().unwrap_or(0),
            Err(_) => 0,
        };
        
        Ok(SpeedLimitResponse { limit })
    }
    
    async fn add_time_rule(&self, mut rule: TimeRule) -> Result<Uuid> {
        // 生成规则ID
        let rule_id = Uuid::new_v4();
        rule.id = Some(rule_id);
        
        // 获取现有规则ID列表
        let mut rule_ids = self.get_time_rule_ids().await?;
        
        // 添加新规则ID
        rule_ids.push(rule_id);
        
        // 保存规则
        let rule_key = format!("time_rules:{}", rule_id);
        let rule_json = serde_json::to_string(&rule)?;
        self.storage.set(&rule_key, &rule_json).await?;
        
        // 更新规则索引
        let index_json = serde_json::to_string(&rule_ids)?;
        self.storage.set("time_rules:index", &index_json).await?;
        
        Ok(rule_id)
    }
    
    async fn get_time_rules(&self) -> Result<Vec<TimeRule>> {
        let rule_ids = self.get_time_rule_ids().await?;
        let mut rules = Vec::new();
        
        for id in rule_ids {
            let rule_key = format!("time_rules:{}", id);
            match self.storage.get(&rule_key).await {
                Ok(rule_json) => {
                    match serde_json::from_str::<TimeRule>(&rule_json) {
                        Ok(rule) => rules.push(rule),
                        Err(e) => error!("无法解析时间规则 {}: {}", id, e),
                    }
                },
                Err(e) => error!("无法获取时间规则 {}: {}", id, e),
            }
        }
        
        Ok(rules)
    }
    
    async fn delete_time_rule(&self, rule_id: Uuid) -> Result<()> {
        // 获取现有规则ID列表
        let mut rule_ids = self.get_time_rule_ids().await?;
        
        // 移除规则ID
        rule_ids.retain(|id| *id != rule_id);
        
        // 删除规则
        let rule_key = format!("time_rules:{}", rule_id);
        self.storage.delete(&rule_key).await?;
        
        // 更新规则索引
        let index_json = serde_json::to_string(&rule_ids)?;
        self.storage.set("time_rules:index", &index_json).await?;
        
        Ok(())
    }
    
    async fn get_config(&self) -> Result<Config> {
        // 从配置管理器获取各项配置
        let server_host = self.config_manager.get_value("server.host").await.unwrap_or_else(|_| "127.0.0.1".to_string());
        let server_port = self.config_manager.get_value("server.port").await.unwrap_or_else(|_| "8080".to_string()).parse::<u16>().unwrap_or(8080);
        
        let download_path = self.config_manager.get_value("download.path").await.unwrap_or_else(|_| "./downloads".to_string());
        let concurrent_downloads = self.config_manager.get_value("download.concurrent_downloads").await.unwrap_or_else(|_| "3".to_string()).parse::<u32>().unwrap_or(3);
        let connections_per_download = self.config_manager.get_value("download.connections_per_download").await.unwrap_or_else(|_| "3".to_string()).parse::<u32>().unwrap_or(3);
        let chunk_size = self.config_manager.get_value("download.chunk_size").await.unwrap_or_else(|_| "1048576".to_string()).parse::<u64>().unwrap_or(1048576);
        let buffer_size = self.config_manager.get_value("download.buffer_size").await.unwrap_or_else(|_| "8192".to_string()).parse::<u64>().unwrap_or(8192);
        let speed_limit = self.config_manager.get_value("download.speed_limit").await.unwrap_or_else(|_| "0".to_string()).parse::<u64>().unwrap_or(0);
        
        let database_url = self.config_manager.get_value("database.url").await.unwrap_or_else(|_| "sqlite://./tonitru.db".to_string());
        
        let mirror_enabled = self.config_manager.get_value("mirror.enabled").await.unwrap_or_else(|_| "false".to_string()).parse::<bool>().unwrap_or(false);
        let mirror_max_mirrors = self.config_manager.get_value("mirror.max_mirrors").await.unwrap_or_else(|_| "3".to_string()).parse::<u32>().unwrap_or(3);
        let mirror_health_check_interval = self.config_manager.get_value("mirror.health_check_interval_secs").await.unwrap_or_else(|_| "300".to_string()).parse::<u64>().unwrap_or(300);
        
        let proxy_enabled = self.config_manager.get_value("proxy.enabled").await.unwrap_or_else(|_| "false".to_string()).parse::<bool>().unwrap_or(false);
        let proxy_url = self.config_manager.get_value("proxy.url").await.unwrap_or_else(|_| "".to_string());
        let proxy_type = self.config_manager.get_value("proxy.type").await.unwrap_or_else(|_| "http".to_string());
        let proxy_username = self.config_manager.get_value("proxy.username").await.unwrap_or_else(|_| "".to_string());
        let proxy_password = self.config_manager.get_value("proxy.password").await.unwrap_or_else(|_| "".to_string());
        
        // 获取no_proxy列表
        let no_proxy_str = self.config_manager.get_value("proxy.no_proxy").await.unwrap_or_else(|_| "".to_string());
        let no_proxy = if no_proxy_str.is_empty() {
            Vec::new()
        } else {
            no_proxy_str.split(',').map(|s| s.trim().to_string()).collect()
        };
        
        // 构建配置对象
        Ok(Config {
            server: super::models::ServerConfig {
                host: server_host,
                port: server_port,
            },
            download: super::models::DownloadConfig {
                path: download_path,
                concurrent_downloads,
                connections_per_download,
                chunk_size,
                buffer_size,
                speed_limit,
            },
            database: super::models::DatabaseConfig {
                url: database_url,
            },
            mirror: super::models::MirrorConfig {
                enabled: mirror_enabled,
                max_mirrors: mirror_max_mirrors,
                health_check_interval_secs: mirror_health_check_interval,
            },
            proxy: super::models::ProxyConfig {
                enabled: proxy_enabled,
                url: proxy_url,
                proxy_type,
                username: proxy_username,
                password: proxy_password,
                no_proxy,
            },
        })
    }
    
    async fn update_config(&self, config: Config) -> Result<()> {
        // 更新服务器配置
        self.config_manager.set_value("server.host", config.server.host).await?;
        self.config_manager.set_value("server.port", config.server.port.to_string()).await?;
        
        // 更新下载配置
        self.config_manager.set_value("download.path", config.download.path).await?;
        self.config_manager.set_value("download.concurrent_downloads", config.download.concurrent_downloads.to_string()).await?;
        self.config_manager.set_value("download.connections_per_download", config.download.connections_per_download.to_string()).await?;
        self.config_manager.set_value("download.chunk_size", config.download.chunk_size.to_string()).await?;
        self.config_manager.set_value("download.buffer_size", config.download.buffer_size.to_string()).await?;
        self.config_manager.set_value("download.speed_limit", config.download.speed_limit.to_string()).await?;
        
        // 更新数据库配置
        self.config_manager.set_value("database.url", config.database.url).await?;
        
        // 更新镜像配置
        self.config_manager.set_value("mirror.enabled", config.mirror.enabled.to_string()).await?;
        self.config_manager.set_value("mirror.max_mirrors", config.mirror.max_mirrors.to_string()).await?;
        self.config_manager.set_value("mirror.health_check_interval_secs", config.mirror.health_check_interval_secs.to_string()).await?;
        
        // 更新代理配置
        self.config_manager.set_value("proxy.enabled", config.proxy.enabled.to_string()).await?;
        self.config_manager.set_value("proxy.url", config.proxy.url).await?;
        self.config_manager.set_value("proxy.type", config.proxy.proxy_type).await?;
        self.config_manager.set_value("proxy.username", config.proxy.username).await?;
        self.config_manager.set_value("proxy.password", config.proxy.password).await?;
        
        // 更新no_proxy列表
        let no_proxy_str = config.proxy.no_proxy.join(",");
        self.config_manager.set_value("proxy.no_proxy", no_proxy_str).await?;
        
        Ok(())
    }
    
    async fn update_config_item(&self, key: &str, value: &str) -> Result<()> {
        self.config_manager.set_value(key, value.to_string()).await
    }
    
    async fn save_config(&self) -> Result<()> {
        // 获取配置路径
        let config_path = self.config_manager.get_config_path().await?
            .ok_or_else(|| anyhow!("No default config path set"))?;
        
        // 保存配置到默认路径
        self.config_manager.save_to_file(Some(&config_path)).await?;
        
        Ok(())
    }
    
    async fn reset_config(&self) -> Result<()> {
        // 重置配置到默认值
        self.config_manager.reset_to_default().await?;
        
        // 尝试保存重置后的配置
        if let Ok(Some(config_path)) = self.config_manager.get_config_path().await {
            // 如果有配置路径，保存到该路径
            self.config_manager.save_to_file(Some(&config_path)).await?
        } else {
            // 如果没有配置路径，保存到默认路径
            let default_path = std::path::Path::new("config/default.json");
            self.config_manager.save_to_file(Some(default_path)).await?
        }
        
        info!("Configuration has been reset to default values");
        Ok(())
    }
    
    async fn get_system_status(&self) -> Result<Status> {
        // 获取系统状态信息 
        let version = env!("CARGO_PKG_VERSION").to_string(); 
        
        // 使用 sysinfo 库获取系统信息
        use sysinfo::{System, SystemExt, CpuExt, DiskExt};
        
        // 创建系统信息实例并刷新所有数据
        let mut sys = System::new_all();
        sys.refresh_all();
        
        // 计算CPU使用率（所有核心的平均值）
        let mut cpu_usage = 0.0;
        let cpu_count = sys.cpus().len();
        if cpu_count > 0 {
            for cpu in sys.cpus() {
                cpu_usage += cpu.cpu_usage() as f64;
            }
            cpu_usage /= cpu_count as f64;
        }
        
        // 计算磁盘使用率（所有磁盘的平均值）
        let mut disk_usage = 0.0;
        let disks = sys.disks();
        let disk_count = disks.len();
        if disk_count > 0 {
            for disk in disks {
                let total = disk.total_space();
                if total > 0 {
                    let used = total - disk.available_space();
                    disk_usage += (used as f64 / total as f64) * 100.0;
                }
            }
            disk_usage /= disk_count as f64;
        }
        
        // 获取内存信息
        let total_memory = sys.total_memory();
        let used_memory = sys.used_memory();
        let free_memory = sys.free_memory();
        
        // 计算系统运行时间（秒）
        let uptime = sys.uptime();
         
        Ok(Status { 
            version, 
            uptime, 
            load: super::models::SystemLoad { 
                cpu_usage, 
                disk_usage, 
            }, 
            memory: super::models::MemoryUsage { 
                total: total_memory, 
                used: used_memory, 
                free: free_memory, 
            }, 
        }) 
    }
    
    /// 获取速度限制
    async fn get_speed_limit(&self) -> Result<Option<u64>> {
        // 从配置中获取速度限制
        match self.config_manager.get_value("speed.limit").await {
            Ok(value) => {
                let limit = value.parse::<u64>().unwrap_or(0);
                if limit > 0 {
                    Ok(Some(limit))
                } else {
                    Ok(None)
                }
            },
            Err(_) => Ok(None),
        }
    }
    
    /// 设置速度限制
    async fn set_speed_limit(&self, limit: u64) -> Result<()> {
        // 保存速度限制到配置
        self.config_manager.set_value("speed.limit", limit.to_string()).await?;
        
        // 如果有带宽调度器，同时设置全局下载和上传限制
        if let Some(scheduler) = self.get_bandwidth_scheduler().await? {
            scheduler.set_global_download_limit(Some(limit)).await?;
            scheduler.set_global_upload_limit(Some(limit)).await?;
        }
        
        Ok(())
    }
    
    /// 移除速度限制
    async fn remove_speed_limit(&self) -> Result<()> {
        // 从配置中移除速度限制
        self.config_manager.set_value("speed.limit", "0".to_string()).await?;
        
        // 如果有带宽调度器，移除全局下载和上传限制
        if let Some(scheduler) = self.get_bandwidth_scheduler().await? {
            scheduler.set_global_download_limit(None).await?;
            scheduler.set_global_upload_limit(None).await?;
        }
        
        Ok(())
    }
}