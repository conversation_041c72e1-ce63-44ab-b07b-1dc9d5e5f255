<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useThemeStore } from '../stores/theme'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const route = useRoute()
const router = useRouter()
const themeStore = useThemeStore()

// 当前设置页面
const currentTab = ref('basic')

// 设置表单
const form = ref({
  basic: {
    theme: themeStore.theme,
    downloadDir: 'D:\\Downloads',
    maxConcurrentDownloads: 5,
    maxOverallDownloadLimit: 0,
    maxOverallUploadLimit: 0
  },
  advanced: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    maxConnectionPerServer: 16,
    splitSize: 10,
    minSplitSize: 1,
    enableIPv6: false
  }
})

// 切换设置页面
const handleTabChange = (tab: string) => {
  currentTab.value = tab
}

// 保存基本设置
const saveBasicSettings = async () => {
  try {
    // 保存主题设置
    themeStore.setTheme(form.value.basic.theme)

    // 保存全局下载速度限制
    if (form.value.basic.maxOverallDownloadLimit > 0) {
      await axios.post('/speed/download', {
        limit: form.value.basic.maxOverallDownloadLimit * 1024
      })
    }

    // 保存全局上传速度限制
    if (form.value.basic.maxOverallUploadLimit > 0) {
      await axios.post('/speed/upload', {
        limit: form.value.basic.maxOverallUploadLimit * 1024
      })
    }

    // 保存下载目录和并发下载数等其他设置
    // 注意：这些设置可能需要通过配置文件或其他API保存
    // 目前后端API可能不支持这些设置，这里仅作为示例

    ElMessage.success('基本设置已保存')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  }
}

// 保存高级设置
const saveAdvancedSettings = async () => {
  try {
    // 注意：这些高级设置可能需要通过配置文件或其他API保存
    // 目前后端API可能不支持这些设置，这里仅作为示例
    ElMessage.success('高级设置已保存')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  }
}

// 选择下载目录
const selectDirectory = () => {
  // 在实际的Electron环境中，这里会调用选择目录的API
  console.log('选择下载目录')
  // 模拟选择了一个目录
  form.value.basic.downloadDir = 'D:\\Downloads'
}

// 加载设置
const loadSettings = async () => {
  try {
    // 加载下载速度限制
    try {
      const downloadLimitResponse = await axios.get('/speed/download')
      if (downloadLimitResponse.data && downloadLimitResponse.data.success) {
        const limit = downloadLimitResponse.data.data?.limit || 0
        form.value.basic.maxOverallDownloadLimit = limit / 1024
      }
    } catch (err) {
      console.error('加载下载速度限制失败:', err)
    }

    // 加载上传速度限制
    try {
      const uploadLimitResponse = await axios.get('/speed/upload')
      if (uploadLimitResponse.data && uploadLimitResponse.data.success) {
        const limit = uploadLimitResponse.data.data?.limit || 0
        form.value.basic.maxOverallUploadLimit = limit / 1024
      }
    } catch (err) {
      console.error('加载上传速度限制失败:', err)
    }

    // 加载系统状态，获取版本信息等
    try {
      const statusResponse = await axios.get('/status')
      if (statusResponse.data && statusResponse.data.success) {
        console.log('系统状态:', statusResponse.data.data)
        // 这里可以更新一些系统状态相关的设置
      }
    } catch (err) {
      console.error('加载系统状态失败:', err)
    }

    // 注意：其他设置如下载目录、并发下载数等可能需要通过配置文件或其他API加载
    // 目前使用默认值
  } catch (error) {
    console.error('加载设置失败:', error)
    ElMessage.error('加载设置失败')
  }
}

// 组件挂载时加载设置
onMounted(() => {
  loadSettings()
})
</script>

<template>
  <el-container class="preference-container">
    <el-aside width="200px" class="subnav">
      <div class="subnav-inner">
        <h3 class="subnav-title">设置</h3>
        <el-menu
          :default-active="currentTab"
          class="preference-menu"
          @select="handleTabChange"
        >
          <el-menu-item index="basic">
            <el-icon><Setting /></el-icon>
            <span>基本设置</span>
          </el-menu-item>
          <el-menu-item index="advanced">
            <el-icon><SetUp /></el-icon>
            <span>高级设置</span>
          </el-menu-item>
        </el-menu>
      </div>
    </el-aside>

    <el-main class="main">
      <!-- 基本设置 -->
      <div v-if="currentTab === 'basic'" class="preference-panel">
        <h2 class="panel-title">基本设置</h2>

        <el-form :model="form.basic" label-width="140px" class="preference-form">
          <el-form-item label="主题">
            <el-radio-group v-model="form.basic.theme">
              <el-radio-button value="light">浅色</el-radio-button>
              <el-radio-button value="dark">深色</el-radio-button>
              <el-radio-button value="auto">跟随系统</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="下载目录">
            <div class="directory-select">
              <el-input v-model="form.basic.downloadDir" placeholder="选择下载目录" />
              <el-button @click="selectDirectory">浏览...</el-button>
            </div>
          </el-form-item>

          <el-form-item label="最大同时下载数">
            <el-input-number
              v-model="form.basic.maxConcurrentDownloads"
              :min="1"
              :max="20"
              controls-position="right"
            />
          </el-form-item>

          <el-form-item label="全局下载限速">
            <el-input-number
              v-model="form.basic.maxOverallDownloadLimit"
              :min="0"
              :step="10"
              controls-position="right"
            />
            <span class="unit-label">KB/s (0表示不限速)</span>
          </el-form-item>

          <el-form-item label="全局上传限速">
            <el-input-number
              v-model="form.basic.maxOverallUploadLimit"
              :min="0"
              :step="10"
              controls-position="right"
            />
            <span class="unit-label">KB/s (0表示不限速)</span>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 高级设置 -->
      <div v-if="currentTab === 'advanced'" class="preference-panel">
        <h2 class="panel-title">高级设置</h2>

        <el-form :model="form.advanced" label-width="160px" class="preference-form">
          <el-form-item label="User Agent">
            <el-input v-model="form.advanced.userAgent" />
          </el-form-item>

          <el-form-item label="每服务器最大连接数">
            <el-input-number
              v-model="form.advanced.maxConnectionPerServer"
              :min="1"
              :max="32"
              controls-position="right"
            />
          </el-form-item>

          <el-form-item label="文件分片数">
            <el-input-number
              v-model="form.advanced.splitSize"
              :min="1"
              :max="32"
              controls-position="right"
            />
          </el-form-item>

          <el-form-item label="最小分片大小">
            <el-input-number
              v-model="form.advanced.minSplitSize"
              :min="1"
              :step="1"
              controls-position="right"
            />
            <span class="unit-label">MB</span>
          </el-form-item>

          <el-form-item label="启用 IPv6">
            <el-switch v-model="form.advanced.enableIPv6" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="saveAdvancedSettings">保存设置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-main>
  </el-container>
</template>

<style scoped>
.preference-container {
  height: 100%;
}

.subnav-inner {
  padding: 20px 0;
}

.subnav-title {
  padding: 0 20px;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
}

.preference-menu {
  border-right: none;
}

.el-menu-item {
  display: flex;
  align-items: center;
}

.el-menu-item .el-icon {
  margin-right: 8px;
}

.preference-panel {
  max-width: 800px;
  padding: 20px;
}

.panel-title {
  margin-top: 0;
  margin-bottom: 30px;
  font-size: 20px;
  font-weight: 500;
}

.preference-form {
  max-width: 600px;
}

.directory-select {
  display: flex;
  align-items: center;
}

.directory-select .el-input {
  margin-right: 10px;
}

.unit-label {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
