#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::constants::{PROTOCOL_HTTP, PROTOCOL_HTTPS, PROTOCOL_BT, PROTOCOL_MAGNET, PROTOCOL_P2P, PROTOCOL_R2};
    use tokio::test;

    #[tokio::test]
    async fn test_analyze_http_url() {
        let analyzer = LinkAnalyzerImpl::new();
        let url = "http://example.com/file.zip";
        let result = analyzer.analyze(url).await.unwrap();
        assert_eq!(result, PROTOCOL_HTTP);
    }

    #[tokio::test]
    async fn test_analyze_https_url() {
        let analyzer = LinkAnalyzerImpl::new();
        let url = "https://example.com/file.zip";
        let result = analyzer.analyze(url).await.unwrap();
        assert_eq!(result, PROTOCOL_HTTPS);
    }

    #[tokio::test]
    async fn test_analyze_torrent_url() {
        let analyzer = LinkAnalyzerImpl::new();
        let url = "https://example.com/file.torrent";
        let result = analyzer.analyze(url).await.unwrap();
        assert_eq!(result, PROTOCOL_BT);
    }

    #[tokio::test]
    async fn test_analyze_magnet_url() {
        let analyzer = LinkAnalyzerImpl::new();
        let url = "magnet:?xt=urn:btih:123456789";
        let result = analyzer.analyze(url).await.unwrap();
        assert_eq!(result, PROTOCOL_MAGNET);
    }

    #[tokio::test]
    async fn test_analyze_p2p_url() {
        let analyzer = LinkAnalyzerImpl::new();
        let url = "p2p://example.com/file.zip";
        let result = analyzer.analyze(url).await.unwrap();
        assert_eq!(result, PROTOCOL_P2P);
    }

    #[tokio::test]
    async fn test_analyze_r2_url() {
        let analyzer = LinkAnalyzerImpl::new();
        let url = "https://example.r2.dev/file.zip";
        let result = analyzer.analyze(url).await.unwrap();
        assert_eq!(result, PROTOCOL_R2);
    }

    #[tokio::test]
    async fn test_extract_file_name() {
        let analyzer = LinkAnalyzerImpl::new();
        let url = "https://example.com/path/to/file.zip";
        let result = analyzer.extract_file_name(url).unwrap();
        assert_eq!(result, "file.zip");
    }

    #[tokio::test]
    async fn test_extract_file_name_no_path() {
        let analyzer = LinkAnalyzerImpl::new();
        let url = "https://example.com/file.zip";
        let result = analyzer.extract_file_name(url).unwrap();
        assert_eq!(result, "file.zip");
    }

    #[tokio::test]
    async fn test_extract_file_name_with_query() {
        let analyzer = LinkAnalyzerImpl::new();
        let url = "https://example.com/file.zip?param=value";
        let result = analyzer.extract_file_name(url).unwrap();
        assert_eq!(result, "file.zip");
    }

    #[tokio::test]
    async fn test_follow_redirects() {
        let analyzer = LinkAnalyzerImpl::new();
        let server = MockServer::start();

        // 测试没有重定向的情况
        {
            // 创建一个模拟端点
            let mock = server.mock(|when, then| {
                when.method(GET).path("/no_redirect");
                then.status(200).body("No redirect");
            });

            // 获取URL
            let url = format!("{}/no_redirect", server.base_url());

            // 跟踪重定向
            let result = analyzer.clone().follow_redirects(&url).await.unwrap();

            // 验证结果
            assert_eq!(result.redirect_count, 0);
            assert_eq!(result.final_url, url);
            assert!(!result.is_redirect_loop);

            // 验证模拟端点被调用
            mock.assert();
        }

        // 测试单次重定向的情况
        {
            // 创建模拟端点
            let mock1 = server.mock(|when, then| {
                when.method(GET).path("/single_redirect");
                then.status(302)
                    .header("Location", format!("{}/destination", server.base_url()));
            });

            let mock2 = server.mock(|when, then| {
                when.method(GET).path("/destination");
                then.status(200).body("Destination");
            });

            // 获取URL
            let url = format!("{}/single_redirect", server.base_url());

            // 跟踪重定向
            let result = analyzer.clone().follow_redirects(&url).await.unwrap();

            // 验证结果
            assert_eq!(result.redirect_count, 1);
            assert_eq!(result.final_url, format!("{}/destination", server.base_url()));
            assert!(!result.is_redirect_loop);

            // 测试多次重定向的情况
            let mock3 = server.mock(|when, then| {
                when.method(GET).path("/multi_redirect_1");
                then.status(302)
                    .header("Location", format!("{}/multi_redirect_2", server.base_url()));
            });

            let mock4 = server.mock(|when, then| {
                when.method(GET).path("/multi_redirect_2");
                then.status(302)
                    .header("Location", format!("{}/multi_redirect_3", server.base_url()));
            });

            let mock5 = server.mock(|when, then| {
                when.method(GET).path("/multi_redirect_3");
                then.status(200).body("Multi Redirect Destination");
            });

            let url = format!("{}/multi_redirect_1", server.base_url());
            let result = analyzer.clone().follow_redirects(&url).await.unwrap();

            assert_eq!(result.redirect_count, 3);
            assert_eq!(result.final_url, format!("{}/multi_redirect_3", server.base_url()));
            assert!(!result.is_redirect_loop);

            mock3.assert();
            mock4.assert();
            mock5.assert();
        }

        // 测试重定向循环的情况
        {
            let mock1 = server.mock(|when, then| {
                when.method(GET).path("/loop_redirect_1");
                then.status(302)
                    .header("Location", format!("{}/loop_redirect_2", server.base_url()));
            });

            let mock2 = server.mock(|when, then| {
                when.method(GET).path("/loop_redirect_2");
                then.status(302)
                    .header("Location", format!("{}/loop_redirect_1", server.base_url()));
            });

            let url = format!("{}/loop_redirect_1", server.base_url());
            let result = analyzer.clone().follow_redirects(&url).await.unwrap();

            assert!(result.is_redirect_loop);
            assert_eq!(result.redirect_count, 10); // 默认最大重定向次数
            assert_eq!(result.final_url, url); // 循环会回到原始URL

            mock1.assert();
            mock2.assert();
        }

        // 测试最大重定向次数限制
        {
            let mock_redirect = server.mock(|when, then| {
                when.method(GET).path_matches(Regex::new("/redirect_limit_(\\d+)").unwrap());
                then.status(302)
                    .header("Location", |req: &Request| {
                        let path = req.url.path();
                        let num: u32 = path.trim_start_matches("/redirect_limit_").parse().unwrap();
                        format!("{}/redirect_limit_{}", server.base_url(), num + 1)
                    });
            });

            let mock_final = server.mock(|when, then| {
                when.method(GET).path("/redirect_limit_11"); // 超过10次重定向后的最终URL
                then.status(200).body("Final Destination");
            });

            let url = format!("{}/redirect_limit_1", server.base_url());
            let result = analyzer.clone().follow_redirects(&url).await.unwrap();

            assert_eq!(result.redirect_count, 10);
            assert_eq!(result.final_url, format!("{}/redirect_limit_11", server.base_url()));
            assert!(!result.is_redirect_loop);

            mock_redirect.assert_hits(10);
            mock_final.assert();
        }
    }
}
