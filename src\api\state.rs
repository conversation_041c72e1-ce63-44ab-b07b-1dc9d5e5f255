use std::sync::Arc;

use crate::api::events::EventManager;
use crate::api::websocket::WebSocketManager;
use crate::config::ConfigManager;
use crate::download::manager::DownloadManager;
use crate::download::bandwidth_scheduler::BandwidthScheduler;

/// 应用状态，包含所有共享依赖
#[derive(Clone)]
pub struct AppState {
    /// 下载管理器
    pub download_manager: Arc<dyn DownloadManager>,
    /// 带宽调度器
    pub bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
    /// 配置管理器
    pub config_manager: Option<Arc<ConfigManager>>,
    /// WebSocket管理器
    pub ws_manager: WebSocketManager,
    /// 事件管理器
    #[allow(dead_code)]
    pub event_manager: EventManager,
}

impl AppState {
    /// 创建新的应用状态
    #[allow(dead_code)]
    pub fn new(
        download_manager: Arc<dyn DownloadManager>,
        bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
        config_manager: Option<Arc<ConfigManager>>,
    ) -> Self {
        // 创建WebSocket管理器
        let ws_manager = WebSocketManager::new();

        // 创建事件管理器
        let event_manager = EventManager::new(ws_manager.get_sender());

        Self {
            download_manager,
            bandwidth_scheduler,
            config_manager,
            ws_manager,
            event_manager,
        }
    }
}
