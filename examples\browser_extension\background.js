// 浏览器扩展后台脚本

// 创建右键菜单
chrome.runtime.onInstalled.addListener(() => {
  // 为链接创建右键菜单项
  chrome.contextMenus.create({
    id: "downloadWithLumen",
    title: "使用Lumen下载",
    contexts: ["link"]
  });
  
  // 为媒体文件创建右键菜单项
  chrome.contextMenus.create({
    id: "downloadMediaWithLumen",
    title: "使用Lumen下载此媒体",
    contexts: ["image", "video", "audio"]
  });
  
  // 为页面创建右键菜单项
  chrome.contextMenus.create({
    id: "downloadPageWithLumen",
    title: "使用Lumen下载此页面",
    contexts: ["page"]
  });
});

// 处理右键菜单点击事件
chrome.contextMenus.onClicked.addListener((info, tab) => {
  let downloadUrl = "";
  
  // 根据不同的菜单项获取下载URL
  if (info.menuItemId === "downloadWithLumen" && info.linkUrl) {
    downloadUrl = info.linkUrl;
  } else if (info.menuItemId === "downloadMediaWithLumen" && info.srcUrl) {
    downloadUrl = info.srcUrl;
  } else if (info.menuItemId === "downloadPageWithLumen") {
    downloadUrl = info.pageUrl;
  }
  
  if (downloadUrl) {
    // 使用自定义协议打开Lumen下载管理器
    sendToLumen(downloadUrl);
  }
});

// 发送下载链接到Lumen下载管理器
function sendToLumen(url) {
  // 获取扩展配置
  chrome.storage.sync.get({
    useProtocol: true, // 默认使用协议
    lumenAppUrl: "http://localhost:9080" // 默认应用URL
  }, (config) => {
    try {
      if (config.useProtocol) {
        // 使用自定义协议
        const protocolUrl = `lumen://download?url=${encodeURIComponent(url)}`;
        
        // 尝试打开自定义协议URL
        chrome.tabs.create({ url: protocolUrl }, (tab) => {
          // 如果协议处理失败，浏览器可能会显示错误页面
          // 我们可以在短暂延迟后检查并关闭该标签页
          setTimeout(() => {
            chrome.tabs.get(tab.id, (tabInfo) => {
              // 如果标签页仍然存在且URL包含错误信息，则关闭它并尝试备用方法
              if (tabInfo && tabInfo.url.includes("ERR_UNKNOWN_URL_SCHEME")) {
                chrome.tabs.remove(tab.id);
                // 使用备用方法
                sendToLumenViaWebApp(url, config.lumenAppUrl);
              }
            });
          }, 500);
        });
      } else {
        // 直接使用Web应用URL
        sendToLumenViaWebApp(url, config.lumenAppUrl);
      }
    } catch (error) {
      console.error("发送到Lumen失败:", error);
      // 出错时尝试备用方法
      sendToLumenViaWebApp(url, config.lumenAppUrl);
    }
  });
}

// 通过Web应用URL发送下载链接
function sendToLumenViaWebApp(url, appUrl) {
  const lumenUrl = `${appUrl}/?url=${encodeURIComponent(url)}`;
  chrome.tabs.create({ url: lumenUrl });
}