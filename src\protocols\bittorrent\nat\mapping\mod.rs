// 端口映射模块
pub mod upnp;
pub mod natpmp;
pub mod pcp;
pub mod lifetime_manager;

use std::net::{IpAddr, SocketAddr};
use std::time::Duration;
use anyhow::Result;
use async_trait::async_trait;

/// 协议类型
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum Protocol {
    /// UDP协议
    UDP,
    /// TCP协议
    TCP,
}

impl std::fmt::Display for Protocol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Protocol::UDP => write!(f, "UDP"),
            Protocol::TCP => write!(f, "TCP"),
        }
    }
}

/// 映射信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct MappingInfo {
    /// 内部地址
    pub internal_addr: SocketAddr,
    /// 外部地址
    pub external_addr: SocketAddr,
    /// 协议类型
    pub protocol: Protocol,
    /// 生命周期（秒）
    pub lifetime: u64,
    /// 创建时间
    pub created_at: std::time::Instant,
}

/// 端口映射接口
#[async_trait]
pub trait PortMapper: Send + Sync {
    /// 创建端口映射
    async fn create_mapping(&self, protocol: Protocol, internal_port: u16, external_port: u16, lifetime: Duration) -> Result<MappingInfo>;
    
    /// 删除端口映射
    async fn delete_mapping(&self, mapping: &MappingInfo) -> Result<()>;
    
    /// 更新端口映射生命周期
    async fn renew_mapping(&self, mapping: &mut MappingInfo, lifetime: Duration) -> Result<()>;
    
    /// 获取外部IP地址
    async fn get_external_address(&self) -> Result<IpAddr>;
}
