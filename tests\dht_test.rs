use std::net::{Ip<PERSON>ddr, Ipv4<PERSON>ddr, SocketAddr};
use std::sync::Arc;
use std::time::Duration;
use anyhow::Result;
use tokio::sync::Mutex;
use tokio::test;

use tonitru_downloader::config::ConfigManager;
use tonitru_downloader::core::p2p::dht::{DHT, DHTEvent, DHTEventListener};
use tonitru_downloader::protocols::bittorrent::dht::client::DHTClient;
use tonitru_downloader::protocols::bittorrent::dht::config::DHTClientConfig;
use tonitru_downloader::protocols::bittorrent::dht::routing::RoutingTableConfig;

// 测试事件监听器
struct TestDHTEventListener {
    events: Arc<Mutex<Vec<DHTEvent>>>,
}

impl TestDHTEventListener {
    fn new() -> Self {
        Self {
            events: Arc::new(Mutex::new(Vec::new())),
        }
    }

    async fn get_events(&self) -> Vec<DHTEvent> {
        self.events.lock().await.clone()
    }
}

#[async_trait::async_trait]
impl DHTEventListener for TestDHTEventListener {
    async fn on_event(&self, event: DHTEvent) -> Result<()> {
        self.events.lock().await.push(event);
        Ok(())
    }
}

// 创建测试用的DHT客户端配置
fn create_test_config() -> DHTClientConfig {
    // 使用一些公共的DHT引导节点
    let bootstrap_nodes = vec![
        // 使用更多的公共DHT引导节点，增加成功率
        SocketAddr::new(IpAddr::V4(Ipv4Addr::new(67, 215, 246, 10)), 6881), // router.bittorrent.com
        SocketAddr::new(IpAddr::V4(Ipv4Addr::new(87, 98, 162, 88)), 6881),  // dht.transmissionbt.com
        SocketAddr::new(IpAddr::V4(Ipv4Addr::new(82, 221, 103, 244)), 6881), // router.utorrent.com
        SocketAddr::new(IpAddr::V4(Ipv4Addr::new(23, 137, 251, 10)), 6881), // dht.libtorrent.org
        SocketAddr::new(IpAddr::V4(Ipv4Addr::new(212, 129, 33, 59)), 6881), // dht.aelitis.com
        SocketAddr::new(IpAddr::V4(Ipv4Addr::new(208, 83, 20, 20)), 6881), // router.utorrent.com alternative
        SocketAddr::new(IpAddr::V4(Ipv4Addr::new(91, 121, 60, 42)), 6881), // router.transmission.com
    ];

    DHTClientConfig {
        port: 0, // 使用随机端口
        bootstrap_nodes,
        routing_table_config: RoutingTableConfig::default(),
        query_timeout: 10,
        parallel_queries: 3,
        enable_ipv6: true, // 启用IPv6支持
    }
}

#[test]
async fn test_dht_client_init() -> Result<()> {
    let config = create_test_config();
    let mut client = DHTClient::new(config).await?;

    // 初始化客户端
    client.init().await?;

    // 验证客户端已初始化
    let status = client.get_status().await?;
    assert!(status.initialized);
    assert!(!status.running);

    Ok(())
}

#[test]
async fn test_dht_client_start_stop() -> Result<()> {
    let config = create_test_config();
    let mut client = DHTClient::new(config).await?;

    // 启动客户端
    match client.start().await {
        Ok(_) => {
            // 验证客户端已启动
            let status = client.get_status().await?;
            assert!(status.initialized);
            assert!(status.running);

            // 停止客户端
            client.stop().await?;

            // 验证客户端已停止
            let status = client.get_status().await?;
            assert!(!status.running);
        },
        Err(e) => {
            // 在测试环境中，引导过程可能会失败，这是正常的
            // 我们只需要确保客户端能够正确初始化
            println!("Bootstrap failed (expected in some environments): {}", e);

            // 验证客户端已初始化
            let status = client.get_status().await?;
            assert!(status.initialized);
        }
    }

    Ok(())
}

#[test]
async fn test_dht_client_bootstrap() -> Result<()> {
    let config = create_test_config();
    let mut client = DHTClient::new(config).await?;

    // 添加事件监听器
    let listener = Arc::new(TestDHTEventListener::new());
    client.add_event_listener(listener.clone()).await?;

    // 启动客户端
    match client.start().await {
        Ok(_) => {
            // 等待一段时间，让引导过程完成
            tokio::time::sleep(Duration::from_secs(5)).await;

            // 验证客户端状态
            let status = client.get_status().await?;
            assert!(status.initialized);
            assert!(status.running);

            // 验证路由表中有节点
            assert!(status.node_count > 0);

            // 验证收到了状态变更事件
            let events = listener.get_events().await;
            assert!(!events.is_empty());

            // 至少应该有一个NodeStateChanged事件
            let has_node_state_changed = events.iter().any(|event| {
                if let DHTEvent::NodeStateChanged { bootstrapped, .. } = event {
                    *bootstrapped
                } else {
                    false
                }
            });
            assert!(has_node_state_changed);

            // 停止客户端
            client.stop().await?;
        },
        Err(e) => {
            // 在测试环境中，引导过程可能会失败，这是正常的
            println!("Bootstrap failed (expected in some environments): {}", e);

            // 验证客户端已初始化
            let status = client.get_status().await?;
            assert!(status.initialized);

            // 验证收到了事件
            let events = listener.get_events().await;
            println!("Received {} events", events.len());
        }
    }

    Ok(())
}

#[test]
async fn test_dht_with_config_manager() -> Result<()> {
    // 创建配置管理器
    let config_manager = ConfigManager::new().await?;

    // 获取DHT设置
    let (enable_dht, dht_port, bootstrap_nodes) = config_manager.get_dht_settings().await;

    // 验证DHT设置
    assert!(enable_dht);
    assert!(dht_port > 0);
    assert!(!bootstrap_nodes.is_empty());

    // 创建DHT客户端配置
    let bootstrap_addrs = bootstrap_nodes.iter()
        .filter_map(|node| {
            let parts: Vec<&str> = node.split(':').collect();
            if parts.len() == 2 {
                if let (Ok(ip), Ok(port)) = (parts[0].parse::<Ipv4Addr>(), parts[1].parse::<u16>()) {
                    Some(SocketAddr::new(IpAddr::V4(ip), port))
                } else {
                    None
                }
            } else {
                None
            }
        })
        .collect::<Vec<_>>();

    let config = DHTClientConfig {
        port: dht_port,
        bootstrap_nodes: bootstrap_addrs,
        routing_table_config: RoutingTableConfig::default(),
        query_timeout: 10,
        parallel_queries: 3,
        enable_ipv6: true, // 启用IPv6支持
    };

    // 创建DHT客户端
    let mut client = DHTClient::new(config).await?;

    // 初始化客户端
    client.init().await?;

    // 验证客户端已初始化
    let status = client.get_status().await?;
    assert!(status.initialized);

    Ok(())
}
