use crate::config::settings::ProxyConfig;
use super::error::ProxyError;

/// 代理检测器特征，定义了代理检测的核心功能
/// 
/// 所有具体的代理检测器都应实现此特征
pub trait ProxyDetector {
    /// 检测代理设置并返回代理配置
    /// 
    /// # 返回值
    /// 
    /// 成功时返回 `Ok(Option<ProxyConfig>)`，其中 `Option<ProxyConfig>` 表示检测到的代理配置（如果有）
    /// 失败时返回 `Err(ProxyError)` 表示检测过程中发生的错误
    fn detect(&self) -> Result<Option<ProxyConfig>, ProxyError>;
}

/// 将字符串形式的 no_proxy 列表转换为字符串向量
/// 
/// # 参数
/// 
/// * `no_proxy_str` - 逗号分隔的 no_proxy 字符串
/// 
/// # 返回值
/// 
/// 返回字符串向量，每个元素是一个不应使用代理的域名或 IP
pub fn parse_no_proxy(no_proxy_str: &str) -> Vec<String> {
    no_proxy_str
        .split(',')
        .map(|s| s.trim().to_string())
        .filter(|s| !s.is_empty())
        .collect()
}

/// 从 URL 中解析用户名和密码
/// 
/// # 参数
/// 
/// * `url` - 包含可能的认证信息的 URL 字符串
/// 
/// # 返回值
/// 
/// 返回元组 `(cleaned_url, username, password)`，其中：
/// - `cleaned_url` 是移除了认证信息的 URL
/// - `username` 是可选的用户名
/// - `password` 是可选的密码
pub fn parse_auth_from_url(url: &str) -> Result<(String, Option<String>, Option<String>), ProxyError> {
    match url::Url::parse(url) {
        Ok(mut url_parts) => {
            let username = if url_parts.username().is_empty() {
                None
            } else {
                Some(url_parts.username().to_string())
            };
            
            let password = url_parts.password().map(|s| s.to_string());

            // 移除 URL 中的用户名和密码
            if username.is_some() {
                url_parts.set_username("").map_err(|_| {
                    ProxyError::UrlParseError("无法从URL中移除用户名".to_string())
                })?;
            }
            
            if password.is_some() {
                url_parts.set_password(None).map_err(|_| {
                    ProxyError::UrlParseError("无法从URL中移除密码".to_string())
                })?;
            }

            Ok((url_parts.to_string(), username, password))
        },
        Err(e) => Err(ProxyError::UrlParseError(format!("无效的URL: {}, 错误: {}", url, e)))
    }
}