<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAppStore } from '../stores/app'

const appStore = useAppStore()

// 下载速度
const downloadSpeed = computed(() => appStore.formattedDownloadSpeed)

// 上传速度
const uploadSpeed = computed(() => appStore.formattedUploadSpeed)

// 进度
const progress = computed(() => {
  if (appStore.progress < 0) {
    return 0
  } else if (appStore.progress > 1) {
    return 100
  } else {
    return Math.floor(appStore.progress * 100)
  }
})

// 是否显示进度条
const showProgress = computed(() => appStore.progress >= 0 && appStore.progress <= 1)
</script>

<template>
  <div class="speedometer">
    <div class="speed-info">
      <div class="download-info">
        <el-icon><Download /></el-icon>
        <span>{{ downloadSpeed }}</span>
      </div>
      <div class="upload-info">
        <el-icon><Upload /></el-icon>
        <span>{{ uploadSpeed }}</span>
      </div>
    </div>
    <el-progress 
      v-if="showProgress" 
      :percentage="progress" 
      :show-text="false" 
      :stroke-width="2"
      class="progress-bar"
    />
  </div>
</template>

<style scoped>
.speedometer {
  position: fixed;
  right: 20px;
  bottom: 20px;
  background-color: var(--subnav-background);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 10px 15px;
  z-index: 100;
  min-width: 120px;
}

.speed-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.download-info, .upload-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: var(--subnav-text-color);
}

.download-info {
  margin-right: 15px;
}

.download-info .el-icon, .upload-info .el-icon {
  margin-right: 5px;
}

.progress-bar {
  margin-top: 5px;
}
</style>
