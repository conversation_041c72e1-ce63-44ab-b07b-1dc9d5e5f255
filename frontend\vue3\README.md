# Lumen 下载管理器前端

这是 Lumen 下载管理器的前端项目，使用 Vue 3、TypeScript 和 Element Plus 构建。

## 技术栈

- **运行时**: [Bun](https://bun.sh/)
- **框架**: [Vue 3](https://vuejs.org/)
- **UI 库**: [Element Plus](https://element-plus.org/)
- **状态管理**: [Pinia](https://pinia.vuejs.org/)
- **路由**: [Vue Router](https://router.vuejs.org/)
- **HTTP 客户端**: [Axios](https://axios-http.com/)
- **构建工具**: [Vite](https://vitejs.dev/)
- **类型检查**: [TypeScript](https://www.typescriptlang.org/)
- **代码规范**: [ESLint](https://eslint.org/) + [Prettier](https://prettier.io/)
- **测试工具**: [Vitest](https://vitest.dev/) + [Playwright](https://playwright.dev/)

## 项目结构

```
src/
├── assets/         # 静态资源
├── components/     # 组件
│   ├── About/      # 关于面板相关组件
│   ├── Task/       # 任务相关组件
│   └── ...
├── router/         # 路由配置
├── stores/         # 状态管理
├── utils/          # 工具函数
│   ├── api.ts      # API 服务
│   └── websocket.ts # WebSocket 连接管理
├── views/          # 页面视图
├── App.vue         # 根组件
└── main.ts         # 入口文件
```

## 开发指南

### 环境要求

- [Bun](https://bun.sh/) >= 1.0.0
- [Node.js](https://nodejs.org/) >= 18.0.0 (可选)

### 安装依赖

```sh
bun install
```

### 开发模式

```sh
bun dev
# 或
bun start
```

这将启动开发服务器，默认地址为 http://localhost:9080

### 构建生产版本

```sh
bun run build
```

构建后的文件将位于 `dist` 目录中。

### 代码格式化

```sh
bun format
```

### 代码检查

```sh
bun lint
```

### 运行单元测试

```sh
bun test:unit
```

### 运行端到端测试

```sh
# 首次运行需要安装浏览器
bunx playwright install

# 运行端到端测试
bun test:e2e
```

## 与后端集成

前端通过 HTTP API 和 WebSocket 与后端通信：

- HTTP API: 用于常规的数据交互
- WebSocket: 用于实时事件通知

API 基础路径: `http://localhost:8080/api`
WebSocket 路径: `ws://localhost:8080/ws`

## 主题支持

支持亮色主题、暗色主题和跟随系统主题。
