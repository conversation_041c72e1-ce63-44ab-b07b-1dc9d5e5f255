use anyhow::Result;
use async_trait::async_trait;
use std::path::Path;
use std::sync::Arc;
use tokio::sync::Mutex;

use crate::core::p2p::piece::{PieceManager, PieceManagerFactory};
use crate::protocols::bittorrent::piece_manager::{BitTorrentPieceManager, PieceSelectionStrategy};
use super::torrent::TorrentInfo;

/// BitTorrent分片管理器工厂
pub struct BitTorrentPieceManagerFactory {
    /// 种子信息
    pub torrent_info: TorrentInfo,
}

impl BitTorrentPieceManagerFactory {
    /// 创建新的BitTorrent分片管理器工厂
    pub fn new(torrent_info: TorrentInfo) -> Self {
        Self {
            torrent_info,
        }
    }
}

#[async_trait]
impl PieceManagerFactory for BitTorrentPieceManagerFactory {
    async fn create_piece_manager(&self, file_path: &str) -> Result<Box<dyn PieceManager>> {
        // 创建BitTorrent分片管理器
        let piece_manager = BitTorrentPieceManager::new(self.torrent_info.clone(), file_path).await?;

        // 初始化分片管理器
        let mut boxed_manager: Box<dyn PieceManager> = Box::new(piece_manager);
        boxed_manager.init().await?;

        Ok(boxed_manager)
    }
}

/// 创建BitTorrent分片管理器
pub async fn create_piece_manager(torrent_info: TorrentInfo, output_path: impl AsRef<Path>) -> Result<Arc<Mutex<dyn PieceManager>>> {
    // 使用默认的RandomFirst策略创建分片管理器
    create_piece_manager_with_strategy(torrent_info, output_path, PieceSelectionStrategy::RandomFirst).await
}

/// 创建BitTorrent分片管理器，使用指定的分片选择策略
pub async fn create_piece_manager_with_strategy(
    torrent_info: TorrentInfo, 
    output_path: impl AsRef<Path>,
    initial_strategy: PieceSelectionStrategy
) -> Result<Arc<Mutex<dyn PieceManager>>> {
    // 创建BitTorrent分片管理器
    let mut piece_manager = BitTorrentPieceManager::new_with_strategy(torrent_info, output_path, initial_strategy).await?;

    // 初始化分片管理器
    piece_manager.init().await?;

    // 包装为Arc<Mutex<dyn PieceManager>>
    Ok(Arc::new(Mutex::new(piece_manager)))
}
