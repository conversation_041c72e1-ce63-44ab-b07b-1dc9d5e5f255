//! CLI后端工厂
//!
//! 提供创建CLI后端实例的工厂。

use std::sync::Arc;

use crate::config::ConfigManager;
use crate::download::manager::DownloadManager;
use crate::core::interfaces::storage::Storage;

use super::backend::CliBackend;
use super::backend_impl::Backend;

/// CLI后端工厂
pub struct BackendFactory;

impl BackendFactory {
    /// 创建CLI后端实例
    pub fn create_backend(
        download_manager: Arc<dyn DownloadManager>,
        config_manager: Arc<ConfigManager>,
        storage: Arc<dyn Storage>,
    ) -> Arc<dyn CliBackend> {
        Arc::new(Backend::new(download_manager, config_manager, storage))
    }
}