use std::net::SocketAddr;
use super::routing::RoutingTableConfig;
use crate::core::p2p::dht::DHTConfig;

/// DHT客户端配置
#[derive(Debug, Clone)]
pub struct DHTClientConfig {
    /// 监听端口
    pub port: u16,
    /// 引导节点列表
    pub bootstrap_nodes: Vec<SocketAddr>,
    /// 路由表配置
    pub routing_table_config: RoutingTableConfig,
    /// 查询超时时间（秒）
    pub query_timeout: u64,
    /// 并行查询数
    pub parallel_queries: usize,
    /// 是否启用IPv6
    pub enable_ipv6: bool,
}

impl Default for DHTClientConfig {
    fn default() -> Self {
        Self {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true, // 默认启用IPv6
        }
    }
}

impl DHTClientConfig {
    /// 创建新的DHT客户端配置
    pub fn new(port: u16, bootstrap_nodes: Vec<SocketAddr>) -> Self {
        Self {
            port,
            bootstrap_nodes,
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true, // 默认启用IPv6
        }
    }

    /// 设置路由表配置
    pub fn with_routing_table_config(mut self, config: RoutingTableConfig) -> Self {
        self.routing_table_config = config;
        self
    }

    /// 设置查询超时时间
    pub fn with_query_timeout(mut self, timeout: u64) -> Self {
        self.query_timeout = timeout;
        self
    }

    /// 设置并行查询数
    pub fn with_parallel_queries(mut self, parallel_queries: usize) -> Self {
        self.parallel_queries = parallel_queries;
        self
    }

    /// 设置是否启用IPv6
    pub fn with_ipv6(mut self, enable_ipv6: bool) -> Self {
        self.enable_ipv6 = enable_ipv6;
        self
    }

    /// 添加引导节点
    pub fn add_bootstrap_node(&mut self, node: SocketAddr) {
        if !self.bootstrap_nodes.contains(&node) {
            self.bootstrap_nodes.push(node);
        }
    }

    /// 清空引导节点
    pub fn clear_bootstrap_nodes(&mut self) {
        self.bootstrap_nodes.clear();
    }

    /// 从DHTConfig创建DHTClientConfig
    pub fn from_dht_config(config: DHTConfig) -> Self {
        // 解析引导节点
        let bootstrap_nodes = config.bootstrap_nodes
            .iter()
            .filter_map(|addr_str| {
                addr_str.parse::<SocketAddr>().ok()
            })
            .collect();

        // 创建路由表配置
        let routing_table_config = RoutingTableConfig {
            k: config.routing_table_size,
            node_timeout: config.node_timeout,
            max_failures: 3,
            replace_questionable: true,
        };

        Self {
            port: config.port,
            bootstrap_nodes,
            routing_table_config,
            query_timeout: config.query_timeout,
            parallel_queries: 3,
            enable_ipv6: config.enable_ipv6, // 使用配置中的IPv6设置
        }
    }
}
