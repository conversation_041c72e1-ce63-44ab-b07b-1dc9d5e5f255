use async_trait::async_trait;
use serde::{de::DeserializeOwned, Serialize};
use std::path::Path;

use crate::core::error::CoreResult;

/// 泛型配置操作接口
/// 
/// 该接口定义了配置操作的基本方法，包括泛型的get和set方法。
/// 注意：这些方法主要用于内部实现，外部代码通常应该使用ConfigManager提供的方法。
/// 
/// ConfigManager提供了两种方式获取和设置配置：
/// 1. get_value/set_value：基于字符串的简单方法，需要手动处理类型转换
/// 2. get<T>/set<T>：泛型方法，提供自动类型转换，更加类型安全
/// 
/// 示例代码可以参考 examples::config_usage_example 模块
#[async_trait]
pub trait ConfigOps: Send + Sync {
    /// Get a configuration value
    /// 
    /// 获取指定键的配置值，并尝试将其转换为请求的类型T
    /// 如果配置不存在，返回None
    #[allow(dead_code)]
    async fn get<T: DeserializeOwned + Send + Sync>(&self, key: &str) -> CoreResult<Option<T>>;

    /// Set a configuration value
    /// 
    /// 设置指定键的配置值，值会被序列化后存储
    async fn set<T: Serialize + Send + Sync>(&self, key: &str, value: T) -> CoreResult<()>;
}

/// Configuration interface
#[async_trait]
pub trait Config: ConfigOps {
    /// Remove a configuration value
    async fn remove(&self, key: &str) -> CoreResult<()>;

    /// Check if a configuration value exists
    async fn exists(&self, key: &str) -> CoreResult<bool>;

    /// Get all configuration keys
    async fn get_keys(&self) -> CoreResult<Vec<String>>;

    /// Get all configuration values as a JSON string
    async fn get_all_as_json(&self) -> CoreResult<String>;

    /// Load configuration from a file
    async fn load_from_file(&self, path: &Path) -> CoreResult<()>;

    /// Save configuration to a file
    async fn save_to_file(&self, path: &Path) -> CoreResult<()>;

    /// Reset configuration to default values
    async fn reset_to_default(&self) -> CoreResult<()>;
}

/// Configuration factory interface
#[async_trait]
pub trait ConfigFactory: Send + Sync {
    /// Create a configuration instance
    async fn create_config(&self) -> CoreResult<crate::config::config_impl::ConfigImpl>;
}

pub struct ConfigFactoryImpl;

#[async_trait]
impl ConfigFactory for ConfigFactoryImpl {
    async fn create_config(&self) -> CoreResult<crate::config::config_impl::ConfigImpl> {
        crate::config::config_impl::ConfigImpl::new().await
    }
}
