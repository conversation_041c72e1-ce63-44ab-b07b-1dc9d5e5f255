use anyhow::{Result, anyhow};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sha2::{Sha256, Digest};
use std::collections::{HashMap, HashSet};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::fs;
use tokio::io::{AsyncReadExt, AsyncSeekExt, BufReader};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};
use uuid::Uuid;

use crate::config::Settings;
use crate::core::interfaces::storage::Storage;
use crate::download::manager::TaskInfo;

/// 下载恢复点信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResumePoint {
    /// 任务ID
    pub task_id: Uuid,
    /// 下载URL
    pub url: String,
    /// 输出路径
    pub output_path: String,
    /// 已下载大小
    pub downloaded_size: u64,
    /// 总大小
    pub total_size: Option<u64>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 校验和
    pub checksum: Option<String>,
    /// 校验和算法
    pub checksum_algorithm: Option<String>,
    /// 分片信息
    pub chunks: Vec<ChunkInfo>,
    /// 元数据
    pub metadata: HashMap<String, String>,
}

/// 分片信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChunkInfo {
    /// 分片索引
    pub index: usize,
    /// 分片起始位置
    pub start: u64,
    /// 分片结束位置
    pub end: u64,
    /// 分片是否已下载
    pub downloaded: bool,
    /// 分片校验和
    pub checksum: Option<String>,
}

/// 恢复管理器接口
#[async_trait::async_trait]
pub trait ResumeManager: Send + Sync {
    /// 保存恢复点
    async fn save_resume_point(&self, task_info: &TaskInfo) -> Result<()>;

    /// 加载恢复点
    async fn load_resume_point(&self, task_id: Uuid) -> Result<Option<ResumePoint>>;

    /// 删除恢复点
    async fn delete_resume_point(&self, task_id: Uuid) -> Result<()>;

    /// 获取所有恢复点
    async fn get_all_resume_points(&self) -> Result<Vec<ResumePoint>>;

    /// 验证文件完整性
    async fn verify_file_integrity(&self, task_id: Uuid) -> Result<bool>;

    /// 验证分片完整性
    async fn verify_chunk_integrity(&self, task_id: Uuid, chunk_index: usize) -> Result<bool>;

    /// 暂停任务
    async fn pause_task(&self, task_id: &Uuid) -> Result<()>;

    /// 恢复任务
    async fn resume_task(&self, task_id: &Uuid) -> Result<()>;

    /// 检查任务是否已暂停
    async fn is_paused(&self, task_id: &Uuid) -> Result<bool>;
}

/// 恢复管理器实现
pub struct ResumeManagerImpl {
    /// 配置
    settings: Settings,
    /// 存储
    storage: Arc<dyn Storage>,
    /// 恢复点缓存
    resume_points: Arc<RwLock<HashMap<Uuid, ResumePoint>>>,
    /// 恢复点目录
    resume_dir: PathBuf,
    /// 暂停状态缓存
    paused_tasks: Arc<RwLock<HashSet<Uuid>>>,
}

impl ResumeManagerImpl {
    /// 创建新的恢复管理器
    pub fn new(settings: Settings, storage: Arc<dyn Storage>) -> Self {
        let resume_dir = PathBuf::from(&settings.download.path).join(".resume");

        Self {
            settings,
            storage,
            resume_points: Arc::new(RwLock::new(HashMap::new())),
            resume_dir,
            paused_tasks: Arc::new(RwLock::new(HashSet::new())),
        }
    }

    /// 获取恢复点文件路径
    fn get_resume_file_path(&self, task_id: Uuid) -> PathBuf {
        self.resume_dir.join(format!("{}.json", task_id))
    }

    /// 初始化恢复管理器
    pub async fn init(&self) -> Result<()> {
        // 创建恢复点目录
        if !self.resume_dir.exists() {
            fs::create_dir_all(&self.resume_dir).await?;
        }

        // 加载所有恢复点
        self.load_all_resume_points().await?;

        Ok(())
    }

    /// 加载所有恢复点
    async fn load_all_resume_points(&self) -> Result<()> {
        // 读取恢复点目录中的所有文件
        let mut entries = fs::read_dir(&self.resume_dir).await?;

        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();

            // 只处理JSON文件
            if path.extension().map_or(false, |ext| ext == "json") {
                if let Some(file_stem) = path.file_stem() {
                    if let Some(file_name) = file_stem.to_str() {
                        // 解析任务ID
                        if let Ok(task_id) = Uuid::parse_str(file_name) {
                            // 加载恢复点
                            if let Ok(Some(resume_point)) = self.load_resume_point(task_id).await {
                                // 添加到缓存
                                self.resume_points.write().await.insert(task_id, resume_point);
                            }
                        }
                    }
                }
            }
        }

        info!("Loaded {} resume points", self.resume_points.read().await.len());

        Ok(())
    }

    /// 计算文件的SHA256校验和
    async fn calculate_sha256_checksum(&self, file_path: &Path) -> Result<String> {
        // 打开文件
        let file = fs::File::open(file_path).await?;
        let mut reader = BufReader::new(file);

        // 创建SHA256哈希器
        let mut hasher = Sha256::new();
        let mut buffer = [0; 8192]; // 8KB缓冲区

        // 读取文件内容并更新哈希
        loop {
            let bytes_read = reader.read(&mut buffer).await?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }

        // 计算最终哈希值
        let result = hasher.finalize();

        // 将哈希值转换为十六进制字符串
        Ok(format!("{:x}", result))
    }

    /// 计算文件特定范围的SHA256校验和
    async fn calculate_chunk_sha256_checksum(&self, file_path: &Path, start: u64, end: u64) -> Result<String> {
        // 打开文件
        let file = fs::File::open(file_path).await?;
        let mut reader = BufReader::new(file);

        // 跳过开始位置之前的字节
        reader.seek(std::io::SeekFrom::Start(start)).await?;

        // 创建SHA256哈希器
        let mut hasher = Sha256::new();
        let mut buffer = [0; 8192]; // 8KB缓冲区
        let mut remaining = end - start + 1; // 需要读取的总字节数

        // 读取指定范围的内容并更新哈希
        while remaining > 0 {
            let to_read = std::cmp::min(remaining, buffer.len() as u64);
            let bytes_read = reader.read(&mut buffer[..to_read as usize]).await?;

            if bytes_read == 0 {
                break; // 文件结束
            }

            hasher.update(&buffer[..bytes_read]);
            remaining -= bytes_read as u64;
        }

        // 计算最终哈希值
        let result = hasher.finalize();

        // 将哈希值转换为十六进制字符串
        Ok(format!("{:x}", result))
    }
}

#[async_trait::async_trait]
impl ResumeManager for ResumeManagerImpl {
    async fn save_resume_point(&self, task_info: &TaskInfo) -> Result<()> {
        // 创建恢复点
        let mut resume_point = ResumePoint {
            task_id: task_info.id,
            url: task_info.url.clone(),
            output_path: task_info.output_path.clone(),
            downloaded_size: task_info.downloaded_size,
            total_size: task_info.total_size,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            checksum: None,
            checksum_algorithm: None,
            chunks: Vec::new(),
            metadata: HashMap::new(),
        };

        // 保存上传字节数到元数据
        resume_point.metadata.insert("uploaded_bytes".to_string(), task_info.uploaded_bytes.to_string());

        // 序列化恢复点
        let json = serde_json::to_string_pretty(&resume_point)?;

        // 保存恢复点
        let resume_file = self.get_resume_file_path(task_info.id);

        // 创建父目录
        if let Some(parent) = resume_file.parent() {
            fs::create_dir_all(parent).await?;
        }

        // 写入文件
        fs::write(&resume_file, json).await?;

        // 更新缓存
        self.resume_points.write().await.insert(task_info.id, resume_point);

        debug!("Saved resume point for task: {}", task_info.id);

        Ok(())
    }

    async fn load_resume_point(&self, task_id: Uuid) -> Result<Option<ResumePoint>> {
        // 先从缓存中查找
        if let Some(resume_point) = self.resume_points.read().await.get(&task_id) {
            return Ok(Some(resume_point.clone()));
        }

        // 从文件中加载
        let resume_file = self.get_resume_file_path(task_id);

        if !resume_file.exists() {
            return Ok(None);
        }

        // 读取文件
        let json = fs::read_to_string(&resume_file).await?;

        // 反序列化
        let resume_point: ResumePoint = serde_json::from_str(&json)?;

        // 更新缓存
        self.resume_points.write().await.insert(task_id, resume_point.clone());

        Ok(Some(resume_point))
    }

    async fn delete_resume_point(&self, task_id: Uuid) -> Result<()> {
        // 从缓存中删除
        self.resume_points.write().await.remove(&task_id);

        // 从文件系统中删除
        let resume_file = self.get_resume_file_path(task_id);

        if resume_file.exists() {
            fs::remove_file(&resume_file).await?;
        }

        debug!("Deleted resume point for task: {}", task_id);

        Ok(())
    }

    async fn get_all_resume_points(&self) -> Result<Vec<ResumePoint>> {
        let resume_points = self.resume_points.read().await;

        Ok(resume_points.values().cloned().collect())
    }

    async fn verify_file_integrity(&self, task_id: Uuid) -> Result<bool> {
        // 获取恢复点
        let resume_point = match self.load_resume_point(task_id).await? {
            Some(rp) => rp,
            None => return Err(anyhow!("Resume point not found for task: {}", task_id)),
        };

        // 如果没有校验和，无法验证
        if resume_point.checksum.is_none() || resume_point.checksum_algorithm.is_none() {
            debug!("No checksum or algorithm available for task: {}", task_id);
            return Ok(false);
        }

        let checksum = resume_point.checksum.unwrap();
        let algorithm = resume_point.checksum_algorithm.unwrap();
        let file_path = Path::new(&resume_point.output_path);

        // 检查文件是否存在
        if !file_path.exists() {
            warn!("File does not exist: {}", file_path.display());
            return Ok(false);
        }

        // 根据校验和算法计算文件的校验和
        match algorithm.to_lowercase().as_str() {
            "sha256" => {
                let file_checksum = self.calculate_sha256_checksum(file_path).await?;
                debug!("Calculated SHA256 checksum: {}", file_checksum);
                debug!("Expected SHA256 checksum: {}", checksum);
                Ok(file_checksum == checksum)
            },
            // 可以添加其他校验和算法的支持
            _ => {
                warn!("Unsupported checksum algorithm: {}", algorithm);
                Ok(false)
            }
        }
    }



    async fn verify_chunk_integrity(&self, task_id: Uuid, chunk_index: usize) -> Result<bool> {
        // 获取恢复点
        let resume_point = match self.load_resume_point(task_id).await? {
            Some(rp) => rp,
            None => return Err(anyhow!("Resume point not found for task: {}", task_id)),
        };

        // 查找分片
        let chunk = resume_point.chunks.iter().find(|c| c.index == chunk_index);

        match chunk {
            Some(chunk) => {
                // 如果没有校验和，无法验证
                if chunk.checksum.is_none() {
                    debug!("No checksum available for chunk: {}", chunk_index);
                    return Ok(false);
                }

                let checksum = chunk.checksum.as_ref().unwrap();
                let file_path = Path::new(&resume_point.output_path);

                // 检查文件是否存在
                if !file_path.exists() {
                    warn!("File does not exist: {}", file_path.display());
                    return Ok(false);
                }

                // 计算分片的校验和
                let chunk_checksum = self.calculate_chunk_sha256_checksum(
                    file_path,
                    chunk.start,
                    chunk.end
                ).await?;

                debug!("Calculated chunk SHA256 checksum: {}", chunk_checksum);
                debug!("Expected chunk SHA256 checksum: {}", checksum);

                Ok(chunk_checksum == *checksum)
            },
            None => Err(anyhow!("Chunk not found: index={}", chunk_index)),
        }
    }

    async fn pause_task(&self, task_id: &Uuid) -> Result<()> {
        // 将任务ID添加到暂停任务集合中
        let mut paused_tasks = self.paused_tasks.write().await;
        paused_tasks.insert(*task_id);

        debug!("Task paused in resume manager: {}", task_id);

        Ok(())
    }

    async fn resume_task(&self, task_id: &Uuid) -> Result<()> {
        // 从暂停任务集合中移除任务ID
        let mut paused_tasks = self.paused_tasks.write().await;
        paused_tasks.remove(task_id);

        debug!("Task resumed in resume manager: {}", task_id);

        Ok(())
    }

    async fn is_paused(&self, task_id: &Uuid) -> Result<bool> {
        // 检查任务ID是否在暂停任务集合中
        let paused_tasks = self.paused_tasks.read().await;
        let is_paused = paused_tasks.contains(task_id);

        Ok(is_paused)
    }
}

/// 基于文件的恢复管理器，是ResumeManagerImpl的包装器
pub struct FileResumeManager {
    /// 内部实现
    inner: ResumeManagerImpl,
}

impl FileResumeManager {
    /// 创建新的基于文件的恢复管理器
    pub async fn new(resume_dir: String) -> Result<Self> {
        // 创建恢复目录
        let resume_path = PathBuf::from(&resume_dir);
        if !resume_path.exists() {
            fs::create_dir_all(&resume_path).await?;
        }

        // 创建默认设置
        let mut settings = Settings::default();
        settings.download.path = resume_dir.clone();

        // 创建内部实现
        let inner = ResumeManagerImpl::new(
            settings.clone(),
            Arc::new(crate::storage::storage_impl::LocalStorage::new(settings.download.path.clone()))
        );

        // 初始化
        inner.init().await?;

        Ok(Self { inner })
    }
}

#[async_trait::async_trait]
impl ResumeManager for FileResumeManager {
    async fn save_resume_point(&self, task_info: &TaskInfo) -> Result<()> {
        self.inner.save_resume_point(task_info).await
    }

    async fn load_resume_point(&self, task_id: Uuid) -> Result<Option<ResumePoint>> {
        self.inner.load_resume_point(task_id).await
    }

    async fn delete_resume_point(&self, task_id: Uuid) -> Result<()> {
        self.inner.delete_resume_point(task_id).await
    }

    async fn get_all_resume_points(&self) -> Result<Vec<ResumePoint>> {
        self.inner.get_all_resume_points().await
    }

    async fn verify_file_integrity(&self, task_id: Uuid) -> Result<bool> {
        self.inner.verify_file_integrity(task_id).await
    }

    async fn verify_chunk_integrity(&self, task_id: Uuid, chunk_index: usize) -> Result<bool> {
        self.inner.verify_chunk_integrity(task_id, chunk_index).await
    }

    async fn pause_task(&self, task_id: &Uuid) -> Result<()> {
        self.inner.pause_task(task_id).await
    }

    async fn resume_task(&self, task_id: &Uuid) -> Result<()> {
        self.inner.resume_task(task_id).await
    }

    async fn is_paused(&self, task_id: &Uuid) -> Result<bool> {
        self.inner.is_paused(task_id).await
    }
}
