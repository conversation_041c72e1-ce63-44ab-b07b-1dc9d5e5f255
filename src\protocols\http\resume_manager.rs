//! HTTP下载器断点续传管理模块

use std::path::Path;
use anyhow::Result;
use tracing::debug;

use crate::download::manager::{TaskInfo, TaskStatus};
use crate::core::interfaces::Downloader;

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// 加载恢复点
    pub(crate) async fn load_resume_point(&mut self) -> Result<u64> {
        if let Some(resume_manager) = &self.resume_manager {
            // 尝试加载恢复点
            if let Some(resume_point) = resume_manager.load_resume_point(self.task_id).await? {
                // 检查URL是否匹配
                if resume_point.url == self.url {
                    debug!("Found resume point for URL: {}", self.url);

                    // 确定临时文件路径
                    let temp_file_path = if resume_point.output_path.ends_with(&self.temp_file_extension) {
                        // 恢复点中已经是临时文件路径
                        resume_point.output_path.clone()
                    } else {
                        // 恢复点中是最终文件路径，需要转换为临时文件路径
                        self.get_temp_file_path(&resume_point.output_path)
                    };

                    // 验证临时文件是否存在
                    let temp_path = Path::new(&temp_file_path);
                    if !temp_path.exists() {
                        debug!("Temporary file does not exist: {}, starting from beginning", temp_file_path);
                        return Ok(0);
                    }

                    // 验证临时文件大小是否与恢复点记录一致
                    match tokio::fs::metadata(&temp_path).await {
                        Ok(metadata) => {
                            let file_size = metadata.len();
                            if file_size != resume_point.downloaded_size {
                                debug!("File size mismatch: expected {}, actual {}, starting from beginning",
                                      resume_point.downloaded_size, file_size);
                                // 文件大小不匹配，可能文件已损坏，从头开始下载
                                return Ok(0);
                            }

                            // 如果有总大小信息，检查是否已经完成下载
                            if let Some(total_size) = resume_point.total_size {
                                if file_size >= total_size {
                                    debug!("File appears to be complete, size: {}", file_size);
                                    // 文件已完成，但可能没有重命名，这里返回完整大小
                                    return Ok(file_size);
                                }
                            }

                            debug!("Resume point validated: {} bytes downloaded", file_size);

                            // 保存恢复点信息
                            self.resume_point = Some(resume_point.clone());

                            return Ok(file_size);
                        },
                        Err(e) => {
                            debug!("Failed to get file metadata for {}: {}, starting from beginning", temp_file_path, e);
                            return Ok(0);
                        }
                    }
                } else {
                    debug!("URL mismatch in resume point: expected {}, found {}", self.url, resume_point.url);
                }
            } else {
                debug!("No resume point found for task: {}", self.task_id);
            }
        }

        Ok(0)
    }

    /// 保存恢复点
    pub(crate) async fn save_resume_point(&self) -> Result<()> {
        if let Some(resume_manager) = &self.resume_manager {
            // 获取实际输出路径
            let output_path = if Path::new(&self.output_path).is_absolute() {
                self.output_path.clone()
            } else {
                // 如果是相对路径，则相对于下载目录
                let settings = self.config_manager.get_settings().await;
                let download_dir = Path::new(&settings.download.path);
                let full_path = download_dir.join(&self.output_path);
                full_path.to_string_lossy().to_string()
            };
            
            // 保存临时文件路径而不是最终文件路径
            let temp_file_path = self.get_temp_file_path(&output_path);
            
            // 创建任务信息
            let task_info = TaskInfo {
                id: self.task_id,
                url: self.url.clone(),
                output_path: temp_file_path, // 保存临时文件路径
                status: TaskStatus::Paused,
                progress: self.progress().await?.progress_percentage,
                speed: self.current_speed,
                total_size: self.total_size,
                downloaded_size: self.downloaded_size,
                uploaded_bytes: 0, // HTTP 下载协议通常不上传数据，初始化为0
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
                error_message: None,
            };

            // 保存恢复点
            resume_manager.save_resume_point(&task_info).await?
        }

        Ok(())
    }

    /// 验证恢复点完整性
    pub(crate) async fn verify_resume_point_integrity(&self, temp_file_path: &str) -> Result<bool> {
        debug!("Verifying resume point integrity for task {}", self.task_id);

        // 检查是否有恢复管理器
        let resume_manager = match &self.resume_manager {
            Some(rm) => rm,
            None => {
                debug!("No resume manager available");
                return Ok(false);
            }
        };

        // 检查临时文件是否存在
        let temp_path = Path::new(temp_file_path);
        if !temp_path.exists() {
            debug!("Temporary file does not exist: {}", temp_file_path);
            return Ok(false);
        }

        // 获取文件大小
        let file_size = match tokio::fs::metadata(temp_path).await {
            Ok(metadata) => metadata.len(),
            Err(e) => {
                debug!("Failed to get file metadata: {}", e);
                return Ok(false);
            }
        };

        // 检查文件大小是否与下载器记录一致
        if file_size != self.downloaded_size {
            debug!("File size mismatch: expected {}, actual {}", self.downloaded_size, file_size);
            return Ok(false);
        }

        // 如果有恢复点信息，进行更详细的验证
        if let Some(resume_point) = &self.resume_point {
            // 验证下载大小
            if resume_point.downloaded_size != file_size {
                debug!("Resume point size mismatch: expected {}, actual {}", resume_point.downloaded_size, file_size);
                return Ok(false);
            }

            // 验证URL
            if resume_point.url != self.url {
                debug!("URL mismatch in resume point");
                return Ok(false);
            }

            // 如果有校验和信息，进行文件完整性验证
            if resume_point.checksum.is_some() && resume_point.checksum_algorithm.is_some() {
                match resume_manager.verify_file_integrity(self.task_id).await {
                    Ok(is_valid) => {
                        if !is_valid {
                            debug!("File integrity verification failed");
                            return Ok(false);
                        }
                    },
                    Err(e) => {
                        debug!("Failed to verify file integrity: {}", e);
                        // 不返回错误，继续下载
                    }
                }
            }
        }

        debug!("Resume point integrity verification passed");
        Ok(true)
    }

    /// 验证恢复下载的一致性
    pub(crate) async fn validate_resume_consistency(&mut self, resume_offset: u64) -> Result<bool> {
        debug!("Validating resume consistency for offset: {}", resume_offset);

        // 如果没有恢复偏移量，无需验证
        if resume_offset == 0 {
            return Ok(true);
        }

        // 检查服务器文件是否发生变化
        if let Some(current_total_size) = self.total_size {
            if let Some(resume_point) = &self.resume_point {
                if let Some(original_total_size) = resume_point.total_size {
                    if current_total_size != original_total_size {
                        debug!("File size changed: original {}, current {}",
                              original_total_size, current_total_size);
                        return Ok(false);
                    }
                }
            }
        }

        // 如果支持范围请求，验证恢复点是否有效
        if self.supports_range && resume_offset > 0 {
            // 尝试从恢复点开始请求一小段数据来验证
            match self.test_resume_point(resume_offset).await {
                Ok(is_valid) => {
                    if !is_valid {
                        debug!("Resume point validation failed");
                        return Ok(false);
                    }
                },
                Err(e) => {
                    debug!("Failed to test resume point: {}", e);
                    return Ok(false);
                }
            }
        }

        debug!("Resume consistency validation passed");
        Ok(true)
    }

    /// 测试恢复点是否有效
    async fn test_resume_point(&self, resume_offset: u64) -> Result<bool> {
        debug!("Testing resume point at offset: {}", resume_offset);

        // 请求从恢复点开始的少量数据（比如1KB）
        let test_size = std::cmp::min(1024, self.chunk_size);
        let end_offset = resume_offset + test_size - 1;

        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(reqwest::header::USER_AGENT, "Tonitru-Downloader/1.0".parse().unwrap());
        headers.insert(reqwest::header::RANGE,
                      format!("bytes={}-{}", resume_offset, end_offset).parse().unwrap());

        match self.client.get_with_headers(&self.url, headers).await {
            Ok(response) => {
                let status = response.status();
                debug!("Resume point test response status: {}", status);

                if status == reqwest::StatusCode::PARTIAL_CONTENT {
                    // 验证Content-Range头
                    if let Some(content_range) = response.headers().get(reqwest::header::CONTENT_RANGE) {
                        if let Ok(range_str) = content_range.to_str() {
                            debug!("Resume test Content-Range: {}", range_str);
                            // 检查范围是否正确
                            return Ok(range_str.contains(&format!("{}-", resume_offset)));
                        }
                    }
                    Ok(true)
                } else {
                    debug!("Resume point test failed with status: {}", status);
                    Ok(false)
                }
            },
            Err(e) => {
                debug!("Resume point test request failed: {}", e);
                Ok(false)
            }
        }
    }
}
