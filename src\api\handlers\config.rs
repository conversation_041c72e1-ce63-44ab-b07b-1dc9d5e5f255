use axum::{
    extract::{State, Path},
    Json,
};
use tracing::{debug, info};
use serde::{Deserialize, Serialize};

use crate::api::response::{ApiResponse, ApiError};
use crate::config::Settings;

/// 配置查询参数
#[derive(Debug, Deserialize)]
pub struct ConfigQueryParams {
    /// 配置键前缀过滤
    #[allow(dead_code)]
    pub prefix: Option<String>,
}

/// 配置更新请求
#[derive(Debug, Deserialize)]
pub struct ConfigUpdateRequest {
    /// 配置键
    #[allow(dead_code)]
    pub key: String,
    /// 配置值
    #[allow(dead_code)]
    pub value: serde_json::Value,
}

/// 配置项响应
#[derive(Debug, Serialize)]
pub struct ConfigItemResponse {
    /// 配置键
    pub key: String,
    /// 配置值
    pub value: serde_json::Value,
}

/// 获取系统配置
use crate::api::state::AppState;

pub async fn get_config(
    State(app_state): State<AppState>,
) -> Result<Json<ApiResponse<Settings>>, ApiError> {
    let config_manager = app_state.config_manager.ok_or_else(|| ApiError::internal_error("Config manager not available"))?;
    debug!("Getting system configuration");

    // 获取配置
    let settings = config_manager.get_settings().await;

    // 返回响应
    Ok(Json(ApiResponse::success(settings)))
}

/// 更新系统配置
pub async fn update_config(
    State(app_state): State<AppState>,
    Json(request): Json<Settings>,
) -> Result<Json<ApiResponse<Settings>>, ApiError> {
    let config_manager = app_state.config_manager.ok_or_else(|| ApiError::internal_error("Config manager not available"))?;
    info!("Updating system configuration");

    // 更新配置
    config_manager.update_settings(request).await;

    // 保存配置到文件
    if let Err(e) = config_manager.save_to_file(None::<&str>).await {
        return Err(ApiError::internal_error(format!("Failed to save configuration: {}", e)));
    }

    // 获取更新后的配置
    let updated_settings = config_manager.get_settings().await;

    // 返回响应
    Ok(Json(ApiResponse::success(updated_settings)))
}

/// 获取特定配置项
pub async fn get_config_item(
    State(app_state): State<AppState>,
    Path(key): Path<String>,
) -> Result<Json<ApiResponse<ConfigItemResponse>>, ApiError> {
    let config_manager = app_state.config_manager.ok_or_else(|| ApiError::internal_error("Config manager not available"))?;
    debug!("Getting configuration item: key={}", key);

    // 获取配置
    let settings = config_manager.get_settings().await;

    // 根据键获取值
    let value = match key.as_str() {
        "server.host" => serde_json::to_value(settings.server.host)
            .map_err(|e| ApiError::internal_error(format!("Failed to serialize value: {}", e)))?,
        "server.port" => serde_json::to_value(settings.server.port)
            .map_err(|e| ApiError::internal_error(format!("Failed to serialize value: {}", e)))?,
        "download.path" => serde_json::to_value(settings.download.path)
            .map_err(|e| ApiError::internal_error(format!("Failed to serialize value: {}", e)))?,
        "download.concurrent_downloads" => serde_json::to_value(settings.download.concurrent_downloads)
            .map_err(|e| ApiError::internal_error(format!("Failed to serialize value: {}", e)))?,
        "download.connections_per_download" => serde_json::to_value(settings.download.connections_per_download)
            .map_err(|e| ApiError::internal_error(format!("Failed to serialize value: {}", e)))?,
        "download.chunk_size" => serde_json::to_value(settings.download.chunk_size)
            .map_err(|e| ApiError::internal_error(format!("Failed to serialize value: {}", e)))?,
        "download.buffer_size" => serde_json::to_value(settings.download.buffer_size)
            .map_err(|e| ApiError::internal_error(format!("Failed to serialize value: {}", e)))?,
        "download.speed_limit" => serde_json::to_value(settings.download.speed_limit)
            .map_err(|e| ApiError::internal_error(format!("Failed to serialize value: {}", e)))?,
        _ => return Err(ApiError::not_found(format!("Configuration key not found: {}", key))),
    };

    // 返回响应
    Ok(Json(ApiResponse::success(ConfigItemResponse {
        key,
        value,
    })))
}

/// 更新特定配置项
pub async fn update_config_item(
    State(app_state): State<AppState>,
    Path(key): Path<String>,
    Json(request): Json<serde_json::Value>,
) -> Result<Json<ApiResponse<ConfigItemResponse>>, ApiError> {
    let config_manager = app_state.config_manager.ok_or_else(|| ApiError::internal_error("Config manager not available"))?;
    info!("Updating configuration item: key={}", key);

    // 获取当前配置
    let mut settings = config_manager.get_settings().await;

    // 根据键更新值
    match key.as_str() {
        "server.host" => {
            let value = request.as_str()
                .ok_or_else(|| ApiError::bad_request("Value must be a string"))?;
            settings.server.host = value.to_string();
        },
        "server.port" => {
            let value = request.as_u64()
                .ok_or_else(|| ApiError::bad_request("Value must be a positive integer"))?;
            settings.server.port = value as u16;
        },
        "download.path" => {
            let value = request.as_str()
                .ok_or_else(|| ApiError::bad_request("Value must be a string"))?;
            settings.download.path = value.to_string();
        },
        "download.concurrent_downloads" => {
            let value = request.as_i64()
                .ok_or_else(|| ApiError::bad_request("Value must be an integer"))?;
            settings.download.concurrent_downloads = value;
        },
        "download.connections_per_download" => {
            let value = request.as_i64()
                .ok_or_else(|| ApiError::bad_request("Value must be an integer"))?;
            settings.download.connections_per_download = value;
        },
        "download.chunk_size" => {
            let value = request.as_i64()
                .ok_or_else(|| ApiError::bad_request("Value must be an integer"))?;
            settings.download.chunk_size = value;
        },
        "download.buffer_size" => {
            let value = request.as_i64()
                .ok_or_else(|| ApiError::bad_request("Value must be an integer"))?;
            settings.download.buffer_size = value;
        },
        "download.speed_limit" => {
            let value = if request.is_null() {
                None
            } else {
                Some(request.as_u64()
                    .ok_or_else(|| ApiError::bad_request("Value must be a positive integer or null"))?)
            };
            settings.download.speed_limit = value;
        },
        _ => return Err(ApiError::not_found(format!("Configuration key not found: {}", key))),
    };

    // 更新配置
    config_manager.update_settings(settings.clone()).await;
    
    // 保存配置到文件
    if let Err(e) = config_manager.save_to_file(None::<&str>).await {
        return Err(ApiError::internal_error(format!("Failed to save configuration: {}", e)));
    }

    // 返回响应
    Ok(Json(ApiResponse::success(ConfigItemResponse {
        key,
        value: request,
    })))
}

/// 重置配置到默认值
pub async fn reset_config(
    State(app_state): State<AppState>,
) -> Result<Json<ApiResponse<Settings>>, ApiError> {
    let config_manager = app_state.config_manager.ok_or_else(|| ApiError::internal_error("Config manager not available"))?;
    info!("Resetting configuration to default values");

    // 重置配置到默认值
    if let Err(e) = config_manager.reset_to_default().await {
        return Err(ApiError::internal_error(format!("Failed to reset configuration: {}", e)));
    }

    // 获取更新后的配置
    let updated_settings = config_manager.get_settings().await;

    // 返回响应
    Ok(Json(ApiResponse::success(updated_settings)))
}
