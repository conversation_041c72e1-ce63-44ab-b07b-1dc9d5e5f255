pub mod args;
pub mod batch_operations;
pub mod formatters;
pub mod query_operations;
pub mod single_operations;
pub mod utils;

use anyhow::Result;
use clap::Subcommand;
use std::sync::Arc;

use crate::cli::backend::backend::CliBackend;
use crate::cli::utils::formatter::OutputFormat;

use self::args::{AddArgs, BatchTasksArgs, ListArgs, TaskIdArgs};
use self::batch_operations::{handle_batch_cancel, handle_batch_pause, handle_batch_remove, handle_batch_resume, handle_batch_start};
use self::query_operations::{handle_info, handle_list};
use self::single_operations::{handle_add, handle_cancel, handle_pause, handle_remove, handle_resume, handle_start};

/// Download command arguments
#[derive(Debug, Subcommand)]
pub enum DownloadCommands {
    /// Add a new download task
    Add(AddArgs),
    /// Start a download task
    Start(TaskIdArgs),
    /// Pause a download task
    Pause(TaskIdArgs),
    /// Resume a download task
    Resume(TaskIdArgs),
    /// Cancel a download task
    Cancel(TaskIdArgs),
    /// Remove a download task
    Remove(TaskIdArgs),
    /// List all download tasks
    List(ListArgs),
    /// Show download task information
    Info(TaskIdArgs),
    /// Batch start download tasks
    BatchStart(BatchTasksArgs),
    /// Batch pause download tasks
    BatchPause(BatchTasksArgs),
    /// Batch resume download tasks
    BatchResume(BatchTasksArgs),
    /// Batch cancel download tasks
    BatchCancel(BatchTasksArgs),
    /// Batch remove download tasks
    BatchRemove(BatchTasksArgs),
}

/// Handle download command
pub async fn handle_command(backend: &Arc<dyn CliBackend>, command: &DownloadCommands, format: OutputFormat) -> Result<()> {
    match command {
        DownloadCommands::Add(args) => handle_add(backend, args).await,
        DownloadCommands::Start(args) => handle_start(backend, args).await,
        DownloadCommands::Pause(args) => handle_pause(backend, args).await,
        DownloadCommands::Resume(args) => handle_resume(backend, args).await,
        DownloadCommands::Cancel(args) => handle_cancel(backend, args).await,
        DownloadCommands::Remove(args) => handle_remove(backend, args).await,
        DownloadCommands::List(args) => handle_list(backend, args, format).await,
        DownloadCommands::Info(args) => handle_info(backend, args, format).await,
        DownloadCommands::BatchStart(args) => handle_batch_start(backend, args).await,
        DownloadCommands::BatchPause(args) => handle_batch_pause(backend, args).await,
        DownloadCommands::BatchResume(args) => handle_batch_resume(backend, args).await,
        DownloadCommands::BatchCancel(args) => handle_batch_cancel(backend, args).await,
        DownloadCommands::BatchRemove(args) => handle_batch_remove(backend, args).await,
    }
}