use std::path::{Path, PathBuf};
use std::fs;
use std::collections::HashMap;
use std::sync::Arc;
use async_trait::async_trait;
use tokio::sync::RwLock;
use tracing::{info, error};

use crate::core::error::{CoreResult, CoreError};
use crate::core::interfaces::plugin::{Plugin, PluginManager, PluginMetadata, PluginStatus, PluginCapability};
use crate::plugins::loader::{PluginLoader, PluginLoaderImpl, PluginLibrary};

/// Plugin instance
struct PluginInstance {
    /// Plugin
    plugin: Box<dyn Plugin>,
    /// Plugin library
    library: PluginLibrary,
    /// Plugin status
    status: PluginStatus,
    /// Plugin configuration
    config: HashMap<String, String>,
}

/// Plugin manager implementation
pub struct PluginManagerImpl {
    /// Plugin loader
    loader: Arc<dyn PluginLoader>,
    /// Plugin directory
    plugin_dir: PathBuf,
    /// Loaded plugins
    plugins: Arc<RwLock<HashMap<String, PluginInstance>>>,
}

impl PluginManagerImpl {
    /// Create a new plugin manager
    pub fn new(plugin_dir: impl Into<PathBuf>) -> Self {
        Self {
            loader: Arc::new(PluginLoaderImpl::new()),
            plugin_dir: plugin_dir.into(),
            plugins: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Get plugin directory path
    pub fn plugin_dir(&self) -> &Path {
        &self.plugin_dir
    }

    /// Set plugin loader
    pub fn with_loader(mut self, loader: Arc<dyn PluginLoader>) -> Self {
        self.loader = loader;
        self
    }

    /// Load all plugins from the plugin directory
    pub async fn load_all_plugins(&self) -> CoreResult<Vec<String>> {
        // Check if the plugin directory exists
        if !self.plugin_dir.exists() {
            return Err(CoreError::NotFound(format!("Plugin directory not found: {}", self.plugin_dir.display())));
        }

        // Get all subdirectories
        let entries = fs::read_dir(&self.plugin_dir)
            .map_err(|e| CoreError::IO(format!("Failed to read plugin directory: {}", e)))?;

        let mut loaded_plugins = Vec::new();

        for entry in entries {
            let entry = entry.map_err(|e| CoreError::IO(format!("Failed to read plugin directory entry: {}", e)))?;
            let path = entry.path();

            if path.is_dir() {
                match self.load_plugin(path.to_str().unwrap_or("")).await {
                    Ok(id) => {
                        loaded_plugins.push(id);
                    },
                    Err(e) => {
                        error!("Failed to load plugin from {}: {}", path.display(), e);
                    }
                }
            }
        }

        Ok(loaded_plugins)
    }
}

#[async_trait]
impl PluginManager for PluginManagerImpl {
    async fn load_plugin(&self, path: &str) -> CoreResult<String> {
        let path = Path::new(path);

        // Load the plugin
        let (plugin, library) = self.loader.load_plugin(path).await?;

        // Get the plugin metadata
        let metadata = plugin.metadata();
        let id = metadata.id.clone();

        // Check if the plugin is already loaded
        if self.plugins.read().await.contains_key(&id) {
            return Err(CoreError::AlreadyExists(format!("Plugin already loaded: {}", id)));
        }

        // Create the plugin instance
        let instance = PluginInstance {
            plugin,
            library,
            status: PluginStatus::Disabled,
            config: HashMap::new(),
        };

        // Add the plugin to the list
        self.plugins.write().await.insert(id.clone(), instance);

        info!("Loaded plugin: {}", id);

        Ok(id)
    }

    async fn unload_plugin(&self, id: &str) -> CoreResult<()> {
        let mut plugins = self.plugins.write().await;

        // Check if the plugin exists
        if !plugins.contains_key(id) {
            return Err(CoreError::NotFound(format!("Plugin not found: {}", id)));
        }

        // Get the plugin
        let instance = plugins.remove(id).unwrap();

        // Check if the plugin is enabled
        if instance.status == PluginStatus::Enabled {
            return Err(CoreError::InvalidState(format!("Cannot unload enabled plugin: {}", id)));
        }

        info!("Unloaded plugin: {}", id);

        Ok(())
    }

    async fn enable_plugin(&self, id: &str) -> CoreResult<()> {
        let mut plugins = self.plugins.write().await;

        // Check if the plugin exists
        if !plugins.contains_key(id) {
            return Err(CoreError::NotFound(format!("Plugin not found: {}", id)));
        }

        // Get the plugin
        let instance = plugins.get_mut(id).unwrap();

        // Check if the plugin is already enabled
        if instance.status == PluginStatus::Enabled {
            return Ok(());
        }

        // Initialize the plugin
        instance.plugin.init(instance.config.clone()).await?;

        // Update the status
        instance.status = PluginStatus::Enabled;

        info!("Enabled plugin: {}", id);

        Ok(())
    }

    async fn disable_plugin(&self, id: &str) -> CoreResult<()> {
        let mut plugins = self.plugins.write().await;

        // Check if the plugin exists
        if !plugins.contains_key(id) {
            return Err(CoreError::NotFound(format!("Plugin not found: {}", id)));
        }

        // Get the plugin
        let instance = plugins.get_mut(id).unwrap();

        // Check if the plugin is already disabled
        if instance.status != PluginStatus::Enabled {
            return Ok(());
        }

        // Shutdown the plugin
        instance.plugin.shutdown().await?;

        // Update the status
        instance.status = PluginStatus::Disabled;

        info!("Disabled plugin: {}", id);

        Ok(())
    }

    async fn get_plugin_metadata(&self, id: &str) -> CoreResult<PluginMetadata> {
        let plugins = self.plugins.read().await;

        // Check if the plugin exists
        if !plugins.contains_key(id) {
            return Err(CoreError::NotFound(format!("Plugin not found: {}", id)));
        }

        // Get the plugin metadata
        let metadata = plugins.get(id).unwrap().plugin.metadata().clone();

        Ok(metadata)
    }

    async fn get_all_plugins(&self) -> CoreResult<Vec<PluginMetadata>> {
        let plugins = self.plugins.read().await;

        // Get all plugin metadata
        let metadata = plugins.values()
            .map(|instance| instance.plugin.metadata().clone())
            .collect();

        Ok(metadata)
    }

    async fn get_plugins_by_capability(&self, capability: PluginCapability) -> CoreResult<Vec<PluginMetadata>> {
        let plugins = self.plugins.read().await;

        // Get plugins with the specified capability
        let metadata = plugins.values()
            .filter(|instance| instance.plugin.metadata().capabilities.contains(&capability))
            .map(|instance| instance.plugin.metadata().clone())
            .collect();

        Ok(metadata)
    }

    async fn get_plugin_instance(&self, id: &str) -> CoreResult<Box<dyn Plugin>> {
        let plugins = self.plugins.read().await;

        // Check if the plugin exists
        if !plugins.contains_key(id) {
            return Err(CoreError::NotFound(format!("Plugin not found: {}", id)));
        }

        // Get the plugin
        let instance = plugins.get(id).unwrap();

        // Check if the plugin is enabled
        if instance.status != PluginStatus::Enabled {
            return Err(CoreError::InvalidState(format!("Plugin is not enabled: {}", id)));
        }

        // 由于我们无法直接克隆 trait 对象，我们需要创建一个新的实例
        // 这里我们返回一个错误，因为实际上我们需要插件实现 Clone trait
        // 在实际应用中，插件应该提供一个 clone 方法或者其他方式来创建新实例
        return Err(CoreError::NotImplemented(format!("Plugin cloning is not implemented yet: {}", id)));
    }

    async fn configure_plugin(&self, id: &str, config: HashMap<String, String>) -> CoreResult<()> {
        let mut plugins = self.plugins.write().await;

        // Check if the plugin exists
        if !plugins.contains_key(id) {
            return Err(CoreError::NotFound(format!("Plugin not found: {}", id)));
        }

        // Get the plugin
        let instance = plugins.get_mut(id).unwrap();

        // Update the configuration
        instance.config = config.clone();

        // If the plugin is enabled, reinitialize it
        if instance.status == PluginStatus::Enabled {
            instance.plugin.init(config).await?;
        }

        info!("Configured plugin: {}", id);

        Ok(())
    }
}
