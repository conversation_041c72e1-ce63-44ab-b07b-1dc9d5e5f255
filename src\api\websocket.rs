use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use axum::{
    extract::{ws::{WebSocket, Message}, WebSocketUpgrade, State},
    response::IntoResponse,
};
use futures::{sink::SinkExt, stream::StreamExt};
use serde::{Deserialize, Serialize};
use tokio::sync::broadcast::{self, Sender, Receiver};
use tracing::{debug, info, error};
use uuid::Uuid;

use crate::download::manager::{DownloadManager, TaskInfo, TaskStatus};
use crate::api::state::AppState;
use crate::api::handlers::download::AddDownloadParams;

/// WebSocket消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WsMessage {
    pub event: String,
    pub payload: serde_json::Value,
    pub version: String,
}

/// WebSocket客户端信息
#[derive(Debug)]
struct ClientInfo {
    id: Uuid,
    sender: Option<Sender<WsMessage>>,
}

/// WebSocket连接管理器
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct WebSocketManager {
    clients: Arc<Mutex<HashMap<Uuid, ClientInfo>>>,
    event_sender: Sender<WsMessage>,
}

impl WebSocketManager {
    /// 创建新的WebSocket管理器
    pub fn new() -> Self {
        // 创建一个容量更大的通道，避免消息丢失
        let (sender, receiver) = broadcast::channel(1000);

        // 保持一个接收器活跃，确保通道不会关闭
        let sender_clone: Sender<WsMessage> = sender.clone();
        tokio::spawn(async move {
            let mut receiver = receiver;
            loop {
                match receiver.recv().await {
                    Ok(msg) => {
                        debug!("Received message on keep-alive receiver: {}", msg.event);
                    }
                    Err(e) => {
                        debug!("Error on keep-alive receiver: {}", e);
                        // 如果通道关闭，重新订阅
                        receiver = sender_clone.subscribe();
                    }
                }
            }
        });

        Self {
            clients: Arc::new(Mutex::new(HashMap::new())),
            event_sender: sender,
        }
    }

    /// 获取事件发送器
    pub fn get_sender(&self) -> Sender<WsMessage> {
        self.event_sender.clone()
    }

    /// 广播消息给所有客户端
    pub fn broadcast(&self, event: impl Into<String>, payload: impl Serialize) {
        let event = event.into();
        let payload = match serde_json::to_value(payload) {
            Ok(value) => value,
            Err(e) => {
                error!("Failed to serialize payload: {}", e);
                return;
            }
        };

        let message = WsMessage {
            event,
            payload,
            version: "1.0".to_string(),
        };

        if let Err(e) = self.event_sender.send(message) {
            error!("Failed to broadcast message: {}", e);
        }
    }

    /// 添加客户端
    fn add_client(&self, client_id: Uuid) -> Receiver<WsMessage> {
        let mut clients = self.clients.lock().unwrap();
        let receiver = self.event_sender.subscribe();

        clients.insert(client_id, ClientInfo {
            id: client_id,
            sender: Some(self.event_sender.clone()),
        });

        info!("Client connected: {}", client_id);
        receiver
    }

    /// 移除客户端
    fn remove_client(&self, client_id: Uuid) {
        let mut clients = self.clients.lock().unwrap();
        clients.remove(&client_id);
        info!("Client disconnected: {}", client_id);
    }
}

/// WebSocket处理函数
pub async fn ws_handler(
    ws: WebSocketUpgrade,
    app_state: State<AppState>,
) -> impl IntoResponse {
    let ws_manager = app_state.ws_manager.clone();
    let download_manager = app_state.download_manager.clone();
    ws.on_upgrade(|socket| handle_socket(socket, ws_manager, Some(download_manager)))
}

/// 处理WebSocket连接
async fn handle_socket(socket: WebSocket, ws_manager: WebSocketManager, download_manager: Option<Arc<dyn DownloadManager>>) {
    let client_id = Uuid::new_v4();
    let mut receiver = ws_manager.add_client(client_id);

    // 分割socket为发送和接收部分
    let (sender, mut receiver_socket) = socket.split();

    // 创建一个可以在多个任务间共享的sender
    let sender = Arc::new(tokio::sync::Mutex::new(sender));

    // 创建一个可以在多个任务间共享的ws_manager
    let ws_manager = Arc::new(ws_manager);

    // 发送初始连接消息
    let connect_msg = WsMessage {
        event: "connection".to_string(),
        payload: serde_json::json!({
            "status": "connected",
            "client_id": client_id.to_string(),
            "server_time": chrono::Utc::now().to_rfc3339(),
        }),
        version: "1.0".to_string(),
    };

    // 获取锁并发送消息
    let mut sender_guard = sender.lock().await;
    if let Err(e) = sender_guard.send(Message::Text(serde_json::to_string(&connect_msg).unwrap().into())).await {
        error!("Error sending connection message: {}", e);
        return;
    }
    drop(sender_guard); // 显式释放锁

    // 处理从客户端接收的消息
    let download_manager_clone = download_manager.clone();
    let ws_manager_clone = Arc::clone(&ws_manager);
    let sender_clone = Arc::clone(&sender);

    let mut recv_task = tokio::spawn(async move {
        while let Some(result) = receiver_socket.next().await {
            match result {
                Ok(Message::Text(text)) => {
                    debug!("Received message: {}", text);
                    if let Err(e) = handle_client_message(&text, &download_manager_clone, &ws_manager_clone, client_id).await {
                        error!("Error handling client message: {}", e);
                    }
                }
                Ok(Message::Close(_)) => {
                    info!("Client requested close: {}", client_id);
                    break;
                }
                Ok(Message::Ping(ping)) => {
                    // 获取锁并发送消息
                    let mut sender_guard = sender_clone.lock().await;
                    if let Err(e) = sender_guard.send(Message::Pong(ping)).await {
                        error!("Error sending pong: {}", e);
                        break;
                    }
                    drop(sender_guard); // 显式释放锁
                }
                _ => {}
            }
        }
    });

    // 处理广播消息
    let sender_clone = Arc::clone(&sender);
    let mut send_task = tokio::spawn(async move {
        while let Ok(msg) = receiver.recv().await {
            let json = match serde_json::to_string(&msg) {
                Ok(json) => json,
                Err(e) => {
                    error!("Failed to serialize message: {}", e);
                    continue;
                }
            };

            // 获取锁并发送消息
            let mut sender_guard = sender_clone.lock().await;
            if let Err(e) = sender_guard.send(Message::Text(json.into())).await {
                error!("Failed to send message to client {}: {}", client_id, e);
                break;
            }
            drop(sender_guard); // 显式释放锁
        }
    });

    // 等待任一任务完成
    tokio::select! {
        _ = (&mut recv_task) => send_task.abort(),
        _ = (&mut send_task) => recv_task.abort(),
    }

    // 移除客户端
    ws_manager.remove_client(client_id);
}

/// 处理客户端消息
async fn handle_client_message(
    text: &str,
    download_manager: &Option<Arc<dyn DownloadManager>>,
    ws_manager: &Arc<WebSocketManager>,
    client_id: Uuid,
) -> Result<(), String> {
    let msg: WsMessage = serde_json::from_str(text).map_err(|e| e.to_string())?;

    match msg.event.as_str() {
        "ping" => {
            // 回复pong消息
            ws_manager.broadcast("pong", serde_json::json!({
                "time": chrono::Utc::now().timestamp_millis(),
                "client_id": client_id.to_string(),
            }));
        }
        "get_task_info" => {
            if let Some(dm) = download_manager {
                let task_id: Uuid = serde_json::from_value(msg.payload).map_err(|e| e.to_string())?;
                match dm.get_task(task_id).await {
                    Ok(task_info) => ws_manager.broadcast("task_info", task_info),
                    Err(e) => {
                        error!("Failed to get task info via WebSocket: {}", e);
                        ws_manager.broadcast("error", serde_json::json!({ "message": format!("Failed to get task info: {}", e) }));
                    }
                }
            } else {
                error!("Download manager not available for get_task");
                ws_manager.broadcast("error", serde_json::json!({ "message": "Download manager not available".to_string() }));
            }
        },
        "get_all_tasks" => {
            if let Some(dm) = download_manager {
                let tasks = dm.get_all_tasks().await.map_err(|e| e.to_string())?;
                ws_manager.broadcast("all_tasks", tasks);
            } else {
                error!("Download manager not available for get_all_tasks");
            }
        },
        "add_download" => {
            if let Some(dm) = download_manager {
                let req: AddDownloadParams = serde_json::from_value(msg.payload).map_err(|e| e.to_string())?;
                
                // 创建一个新的TaskInfo对象
                let task = TaskInfo {
                    id: Uuid::new_v4(),
                    url: req.url,
                    output_path: req.output_path.unwrap_or_else(|| "./downloads".to_string()),
                    status: TaskStatus::Pending,
                    progress: 0.0,
                    speed: 0,
                    total_size: None,
                    downloaded_size: 0,
                    uploaded_bytes: 0, // 添加上传字节数字段，初始值为0
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                    error_message: None,
                };
                
                match dm.add_task(task).await {
                    Ok(task_id) => {
                        ws_manager.broadcast("download_added", serde_json::json!({ "task_id": task_id }));
                    },
                    Err(e) => {
                        error!("Failed to add download via WebSocket: {}", e);
                        ws_manager.broadcast("error", serde_json::json!({ "message": format!("Failed to add download: {}", e) }));
                    }
                }
            } else {
                error!("Download manager not available for add_download");
                ws_manager.broadcast("error", serde_json::json!({ "message": "Download manager not available".to_string() }));
            }
        },
        "start_download" => {
            if let Some(dm) = download_manager {
                let task_id: Uuid = serde_json::from_value(msg.payload).map_err(|e| e.to_string())?;
                match dm.start_task(task_id).await {
                    Ok(_) => ws_manager.broadcast("download_started", serde_json::json!({ "task_id": task_id })),
                    Err(e) => {
                        error!("Failed to start download via WebSocket: {}", e);
                        ws_manager.broadcast("error", serde_json::json!({ "message": format!("Failed to start download: {}", e) }));
                    }
                }
            } else {
                error!("Download manager not available for start_download");
                ws_manager.broadcast("error", serde_json::json!({ "message": "Download manager not available".to_string() }));
            }
        },
        "pause_download" => {
            if let Some(dm) = download_manager {
                let task_id: Uuid = serde_json::from_value(msg.payload).map_err(|e| e.to_string())?;
                match dm.pause_task(task_id).await {
                    Ok(_) => ws_manager.broadcast("download_paused", serde_json::json!({ "task_id": task_id })),
                    Err(e) => {
                        error!("Failed to pause download via WebSocket: {}", e);
                        ws_manager.broadcast("error", serde_json::json!({ "message": format!("Failed to pause download: {}", e) }));
                    }
                }
            } else {
                error!("Download manager not available for pause_download");
                ws_manager.broadcast("error", serde_json::json!({ "message": "Download manager not available".to_string() }));
            }
        },
        "resume_download" => {
            if let Some(dm) = download_manager {
                let task_id: Uuid = serde_json::from_value(msg.payload).map_err(|e| e.to_string())?;
                match dm.resume_task(task_id).await {
                    Ok(_) => ws_manager.broadcast("download_resumed", serde_json::json!({ "task_id": task_id })),
                    Err(e) => {
                        error!("Failed to resume download via WebSocket: {}", e);
                        ws_manager.broadcast("error", serde_json::json!({ "message": format!("Failed to resume download: {}", e) }));
                    }
                }
            } else {
                error!("Download manager not available for resume_download");
                ws_manager.broadcast("error", serde_json::json!({ "message": "Download manager not available".to_string() }));
            }
        },
        "cancel_download" => {
            if let Some(dm) = download_manager {
                let task_id: Uuid = serde_json::from_value(msg.payload).map_err(|e| e.to_string())?;
                match dm.cancel_task(task_id).await {
                    Ok(_) => ws_manager.broadcast("download_cancelled", serde_json::json!({ "task_id": task_id })),
                    Err(e) => {
                        error!("Failed to cancel download via WebSocket: {}", e);
                        ws_manager.broadcast("error", serde_json::json!({ "message": format!("Failed to cancel download: {}", e) }));
                    }
                }
            } else {
                error!("Download manager not available for cancel_download");
                ws_manager.broadcast("error", serde_json::json!({ "message": "Download manager not available".to_string() }));
            }
        },
        "remove_download" => {
            if let Some(dm) = download_manager {
                let task_id: Uuid = serde_json::from_value(msg.payload).map_err(|e| e.to_string())?;
                match dm.remove_task(task_id).await {
                    Ok(_) => ws_manager.broadcast("download_removed", serde_json::json!({ "task_id": task_id })),
                    Err(e) => {
                        error!("Failed to remove download via WebSocket: {}", e);
                        ws_manager.broadcast("error", serde_json::json!({ "message": format!("Failed to remove download: {}", e) }));
                    }
                }
            } else {
                error!("Download manager not available for remove_download");
                ws_manager.broadcast("error", serde_json::json!({ "message": "Download manager not available".to_string() }));
            }
        },
        _ => {
            debug!("Unhandled event: {}", msg.event);
        }
    }

    Ok(())
}
