use anyhow::{Result, anyhow};
use async_trait::async_trait;
use bytes::Bytes;
use std::path::PathBuf;
use tokio::fs::{self, File, OpenOptions};
use tokio::io::{AsyncReadExt, AsyncSeekExt, AsyncWriteExt, SeekFrom};
use tracing::debug;

use crate::config::Settings;

/// Storage interface
#[async_trait]
pub trait Storage: Send + Sync {
    /// Write data to a file
    async fn write(&self, path: &str, data: &[u8]) -> Result<()>;
    
    /// Write data to a file at a specific offset
    async fn write_at(&self, path: &str, data: &[u8], offset: u64) -> Result<()>;
    
    /// Read data from a file
    async fn read(&self, path: &str) -> Result<Bytes>;
    
    /// Read data from a file at a specific offset
    async fn read_at(&self, path: &str, offset: u64, length: usize) -> Result<Bytes>;
    
    /// Delete a file
    async fn delete(&self, path: &str) -> Result<()>;
    
    /// Check if a file exists
    async fn exists(&self, path: &str) -> Result<bool>;
    
    /// Get the size of a file
    async fn size(&self, path: &str) -> Result<u64>;
    
    /// Create a directory
    async fn create_dir(&self, path: &str) -> Result<()>;
}

/// Local storage implementation
pub struct LocalStorage {
    base_path: PathBuf,
}

impl LocalStorage {
    /// Create a new local storage
    pub fn new(settings: &Settings) -> Self {
        let base_path = PathBuf::from(&settings.download.path);
        
        Self {
            base_path,
        }
    }
    
    /// Get the full path for a file
    fn get_full_path(&self, path: &str) -> PathBuf {
        self.base_path.join(path)
    }
}

#[async_trait]
impl Storage for LocalStorage {
    async fn write(&self, path: &str, data: &[u8]) -> Result<()> {
        let full_path = self.get_full_path(path);
        
        // Create the parent directory if it doesn't exist
        if let Some(parent) = full_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        // Write the data
        fs::write(&full_path, data).await?;
        
        debug!("Wrote {} bytes to {}", data.len(), full_path.display());
        
        Ok(())
    }
    
    async fn write_at(&self, path: &str, data: &[u8], offset: u64) -> Result<()> {
        let full_path = self.get_full_path(path);
        
        // Create the parent directory if it doesn't exist
        if let Some(parent) = full_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        // Open the file
        let mut file = OpenOptions::new()
            .write(true)
            .create(true)
            .open(&full_path)
            .await?;
        
        // Seek to the offset
        file.seek(SeekFrom::Start(offset)).await?;
        
        // Write the data
        file.write_all(data).await?;
        
        debug!("Wrote {} bytes at offset {} to {}", data.len(), offset, full_path.display());
        
        Ok(())
    }
    
    async fn read(&self, path: &str) -> Result<Bytes> {
        let full_path = self.get_full_path(path);
        
        // Read the file
        let data = fs::read(&full_path).await?;
        
        debug!("Read {} bytes from {}", data.len(), full_path.display());
        
        Ok(Bytes::from(data))
    }
    
    async fn read_at(&self, path: &str, offset: u64, length: usize) -> Result<Bytes> {
        let full_path = self.get_full_path(path);
        
        // Open the file
        let mut file = File::open(&full_path).await?;
        
        // Get the file size
        let file_size = file.metadata().await?.len();
        
        // Check if the offset is valid
        if offset >= file_size {
            return Err(anyhow!("Offset {} is beyond the end of the file (size: {})", offset, file_size));
        }
        
        // Calculate the actual length to read
        let actual_length = length.min((file_size - offset) as usize);
        
        // Seek to the offset
        file.seek(SeekFrom::Start(offset)).await?;
        
        // Read the data
        let mut buffer = vec![0; actual_length];
        file.read_exact(&mut buffer).await?;
        
        debug!("Read {} bytes at offset {} from {}", actual_length, offset, full_path.display());
        
        Ok(Bytes::from(buffer))
    }
    
    async fn delete(&self, path: &str) -> Result<()> {
        let full_path = self.get_full_path(path);
        
        // Delete the file
        fs::remove_file(&full_path).await?;
        
        debug!("Deleted {}", full_path.display());
        
        Ok(())
    }
    
    async fn exists(&self, path: &str) -> Result<bool> {
        let full_path = self.get_full_path(path);
        
        // Check if the file exists
        let exists = fs::metadata(&full_path).await.is_ok();
        
        Ok(exists)
    }
    
    async fn size(&self, path: &str) -> Result<u64> {
        let full_path = self.get_full_path(path);
        
        // Get the file size
        let metadata = fs::metadata(&full_path).await?;
        let size = metadata.len();
        
        Ok(size)
    }
    
    async fn create_dir(&self, path: &str) -> Result<()> {
        let full_path = self.get_full_path(path);
        
        // Create the directory
        fs::create_dir_all(&full_path).await?;
        
        debug!("Created directory {}", full_path.display());
        
        Ok(())
    }
}
