use std::net::{IpAddr, SocketAddr};
use std::sync::Arc;
use std::time::Duration;
use anyhow::{Result, anyhow};
use tokio::net::{TcpSocket, TcpStream, UdpSocket};
use tokio::sync::Mutex;
use tokio::time::timeout;
use tracing::{debug, info, warn};

use super::nat_detector::{NATType, NATTypeDetector};

/// 打洞结果
#[derive(Debug)]
pub enum HolePunchingResult {
    /// 成功（TCP流）
    TcpSuccess(TcpStream),
    /// 成功（UDP套接字）
    UdpSuccess(UdpSocket),
    /// 失败
    Failed(String),
}

/// 打洞器
pub struct HolePuncher {
    /// 最大尝试次数
    max_attempts: usize,
    /// 超时时间（秒）
    timeout_secs: u64,
}

impl HolePuncher {
    /// 创建新的打洞器
    pub fn new() -> Self {
        Self {
            max_attempts: 5,
            timeout_secs: 10,
        }
    }
    
    /// 设置最大尝试次数
    pub fn set_max_attempts(&mut self, attempts: usize) {
        self.max_attempts = attempts;
    }
    
    /// 设置超时时间
    pub fn set_timeout(&mut self, secs: u64) {
        self.timeout_secs = secs;
    }
    
    /// TCP打洞
    pub async fn tcp_hole_punching(
        &self,
        local_addr: SocketAddr,
        peer_addr: SocketAddr,
        nat_detector: &NATTypeDetector,
    ) -> Result<TcpStream> {
        // 检查NAT类型
        let nat_type = nat_detector.get_nat_type().await;
        
        // 对于对称NAT，打洞通常不起作用
        if nat_type == NATType::Symmetric {
            return Err(anyhow!("TCP hole punching not supported for Symmetric NAT"));
        }
        
        // 创建TCP套接字
        let socket = match local_addr.ip() {
            IpAddr::V4(_) => TcpSocket::new_v4()?,
            IpAddr::V6(_) => TcpSocket::new_v6()?,
        };
        
        // 设置重用地址和端口
        socket.set_reuseaddr(true)?;
        #[cfg(unix)]
        socket.set_reuseport(true)?;
        
        // 绑定本地地址
        socket.bind(local_addr)?;
        
        // 尝试连接对等点
        for attempt in 1..=self.max_attempts {
            debug!("TCP hole punching attempt {}/{} to {}", attempt, self.max_attempts, peer_addr);
            
            match timeout(
                Duration::from_secs(self.timeout_secs),
                socket.connect(peer_addr)
            ).await {
                Ok(Ok(stream)) => {
                    info!("TCP hole punching successful to {}", peer_addr);
                    return Ok(stream);
                },
                Ok(Err(e)) => {
                    debug!("TCP hole punching attempt {} failed: {}", attempt, e);
                },
                Err(_) => {
                    debug!("TCP hole punching attempt {} timed out", attempt);
                },
            }
            
            // 等待一段时间再尝试
            tokio::time::sleep(Duration::from_millis(500)).await;
        }
        
        Err(anyhow!("TCP hole punching failed after {} attempts", self.max_attempts))
    }
    
    /// UDP打洞
    pub async fn udp_hole_punching(
        &self,
        local_addr: SocketAddr,
        peer_addr: SocketAddr,
        nat_detector: &NATTypeDetector,
    ) -> Result<UdpSocket> {
        // 检查NAT类型
        let nat_type = nat_detector.get_nat_type().await;
        
        // 对于对称NAT，打洞通常不起作用
        if nat_type == NATType::Symmetric {
            return Err(anyhow!("UDP hole punching not supported for Symmetric NAT"));
        }
        
        // 创建UDP套接字
        let socket = UdpSocket::bind(local_addr).await?;
        
        // 发送打洞数据包
        let punch_data = b"PUNCH";
        
        for attempt in 1..=self.max_attempts {
            debug!("UDP hole punching attempt {}/{} to {}", attempt, self.max_attempts, peer_addr);
            
            // 发送数据包
            if let Err(e) = socket.send_to(punch_data, peer_addr).await {
                debug!("Failed to send UDP punch packet: {}", e);
                continue;
            }
            
            // 等待响应
            let mut buf = [0u8; 1024];
            
            match timeout(
                Duration::from_secs(self.timeout_secs),
                socket.recv_from(&mut buf)
            ).await {
                Ok(Ok((len, addr))) => {
                    // 检查是否是来自对等点的响应
                    if addr.ip() == peer_addr.ip() {
                        info!("UDP hole punching successful to {}", peer_addr);
                        return Ok(socket);
                    } else {
                        debug!("Received UDP packet from unexpected address: {}", addr);
                    }
                },
                Ok(Err(e)) => {
                    debug!("UDP hole punching attempt {} failed: {}", attempt, e);
                },
                Err(_) => {
                    debug!("UDP hole punching attempt {} timed out", attempt);
                },
            }
            
            // 等待一段时间再尝试
            tokio::time::sleep(Duration::from_millis(500)).await;
        }
        
        Err(anyhow!("UDP hole punching failed after {} attempts", self.max_attempts))
    }
    
    /// 同时TCP打洞
    pub async fn simultaneous_tcp_hole_punching(
        &self,
        local_addr: SocketAddr,
        peer_addr: SocketAddr,
    ) -> Result<TcpStream> {
        // 创建TCP套接字
        let socket = match local_addr.ip() {
            IpAddr::V4(_) => TcpSocket::new_v4()?,
            IpAddr::V6(_) => TcpSocket::new_v6()?,
        };
        
        // 设置重用地址和端口
        socket.set_reuseaddr(true)?;
        #[cfg(unix)]
        socket.set_reuseport(true)?;
        
        // 绑定本地地址
        socket.bind(local_addr)?;
        
        // 同时尝试连接和监听
        let connect_future = socket.connect(peer_addr);
        
        // 等待连接完成
        match timeout(Duration::from_secs(self.timeout_secs), connect_future).await {
            Ok(Ok(stream)) => {
                info!("Simultaneous TCP hole punching successful to {}", peer_addr);
                Ok(stream)
            },
            Ok(Err(e)) => {
                Err(anyhow!("Simultaneous TCP hole punching failed: {}", e))
            },
            Err(_) => {
                Err(anyhow!("Simultaneous TCP hole punching timed out"))
            },
        }
    }
    
    /// 尝试所有打洞方法
    pub async fn try_all_methods(
        &self,
        local_addr: SocketAddr,
        peer_addr: SocketAddr,
        nat_detector: &NATTypeDetector,
    ) -> HolePunchingResult {
        // 首先尝试UDP打洞
        match self.udp_hole_punching(local_addr, peer_addr, nat_detector).await {
            Ok(socket) => return HolePunchingResult::UdpSuccess(socket),
            Err(e) => debug!("UDP hole punching failed: {}", e),
        }
        
        // 然后尝试TCP打洞
        match self.tcp_hole_punching(local_addr, peer_addr, nat_detector).await {
            Ok(stream) => return HolePunchingResult::TcpSuccess(stream),
            Err(e) => debug!("TCP hole punching failed: {}", e),
        }
        
        // 最后尝试同时TCP打洞
        match self.simultaneous_tcp_hole_punching(local_addr, peer_addr).await {
            Ok(stream) => return HolePunchingResult::TcpSuccess(stream),
            Err(e) => debug!("Simultaneous TCP hole punching failed: {}", e),
        }
        
        HolePunchingResult::Failed("All hole punching methods failed".to_string())
    }
}
