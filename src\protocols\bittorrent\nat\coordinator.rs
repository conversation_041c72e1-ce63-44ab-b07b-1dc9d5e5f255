use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Duration;
use anyhow::{Result, anyhow};
use tokio::net::{TcpStream, UdpSocket};
use tokio::sync::{<PERSON>te<PERSON>, RwLock};
use tracing::{debug, info, warn};

use super::stun::client::{StunClient, StunClientConfig};
use super::mapping::{Protocol, MappingInfo, PortMapper};
use super::mapping::upnp::UPnPMapper;
use super::mapping::natpmp::NATPMPMapper;
use super::mapping::pcp::PCPMapper;
use super::mapping::lifetime_manager::{MappingLifetimeManager, MappingLifetimeManagerConfig};
use super::hole_puncher::{HolePuncher, HolePunchingResult};
use super::nat_detector::{NATType, NATTypeDetector};

/// 穿透策略
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum TraversalStrategy {
    /// 直接连接
    Direct,
    /// 端口映射
    PortMapping,
    /// UDP打洞
    UDPHolePunching,
    /// TCP打洞
    TCPHolePunching,
    /// 中继
    Relay,
}

impl std::fmt::Display for TraversalStrategy {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TraversalStrategy::Direct => write!(f, "Direct Connection"),
            TraversalStrategy::PortMapping => write!(f, "Port Mapping"),
            TraversalStrategy::UDPHolePunching => write!(f, "UDP Hole Punching"),
            TraversalStrategy::TCPHolePunching => write!(f, "TCP Hole Punching"),
            TraversalStrategy::Relay => write!(f, "Relay"),
        }
    }
}

/// NAT穿透配置
#[derive(Debug, Clone)]
pub struct TraversalConfig {
    /// 是否启用STUN
    pub enable_stun: bool,
    /// 是否启用UPnP
    pub enable_upnp: bool,
    /// 是否启用NAT-PMP
    pub enable_natpmp: bool,
    /// 是否启用PCP
    pub enable_pcp: bool,
    /// 是否启用打洞
    pub enable_hole_punching: bool,
    /// 是否启用中继
    pub enable_relay: bool,
    /// STUN服务器列表
    pub stun_servers: Vec<String>,
    /// 中继服务器列表
    pub relay_servers: Vec<String>,
    /// 请求超时时间（秒）
    pub request_timeout: u64,
    /// 映射生命周期（秒）
    pub mapping_lifetime: u64,
    /// 映射检查间隔（秒）
    pub mapping_check_interval: u64,
}

impl Default for TraversalConfig {
    fn default() -> Self {
        Self {
            enable_stun: true,
            enable_upnp: true,
            enable_natpmp: true,
            enable_pcp: true,
            enable_hole_punching: true,
            enable_relay: false,
            stun_servers: vec![
                "stun.l.google.com:19302".to_string(),
                "stun1.l.google.com:19302".to_string(),
                "stun2.l.google.com:19302".to_string(),
            ],
            relay_servers: Vec::new(),
            request_timeout: 5,
            mapping_lifetime: 7200, // 2小时
            mapping_check_interval: 60, // 1分钟
        }
    }
}

/// NAT穿透状态
#[derive(Debug, Clone)]
pub struct TraversalStatus {
    /// NAT类型
    pub nat_type: NATType,
    /// 本地地址
    pub local_addr: Option<SocketAddr>,
    /// 映射地址
    pub mapped_addr: Option<SocketAddr>,
    /// 端口映射数量
    pub mapping_count: usize,
    /// 是否支持UPnP
    pub upnp_supported: bool,
    /// 是否支持NAT-PMP
    pub natpmp_supported: bool,
    /// 是否支持PCP
    pub pcp_supported: bool,
}

/// NAT穿透协调器
pub struct NATTraversalCoordinator {
    /// STUN客户端
    stun_client: Arc<StunClient>,
    /// 端口映射器列表
    port_mappers: Vec<Arc<dyn PortMapper>>,
    /// 映射生命周期管理器
    mapping_manager: Arc<MappingLifetimeManager>,
    /// 打洞器
    hole_puncher: Arc<HolePuncher>,
    /// NAT类型检测器
    nat_detector: Arc<NATTypeDetector>,
    /// 配置
    config: TraversalConfig,
    /// 状态
    status: RwLock<TraversalStatus>,
}

impl NATTraversalCoordinator {
    /// 创建新的NAT穿透协调器
    pub async fn new(config: TraversalConfig) -> Result<Self> {
        // 创建STUN客户端
        let stun_config = StunClientConfig {
            servers: config.stun_servers.clone(),
            request_timeout: config.request_timeout,
            cache_ttl: 300, // 5分钟
        };
        let stun_client = Arc::new(StunClient::new(stun_config));
        
        // 创建NAT类型检测器
        let nat_detector = Arc::new(NATTypeDetector::new(stun_client.clone()));
        
        // 创建端口映射器
        let mut port_mappers: Vec<Arc<dyn PortMapper>> = Vec::new();
        
        let mut upnp_supported = false;
        let mut natpmp_supported = false;
        let mut pcp_supported = false;
        
        if config.enable_upnp {
            match UPnPMapper::new().await {
                Ok(mapper) => {
                    upnp_supported = true;
                    port_mappers.push(Arc::new(mapper));
                },
                Err(e) => {
                    debug!("Failed to initialize UPnP mapper: {}", e);
                }
            }
        }
        
        if config.enable_natpmp {
            match NATPMPMapper::new().await {
                Ok(mapper) => {
                    natpmp_supported = true;
                    port_mappers.push(Arc::new(mapper));
                },
                Err(e) => {
                    debug!("Failed to initialize NAT-PMP mapper: {}", e);
                }
            }
        }
        
        if config.enable_pcp {
            match PCPMapper::new().await {
                Ok(mapper) => {
                    pcp_supported = true;
                    port_mappers.push(Arc::new(mapper));
                },
                Err(e) => {
                    debug!("Failed to initialize PCP mapper: {}", e);
                }
            }
        }
        
        // 创建映射生命周期管理器
        let mapping_config = MappingLifetimeManagerConfig {
            check_interval: config.mapping_check_interval,
            renew_ahead: 120, // 2分钟
            default_lifetime: config.mapping_lifetime,
        };
        let mapping_manager = Arc::new(MappingLifetimeManager::new(port_mappers.clone(), mapping_config));
        
        // 创建打洞器
        let hole_puncher = Arc::new(HolePuncher::new());
        
        // 创建状态
        let status = TraversalStatus {
            nat_type: NATType::Unknown,
            local_addr: None,
            mapped_addr: None,
            mapping_count: 0,
            upnp_supported,
            natpmp_supported,
            pcp_supported,
        };
        
        Ok(Self {
            stun_client,
            port_mappers,
            mapping_manager,
            hole_puncher,
            nat_detector,
            config,
            status: RwLock::new(status),
        })
    }
    
    /// 初始化NAT穿透
    pub async fn initialize(&self) -> Result<()> {
        // 启动映射生命周期管理器
        self.mapping_manager.start().await?;
        
        // 检测NAT类型
        let nat_type = self.nat_detector.detect_nat_type().await?;
        
        // 获取本地地址
        let local_addr = match tokio::net::UdpSocket::bind("0.0.0.0:0").await {
            Ok(socket) => {
                // 连接到公共DNS服务器
                if socket.connect("8.8.8.8:53").await.is_ok() {
                    socket.local_addr().ok()
                } else {
                    None
                }
            },
            Err(_) => None,
        };
        
        // 获取映射地址
        let mapped_addr = if let Some(local) = local_addr {
            self.stun_client.discover_binding(local).await.ok()
        } else {
            None
        };
        
        // 更新NAT检测器地址信息
        if let (Some(local), Some(mapped)) = (local_addr, mapped_addr) {
            self.nat_detector.update_addresses(local, mapped).await;
        }
        
        // 更新状态
        {
            let mut status = self.status.write().await;
            status.nat_type = nat_type;
            status.local_addr = local_addr;
            status.mapped_addr = mapped_addr;
        }
        
        info!("NAT traversal initialized: type={}, local={:?}, mapped={:?}",
            nat_type, local_addr, mapped_addr);
        
        Ok(())
    }
    
    /// 关闭NAT穿透
    pub async fn shutdown(&self) -> Result<()> {
        // 停止映射生命周期管理器
        self.mapping_manager.stop().await?;
        
        info!("NAT traversal shutdown");
        
        Ok(())
    }
    
    /// 创建端口映射
    pub async fn create_mapping(&self, protocol: Protocol, internal_port: u16, external_port: u16) -> Result<MappingInfo> {
        self.mapping_manager.create_mapping(protocol, internal_port, external_port).await
    }
    
    /// 删除端口映射
    pub async fn delete_mapping(&self, protocol: Protocol, external_port: u16) -> Result<()> {
        self.mapping_manager.remove_mapping(protocol, external_port).await
    }
    
    /// 获取所有映射
    pub async fn get_all_mappings(&self) -> Vec<MappingInfo> {
        self.mapping_manager.get_all_mappings().await
    }
    
    /// 获取状态
    pub async fn get_status(&self) -> TraversalStatus {
        // 更新映射数量
        let mapping_count = self.mapping_manager.get_all_mappings().await.len();
        
        let mut status = self.status.read().await.clone();
        status.mapping_count = mapping_count;
        
        status
    }
    
    /// 建立对等点连接
    pub async fn establish_connection(&self, peer_addr: SocketAddr) -> Result<TcpStream> {
        // 获取最佳连接策略
        let strategy = self.get_optimal_strategy(&peer_addr).await?;
        
        info!("Using {} strategy to connect to {}", strategy, peer_addr);
        
        match strategy {
            TraversalStrategy::Direct => {
                // 尝试直接连接
                match TcpStream::connect(peer_addr).await {
                    Ok(stream) => {
                        info!("Direct connection successful to {}", peer_addr);
                        Ok(stream)
                    },
                    Err(e) => Err(anyhow!("Direct connection failed: {}", e)),
                }
            },
            TraversalStrategy::PortMapping => {
                // 端口映射已建立，尝试直接连接对等点
                // 这里的连接是出站连接，端口映射主要用于入站连接，但在此策略下，
                // 我们假定网络环境已通过端口映射配置，允许更直接的连接尝试。
                match TcpStream::connect(peer_addr).await {
                    Ok(stream) => {
                        info!("Connection attempt through port mapping strategy successful to {}", peer_addr);
                        Ok(stream)
                    },
                    Err(e) => Err(anyhow!("Connection attempt through port mapping strategy failed: {}", e)),
                }
            },
            TraversalStrategy::TCPHolePunching => {
                // 获取本地地址
                let local_addr = match self.status.read().await.local_addr {
                    Some(addr) => SocketAddr::new(addr.ip(), 0), // 使用随机端口
                    None => return Err(anyhow!("No local address available")),
                };
                
                // 尝试TCP打洞
                match self.hole_puncher.tcp_hole_punching(local_addr, peer_addr, &self.nat_detector).await {
                    Ok(stream) => {
                        info!("TCP hole punching successful to {}", peer_addr);
                        Ok(stream)
                    },
                    Err(e) => Err(anyhow!("TCP hole punching failed: {}", e)),
                }
            },
            TraversalStrategy::UDPHolePunching => {
                // UDP打洞不能直接用于TCP连接
                Err(anyhow!("UDP hole punching cannot be used for TCP connections"))
            },
            TraversalStrategy::Relay => {
                // 实现中继
                Err(anyhow!("Relay not implemented yet"))
            },
        }
    }
    
    /// 获取最佳连接策略
    pub async fn get_optimal_strategy(&self, peer_addr: &SocketAddr) -> Result<TraversalStrategy> {
        // 获取NAT类型
        let nat_type = self.nat_detector.get_nat_type().await;
        
        // 根据NAT类型和对等点信息选择最佳策略
        match nat_type {
            NATType::Open => {
                // 开放互联网，直接连接
                Ok(TraversalStrategy::Direct)
            },
            NATType::FullCone => {
                // 完全锥形NAT，可以使用端口映射
                if !self.port_mappers.is_empty() {
                    Ok(TraversalStrategy::PortMapping)
                } else {
                    // 如果没有可用的端口映射器，尝试直接连接
                    Ok(TraversalStrategy::Direct)
                }
            },
            NATType::RestrictedCone | NATType::PortRestrictedCone => {
                // 受限锥形NAT，尝试打洞
                if self.config.enable_hole_punching {
                    Ok(TraversalStrategy::TCPHolePunching)
                } else if !self.port_mappers.is_empty() {
                    Ok(TraversalStrategy::PortMapping)
                } else {
                    // 如果没有可用的打洞或端口映射，尝试直接连接
                    Ok(TraversalStrategy::Direct)
                }
            },
            NATType::Symmetric => {
                // 对称NAT，尝试端口映射
                if !self.port_mappers.is_empty() {
                    Ok(TraversalStrategy::PortMapping)
                } else if self.config.enable_relay {
                    Ok(TraversalStrategy::Relay)
                } else {
                    // 如果没有可用的端口映射或中继，尝试直接连接
                    Ok(TraversalStrategy::Direct)
                }
            },
            NATType::Unknown => {
                // 未知NAT类型，尝试直接连接
                Ok(TraversalStrategy::Direct)
            },
        }
    }
}
