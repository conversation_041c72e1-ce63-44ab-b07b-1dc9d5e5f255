const { app, BrowserWindow, ipcMain, protocol } = require('electron');
const path = require('path');
const url = require('url');

// 保持对窗口对象的全局引用，避免JavaScript对象被垃圾回收时窗口关闭
let mainWindow;

// 创建主窗口
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 加载应用的主页面
  mainWindow.loadURL(
    process.env.NODE_ENV === 'development'
      ? 'http://localhost:9080'
      : url.format({
          pathname: path.join(__dirname, 'index.html'),
          protocol: 'file:',
          slashes: true
        })
  );

  // 当窗口关闭时触发
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 注册自定义协议
function registerProtocolHandler() {
  // 注册自定义协议
  app.setAsDefaultProtocolClient('lumen');

  // 处理自定义协议的URL
  const handleProtocolUrl = (protocolUrl) => {
    if (!protocolUrl) return;

    // 解析URL
    try {
      // 处理Windows下的协议URL格式 (lumen://download?url=https://example.com/file.zip)
      let urlStr = protocolUrl;
      if (process.platform === 'win32' && urlStr.startsWith('lumen://')) {
        urlStr = urlStr.replace('lumen://', 'lumen:');
      }

      const parsedUrl = new URL(urlStr);
      if (parsedUrl.protocol === 'lumen:') {
        // 提取下载链接
        let downloadUrl = '';
        
        // 尝试从查询参数中获取URL
        if (parsedUrl.searchParams.has('url')) {
          downloadUrl = parsedUrl.searchParams.get('url');
        } 
        // 如果查询参数中没有URL，则使用路径部分作为URL
        else if (parsedUrl.pathname && parsedUrl.pathname !== '/') {
          // 移除开头的斜杠
          downloadUrl = parsedUrl.pathname.startsWith('/') 
            ? parsedUrl.pathname.substring(1) 
            : parsedUrl.pathname;
          
          // 确保下载URL包含协议
          if (!downloadUrl.includes('://')) {
            downloadUrl = 'http://' + downloadUrl;
          }
        }

        if (downloadUrl) {
          console.log('接收到下载链接:', downloadUrl);
          // 将下载链接传递给渲染进程
          if (mainWindow && mainWindow.webContents) {
            mainWindow.webContents.send('add-download-url', downloadUrl);
          } else {
            // 如果窗口还未创建，保存URL以便窗口创建后使用
            global.downloadUrlFromProtocol = downloadUrl;
          }
        }
      }
    } catch (error) {
      console.error('解析协议URL失败:', error);
    }
  };

  // 处理从协议启动的情况 (Windows & Linux)
  const gotTheLock = app.requestSingleInstanceLock();
  if (!gotTheLock) {
    app.quit();
    return;
  }

  app.on('second-instance', (event, commandLine) => {
    // 有人试图运行第二个实例，我们应该聚焦到我们的窗口
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }

    // 在Windows上，commandLine包含启动应用程序的完整路径和参数
    // 我们需要查找协议URL
    if (process.platform === 'win32' || process.platform === 'linux') {
      const protocolUrl = commandLine.find(arg => arg.startsWith('lumen:'));
      if (protocolUrl) {
        handleProtocolUrl(protocolUrl);
      }
    }
  });

  // macOS 处理协议URL
  app.on('open-url', (event, url) => {
    event.preventDefault();
    handleProtocolUrl(url);
  });

  // 处理启动时的协议URL (Windows)
  if (process.platform === 'win32') {
    const protocolUrl = process.argv.find(arg => arg.startsWith('lumen:'));
    if (protocolUrl) {
      handleProtocolUrl(protocolUrl);
    }
  }
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.on('ready', () => {
  createWindow();
  registerProtocolHandler();

  // 如果有从协议启动保存的URL，发送给渲染进程
  if (global.downloadUrlFromProtocol && mainWindow && mainWindow.webContents) {
    setTimeout(() => {
      mainWindow.webContents.send('add-download-url', global.downloadUrlFromProtocol);
      global.downloadUrlFromProtocol = null;
    }, 1000); // 给渲染进程一些时间来初始化
  }
});

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  // 在macOS上，应用和菜单栏通常会保持活动状态，直到用户使用Cmd + Q明确退出
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，通常会在应用程序中重新创建一个窗口
  if (mainWindow === null) {
    createWindow();
  }
});

// 监听来自渲染进程的IPC消息
ipcMain.on('app-ready', () => {
  // 渲染进程已准备好接收消息
  if (global.downloadUrlFromProtocol) {
    mainWindow.webContents.send('add-download-url', global.downloadUrlFromProtocol);
    global.downloadUrlFromProtocol = null;
  }
});