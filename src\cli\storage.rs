//! CLI 存储包装模块
//!
//! 提供存储接口的包装实现。

use std::sync::Arc;
use std::path::Path;
use async_trait::async_trait;
use bytes::Bytes;

use crate::core::interfaces::storage::{Storage, StorageType};
use crate::core::error::CoreResult;

/// 包装Box<dyn Storage>以实现Storage trait
pub struct StorageWrapper(pub Box<dyn Storage>);

#[async_trait]
impl Storage for StorageWrapper {
    async fn write(&self, path: &str, data: &[u8]) -> CoreResult<()> {
        self.0.write(path, data).await
    }

    async fn write_at(&self, path: &str, data: &[u8], offset: u64) -> CoreResult<()> {
        self.0.write_at(path, data, offset).await
    }

    async fn read(&self, path: &str) -> CoreResult<Bytes> {
        self.0.read(path).await
    }

    async fn read_at(&self, path: &str, offset: u64, length: usize) -> CoreResult<Bytes> {
        self.0.read_at(path, offset, length).await
    }

    async fn delete(&self, path: &str) -> CoreResult<()> {
        self.0.delete(path).await
    }

    async fn exists(&self, path: &str) -> CoreResult<bool> {
        self.0.exists(path).await
    }

    async fn size(&self, path: &str) -> CoreResult<u64> {
        self.0.size(path).await
    }

    async fn create_dir(&self, path: &str) -> CoreResult<()> {
        self.0.create_dir(path).await
    }

    async fn list_dir(&self, path: &str) -> CoreResult<Vec<String>> {
        self.0.list_dir(path).await
    }

    async fn move_file(&self, src: &str, dst: &str) -> CoreResult<()> {
        self.0.move_file(src, dst).await
    }

    async fn copy_file(&self, src: &str, dst: &str) -> CoreResult<()> {
        self.0.copy_file(src, dst).await
    }

    fn storage_type(&self) -> StorageType {
        self.0.storage_type()
    }

    fn root_path(&self) -> &Path {
        self.0.root_path()
    }
}