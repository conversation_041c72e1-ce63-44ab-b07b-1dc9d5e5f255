// BitTorrent 上传模块
// 实现 BitTorrent 协议的上传功能，包括分片上传、做种和带宽管理

mod uploader;
mod strategy;
mod queue;
mod choking;
mod scheduler;
mod statistics;

// 重新导出主要类型
pub use uploader::{Uploader, PieceUploader};
pub use strategy::{UploadStrategy, StandardUploadStrategy};
pub use queue::{RequestQueue, UploadRequest};
pub use choking::{ChokingAlgorithm, StandardChokingAlgorithm};
pub use scheduler::{UploadScheduler, UploadTask};
pub use statistics::{UploadStatistics, UploadStats};

// 上传模块错误类型
use thiserror::Error;

#[derive(Error, Debug)]
pub enum UploadError {
    #[error("Invalid request: {0}")]
    InvalidRequest(String),
    
    #[error("Piece not found: {0}")]
    PieceNotFound(u32),
    
    #[error("Upload rejected: {0}")]
    Rejected(String),
    
    #[error("Upload limit exceeded")]
    LimitExceeded,
    
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("Other error: {0}")]
    Other(String),
}

// 上传配置
#[derive(Debug, Clone)]
pub struct UploadConfig {
    /// 是否启用上传
    pub enabled: bool,
    
    /// 最大上传速度（字节/秒），None表示不限制
    pub max_upload_rate: Option<u64>,
    
    /// 最大并发上传请求数
    pub max_concurrent_uploads: usize,
    
    /// 最大队列长度
    pub max_queue_length: usize,
    
    /// 阻塞算法间隔（秒）
    pub choking_interval: u64,
    
    /// 乐观解除阻塞间隔（秒）
    pub optimistic_unchoke_interval: u64,
    
    /// 最大解除阻塞对等点数量
    pub max_unchoked_peers: usize,
    
    /// 是否启用超级做种模式（不限制上传）
    pub super_seeding: bool,
}

impl Default for UploadConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_upload_rate: None,
            max_concurrent_uploads: 5,
            max_queue_length: 50,
            choking_interval: 10,
            optimistic_unchoke_interval: 30,
            max_unchoked_peers: 4,
            super_seeding: false,
        }
    }
}
