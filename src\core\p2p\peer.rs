use std::net::SocketAddr;
use std::time::Instant;
use async_trait::async_trait;
use anyhow::Result;

/// 对等点信息
#[derive(Debug, Clone)]
pub struct PeerInfo {
    /// 对等点ID
    pub id: Option<Vec<u8>>,
    /// 对等点地址
    pub addr: SocketAddr,
    /// 客户端名称
    pub client_name: Option<String>,
    /// 是否已连接
    pub connected: bool,
    /// 最后活动时间
    pub last_seen: Instant,
    /// 已下载字节数
    pub downloaded: u64,
    /// 已上传字节数
    pub uploaded: u64,
    /// 下载速度
    pub download_speed: u64,
    /// 上传速度
    pub upload_speed: u64,
}

impl PeerInfo {
    /// 创建新的对等点信息
    pub fn new(addr: SocketAddr) -> Self {
        Self {
            id: None,
            addr,
            client_name: None,
            connected: false,
            last_seen: Instant::now(),
            downloaded: 0,
            uploaded: 0,
            download_speed: 0,
            upload_speed: 0,
        }
    }
}

/// 对等点连接接口
/// 定义与对等点通信的基本操作
#[async_trait]
pub trait Peer: Send + Sync {
    /// 获取对等点信息
    fn info(&self) -> PeerInfo;
    
    /// 连接到对等点
    async fn connect(&mut self) -> Result<()>;
    
    /// 断开与对等点的连接
    async fn disconnect(&mut self) -> Result<()>;
    
    /// 发送消息到对等点
    async fn send_message(&mut self, message: &[u8]) -> Result<()>;
    
    /// 从对等点接收消息
    async fn receive_message(&mut self) -> Result<Vec<u8>>;
    
    /// 检查连接是否活跃
    fn is_connected(&self) -> bool;
    
    /// 获取下载速度
    fn download_speed(&self) -> u64;
    
    /// 获取上传速度
    fn upload_speed(&self) -> u64;
    
    /// 获取已下载字节数
    fn downloaded(&self) -> u64;
    
    /// 获取已上传字节数
    fn uploaded(&self) -> u64;

    /// 检查对等点是否拥有指定分片
    fn has_piece(&self, piece_index: u32) -> bool;

    /// 检查对等点是否阻塞我方
    fn is_choking(&self) -> bool;
    
    /// 添加对等点拥有的分片
    async fn add_piece(&mut self, piece_index: u32) -> Result<()>;
    
    /// 处理请求消息
    async fn handle_request(&mut self, piece_index: u32, offset: u32, length: u32) -> Result<()>;

    /// 处理取消请求消息
    async fn handle_cancel(&mut self, piece_index: u32, offset: u32, length: u32) -> Result<()>;

    /// 设置对等点的位图
    async fn set_bitfield(&mut self, bitfield: Vec<u8>) -> Result<()>;
    
    /// 设置对等点是否阻塞我方
    async fn set_peer_choking(&mut self, choking: bool) -> Result<()>;
    
    /// 设置对等点是否对我方感兴趣
    async fn set_peer_interested(&mut self, interested: bool) -> Result<()>;

    /// 获取拥有的所有分片索引
    fn pieces(&self) -> Vec<u32>;
}

/// 对等点工厂接口
/// 用于创建对等点连接
#[async_trait]
pub trait PeerFactory: Send + Sync {
    /// 创建对等点连接
    async fn create_peer(&self, addr: SocketAddr) -> Result<Box<dyn Peer>>;
}
