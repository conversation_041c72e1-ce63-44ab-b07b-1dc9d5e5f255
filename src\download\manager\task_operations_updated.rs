use anyhow::{Result, anyhow};
use chrono::Utc;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use tracing::{debug, info, warn, error};
use uuid::Uuid;

use crate::download::Downloader;
use super::models::{TaskInfo, TaskStatus, DownloadStats};
use crate::download::downloader_factory_impl::DownloaderFactoryImpl;
use super::event_handling::EventHandler;
use super::resume_handling::ResumeHandler;

/// 任务操作处理器，负责处理下载任务的各种操作
pub struct TaskOperations {
    tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
    downloaders: Arc<Mutex<HashMap<Uuid, Box<dyn Downloader>>>>,
    downloader_factory: Arc<DownloaderFactoryImpl>,
    event_handler: Arc<EventHandler>,
    resume_handler: Arc<ResumeHandler>,
}

impl TaskOperations {
    /// 创建一个新的任务操作处理器
    pub fn new(
        tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
        downloaders: Arc<Mutex<HashMap<Uuid, Box<dyn Downloader>>>>,
        downloader_factory: Arc<DownloaderFactoryImpl>,
        event_handler: Arc<EventHandler>,
        resume_handler: Arc<ResumeHandler>,
    ) -> Self {
        Self {
            tasks,
            downloaders,
            downloader_factory,
            event_handler,
            resume_handler,
        }
    }

    /// 获取下载统计信息
    pub async fn get_download_stats(&self) -> Result<DownloadStats> {
        let tasks = self.tasks.read().await;
        let mut stats = DownloadStats::default();
        
        // 统计总任务数
        stats.total_downloads = tasks.len();
        
        // 遍历所有任务，统计不同状态的任务数量和总下载/上传字节数
        for task in tasks.values() {
            match task.status {
                TaskStatus::Downloading => stats.active_downloads += 1,
                TaskStatus::Completed => stats.completed_downloads += 1,
                TaskStatus::Failed => stats.failed_downloads += 1,
                _ => {}, // 其他状态不计入特定分类
            }
            
            // 累计已下载字节数
            stats.total_downloaded_bytes += task.downloaded_bytes;
            
            // 如果需要，还可以统计上传字节数（对于P2P协议）
            stats.total_uploaded_bytes += task.uploaded_bytes.unwrap_or(0);
        }
        
        debug!("获取下载统计信息: {:?}", stats);
        Ok(stats)
    }

    /// 添加新任务
    pub async fn add_task(&self, task: TaskInfo) -> Result<Uuid> {
        // 获取任务ID
        let task_id = task.id;
        
        // 保存任务信息
        {
            let mut tasks = self.tasks.write().await;
            tasks.insert(task_id, task.clone());
        }
        
        // 保存任务信息到恢复管理器
        self.resume_handler.save_task(&task).await?;
        debug!("Task saved to resume manager: {}", task_id);
        
        Ok(task_id)
    }

    /// 更新任务信息
    pub async fn update_task(&self, task_id: Uuid, task: TaskInfo) -> Result<()> {
        // 获取任务锁
        let mut tasks = self.tasks.write().await;
        
        // 检查任务是否存在
        if !tasks.contains_key(&task_id) {
            return Err(anyhow!("Task not found"));
        }
        
        // 更新任务信息
        tasks.insert(task_id, task);
        
        Ok(())
    }

    /// 获取任务信息
    pub async fn get_task(&self, task_id: Uuid) -> Result<TaskInfo> {
        let tasks = self.tasks.read().await;
        
        if let Some(task) = tasks.get(&task_id) {
            Ok(task.clone())
        } else {
            Err(anyhow!("Task not found"))
        }
    }

    /// 获取所有任务
    pub async fn get_all_tasks(&self) -> Result<Vec<TaskInfo>> {
        let tasks = self.tasks.read().await;
        let task_list = tasks.values().cloned().collect();
        Ok(task_list)
    }

    /// 更新任务状态
    pub async fn update_task_status(&self, task_id: Uuid, status: TaskStatus, error_message: Option<String>) -> Result<()> {
        // 获取任务锁
        let mut tasks = self.tasks.write().await;
        
        // 检查任务是否存在
        if let Some(task) = tasks.get_mut(&task_id) {
            // 更新状态
            task.status = status;
            
            // 如果有错误消息，更新错误消息
            if let Some(message) = error_message {
                task.error_message = Some(message);
            }
            
            // 如果任务正在下载，更新进度和速度
            if status == TaskStatus::Downloading {
                // 获取下载器
                let downloaders = self.downloaders.lock().await;
                
                if let Some(downloader) = downloaders.get(&task_id) {
                    // 获取进度
                    match downloader.progress().await {
                        Ok(progress) => {
                            task.progress = progress.progress_percentage;
                            task.speed = progress.speed;
                            task.downloaded_bytes = progress.downloaded_size;
                            task.total_bytes = progress.total_size;
                            task.eta = progress.eta;
                        },
                        Err(e) => {
                            warn!("Failed to get progress for task {}: {}", task_id, e);
                        }
                    }
                }
            }
            
            // 如果任务已完成，更新完成时间
            if status == TaskStatus::Completed {
                task.completed_at = Some(Utc::now());
            }
            
            // 通知事件
            let task_clone = task.clone();
            tokio::spawn(async move {
                if let Err(e) = self.event_handler.notify_task_status_changed(task_clone).await {
                    error!("Failed to notify task status changed: {}", e);
                }
            });
            
            // 保存任务信息到恢复管理器
            let task_clone = task.clone();
            tokio::spawn(async move {
                if let Err(e) = self.resume_handler.save_task(&task_clone).await {
                    error!("Failed to save task to resume manager: {}", e);
                }
            });
            
            Ok(())
        } else {
            Err(anyhow!("Task not found"))
        }
    }

    /// 启动下载任务
    pub async fn start_task(&self, task_id: Uuid) -> Result<()> {
        // 获取任务信息
        let task = self.get_task(task_id).await?;
        
        // 检查任务状态
        if task.status == TaskStatus::Downloading {
            return Ok(());
        }
        
        // 更新任务状态为初始化中
        self.update_task_status(task_id, TaskStatus::Initializing, None).await?;
        
        // 检查下载器是否已存在
        let mut downloaders = self.downloaders.lock().await;
        
        if !downloaders.contains_key(&task_id) {
            // 创建下载器
            let downloader = self.downloader_factory.create_downloader(&task.url, &task.output_path, task_id)
                .await
                .map_err(|e| anyhow!("Failed to create downloader: {}", e))?;
            
            // 保存下载器
            downloaders.insert(task_id, downloader);
        }
        
        // 获取下载器
        let downloader = downloaders.get_mut(&task_id).unwrap();
        
        // 启动下载
        match downloader.start().await {
            Ok(_) => {
                // 检查是否支持范围请求（在下载开始后检查，因为此时已经发送了HEAD请求）
                if let Some(http_downloader) = downloader.as_any().downcast_ref::<crate::protocols::http::downloader::HttpDownloader>() {
                    let supports_range = http_downloader.supports_range_requests();
                    if !supports_range {
                        info!("HTTP server does not support range requests for {}, pause/resume will restart the download", task.url);
                    } else {
                        debug!("HTTP server supports range requests for {}, pause/resume is fully supported", task.url);
                    }
                }
                
                // 更新任务状态为下载中
                drop(downloaders); // 释放锁
                self.update_task_status(task_id, TaskStatus::Downloading, None).await?;
                
                // 启动进度监控
                self.start_progress_monitor(task_id);
                
                Ok(())
            },
            Err(e) => {
                // 更新任务状态为失败
                drop(downloaders); // 释放锁
                self.update_task_status(task_id, TaskStatus::Failed, Some(e.to_string())).await?;
                
                Err(anyhow!("Failed to start download: {}", e))
            }
        }
    }

    /// 启动进度监控
    fn start_progress_monitor(&self, task_id: Uuid) {
        let tasks = self.tasks.clone();
        let downloaders = self.downloaders.clone();
        let event_handler = self.event_handler.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(1));
            
            loop {
                interval.tick().await;
                
                // 获取任务信息
                let task_opt = {
                    let tasks = tasks.read().await;
                    tasks.get(&task_id).cloned()
                };
                
                if let Some(mut task) = task_opt {
                    // 检查任务是否仍在下载中
                    if task.status != TaskStatus::Downloading {
                        break;
                    }
                    
                    // 获取下载器
                    let downloaders_guard = downloaders.lock().await;
                    
                    if let Some(downloader) = downloaders_guard.get(&task_id) {
                        // 获取进度
                        match downloader.progress().await {
                            Ok(progress) => {
                                // 更新任务进度
                                task.progress = progress.progress_percentage;
                                task.speed = progress.speed;
                                task.downloaded_bytes = progress.downloaded_size;
                                task.total_bytes = progress.total_size;
                                task.eta = progress.eta;
                                
                                // 更新任务信息
                                let mut tasks = tasks.write().await;
                                if let Some(t) = tasks.get_mut(&task_id) {
                                    t.progress = task.progress;
                                    t.speed = task.speed;
                                    t.downloaded_bytes = task.downloaded_bytes;
                                    t.total_bytes = task.total_bytes;
                                    t.eta = task.eta;
                                }
                                
                                // 通知进度更新
                                if let Err(e) = event_handler.notify_task_progress_updated(task.clone()).await {
                                    error!("Failed to notify task progress updated: {}", e);
                                }
                                
                                // 检查是否下载完成
                                if progress.progress_percentage >= 100.0 {
                                    // 释放锁
                                    drop(downloaders_guard);
                                    
                                    // 更新任务状态为已完成
                                    let task_operations = TaskOperations {
                                        tasks: tasks.clone(),
                                        downloaders: downloaders.clone(),
                                        downloader_factory: Arc::new(DownloaderFactoryImpl::new(
                                            Arc::new(crate::config::ConfigManager::new()),
                                            Arc::new(crate::analyzer::link_analyzer::DefaultLinkAnalyzer::new()),
                                            Arc::new(crate::download::resume::ResumeManagerImpl::new(
                                                crate::config::Settings::default(),
                                                Arc::new(crate::storage::local::LocalStorage::new("./downloads").unwrap())
                                            ))
                                        )),
                                        event_handler: event_handler.clone(),
                                        resume_handler: Arc::new(crate::download::manager::resume_handling::ResumeHandler::new(
                                            crate::config::Settings::default(),
                                            Arc::new(crate::download::resume::ResumeManagerImpl::new(
                                                crate::config::Settings::default(),
                                                Arc::new(crate::storage::local::LocalStorage::new("./downloads").unwrap())
                                            )),
                                            tasks.clone()
                                        )),
                                    };
                                    
                                    if let Err(e) = task_operations.update_task_status(task_id, TaskStatus::Completed, None).await {
                                        error!("Failed to update task status to completed: {}", e);
                                    }
                                    
                                    break;
                                }
                            },
                            Err(e) => {
                                warn!("Failed to get progress for task {}: {}", task_id, e);
                            }
                        }
                    } else {
                        // 下载器不存在，退出监控
                        break;
                    }
                } else {
                    // 任务不存在，退出监控
                    break;
                }
            }
        });
    }

    /// 暂停下载任务
    pub async fn pause_task(&self, task_id: Uuid) -> Result<()> {
        // 获取任务信息
        let task = self.get_task(task_id).await?;
        
        // 检查任务状态
        if task.status != TaskStatus::Downloading {
            return Ok(());
        }
        
        // 获取下载器
        let mut downloaders = self.downloaders.lock().await;
        
        if let Some(downloader) = downloaders.get_mut(&task_id) {
            // 暂停下载
            match downloader.pause().await {
                Ok(_) => {
                    // 更新任务状态为暂停
                    drop(downloaders); // 释放锁
                    self.update_task_status(task_id, TaskStatus::Paused, None).await?;
                    
                    // 保存任务信息到恢复管理器
                    let task = self.get_task(task_id).await?;
                    self.resume_handler.save_task(&task).await?;
                    
                    Ok(())
                },
                Err(e) => {
                    Err(anyhow!("Failed to pause download: {}", e))
                }
            }
        } else {
            Err(anyhow!("Downloader not found"))
        }
    }

    /// 恢复下载任务
    pub async fn resume_task(&self, task_id: Uuid) -> Result<()> {
        // 获取任务信息
        let task = self.get_task(task_id).await?;
        
        // 检查任务状态
        if task.status != TaskStatus::Paused {
            return Ok(());
        }
        
        // 获取下载器
        let mut downloaders = self.downloaders.lock().await;
        
        // 检查下载器是否存在
        if !downloaders.contains_key(&task_id) {
            // 创建下载器
            let downloader = self.downloader_factory.create_downloader(&task.url, &task.output_path, task_id)
                .await
                .map_err(|e| anyhow!("Failed to create downloader: {}", e))?;
            
            // 保存下载器
            downloaders.insert(task_id, downloader);
        }
        
        // 获取下载器
        let downloader = downloaders.get_mut(&task_id).unwrap();
        
        // 检查是否支持范围请求
        if let Some(http_downloader) = downloader.as_any().downcast_ref::<crate::protocols::http::downloader::HttpDownloader>() {
            let supports_range = http_downloader.supports_range_requests();
            if !supports_range {
                info!("HTTP server does not support range requests for {}, resuming may restart the download", task.url);
            } else {
                debug!("HTTP server supports range requests for {}, can resume from {} bytes", 
                      task.url, task.downloaded_bytes);
            }
        }
        
        // 恢复下载
        match downloader.resume().await {
            Ok(_) => {
                // 更新任务状态为下载中
                drop(downloaders); // 释放锁
                self.update_task_status(task_id, TaskStatus::Downloading, None).await?;
                
                // 启动进度监控
                self.start_progress_monitor(task_id);
                
                Ok(())
            },
            Err(e) => {
                Err(anyhow!("Failed to resume download: {}", e))
            }
        }
    }

    /// 取消下载任务
    pub async fn cancel_task(&self, task_id: Uuid) -> Result<()> {
        // 获取任务信息
        let task = self.get_task(task_id).await?;
        
        // 检查任务状态
        if task.status == TaskStatus::Cancelled || task.status == TaskStatus::Completed {
            return Ok(());
        }
        
        // 获取下载器
        let mut downloaders = self.downloaders.lock().await;
        
        if let Some(downloader) = downloaders.get_mut(&task_id) {
            // 取消下载
            match downloader.cancel().await {
                Ok(_) => {
                    // 移除下载器
                    downloaders.remove(&task_id);
                    
                    // 更新任务状态为取消
                    drop(downloaders); // 释放锁
                    self.update_task_status(task_id, TaskStatus::Cancelled, None).await?;
                    
                    Ok(())
                },
                Err(e) => {
                    Err(anyhow!("Failed to cancel download: {}", e))
                }
            }
        } else {
            // 下载器不存在，直接更新状态
            self.update_task_status(task_id, TaskStatus::Cancelled, None).await?;
            Ok(())
        }
    }

    /// 移除下载任务
    pub async fn remove_task(&self, task_id: Uuid) -> Result<()> {
        // 先取消任务
        let _ = self.cancel_task(task_id).await;
        
        // 移除任务
        {
            let mut tasks = self.tasks.write().await;
            tasks.remove(&task_id);
        }
        
        // 从恢复管理器中移除任务
        self.resume_handler.remove_task(task_id).await?;
        
        Ok(())
    }
}