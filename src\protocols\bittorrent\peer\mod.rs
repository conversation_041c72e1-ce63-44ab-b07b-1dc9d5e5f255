//! BitTorrent对等点模块

pub mod connection;
pub mod state;

// 重新导出BitTorrentPeer类型
pub use super::peer_impl::BitTorrentPeer;
pub use crate::protocols::bittorrent::peer::connection::PeerConnectionManager;

use std::sync::Arc;
use anyhow::Result;
use crate::protocols::bittorrent::peer_quality::manager::PeerQualityManager;

/// 对等点消息处理器
#[derive(Clone)]
pub struct PeerMessageHandler {
    // 消息处理器字段
    quality_manager: Option<Arc<PeerQualityManager>>,
}

impl PeerMessageHandler {
    /// 创建新的对等点消息处理器
    pub fn new() -> Self {
        Self {
            quality_manager: None,
        }
    }
    
    /// 设置对等点质量管理器
    pub fn set_quality_manager(&mut self, quality_manager: Arc<PeerQualityManager>) {
        self.quality_manager = Some(quality_manager);
    }
    
    /// 处理来自对等点的消息
    pub async fn process_message(&self, peer: &mut BitTorrentPeer, message: &[u8]) -> Result<()> {
        // 消息处理逻辑
        // 如果有质量管理器，可以在这里更新对等点的质量评分
        if let Some(qm) = &self.quality_manager {
            // 可以在这里添加对等点质量评分的更新逻辑
        }
        
        Ok(())
    }
}