use anyhow::{Result, anyhow};
use tracing::{debug, warn};

use crate::protocols::bittorrent::tracker::TrackerResponse;

use super::core::BitTorrentProtocol;

impl BitTorrentProtocol {
    /// Announce to tracker
    pub(crate) async fn announce_to_tracker(&mut self, event: &str) -> Result<TrackerResponse> {
        let torrent_info = self.torrent_info.as_ref()
            .ok_or_else(|| anyhow!("Torrent info not available"))?;

        let tracker_manager = self.tracker_manager.as_mut()
            .ok_or_else(|| anyhow!("Tracker manager not initialized"))?;

        let left = torrent_info.total_size.saturating_sub(self.downloaded);

        // 确保Tracker管理器已初始化
        if !tracker_manager.is_initialized() {
            tracker_manager.init_trackers(torrent_info).await?;
        }

        // 尝试向所有可用的Tracker宣告
        match tracker_manager.announce(torrent_info, event, self.uploaded, self.downloaded, left).await {
            Ok(response) => {
                debug!("Successfully announced to tracker, got {} peers", response.peers.len());
                Ok(response)
            },
            Err(e) => {
                warn!("Failed to announce to all trackers: {}", e);
                Err(e)
            }
        }
    }

    /// Connect to peers from tracker response
    pub(crate) async fn connect_to_peers(&mut self, tracker_response: TrackerResponse) -> Result<()> {
        if let Some(peer_connection_manager) = &mut self.peer_connection_manager {
            if let Some(torrent_info) = &self.torrent_info {
                peer_connection_manager.add_peers_from_tracker(tracker_response, &torrent_info).await?;
            }
        }

        Ok(())
    }
}
