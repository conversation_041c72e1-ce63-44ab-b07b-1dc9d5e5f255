use anyhow::{Result, anyhow};
use std::collections::HashMap;
use tracing::{debug, warn};

/// 块收集器，用于收集分片的块
#[derive(Debug, Clone)]
pub struct BlockCollector {
    /// 分片索引
    pub piece_index: u32,
    /// 分片大小
    pub piece_size: u64,
    /// 块大小
    pub block_size: u32,
    /// 已收集的块
    pub blocks: HashMap<u32, Vec<u8>>,
    /// 总块数
    pub total_blocks: usize,
    /// 已收集的块数
    pub collected_blocks: usize,
    /// 是否完成
    pub complete: bool,
    /// 最后活动时间
    pub last_activity: std::time::Instant,
}

impl BlockCollector {
    /// 创建新的块收集器
    pub fn new(piece_index: u32, piece_size: u64, block_size: u32) -> Self {
        // 计算总块数
        let total_blocks = ((piece_size + block_size as u64 - 1) / block_size as u64) as usize;

        Self {
            piece_index,
            piece_size,
            block_size,
            blocks: HashMap::with_capacity(total_blocks),
            total_blocks,
            collected_blocks: 0,
            complete: false,
            last_activity: std::time::Instant::now(),
        }
    }

    /// 添加块数据
    pub fn add_block(&mut self, offset: u32, data: Vec<u8>) -> Result<bool> {
        // 检查偏移量是否有效
        if offset % self.block_size != 0 && offset != 0 {
            return Err(anyhow!("Invalid block offset: {}, must be multiple of block size {}", offset, self.block_size));
        }

        // 检查是否已经收集了此块
        if self.blocks.contains_key(&offset) {
            debug!("Block at offset {} already collected", offset);
            return Ok(false);
        }

        // 检查数据大小是否合理
        let expected_size = if offset + self.block_size > self.piece_size as u32 {
            self.piece_size as u32 - offset
        } else {
            self.block_size
        };

        if data.len() != expected_size as usize {
            return Err(anyhow!("Block data size mismatch: expected {}, got {}", expected_size, data.len()));
        }

        // 添加块
        self.blocks.insert(offset, data);
        self.collected_blocks += 1;

        // 更新最后活动时间
        self.last_activity = std::time::Instant::now();

        // 检查是否收集完成
        if self.collected_blocks == self.total_blocks {
            self.complete = true;
        }

        Ok(true)
    }

    /// 获取完整的分片数据
    pub fn get_piece_data(&self) -> Result<Vec<u8>> {
        if !self.complete {
            return Err(anyhow!("Cannot get piece data: not all blocks collected"));
        }

        // 按偏移量排序块
        let mut offsets: Vec<u32> = self.blocks.keys().cloned().collect();
        offsets.sort();

        // 合并块数据
        let mut piece_data = Vec::with_capacity(self.piece_size as usize);
        for offset in offsets {
            if let Some(data) = self.blocks.get(&offset) {
                piece_data.extend_from_slice(data);
            } else {
                return Err(anyhow!("Missing block at offset {}", offset));
            }
        }

        // 检查数据大小
        if piece_data.len() != self.piece_size as usize {
            warn!("Piece data size mismatch: expected {}, got {}", self.piece_size, piece_data.len());
        }

        Ok(piece_data)
    }

    /// 获取进度
    pub fn progress(&self) -> f64 {
        self.collected_blocks as f64 / self.total_blocks as f64 * 100.0
    }

    /// 获取缺失的块偏移量
    pub fn missing_blocks(&self) -> Vec<u32> {
        let mut missing = Vec::new();

        // 计算所有可能的偏移量
        for i in 0..self.total_blocks {
            let offset = i as u32 * self.block_size;
            if !self.blocks.contains_key(&offset) {
                missing.push(offset);
            }
        }

        missing
    }

    /// 重置块收集器
    pub fn reset(&mut self) {
        self.blocks.clear();
        self.collected_blocks = 0;
        self.complete = false;
        self.last_activity = std::time::Instant::now();
    }
}
