use async_trait::async_trait;
use sha1::{Sha1, Digest};
use std::collections::{HashSet, VecDeque};
use std::path::Path;
use tracing::{debug, info, warn};
use tokio::sync::broadcast;
use anyhow::Error;

use crate::core::p2p::piece::PIECE_CACHE;

use crate::core::p2p::piece::{PieceManager, PieceState, PieceInfo};
use super::torrent::TorrentInfo;
use super::file_manager::FileManager;
use super::piece_selector::PieceSelector;
use super::block_manager::{BlockManagerCallback, PieceCompleteCallback, BlockAddedCallback};
use super::piece_manager_trait::PieceManagerTrait;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

/// BitTorrent分片块
#[derive(Debug, Clone)]
pub struct Block {
    /// 分片索引
    pub piece_index: u32,
    /// 块在分片中的偏移量
    pub offset: u32,
    /// 块大小
    pub size: u32,
    /// 块数据
    pub data: Option<Vec<u8>>,
    /// 块状态
    pub state: BlockState,
    /// 请求时间
    pub request_time: Option<std::time::Instant>,
}

/// 块状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BlockState {
    /// 缺失
    Missing,
    /// 已请求
    Requested,
    /// 已下载
    Downloaded,
}

/// BitTorrent分片
#[derive(Debug, Clone)]
pub struct Piece {
    /// 分片索引
    pub index: u32,
    /// 分片大小
    pub size: u64,
    /// 分片哈希
    pub hash: Vec<u8>,
    /// 分片状态
    pub state: PieceState,
    /// 分片优先级
    pub priority: u8,
    /// 分片块
    pub blocks: Vec<Block>,
    /// 已下载的块数量
    pub blocks_downloaded: usize,
    /// 总块数
    pub blocks_total: usize,
    /// 下载进度
    pub progress: f64,
    /// 稀有度（拥有此分片的对等点数量）
    pub rarity: usize,
}

impl Piece {
    /// 创建新的分片
    pub fn new(index: u32, size: u64, hash: Vec<u8>, block_size: u32) -> Self {
        // 计算块数量
        let blocks_total = ((size + block_size as u64 - 1) / block_size as u64) as usize;
        let mut blocks = Vec::with_capacity(blocks_total);

        // 创建块
        for i in 0..blocks_total {
            let offset = i as u32 * block_size;
            let block_size = if i == blocks_total - 1 && size % block_size as u64 != 0 {
                (size % block_size as u64) as u32
            } else {
                block_size
            };

            blocks.push(Block {
                piece_index: index,
                offset,
                size: block_size,
                data: None,
                state: BlockState::Missing,
                request_time: None,
            });
        }

        Self {
            index,
            size,
            hash,
            state: PieceState::Missing,
            priority: 1, // 默认优先级
            blocks,
            blocks_downloaded: 0,
            blocks_total,
            progress: 0.0,
            rarity: 0,
        }
    }

    /// 添加块数据
    pub fn add_block_data(&mut self, offset: u32, data: Vec<u8>) -> Result<bool,BitTorrentError> {
        // 查找对应的块
        for block in &mut self.blocks {
            if block.offset == offset {
                // 检查数据大小
                if data.len() != block.size as usize {
                    return Err(BitTorrentError::from(format!("Block data size mismatch: expected {}, got {}", block.size, data.len())));
                }

                // 更新块状态
                block.data = Some(data);
                block.state = BlockState::Downloaded;
                self.blocks_downloaded += 1;

                // 更新进度
                self.progress = self.blocks_downloaded as f64 / self.blocks_total as f64 * 100.0;

                // 如果所有块都已下载，更新分片状态
                if self.blocks_downloaded == self.blocks_total {
                    self.state = PieceState::Downloaded;
                }

                return Ok(true);
            }
        }

        Err(BitTorrentError::from(format!("Block not found for offset {}", offset)))
    }

    /// 验证分片
    pub fn verify(&mut self) -> Result<bool,BitTorrentError> {
        // 检查是否所有块都已下载
        if self.blocks_downloaded < self.blocks_total {
            return Err(BitTorrentError::from("Cannot verify incomplete piece"));
        }

        // 合并所有块数据
        let mut piece_data = Vec::with_capacity(self.size as usize);
        for block in &self.blocks {
            if let Some(data) = &block.data {
                piece_data.extend_from_slice(data);
            } else {
                return Err(BitTorrentError::from("Missing block data"));
            }
        }

        // 计算哈希
        let mut hasher = Sha1::new();
        hasher.update(&piece_data);
        let hash = hasher.finalize().to_vec();

        // 验证哈希
        let valid = hash == self.hash;
        if valid {
            self.state = PieceState::Verified;
        } else {
            // 重置分片状态
            self.state = PieceState::Failed;
            self.blocks_downloaded = 0;
            self.progress = 0.0;
            for block in &mut self.blocks {
                block.data = None;
                block.state = BlockState::Missing;
                block.request_time = None;
            }
        }

        Ok(valid)
    }

    /// 获取下一个要请求的块
    pub fn next_block(&mut self) -> Option<&mut Block> {
        for block in &mut self.blocks {
            if block.state == BlockState::Missing {
                block.state = BlockState::Requested;
                block.request_time = Some(std::time::Instant::now());
                return Some(block);
            }
        }
        None
    }

    /// 重置超时的块请求
    pub fn reset_timed_out_blocks(&mut self, timeout: std::time::Duration) -> usize {
        let now = std::time::Instant::now();
        let mut reset_count = 0;

        for block in &mut self.blocks {
            if block.state == BlockState::Requested {
                if let Some(request_time) = block.request_time {
                    if now.duration_since(request_time) > timeout {
                        block.state = BlockState::Missing;
                        block.request_time = None;
                        reset_count += 1;
                    }
                }
            }
        }

        reset_count
    }
    
    /// 设置分片优先级
    pub fn set_priority(&mut self, priority: u8) {
        self.priority = priority;
    }
}

/// 分片选择策略
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PieceSelectionStrategy {
    /// 顺序选择
    Sequential,
    /// 最稀有优先
    RarestFirst,
    /// 随机首块
    RandomFirst,
    /// 端游模式
    EndGame,
}

/// 分片相关事件
#[derive(Debug, Clone)]
pub enum PieceEvent {
    PieceCompleted { index: u32 },
    BlockCompleted { piece_index: u32, offset: u32 },
    // 可扩展其它事件
}

/// BitTorrent分片管理器
pub struct BitTorrentPieceManager {
    /// 分片列表
    pub pieces: Vec<Piece>,
    /// 分片状态
    pub piece_states: Vec<PieceState>,
    /// 已下载的分片数量
    pub pieces_downloaded: usize,
    /// 已验证的分片数量
    pub pieces_verified: usize,
    /// 总分片数量
    pub pieces_total: usize,
    /// 已下载的数据大小
    pub downloaded_size: u64,
    /// 总数据大小
    pub total_size: u64,
    /// 下载进度
    pub progress: f64,
    /// 块大小
    pub block_size: u32,
    /// 下载队列
    pub download_queue: VecDeque<u32>,
    /// 已初始化
    pub initialized: bool,
    /// 已请求的分片集合
    pub requested_pieces: HashSet<u32>,
    /// 文件管理器
    pub file_manager: FileManager,
    /// 分片选择器
    pub piece_selector: PieceSelector,
    /// 事件通道（可选，便于事件驱动解耦）
    pub event_sender: Option<broadcast::Sender<PieceEvent>>,
}

impl BitTorrentPieceManager {
    /// 创建新的BitTorrent分片管理器，使用默认的RandomFirst策略
    pub async fn new(torrent_info: TorrentInfo, output_path: impl AsRef<Path>) -> Result<Self,BitTorrentError> {
        // 使用默认的RandomFirst策略
        Self::new_with_strategy(torrent_info, output_path, PieceSelectionStrategy::RandomFirst).await
    }
    
    /// 创建新的BitTorrent分片管理器，使用指定的分片选择策略
    pub async fn new_with_strategy(
        torrent_info: TorrentInfo, 
        output_path: impl AsRef<Path>,
        initial_strategy: PieceSelectionStrategy
    ) -> Result<Self,BitTorrentError> {
        let total_size = torrent_info.total_size;
        let piece_length = torrent_info.piece_length;
        let pieces_total = ((total_size + piece_length - 1) / piece_length) as usize;

        // 默认块大小为16KB
        let block_size = 16384;

        // 创建分片列表
        let mut pieces = Vec::with_capacity(pieces_total);
        let mut piece_states = Vec::with_capacity(pieces_total);

        // 检查pieces哈希列表长度
        if torrent_info.pieces.len() != pieces_total * 20 && !torrent_info.pieces.is_empty() {
            warn!("Pieces hash list length mismatch: expected {}, got {}", pieces_total * 20, torrent_info.pieces.len());
        }

        // 创建分片
        for i in 0..pieces_total {
            let piece_size = if i == pieces_total - 1 && total_size % piece_length != 0 {
                total_size % piece_length
            } else {
                piece_length
            };

            // 获取分片哈希
            let hash = if !torrent_info.pieces.is_empty() && i < torrent_info.pieces.len() {
                torrent_info.pieces[i].clone()
            } else {
                // 对于磁力链接，可能没有哈希信息
                vec![0; 20]
            };

            pieces.push(Piece::new(i as u32, piece_size, hash, block_size));
            piece_states.push(PieceState::Missing);
        }

        // 创建文件管理器
        let file_manager = FileManager::new(torrent_info.clone(), output_path).await?;

        // 创建分片选择器，使用指定的初始策略
        let piece_selector = PieceSelector::new(
            initial_strategy, // 使用传入的初始策略
            4, // 端游模式阈值
            4, // 随机首块数量
            pieces_total
        );

        Ok(Self {
            pieces,
            piece_states,
            pieces_downloaded: 0,
            pieces_verified: 0,
            pieces_total,
            downloaded_size: 0,
            total_size,
            progress: 0.0,
            block_size,
            download_queue: VecDeque::new(),
            initialized: false,
            requested_pieces: HashSet::new(),
            file_manager,
            piece_selector,
            event_sender: None,
        })
    }

    /// 获取种子信息
    pub fn get_torrent_info(&self) -> &TorrentInfo {
        self.file_manager.get_torrent_info()
    }

    /// 更新分片稀有度
    pub fn update_piece_rarity(&mut self, peer_pieces: &HashSet<u32>) {
        for piece_index in peer_pieces {
            if *piece_index < self.pieces.len() as u32 {
                self.pieces[*piece_index as usize].rarity += 1;
            }
        }
    }

    /// 选择下一个要下载的分片
    pub fn select_next_piece(&mut self, peer_pieces: &HashSet<u32>) -> Option<u32> {
        // 更新分片选择器的状态
        self.piece_selector.update_verified_pieces(self.pieces_verified);

        // 收集分片稀有度
        let piece_rarities: Vec<usize> = self.pieces.iter().map(|p| p.rarity).collect();

        // 使用分片选择器选择下一个分片
        self.piece_selector.select_next_piece(
            peer_pieces,
            &self.piece_states,
            &self.requested_pieces
        )
    }

    /// 写入分片数据到文件
    async fn write_piece_to_files(&mut self, piece_index: u32) -> Result<(),BitTorrentError> {
        let piece = &self.pieces[piece_index as usize];

        // 检查分片状态
        if piece.state != PieceState::Verified {
            return Err(BitTorrentError::Other("Cannot write unverified piece".to_string()));
        }

        // 合并所有块数据
        let mut piece_data = Vec::with_capacity(piece.size as usize);
        for block in &piece.blocks {
            if let Some(data) = &block.data {
                piece_data.extend_from_slice(data);
            } else {
                return Err(BitTorrentError::Other("Missing block data".to_string()));
            }
        }

        // 使用文件管理器写入数据
        self.file_manager.write_piece_to_files(piece_index, &piece_data).await?;

        Ok(())
    }

    /// 在分片完成时发送事件
    pub async fn notify_piece_completed(&self, index: u32) {
        if let Some(sender) = &self.event_sender {
            let _ = sender.send(PieceEvent::PieceCompleted { index });
        }
    }
}

#[async_trait]
impl PieceManager for BitTorrentPieceManager {
    async fn init(&mut self) -> Result<(),BitTorrentError> {
        if self.initialized {
            return Ok(());
        }

        // 初始化下载队列
        self.download_queue.clear();
        for i in 0..self.pieces.len() {
            if self.pieces[i].state == PieceState::Missing {
                self.download_queue.push_back(i as u32);
            }
        }

        // 设置初始策略为随机首块
        self.piece_selector.set_strategy(PieceSelectionStrategy::RandomFirst);

        self.initialized = true;
        info!("BitTorrent piece manager initialized with {} pieces", self.pieces.len());

        Ok(())
    }

    async fn next_piece(&mut self) -> Result<Option<PieceInfo>, BitTorrentError> {
        // 从下载队列中获取下一个分片
        if let Some(piece_index) = self.download_queue.pop_front() {
            let piece = &self.pieces[piece_index as usize];

            // 如果分片已经下载或验证，跳过
            if piece.state != PieceState::Missing {
                return self.next_piece().await;
            }

            // 创建分片信息
            let piece_info = PieceInfo {
                index: piece_index,
                size: piece.size,
                hash: Some(piece.hash.clone()),
                state: piece.state,
                priority: piece.priority,
                progress: piece.progress,
            };

            // 标记为已请求
            self.requested_pieces.insert(piece_index);

            return Ok(Some(piece_info));
        }

        Ok(None)
    }

    async fn add_piece_data(&mut self, index: u32, data: Vec<u8>) -> Result<bool,BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }

        let piece = &mut self.pieces[index as usize];

        // 检查分片状态
        if piece.state != PieceState::Missing && piece.state != PieceState::Requested {
            return Ok(false);
        }

        // 检查数据大小
        if data.len() != piece.size as usize {
            return Err(BitTorrentError::from(format!("Piece data size mismatch: expected {}, got {}", piece.size, data.len())));
        }

        // 更新分片状态
        piece.state = PieceState::Downloaded;
        self.pieces_downloaded += 1;
        self.downloaded_size += data.len() as u64;

        // 添加数据到所有块
        let block_size = self.block_size as usize;
        let mut remaining_data = data.as_slice();
        let mut blocks_updated = 0;

        for block in &mut piece.blocks {
            // 计算此块应该包含的数据大小
            let data_size = std::cmp::min(block_size, remaining_data.len());
            if data_size == 0 {
                break;
            }

            // 提取此块的数据
            let block_data = remaining_data[..data_size].to_vec();
            remaining_data = &remaining_data[data_size..];

            // 更新块状态
            block.data = Some(block_data);
            block.state = BlockState::Downloaded;
            blocks_updated += 1;
        }

        // 更新分片状态
        piece.blocks_downloaded += blocks_updated;

        // 更新进度
        self.progress = self.downloaded_size as f64 / self.total_size as f64 * 100.0;

        // 验证分片
        self.verify_piece(index).await?;

        Ok(true)
    }

    async fn verify_piece(&mut self, index: u32) -> Result<bool,BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }

        let piece = &mut self.pieces[index as usize];

        // 检查分片状态
        if piece.state != PieceState::Downloaded {
            return Ok(false);
        }

        // 验证分片
        let valid = piece.verify()?;

        if valid {
            // 更新分片状态
            piece.state = PieceState::Verified;
            self.pieces_verified += 1;

            // 更新分片状态数组
            self.piece_states[index as usize] = PieceState::Verified;

            // 从已请求集合中移除
            self.requested_pieces.remove(&index);

            // 写入文件
            self.write_piece_to_files(index).await?;

            info!("Piece {} verified and saved, progress: {:.2}%", index, self.progress);
        } else {
            // 验证失败，重置分片状态
            piece.state = PieceState::Failed;
            self.piece_states[index as usize] = PieceState::Failed;

            // 重新加入下载队列
            self.download_queue.push_back(index);

            warn!("Piece {} verification failed, re-queued for download", index);
        }

        Ok(valid)
    }

    async fn piece_state(&self, index: u32) -> Result<PieceState,BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }

        Ok(self.pieces[index as usize].state)
    }

    async fn all_piece_states(&self) -> Result<Vec<PieceState>, BitTorrentError> {
        Ok(self.piece_states.clone())
    }

    async fn progress(&self) -> Result<f64,BitTorrentError> {
        Ok(self.progress)
    }

    async fn downloaded_size(&self) -> Result<u64,BitTorrentError> {
        Ok(self.downloaded_size)
    }

    async fn total_size(&self) -> Result<u64,BitTorrentError> {
        Ok(self.total_size)
    }

    async fn save_data(&self) -> Result<(),BitTorrentError> {
        // 所有分片都是在验证后立即写入文件的，所以这里不需要额外操作
        Ok(())
    }

    async fn is_complete(&self) -> Result<bool,BitTorrentError> {
        Ok(self.pieces_downloaded == self.pieces_total)
    }

    async fn get_piece_info(&self, index: u32) -> Result<Option<PieceInfo>,BitTorrentError> {
        if index as usize >= self.pieces.len() {
            return Err(BitTorrentError::from(format!("Invalid piece index: {}", index)));
        }

        let piece = &self.pieces[index as usize];

        let piece_info = PieceInfo {
            index,
            size: piece.size,
            hash: Some(piece.hash.clone()),
            state: piece.state,
            priority: piece.priority,
            progress: piece.progress,
        };

        Ok(Some(piece_info))
    }

    async fn get_piece_rarities(&self) -> Result<Vec<usize>,BitTorrentError> {
        let rarities = self.pieces.iter().map(|p| p.rarity).collect();
        Ok(rarities)
    }

    async fn get_requested_pieces(&self) -> Result<HashSet<u32>,BitTorrentError> {
        Ok(self.requested_pieces.clone())
    }

    async fn mark_piece_requested(&mut self, index: u32) -> Result<(), Error> {
        if index as usize >= self.pieces.len() {
            return Err(Error::msg(format!("Invalid piece index: {}", index)));
        }
        self.requested_pieces.insert(index);
        Ok(())
    }

    async fn read_block(&self, index: u32, begin: u32, length: u32) -> Result<Vec<u8>, anyhow::Error> {
        if index as usize >= self.pieces.len() {
            return Err(anyhow::Error::msg(format!("Invalid piece index: {}", index)));
        }
        let piece = &self.pieces[index as usize];
        if piece.state != PieceState::Verified {
            return Err(anyhow::Error::msg("Cannot read from unverified piece"));
        }
        if begin as u64 + length as u64 > piece.size {
            return Err(anyhow::Error::msg(format!("Invalid block range: begin={}, length={}, piece_size={}", begin, length, piece.size)));
        }
        let data = self.file_manager.read_piece_data(index, begin, length).await.map_err(|e| anyhow::Error::from(e))?;
        if data.len() != length as usize {
            return Err(anyhow::Error::msg(format!("Read data size mismatch: expected {}, got {}", length, data.len())));
        }
        Ok(data)
    }
    
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

#[async_trait]
impl PieceManagerTrait for BitTorrentPieceManager {
    /// 检查分片是否全部完成
    async fn is_complete(&self) -> Result<bool, BitTorrentError> {
        Ok(self.pieces_downloaded == self.pieces_total)
    }
    /// 获取下载进度百分比
    async fn progress(&self) -> Result<f64, BitTorrentError> {
        Ok(self.progress)
    }
    /// 获取已下载数据大小
    async fn downloaded_size(&self) -> Result<u64, BitTorrentError> {
        Ok(self.downloaded_size)
    }
}

#[async_trait]
impl PieceCompleteCallback for BitTorrentPieceManager {
    async fn on_piece_complete(&mut self, piece_index: u32, piece_data: Vec<u8>) -> Result<bool, anyhow::Error> {
        let result = self.add_piece_data(piece_index, piece_data).await;
        if let Ok(true) = result {
            self.notify_piece_completed(piece_index).await;
        }
        result.map_err(|e| anyhow::Error::from(e))
    }
}

#[async_trait]
impl BlockAddedCallback for BitTorrentPieceManager {
    async fn on_block_added(&self, piece_index: u32, progress: f64) -> Result<(), anyhow::Error> {
        debug!("Block added for piece {}, overall progress: {:.2}%", piece_index, progress * 100.0);
        let progress_percent = (progress * 100.0) as u32;
        if progress_percent > 0 && progress_percent % 5 == 0 && progress_percent <= 100 {
            let last_progress_key = format!("last_logged_progress_{}", piece_index);
            let mut last_logged = 0;
            if PIECE_CACHE.get_metadata(&last_progress_key).is_some() {
                if let Some(last_progress) = PIECE_CACHE.get_metadata(&last_progress_key) {
                    if let Ok(last) = last_progress.parse::<u32>() {
                        last_logged = last;
                    }
                }
            }
            if progress_percent > last_logged {
                info!("Download progress: {:.2}%, piece {} updated", progress * 100.0, piece_index);
                PIECE_CACHE.set_metadata(&last_progress_key, &progress_percent.to_string());
            }
        }
        Ok(())
    }
}

// 实现标记性 trait，用于兼容现有代码
impl BlockManagerCallback for BitTorrentPieceManager {}
