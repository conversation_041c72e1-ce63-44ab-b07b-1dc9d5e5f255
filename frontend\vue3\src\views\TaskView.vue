<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import TaskList from '../components/Task/TaskList.vue'

const route = useRoute()
const router = useRouter()

// 当前任务状态
const status = computed(() => route.params.status || 'active')

// 切换任务状态
const handleTabChange = (tab: string) => {
  router.push(`/task/${tab}`)
}
</script>

<template>
  <el-container class="task-container">
    <el-aside width="200px" class="subnav">
      <div class="subnav-inner">
        <h3 class="subnav-title">任务</h3>
        <el-menu
          :default-active="status"
          class="task-menu"
          @select="handleTabChange"
        >
          <el-menu-item index="active">
            <el-icon><Loading /></el-icon>
            <span>下载中</span>
          </el-menu-item>
          <el-menu-item index="waiting">
            <el-icon><Clock /></el-icon>
            <span>等待中</span>
          </el-menu-item>
          <el-menu-item index="stopped">
            <el-icon><Select /></el-icon>
            <span>已完成</span>
          </el-menu-item>
        </el-menu>
      </div>
    </el-aside>
    
    <el-main class="main">
      <TaskList />
    </el-main>
  </el-container>
</template>

<style scoped>
.task-container {
  height: 100%;
}

.subnav-inner {
  padding: 20px 0;
}

.subnav-title {
  padding: 0 20px;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
}

.task-menu {
  border-right: none;
}

.el-menu-item {
  display: flex;
  align-items: center;
}

.el-menu-item .el-icon {
  margin-right: 8px;
}
</style>
