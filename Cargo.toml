[package]
name = "tonitru_downloader"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
axum = { version = "0.8.2", features = ["ws", "multipart", "tokio", "http1"] }
axum-extra = { version = "0.9.2", features = ["typed-header"] }
tokio = { version = "1.32.0", features = ["full"] }
tokio-util = "0.7.8"
tokio-stream = "0.1.14"
tower = "0.4.13"
tower-http = { version = "0.6.6", features = ["cors", "trace"] }
tracing = "0.1.37"
tracing-subscriber = { version = "0.3.17", features = ["env-filter"] }
tracing-appender = "0.2.3"
serde = { version = "1.0.188", features = ["derive"] }
serde_json = "1.0.107"
serde_yaml = "0.9.25"
config = "0.13.3"
thiserror = "1.0.49"
anyhow = "1.0.75"
once_cell = "1.18.0"
futures = "0.3.28"
futures-util = "0.3.28"
async-trait = "0.1.73"
async-stream = "0.3.5"
url = "2.4.1"
reqwest = { version = "0.11.20", features = ["json", "stream", "multipart"] }
http = "0.2.9"
http-body = "0.4.5"
http-body-util = "0.1.0-rc.3"
pin-project = "1.1.3"
pin-project-lite = "0.2.13"
bytesize = "1.3.0"
bytes = "1.5.0"
base64 = "0.21.4"
sha1 = "0.10.5"
sha2 = "0.10.7"
md-5 = "0.10.5"
hmac = "0.12.1"
rsa = "0.9.2"
rand = "0.8.5"
uuid = { version = "1.4.1", features = ["v4", "serde"] }
chrono = { version = "0.4.31", features = ["serde"] }
regex = "1.9.5"
lazy_static = "1.4.0"
strum = { version = "0.25.0", features = ["derive"] }
strum_macros = "0.25.2"
sysinfo = "0.29.10"
sqlx = { version = "0.7.2", features = ["runtime-tokio", "tls-rustls", "sqlite", "macros", "uuid", "chrono", "json"] }
rust-embed = "8.0.0"
rust-embed-utils = "8.0.0"
walkdir = "2.4.0"
zip = "0.6.6"
flate2 = "1.0.27"
tar = "0.4.40"
toml = "0.8.0"
log = "0.4.20"
env_logger = "0.10.0"
fnv = "1.0.7"
indexmap = "2.0.2"
bitflags = "2.4.0"
num_cpus = "1.16.0"
clap = { version = "4.4.6", features = ["derive"] }
tabled = "0.14.0"
prettytable-rs = "0.10.0"
serde_bencode = "0.2.4"
serde_bytes = "0.11.17"
base32 = "0.5.1"
urlencoding = "2.1.3"
ed25519-dalek = "2.0.0"
hex = "0.4.3"
libloading = "0.8.0"
winreg = "0.51.0"
colored = "3.0.0"

[dependencies.windows]
version = "0.51.1"
features = [
    "Win32_Foundation",
    "Win32_Security",
    "Win32_System_Threading",
    "Win32_UI_WindowsAndMessaging",
    "Win32_System_Registry",
]

[target.'cfg(target_os = "linux")'.dependencies]
libc = "0.2.149"

[target.'cfg(target_os = "macos")'.dependencies]
libc = "0.2.149"

[[bin]]
name = "tonitru_cli"
path = "src/bin/tonitru_cli.rs"

[lib]
name = "tonitru_downloader"
path = "src/lib.rs"

[features]
default = ["integration_tests"]
integration_tests = []

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[dev-dependencies]
httpmock = "0.7.0"
