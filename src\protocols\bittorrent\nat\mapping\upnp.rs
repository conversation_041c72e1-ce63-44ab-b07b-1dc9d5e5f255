use std::net::{IpAddr, Ipv4Addr, SocketAddr};
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use tokio::sync::Mutex;
use tracing::{debug, info, warn};

use super::{Protocol, MappingInfo, PortMapper};

/// UPnP设备信息
#[derive(Debug, Clone)]
struct UPnPDevice {
    /// 设备URL
    control_url: String,
    /// 服务类型
    service_type: String,
    /// 外部IP地址
    external_ip: Option<IpAddr>,
    /// 上次发现时间
    last_discovery: Instant,
}

/// UPnP映射器
pub struct UPnPMapper {
    /// 设备列表
    devices: Mutex<Vec<UPnPDevice>>,
    /// 本地IP地址
    local_ip: Mutex<Option<IpAddr>>,
    /// 上次发现时间
    last_discovery: Mutex<Option<Instant>>,
    /// 发现超时（秒）
    discovery_timeout: u64,
}

impl UPnPMapper {
    /// 创建新的UPnP映射器
    pub async fn new() -> Result<Self> {
        let mapper = Self {
            devices: Mutex::new(Vec::new()),
            local_ip: Mutex::new(None),
            last_discovery: Mutex::new(None),
            discovery_timeout: 600, // 10分钟
        };
        
        // 初始化发现设备
        mapper.discover_devices().await?;
        
        Ok(mapper)
    }
    
    /// 发现UPnP设备
    async fn discover_devices(&self) -> Result<()> {
        // 检查是否需要重新发现
        {
            let last_discovery = self.last_discovery.lock().await;
            if let Some(time) = *last_discovery {
                if time.elapsed().as_secs() < self.discovery_timeout {
                    let devices = self.devices.lock().await;
                    if !devices.is_empty() {
                        return Ok(());
                    }
                }
            }
        }
        
        // 使用SSDP发现UPnP设备
        debug!("Discovering UPnP devices...");
        
        // 创建SSDP搜索请求
        let search_request = 
            "M-SEARCH * HTTP/1.1\r\n\
             HOST: ***************:1900\r\n\
             MAN: \"ssdp:discover\"\r\n\
             MX: 3\r\n\
             ST: urn:schemas-upnp-org:service:WANIPConnection:1\r\n\
             \r\n";
        
        // 创建UDP套接字
        let socket = tokio::net::UdpSocket::bind("0.0.0.0:0").await?;
        socket.set_broadcast(true)?;
        
        // 发送搜索请求
        let ssdp_addr = "***************:1900".parse::<SocketAddr>()?;
        socket.send_to(search_request.as_bytes(), ssdp_addr).await?;
        
        // 接收响应
        let mut buf = vec![0u8; 8192];
        let mut devices = Vec::new();
        
        // 设置超时
        let timeout = Duration::from_secs(3);
        let start_time = Instant::now();
        
        while start_time.elapsed() < timeout {
            // 设置接收超时
            match tokio::time::timeout(
                Duration::from_millis(500),
                socket.recv_from(&mut buf)
            ).await {
                Ok(Ok((len, _))) => {
                    // 解析响应
                    if let Ok(response) = String::from_utf8(buf[..len].to_vec()) {
                        // 提取设备信息
                        if let Some(device) = self.parse_ssdp_response(&response) {
                            devices.push(device);
                        }
                    }
                },
                Ok(Err(_)) => break,
                Err(_) => break,
            }
        }
        
        // 更新设备列表
        {
            let mut devices_guard = self.devices.lock().await;
            *devices_guard = devices;
        }
        
        // 更新发现时间
        {
            let mut last_discovery = self.last_discovery.lock().await;
            *last_discovery = Some(Instant::now());
        }
        
        // 获取本地IP地址
        self.detect_local_ip().await?;
        
        // 获取外部IP地址
        self.update_external_ip().await?;
        
        let devices_count = self.devices.lock().await.len();
        info!("Discovered {} UPnP devices", devices_count);
        
        Ok(())
    }
    
    /// 解析SSDP响应
    fn parse_ssdp_response(&self, response: &str) -> Option<UPnPDevice> {
        // 检查是否是WANIPConnection服务
        if !response.contains("urn:schemas-upnp-org:service:WANIPConnection:1") {
            return None;
        }
        
        // 提取位置URL
        let location = response.lines()
            .find(|line| line.to_lowercase().starts_with("location:"))
            .and_then(|line| line.split_once(':').map(|(_, v)| v.trim()))
            .or_else(|| {
                response.lines()
                    .find(|line| line.to_lowercase().contains("location:"))
                    .and_then(|line| line.split_once("location:").map(|(_, v)| v.trim()))
            });
        
        if let Some(location) = location {
            // 创建设备信息
            Some(UPnPDevice {
                control_url: location.to_string(),
                service_type: "urn:schemas-upnp-org:service:WANIPConnection:1".to_string(),
                external_ip: None,
                last_discovery: Instant::now(),
            })
        } else {
            None
        }
    }
    
    /// 检测本地IP地址
    async fn detect_local_ip(&self) -> Result<IpAddr> {
        // 检查缓存
        {
            let local_ip = self.local_ip.lock().await;
            if let Some(ip) = *local_ip {
                return Ok(ip);
            }
        }
        
        // 创建UDP套接字连接到公共DNS服务器
        let socket = tokio::net::UdpSocket::bind("0.0.0.0:0").await?;
        socket.connect("*******:53").await?;
        
        // 获取本地地址
        let local_addr = socket.local_addr()?;
        let local_ip = local_addr.ip();
        
        // 更新缓存
        {
            let mut local_ip_guard = self.local_ip.lock().await;
            *local_ip_guard = Some(local_ip);
        }
        
        Ok(local_ip)
    }
    
    /// 更新外部IP地址
    async fn update_external_ip(&self) -> Result<()> {
        let mut devices = self.devices.lock().await;
        
        for device in devices.iter_mut() {
            // 构建SOAP请求
            let soap_request = format!(
                "<?xml version=\"1.0\"?>\r\n\
                 <s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\" s:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\r\n\
                 <s:Body>\r\n\
                 <u:GetExternalIPAddress xmlns:u=\"{}\">\r\n\
                 </u:GetExternalIPAddress>\r\n\
                 </s:Body>\r\n\
                 </s:Envelope>\r\n",
                device.service_type
            );
            
            // 发送SOAP请求
            let client = reqwest::Client::new();
            let response = client.post(&device.control_url)
                .header("Content-Type", "text/xml; charset=\"utf-8\"")
                .header("SOAPAction", format!("\"{}#GetExternalIPAddress\"", device.service_type))
                .body(soap_request)
                .timeout(Duration::from_secs(5))
                .send()
                .await?;
            
            // 解析响应
            let response_text = response.text().await?;
            
            // 提取外部IP地址
            if let Some(ip_str) = self.extract_external_ip(&response_text) {
                if let Ok(ip) = ip_str.parse() {
                    device.external_ip = Some(ip);
                    break;
                }
            }
        }
        
        Ok(())
    }
    
    /// 提取外部IP地址
    fn extract_external_ip(&self, response: &str) -> Option<String> {
        // 简单解析XML
        let start_tag = "<NewExternalIPAddress>";
        let end_tag = "</NewExternalIPAddress>";
        
        if let Some(start) = response.find(start_tag) {
            if let Some(end) = response.find(end_tag) {
                let start_pos = start + start_tag.len();
                if start_pos < end {
                    return Some(response[start_pos..end].to_string());
                }
            }
        }
        
        None
    }
}

#[async_trait]
impl PortMapper for UPnPMapper {
    async fn create_mapping(&self, protocol: Protocol, internal_port: u16, external_port: u16, lifetime: Duration) -> Result<MappingInfo> {
        // 确保设备已发现
        self.discover_devices().await?;
        
        let devices = self.devices.lock().await;
        if devices.is_empty() {
            return Err(anyhow!("No UPnP devices found"));
        }
        
        // 获取本地IP地址
        let local_ip = self.detect_local_ip().await?;
        
        // 使用第一个可用设备
        let device = &devices[0];
        
        // 构建SOAP请求
        let protocol_str = match protocol {
            Protocol::TCP => "TCP",
            Protocol::UDP => "UDP",
        };
        
        let soap_request = format!(
            "<?xml version=\"1.0\"?>\r\n\
             <s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\" s:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\r\n\
             <s:Body>\r\n\
             <u:AddPortMapping xmlns:u=\"{}\">\r\n\
             <NewRemoteHost></NewRemoteHost>\r\n\
             <NewExternalPort>{}</NewExternalPort>\r\n\
             <NewProtocol>{}</NewProtocol>\r\n\
             <NewInternalPort>{}</NewInternalPort>\r\n\
             <NewInternalClient>{}</NewInternalClient>\r\n\
             <NewEnabled>1</NewEnabled>\r\n\
             <NewPortMappingDescription>Tonitru BitTorrent</NewPortMappingDescription>\r\n\
             <NewLeaseDuration>{}</NewLeaseDuration>\r\n\
             </u:AddPortMapping>\r\n\
             </s:Body>\r\n\
             </s:Envelope>\r\n",
            device.service_type,
            external_port,
            protocol_str,
            internal_port,
            local_ip,
            lifetime.as_secs()
        );
        
        // 发送SOAP请求
        let client = reqwest::Client::new();
        let response = client.post(&device.control_url)
            .header("Content-Type", "text/xml; charset=\"utf-8\"")
            .header("SOAPAction", format!("\"{}#AddPortMapping\"", device.service_type))
            .body(soap_request)
            .timeout(Duration::from_secs(5))
            .send()
            .await?;
        
        // 检查响应状态
        if !response.status().is_success() {
            return Err(anyhow!("Failed to create UPnP mapping: {}", response.status()));
        }
        
        // 获取外部IP地址
        let external_ip = if let Some(ip) = device.external_ip {
            ip
        } else {
            self.get_external_address().await?
        };
        
        // 创建映射信息
        let mapping = MappingInfo {
            internal_addr: SocketAddr::new(local_ip, internal_port),
            external_addr: SocketAddr::new(external_ip, external_port),
            protocol,
            lifetime: lifetime.as_secs(),
            created_at: Instant::now(),
        };
        
        info!("Created UPnP mapping: {}:{} -> {}:{} ({})",
            local_ip, internal_port, external_ip, external_port, protocol);
        
        Ok(mapping)
    }
    
    async fn delete_mapping(&self, mapping: &MappingInfo) -> Result<()> {
        // 确保设备已发现
        self.discover_devices().await?;
        
        let devices = self.devices.lock().await;
        if devices.is_empty() {
            return Err(anyhow!("No UPnP devices found"));
        }
        
        // 使用第一个可用设备
        let device = &devices[0];
        
        // 构建SOAP请求
        let protocol_str = match mapping.protocol {
            Protocol::TCP => "TCP",
            Protocol::UDP => "UDP",
        };
        
        let soap_request = format!(
            "<?xml version=\"1.0\"?>\r\n\
             <s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\" s:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\r\n\
             <s:Body>\r\n\
             <u:DeletePortMapping xmlns:u=\"{}\">\r\n\
             <NewRemoteHost></NewRemoteHost>\r\n\
             <NewExternalPort>{}</NewExternalPort>\r\n\
             <NewProtocol>{}</NewProtocol>\r\n\
             </u:DeletePortMapping>\r\n\
             </s:Body>\r\n\
             </s:Envelope>\r\n",
            device.service_type,
            mapping.external_addr.port(),
            protocol_str
        );
        
        // 发送SOAP请求
        let client = reqwest::Client::new();
        let response = client.post(&device.control_url)
            .header("Content-Type", "text/xml; charset=\"utf-8\"")
            .header("SOAPAction", format!("\"{}#DeletePortMapping\"", device.service_type))
            .body(soap_request)
            .timeout(Duration::from_secs(5))
            .send()
            .await?;
        
        // 检查响应状态
        if !response.status().is_success() {
            return Err(anyhow!("Failed to delete UPnP mapping: {}", response.status()));
        }
        
        info!("Deleted UPnP mapping: {}:{} ({})",
            mapping.external_addr.ip(), mapping.external_addr.port(), mapping.protocol);
        
        Ok(())
    }
    
    async fn renew_mapping(&self, mapping: &mut MappingInfo, lifetime: Duration) -> Result<()> {
        // 创建新的映射（UPnP没有专门的更新方法）
        let new_mapping = self.create_mapping(
            mapping.protocol,
            mapping.internal_addr.port(),
            mapping.external_addr.port(),
            lifetime
        ).await?;
        
        // 更新映射信息
        mapping.lifetime = new_mapping.lifetime;
        mapping.created_at = new_mapping.created_at;
        
        Ok(())
    }
    
    async fn get_external_address(&self) -> Result<IpAddr> {
        // 确保设备已发现
        self.discover_devices().await?;
        
        let devices = self.devices.lock().await;
        if devices.is_empty() {
            return Err(anyhow!("No UPnP devices found"));
        }
        
        // 查找具有外部IP的设备
        for device in devices.iter() {
            if let Some(ip) = device.external_ip {
                return Ok(ip);
            }
        }
        
        // 如果没有找到，尝试更新外部IP
        drop(devices);
        self.update_external_ip().await?;
        
        let devices = self.devices.lock().await;
        for device in devices.iter() {
            if let Some(ip) = device.external_ip {
                return Ok(ip);
            }
        }
        
        Err(anyhow!("Failed to get external IP address"))
    }
}
