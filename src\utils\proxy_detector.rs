//! 代理检测器模块
//!
//! 该模块提供了自动检测系统代理设置的功能，是旧版API的兼容层。
//! 实际实现已移至 `proxy` 子模块。

use tracing::debug;
use crate::config::settings::ProxyConfig;
use crate::utils::proxy::ProxyDetectorFactory;

/// 代理检测器，用于自动检测系统代理设置
/// 
/// 注意：此结构体保留用于向后兼容，新代码应使用 `proxy` 子模块中的 `ProxyDetectorFactory`
pub struct ProxyDetector;

impl ProxyDetector {
    /// 检测系统代理设置
    /// 
    /// 首先检查环境变量，然后检查操作系统特定的设置
    /// 
    /// # 返回值
    /// 
    /// 返回 `Option<ProxyConfig>`，表示检测到的代理配置（如果有）
    pub fn detect_system_proxy() -> Option<ProxyConfig> {
        debug!("检测系统代理设置（兼容API）...");
        ProxyDetectorFactory::detect_system_proxy()
    }

    /// 从环境变量中检测代理设置
    /// 
    /// # 返回值
    /// 
    /// 返回 `Option<ProxyConfig>`，表示检测到的代理配置（如果有）
    #[deprecated(since = "0.2.0", note = "请使用 proxy::ProxyDetectorFactory::detect_from_env")]
    pub fn detect_from_env() -> Option<ProxyConfig> {
        debug!("从环境变量检测代理（兼容API）...");
        ProxyDetectorFactory::detect_from_env().ok().flatten()
    }

    /// 从操作系统设置中检测代理
    /// 
    /// # 返回值
    /// 
    /// 返回 `Option<ProxyConfig>`，表示检测到的代理配置（如果有）
    #[deprecated(since = "0.2.0", note = "请使用 proxy::ProxyDetectorFactory::detect_from_os")]
    pub fn detect_from_os() -> Option<ProxyConfig> {
        debug!("从操作系统设置检测代理（兼容API）...");
        ProxyDetectorFactory::detect_from_os().ok().flatten()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;

    #[test]
    fn test_detect_from_env_http_proxy() {
        env::set_var("http_proxy", "http://test.com:8080");
        let config = ProxyDetector::detect_from_env().unwrap();
        assert_eq!(config.url, "http://test.com:8080/");
        assert_eq!(config.proxy_type, "http");
        env::remove_var("http_proxy");
    }

    #[test]
    fn test_detect_from_env_https_proxy() {
        env::set_var("https_proxy", "https://test.com:8443");
        let config = ProxyDetector::detect_from_env().unwrap();
        assert_eq!(config.url, "https://test.com:8443/");
        assert_eq!(config.proxy_type, "https");
        env::remove_var("https_proxy");
    }

    #[test]
    fn test_detect_from_env_all_proxy_socks() {
        env::set_var("all_proxy", "socks5://test.com:1080");
        let config = ProxyDetector::detect_from_env().unwrap();
        assert_eq!(config.url, "socks5://test.com:1080/");
        assert_eq!(config.proxy_type, "socks5");
        env::remove_var("all_proxy");
    }

    #[test]
    fn test_detect_from_env_no_proxy() {
        env::set_var("http_proxy", "http://test.com:8080");
        env::set_var("no_proxy", "localhost,127.0.0.1");
        let config = ProxyDetector::detect_from_env().unwrap();
        assert_eq!(config.no_proxy, vec!["localhost".to_string(), "127.0.0.1".to_string()]);
        env::remove_var("http_proxy");
        env::remove_var("no_proxy");
    }
}
