use std::sync::Arc;
use tokio::test;
use uuid::Uuid;
use mockall::predicate::*;
use mockall::mock;

use crate::analyzer::LinkAnalyzer;
use crate::config::ConfigManager;
use crate::core::interfaces::{Downloader, DownloaderFactory, ProtocolType};
use crate::download::manager::unified_downloader_factory::UnifiedDownloaderFactory;
use crate::download::resume::ResumeManager;

// 创建 LinkAnalyzer 的 Mock
mock! {
    pub LinkAnalyzerMock {}
    #[async_trait::async_trait]
    impl LinkAnalyzer for LinkAnalyzerMock {
        async fn analyze(&self, url: &str) -> anyhow::Result<String>;
        async fn follow_redirects(&self, url: &str) -> anyhow::Result<crate::analyzer::RedirectResult>;
    }
}

// 创建 ResumeManager 的 Mock
mock! {
    pub ResumeManagerMock {}
    #[async_trait::async_trait]
    impl ResumeManager for ResumeManagerMock {
        async fn create_resume_point(&self, task_id: &Uuid, url: &str, output_path: &str, downloaded_size: u64) -> anyhow::Result<()>;
        async fn get_resume_point(&self, task_id: &Uuid) -> anyhow::Result<Option<crate::download::resume::ResumePoint>>;
        async fn update_resume_point(&self, task_id: &Uuid, downloaded_size: u64) -> anyhow::Result<()>;
        async fn delete_resume_point(&self, task_id: Uuid) -> anyhow::Result<()>;
        async fn pause_task(&self, task_id: &Uuid) -> anyhow::Result<()>;
        async fn resume_task(&self, task_id: &Uuid) -> anyhow::Result<()>;
    }
}

#[test]
async fn test_unified_downloader_factory_create_http_downloader() {
    // 创建 ConfigManager
    let config_manager = Arc::new(ConfigManager::new_for_test());
    
    // 创建 LinkAnalyzer 的 Mock
    let mut link_analyzer = LinkAnalyzerMock::new();
    link_analyzer.expect_analyze()
        .with(eq("https://example.com/file.zip"))
        .returning(|_| Ok("https".to_string()));
    
    link_analyzer.expect_follow_redirects()
        .with(eq("https://example.com/file.zip"))
        .returning(|_| Ok(crate::analyzer::RedirectResult {
            final_url: "https://example.com/file.zip".to_string(),
            file_name: Some("file.zip".to_string()),
            is_redirect_loop: false,
        }));
    
    // 创建 ResumeManager 的 Mock
    let resume_manager = Arc::new(ResumeManagerMock::new());
    
    // 创建统一下载器工厂
    let factory = UnifiedDownloaderFactory::new(
        config_manager,
        Arc::new(link_analyzer),
        resume_manager,
    );
    
    // 创建下载器
    let task_id = Uuid::new_v4();
    let downloader = factory.create_downloader(
        "https://example.com/file.zip",
        "d:/downloads/file.zip",
        task_id,
    ).await.expect("Failed to create downloader");
    
    // 验证下载器类型
    assert_eq!(downloader.protocol_type(), ProtocolType::Http);
    assert_eq!(downloader.url(), "https://example.com/file.zip");
    assert_eq!(downloader.output_path(), "d:/downloads/file.zip");
    assert_eq!(downloader.id(), task_id);
}

#[test]
async fn test_unified_downloader_factory_supported_protocols() {
    // 创建 ConfigManager
    let config_manager = Arc::new(ConfigManager::new_for_test());
    
    // 创建 LinkAnalyzer 的 Mock
    let link_analyzer = Arc::new(LinkAnalyzerMock::new());
    
    // 创建 ResumeManager 的 Mock
    let resume_manager = Arc::new(ResumeManagerMock::new());
    
    // 创建统一下载器工厂
    let factory = UnifiedDownloaderFactory::new(
        config_manager,
        link_analyzer,
        resume_manager,
    );
    
    // 获取支持的协议
    let protocols = factory.supported_protocols();
    
    // 验证支持的协议类型
    assert!(protocols.contains(&ProtocolType::Http));
    assert!(protocols.contains(&ProtocolType::Https));
    assert!(protocols.contains(&ProtocolType::BitTorrent));
    assert!(protocols.contains(&ProtocolType::Magnet));
    assert!(protocols.contains(&ProtocolType::P2P));
    assert!(protocols.contains(&ProtocolType::R2));
    assert_eq!(protocols.len(), 6);
}