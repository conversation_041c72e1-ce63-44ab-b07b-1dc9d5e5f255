use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, SocketAddr};
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow, Context};
use tokio::net::UdpSocket;
use tokio::sync::{Mutex, RwLock};
use tokio::time::timeout;
use tracing::{debug, info, warn, error};
use rand::Rng;

use super::{Protocol, MappingInfo, PortMapper};

/// NAT-PMP常量
const NATPMP_PORT: u16 = 5351;
const NATPMP_VERSION: u8 = 0;
const NATPMP_OP_EXTERNAL_ADDRESS: u8 = 0;
const NATPMP_OP_MAP_UDP: u8 = 1;
const NATPMP_OP_MAP_TCP: u8 = 2;
const NATPMP_RESULT_SUCCESS: u16 = 0;
const NATPMP_INITIAL_TIMEOUT: u64 = 250; // 毫秒
const NATPMP_MAX_RETRIES: u32 = 9;       // 增加重试次数以提高成功率
const NATPMP_MAX_LIFETIME: u32 = 7200;   // 最大映射生存时间（秒）
const NATPMP_RECOMMENDED_LIFETIME: u32 = 3600; // 推荐映射生存时间（秒）
const NATPMP_MIN_LIFETIME: u32 = 600;    // 最小映射生存时间（秒）

/// NAT-PMP错误码
const NATPMP_ERR_UNSUPPORTED_VERSION: u16 = 1;
const NATPMP_ERR_NOT_AUTHORIZED: u16 = 2;
const NATPMP_ERR_NETWORK_FAILURE: u16 = 3;
const NATPMP_ERR_OUT_OF_RESOURCES: u16 = 4;
const NATPMP_ERR_UNSUPPORTED_OPCODE: u16 = 5;

/// 将NAT-PMP错误码转换为可读的错误消息
fn natpmp_error_message(code: u16) -> &'static str {
    match code {
        NATPMP_RESULT_SUCCESS => "Success",
        NATPMP_ERR_UNSUPPORTED_VERSION => "Unsupported protocol version",
        NATPMP_ERR_NOT_AUTHORIZED => "Not authorized to create mapping",
        NATPMP_ERR_NETWORK_FAILURE => "Network failure",
        NATPMP_ERR_OUT_OF_RESOURCES => "Out of resources, cannot create more mappings",
        NATPMP_ERR_UNSUPPORTED_OPCODE => "Unsupported operation code",
        _ => "Unknown error code",
    }
}

/// NAT-PMP映射器
pub struct NATPMPMapper {
    /// 网关地址
    gateway: Mutex<Option<IpAddr>>,
    /// 外部IP地址
    external_ip: Mutex<Option<IpAddr>>,
    /// 上次发现时间
    last_discovery: Mutex<Option<Instant>>,
    /// 发现超时（秒）
    discovery_timeout: u64,
    /// 活跃映射
    active_mappings: RwLock<HashMap<String, MappingInfo>>,
    /// 上次更新外部IP时间
    last_external_ip_update: Mutex<Option<Instant>>,
    /// 外部IP更新间隔（秒）
    external_ip_update_interval: u64,
    /// 映射更新间隔（秒）
    mapping_refresh_interval: u64,
    /// 是否启用自动刷新
    auto_refresh_enabled: bool,
}

impl NATPMPMapper {
    /// 创建新的NAT-PMP映射器
    pub async fn new() -> Result<Self> {
        let mapper = Self {
            gateway: Mutex::new(None),
            external_ip: Mutex::new(None),
            last_discovery: Mutex::new(None),
            discovery_timeout: 600, // 10分钟
            active_mappings: RwLock::new(HashMap::new()),
            last_external_ip_update: Mutex::new(None),
            external_ip_update_interval: 3600, // 1小时
            mapping_refresh_interval: 1800, // 30分钟
            auto_refresh_enabled: true,
        };

        // 初始化发现网关
        mapper.discover_gateway().await?;

        // 启动自动刷新任务
        mapper.start_auto_refresh().await?;

        Ok(mapper)
    }

    /// 创建新的NAT-PMP映射器，带配置
    pub async fn new_with_config(
        discovery_timeout: u64,
        external_ip_update_interval: u64,
        mapping_refresh_interval: u64,
        auto_refresh_enabled: bool
    ) -> Result<Self> {
        let mapper = Self {
            gateway: Mutex::new(None),
            external_ip: Mutex::new(None),
            last_discovery: Mutex::new(None),
            discovery_timeout,
            active_mappings: RwLock::new(HashMap::new()),
            last_external_ip_update: Mutex::new(None),
            external_ip_update_interval,
            mapping_refresh_interval,
            auto_refresh_enabled,
        };

        // 初始化发现网关
        mapper.discover_gateway().await?;

        // 如果启用了自动刷新，启动自动刷新任务
        if auto_refresh_enabled {
            mapper.start_auto_refresh().await?;
        }

        Ok(mapper)
    }

    /// 发现网关
    async fn discover_gateway(&self) -> Result<IpAddr> {
        // 检查缓存
        {
            let gateway = self.gateway.lock().await;
            if let Some(addr) = *gateway {
                return Ok(addr);
            }
        }

        // 检查是否需要重新发现
        {
            let last_discovery = self.last_discovery.lock().await;
            if let Some(time) = *last_discovery {
                if time.elapsed().as_secs() < self.discovery_timeout {
                    let gateway = self.gateway.lock().await;
                    if let Some(addr) = *gateway {
                        return Ok(addr);
                    }
                }
            }
        }

        // 获取默认网关
        debug!("Discovering NAT-PMP gateway...");

        // 尝试多种方法查找网关
        let gateway = self.find_gateway().await
            .context("Failed to find NAT-PMP gateway")?;

        // 验证网关是否支持NAT-PMP
        self.verify_gateway_support(&gateway).await
            .context("Gateway does not support NAT-PMP")?;

        // 更新缓存
        {
            let mut gateway_guard = self.gateway.lock().await;
            *gateway_guard = Some(gateway);
        }

        // 更新发现时间
        {
            let mut last_discovery = self.last_discovery.lock().await;
            *last_discovery = Some(Instant::now());
        }

        // 获取外部IP地址
        if let Err(e) = self.update_external_ip().await {
            warn!("Failed to get external IP address: {}", e);
            // 继续执行，因为这不是致命错误
        }

        info!("Discovered NAT-PMP gateway: {}", gateway);

        Ok(gateway)
    }

    /// 查找网关
    async fn find_gateway(&self) -> Result<IpAddr> {
        // 方法1: 使用本地IP地址推断网关
        if let Ok(local_ip) = self.detect_local_ip().await {
            if let IpAddr::V4(ipv4) = local_ip {
                // 假设网关是本地网络的第一个地址
                let octets = ipv4.octets();
                let gateway = IpAddr::V4(Ipv4Addr::new(octets[0], octets[1], octets[2], 1));

                // 验证网关是否可达
                if self.ping_gateway(&gateway).await {
                    debug!("Found gateway at {} based on local IP {}", gateway, local_ip);
                    return Ok(gateway);
                }
            }
        }

        // 方法2: 尝试常见的网关地址
        let common_gateways = [
            Ipv4Addr::new(192, 168, 0, 1),
            Ipv4Addr::new(192, 168, 1, 1),
            Ipv4Addr::new(10, 0, 0, 1),
            Ipv4Addr::new(10, 0, 1, 1),
            Ipv4Addr::new(10, 1, 1, 1),
            Ipv4Addr::new(172, 16, 0, 1),
        ];

        for &ip in &common_gateways {
            let gateway = IpAddr::V4(ip);
            if self.ping_gateway(&gateway).await {
                debug!("Found responsive gateway at {}", gateway);
                return Ok(gateway);
            }
        }

        // 如果所有方法都失败，返回错误
        Err(anyhow!("Could not find NAT-PMP gateway"))
    }

    /// 检查网关是否可达
    async fn ping_gateway(&self, gateway: &IpAddr) -> bool {
        // 尝试连接网关的NAT-PMP端口
        if let Ok(socket) = UdpSocket::bind("0.0.0.0:0").await {
            let gateway_addr = SocketAddr::new(*gateway, NATPMP_PORT);
            if socket.connect(gateway_addr).await.is_ok() {
                // 发送一个外部地址请求作为ping
                let request = [NATPMP_VERSION, NATPMP_OP_EXTERNAL_ADDRESS];
                if let Ok(_) = socket.send(&request).await {
                    // 尝试接收响应，但不关心内容
                    let mut buf = [0u8; 12];
                    if let Ok(Ok(_)) = timeout(Duration::from_millis(500), socket.recv(&mut buf)).await {
                        return true;
                    }
                }
            }
        }
        false
    }

    /// 验证网关是否支持NAT-PMP
    async fn verify_gateway_support(&self, gateway: &IpAddr) -> Result<()> {
        // 尝试获取外部IP地址，如果成功则表示网关支持NAT-PMP
        let socket = UdpSocket::bind("0.0.0.0:0").await
            .context("Failed to bind UDP socket")?;

        let gateway_addr = SocketAddr::new(*gateway, NATPMP_PORT);
        socket.connect(gateway_addr).await
            .context("Failed to connect to gateway")?;

        // 构造外部地址请求
        let request = [NATPMP_VERSION, NATPMP_OP_EXTERNAL_ADDRESS];

        // 发送请求
        socket.send(&request).await
            .context("Failed to send external address request")?;

        // 接收响应
        let mut buf = [0u8; 12];
        match timeout(Duration::from_millis(500), socket.recv(&mut buf)).await {
            Ok(Ok(size)) if size >= 12 => {
                // 检查响应格式
                if buf[0] != NATPMP_VERSION || buf[1] != NATPMP_OP_EXTERNAL_ADDRESS + 128 {
                    return Err(anyhow!("Invalid NAT-PMP response"));
                }

                // 检查结果码
                let result_code = u16::from_be_bytes([buf[2], buf[3]]);
                if result_code != NATPMP_RESULT_SUCCESS {
                    return Err(anyhow!("NAT-PMP error: {}", natpmp_error_message(result_code)));
                }

                // 成功验证
                Ok(())
            },
            _ => Err(anyhow!("Gateway did not respond to NAT-PMP request"))
        }
    }

    /// 检测本地IP地址
    async fn detect_local_ip(&self) -> Result<IpAddr> {
        // 创建UDP套接字连接到公共DNS服务器
        let socket = UdpSocket::bind("0.0.0.0:0").await?;
        socket.connect("*******:53").await?;

        // 获取本地地址
        let local_addr = socket.local_addr()?;
        let local_ip = local_addr.ip();

        Ok(local_ip)
    }

    /// 更新外部IP地址
    async fn update_external_ip(&self) -> Result<()> {
        // 检查是否需要更新
        {
            let last_update = self.last_external_ip_update.lock().await;
            if let Some(time) = *last_update {
                if time.elapsed().as_secs() < self.external_ip_update_interval {
                    // 如果已经有外部IP地址，且更新时间未到，则直接返回
                    let external_ip = self.external_ip.lock().await;
                    if external_ip.is_some() {
                        return Ok(());
                    }
                }
            }
        }

        // 获取网关
        let gateway = match self.gateway.lock().await.clone() {
            Some(gw) => gw,
            None => self.discover_gateway().await?
        };

        // 创建UDP套接字
        let socket = UdpSocket::bind("0.0.0.0:0").await
            .context("Failed to bind UDP socket for external IP request")?;

        // 构建NAT-PMP请求
        let request = [NATPMP_VERSION, NATPMP_OP_EXTERNAL_ADDRESS];

        // 发送请求，带重试
        let gateway_addr = SocketAddr::new(gateway, NATPMP_PORT);
        socket.connect(gateway_addr).await
            .context("Failed to connect to gateway for external IP request")?;

        // 实现指数退避重试
        let mut timeout_ms = NATPMP_INITIAL_TIMEOUT;

        for retry in 0..NATPMP_MAX_RETRIES {
            // 发送请求
            if let Err(e) = socket.send(&request).await {
                debug!("Failed to send external IP request (retry {}/{}): {}",
                       retry + 1, NATPMP_MAX_RETRIES, e);
                continue;
            }

            // 接收响应
            let mut buf = [0u8; 12];
            match timeout(Duration::from_millis(timeout_ms), socket.recv(&mut buf)).await {
                Ok(Ok(len)) => {
                    if len < 12 {
                        debug!("Invalid NAT-PMP response length: {} (retry {}/{})",
                               len, retry + 1, NATPMP_MAX_RETRIES);
                        timeout_ms *= 2; // 指数退避
                        continue;
                    }

                    // 解析响应
                    let version = buf[0];
                    let op = buf[1];
                    let result = u16::from_be_bytes([buf[2], buf[3]]);

                    if version != NATPMP_VERSION {
                        debug!("Invalid NAT-PMP version: {} (retry {}/{})",
                               version, retry + 1, NATPMP_MAX_RETRIES);
                        timeout_ms *= 2;
                        continue;
                    }

                    if op != NATPMP_OP_EXTERNAL_ADDRESS + 128 {
                        debug!("Invalid NAT-PMP operation: {} (retry {}/{})",
                               op, retry + 1, NATPMP_MAX_RETRIES);
                        timeout_ms *= 2;
                        continue;
                    }

                    if result != NATPMP_RESULT_SUCCESS {
                        debug!("NAT-PMP error: {} - {} (retry {}/{})",
                               result, natpmp_error_message(result), retry + 1, NATPMP_MAX_RETRIES);

                        // 如果是临时错误，继续重试
                        if result == NATPMP_ERR_NETWORK_FAILURE {
                            timeout_ms *= 2;
                            continue;
                        }

                        // 其他错误直接返回
                        return Err(anyhow!("NAT-PMP error: {}", natpmp_error_message(result)));
                    }

                    // 提取外部IP地址
                    let ip = Ipv4Addr::new(buf[8], buf[9], buf[10], buf[11]);
                    let external_ip = IpAddr::V4(ip);

                    // 更新缓存
                    {
                        let mut external_ip_guard = self.external_ip.lock().await;
                        *external_ip_guard = Some(external_ip);
                    }

                    // 更新最后更新时间
                    {
                        let mut last_update = self.last_external_ip_update.lock().await;
                        *last_update = Some(Instant::now());
                    }

                    info!("Updated external IP address: {}", external_ip);
                    return Ok(());
                },
                Ok(Err(e)) => {
                    debug!("Failed to receive NAT-PMP response (retry {}/{}): {}",
                           retry + 1, NATPMP_MAX_RETRIES, e);
                },
                Err(_) => {
                    debug!("NAT-PMP request timed out after {}ms (retry {}/{})",
                           timeout_ms, retry + 1, NATPMP_MAX_RETRIES);
                }
            }

            // 指数退避，但添加一些随机性以避免同步
            timeout_ms = std::cmp::min(timeout_ms * 2, 10000); // 最大10秒
            timeout_ms += rand::thread_rng().gen_range(0..100); // 添加随机抖动
        }

        Err(anyhow!("Failed to get external IP address after {} retries", NATPMP_MAX_RETRIES))
    }
}

#[async_trait]
impl PortMapper for NATPMPMapper {
    async fn create_mapping(&self, protocol: Protocol, internal_port: u16, external_port: u16, lifetime: Duration) -> Result<MappingInfo> {
        // 获取网关
        let gateway = self.discover_gateway().await?;

        // 创建UDP套接字
        let socket = UdpSocket::bind("0.0.0.0:0").await?;

        // 构建NAT-PMP请求
        let op = match protocol {
            Protocol::UDP => NATPMP_OP_MAP_UDP,
            Protocol::TCP => NATPMP_OP_MAP_TCP,
        };

        let mut request = vec![
            NATPMP_VERSION,
            op,
            0, 0, // 保留字段
        ];

        // 内部端口
        request.extend_from_slice(&internal_port.to_be_bytes());

        // 外部端口
        request.extend_from_slice(&external_port.to_be_bytes());

        // 生命周期 - 确保不超过最大值
        let lifetime_secs = std::cmp::min(lifetime.as_secs() as u32, NATPMP_MAX_LIFETIME);
        request.extend_from_slice(&lifetime_secs.to_be_bytes());

        // 发送请求，带重试
        let gateway_addr = SocketAddr::new(gateway, NATPMP_PORT);
        socket.connect(gateway_addr).await
            .context("Failed to connect to gateway for mapping request")?;

        // 实现指数退避重试
        let mut timeout_ms = NATPMP_INITIAL_TIMEOUT;

        for retry in 0..NATPMP_MAX_RETRIES {
            // 发送请求
            if let Err(e) = socket.send(&request).await {
                debug!("Failed to send mapping request (retry {}/{}): {}",
                       retry + 1, NATPMP_MAX_RETRIES, e);
                continue;
            }

            // 接收响应
            let mut buf = [0u8; 16];
            match timeout(Duration::from_millis(timeout_ms), socket.recv(&mut buf)).await {
                Ok(Ok(len)) => {
                    if len < 16 {
                        debug!("Invalid NAT-PMP response length: {} (retry {}/{})",
                               len, retry + 1, NATPMP_MAX_RETRIES);
                        timeout_ms *= 2; // 指数退避
                        continue;
                    }

                    // 解析响应
                    let version = buf[0];
                    let resp_op = buf[1];
                    let result = u16::from_be_bytes([buf[2], buf[3]]);

                    if version != NATPMP_VERSION {
                        debug!("Invalid NAT-PMP version: {} (retry {}/{})",
                               version, retry + 1, NATPMP_MAX_RETRIES);
                        timeout_ms *= 2;
                        continue;
                    }

                    if resp_op != op + 128 {
                        debug!("Invalid NAT-PMP operation: {} (retry {}/{})",
                               resp_op, retry + 1, NATPMP_MAX_RETRIES);
                        timeout_ms *= 2;
                        continue;
                    }

                    if result != NATPMP_RESULT_SUCCESS {
                        debug!("NAT-PMP error: {} - {} (retry {}/{})",
                               result, natpmp_error_message(result), retry + 1, NATPMP_MAX_RETRIES);

                        // 如果是临时错误，继续重试
                        if result == NATPMP_ERR_NETWORK_FAILURE {
                            timeout_ms *= 2;
                            continue;
                        }

                        // 其他错误直接返回
                        return Err(anyhow!("NAT-PMP error: {}", natpmp_error_message(result)));
                    }

                    // 提取映射信息
                    let internal_port = u16::from_be_bytes([buf[8], buf[9]]);
                    let external_port = u16::from_be_bytes([buf[10], buf[11]]);
                    let lifetime = u32::from_be_bytes([buf[12], buf[13], buf[14], buf[15]]);

                    // 获取本地IP地址
                    let local_ip = self.detect_local_ip().await?;

                    // 获取外部IP地址
                    let external_ip = self.get_external_address().await?;

                    // 创建映射信息
                    let mapping = MappingInfo {
                        internal_addr: SocketAddr::new(local_ip, internal_port),
                        external_addr: SocketAddr::new(external_ip, external_port),
                        protocol,
                        lifetime: lifetime as u64,
                        created_at: Instant::now(),
                    };

                    // 添加到活跃映射列表
                    self.track_mapping(&mapping).await;

                    info!("Created NAT-PMP mapping: {}:{} -> {}:{} ({}) for {} seconds",
                        local_ip, internal_port, external_ip, external_port, protocol, lifetime);

                    return Ok(mapping);
                },
                Ok(Err(e)) => {
                    debug!("Failed to receive NAT-PMP response (retry {}/{}): {}",
                           retry + 1, NATPMP_MAX_RETRIES, e);
                },
                Err(_) => {
                    debug!("NAT-PMP request timed out after {}ms (retry {}/{})",
                           timeout_ms, retry + 1, NATPMP_MAX_RETRIES);
                }
            }

            // 指数退避，但添加一些随机性以避免同步
            timeout_ms = std::cmp::min(timeout_ms * 2, 10000); // 最大10秒
            timeout_ms += rand::thread_rng().gen_range(0..100); // 添加随机抖动
        }

        Err(anyhow!("Failed to create mapping after {} retries", NATPMP_MAX_RETRIES))
    }

    /// 跟踪映射
    async fn track_mapping(&self, mapping: &MappingInfo) {
        // 创建映射键
        let key = format!("{}:{}:{}",
            mapping.external_addr.ip(),
            mapping.external_addr.port(),
            mapping.protocol);

        // 添加到活跃映射列表
        let mut mappings = self.active_mappings.write().await;
        mappings.insert(key, mapping.clone());

        debug!("Tracking NAT-PMP mapping: {}:{} ({}) for {} seconds",
            mapping.external_addr.ip(), mapping.external_addr.port(),
            mapping.protocol, mapping.lifetime);
    }

    async fn delete_mapping(&self, mapping: &MappingInfo) -> Result<()> {
        // 在NAT-PMP中，删除映射是通过将生命周期设置为0来实现的
        let _new_mapping = self.create_mapping(
            mapping.protocol,
            mapping.internal_addr.port(),
            mapping.external_addr.port(),
            Duration::from_secs(0)
        ).await?;

        // 从活跃映射列表中移除
        let key = format!("{}:{}:{}",
            mapping.external_addr.ip(),
            mapping.external_addr.port(),
            mapping.protocol);

        let mut mappings = self.active_mappings.write().await;
        mappings.remove(&key);

        info!("Deleted NAT-PMP mapping: {}:{} ({})",
            mapping.external_addr.ip(), mapping.external_addr.port(), mapping.protocol);

        Ok(())
    }

    async fn renew_mapping(&self, mapping: &mut MappingInfo, lifetime: Duration) -> Result<()> {
        // 从活跃映射列表中移除旧映射
        let key = format!("{}:{}:{}",
            mapping.external_addr.ip(),
            mapping.external_addr.port(),
            mapping.protocol);

        {
            let mut mappings = self.active_mappings.write().await;
            mappings.remove(&key);
        }

        // 创建新的映射
        let new_mapping = self.create_mapping(
            mapping.protocol,
            mapping.internal_addr.port(),
            mapping.external_addr.port(),
            lifetime
        ).await?;

        // 更新映射信息
        mapping.lifetime = new_mapping.lifetime;
        mapping.created_at = new_mapping.created_at;

        // 重新添加到活跃映射列表
        self.track_mapping(mapping).await;

        info!("Renewed NAT-PMP mapping: {}:{} ({}) for {} seconds",
            mapping.external_addr.ip(), mapping.external_addr.port(),
            mapping.protocol, mapping.lifetime);

        Ok(())
    }

    /// 获取所有活跃映射
    pub async fn get_active_mappings(&self) -> Vec<MappingInfo> {
        let mappings = self.active_mappings.read().await;
        mappings.values().cloned().collect()
    }

    /// 清理过期映射
    pub async fn cleanup_expired_mappings(&self) -> Result<usize> {
        let mut expired_count = 0;
        let mut to_remove = Vec::new();

        // 查找过期映射
        {
            let mappings = self.active_mappings.read().await;
            for (key, mapping) in mappings.iter() {
                if mapping.created_at.elapsed().as_secs() >= mapping.lifetime {
                    to_remove.push((key.clone(), mapping.clone()));
                }
            }
        }

        // 删除过期映射
        for (key, mapping) in to_remove {
            if let Err(e) = self.delete_mapping(&mapping).await {
                warn!("Failed to delete expired mapping {}: {}", key, e);
            } else {
                expired_count += 1;
            }
        }

        if expired_count > 0 {
            info!("Cleaned up {} expired NAT-PMP mappings", expired_count);
        }

        Ok(expired_count)
    }

    /// 启动自动刷新任务
    pub async fn start_auto_refresh(&self) -> Result<()> {
        if !self.auto_refresh_enabled {
            return Ok(());
        }

        // 创建一个弱引用，避免循环引用
        let self_weak = Arc::downgrade(&Arc::new(self.clone()));

        // 启动后台任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60)); // 每分钟检查一次

            loop {
                interval.tick().await;

                // 尝试升级弱引用
                if let Some(mapper) = self_weak.upgrade() {
                    // 清理过期映射
                    if let Err(e) = mapper.cleanup_expired_mappings().await {
                        warn!("Failed to cleanup expired mappings: {}", e);
                    }

                    // 刷新需要更新的映射
                    let mappings = mapper.get_active_mappings().await;
                    for mut mapping in mappings {
                        // 如果映射已经过期，跳过
                        if mapping.created_at.elapsed().as_secs() >= mapping.lifetime {
                            continue;
                        }

                        // 如果映射还有超过一半的生命周期，跳过
                        if mapping.created_at.elapsed().as_secs() < mapping.lifetime / 2 {
                            continue;
                        }

                        // 更新映射
                        let lifetime = Duration::from_secs(mapping.lifetime);
                        if let Err(e) = mapper.renew_mapping(&mut mapping, lifetime).await {
                            warn!("Failed to renew mapping: {}", e);
                        }
                    }
                } else {
                    // 如果弱引用无法升级，说明映射器已被释放，退出循环
                    break;
                }
            }
        });

        Ok(())
    }

    async fn get_external_address(&self) -> Result<IpAddr> {
        // 检查缓存
        {
            let external_ip = self.external_ip.lock().await;
            if let Some(ip) = *external_ip {
                return Ok(ip);
            }
        }

        // 更新外部IP地址
        self.update_external_ip().await?;

        // 获取更新后的外部IP地址
        let external_ip = self.external_ip.lock().await;
        if let Some(ip) = *external_ip {
            Ok(ip)
        } else {
            Err(anyhow!("Failed to get external IP address"))
        }
    }
}
