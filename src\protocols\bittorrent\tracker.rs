use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use std::net::{IpAddr, Ipv4Addr};
use url::Url;
use tracing::debug;
use std::sync::Arc;

use super::torrent::TorrentInfo;
use crate::utils::{HttpClientTrait, HttpClient};

/// Tracker request parameters
#[derive(Debug, Clone, Serialize)]
pub struct TrackerRequest {
    pub info_hash: String,
    pub peer_id: String,

    pub uploaded: u64,
    pub downloaded: u64,
    pub left: u64,
    pub compact: u8,
    pub no_peer_id: u8,
    pub event: String,
    pub numwant: u32,
    pub key: String,
}

/// Peer information from tracker
#[derive(Debug, <PERSON>lone)]
pub struct TrackerPeer {
    pub peer_id: Option<String>,
    pub ip: IpAddr,
    pub port: u16,
}

/// Tracker response
#[derive(Debug, <PERSON>lone)]
pub struct TrackerResponse {
    pub interval: u32,
    pub min_interval: Option<u32>,
    pub complete: Option<u32>,
    pub incomplete: Option<u32>,
    pub peers: Vec<TrackerPeer>,
    pub warning_message: Option<String>,
    pub failure_reason: Option<String>,
}

/// Tracker response dictionary for serde
#[derive(Debug, Deserialize)]
struct BencodeTrackerResponse {
    interval: u32,
    #[serde(rename = "min interval")]
    #[serde(default)]
    min_interval: Option<u32>,
    #[serde(default)]
    complete: Option<u32>,
    #[serde(default)]
    incomplete: Option<u32>,
    #[serde(with = "serde_bytes")]
    peers: Vec<u8>,
    #[serde(rename = "warning message")]
    #[serde(default)]
    warning_message: Option<String>,
    #[serde(rename = "failure reason")]
    #[serde(default)]
    failure_reason: Option<String>,
}

/// BitTorrent tracker client
#[derive(Clone)]
pub struct TrackerClient {
    client: Arc<dyn HttpClientTrait>,
    peer_id: String,

    key: String,
}

impl TrackerClient {
    /// Get the peer ID of the client
    pub fn peer_id(&self) -> String {
        self.peer_id.clone()
    }


    /// Create a new tracker client
    pub fn new(peer_id: String) -> Self {
        let client = Arc::new(HttpClient::new().unwrap());

        // Generate a random key
        let key = format!("{:08x}", rand::random::<u32>());

        Self {
            client,
            peer_id,

            key,
        }
    }

    /// Set the HTTP client for the tracker
    pub fn with_http_client(mut self, client: Arc<dyn HttpClientTrait>) -> Self {
        self.client = client;
        self
    }

    /// Announce to tracker using the torrent's announce URL
    pub async fn announce(&self, torrent: &TorrentInfo, event: &str, uploaded: u64, downloaded: u64, left: u64) -> Result<TrackerResponse> {
        // Build the announce URL
        if torrent.announce.is_none() {
            return Err(anyhow!("No announce URL in torrent"));
        }

        let tracker_url = torrent.announce.as_ref().unwrap();
        self.announce_to_url(tracker_url, torrent, event, uploaded, downloaded, left).await
    }

    /// Announce to a specific tracker URL
    pub async fn announce_to_url(&self, tracker_url: &str, torrent: &TorrentInfo, event: &str, uploaded: u64, downloaded: u64, left: u64) -> Result<TrackerResponse> {
        debug!("Announcing to tracker: {}", tracker_url);

        // Parse the URL
        let mut url = Url::parse(tracker_url)?;

        // Add query parameters
        url.query_pairs_mut()
            .append_pair("info_hash", &torrent.info_hash_encoded)
            .append_pair("peer_id", &self.peer_id)
            
            .append_pair("uploaded", &uploaded.to_string())
            .append_pair("downloaded", &downloaded.to_string())
            .append_pair("left", &left.to_string())
            .append_pair("compact", "1")
            .append_pair("no_peer_id", "1")
            .append_pair("event", event)
            .append_pair("numwant", "50")
            .append_pair("key", &self.key);

        // Send the request
        let response = self.client.get(url.clone().as_str()).await?;

        // Check if the request was successful
        if !response.status().is_success() {
            return Err(anyhow!("Tracker request failed with status: {}", response.status()));
        }

        // Get the response body
        let body = response.bytes().await?;

        // Parse the response
        let bencode_response: BencodeTrackerResponse = match serde_bencode::from_bytes(&body) {
            Ok(response) => response,
            Err(e) => {
                return Err(anyhow!("Failed to parse tracker response: {}", e));
            }
        };

        // Check for failure reason
        if let Some(reason) = &bencode_response.failure_reason {
            return Err(anyhow!("Tracker returned failure: {}", reason));
        }

        // Parse peers
        let peers = self.parse_peers(&bencode_response.peers);

        debug!("Received {} peers from tracker {}", peers.len(), tracker_url);

        Ok(TrackerResponse {
            interval: bencode_response.interval,
            min_interval: bencode_response.min_interval,
            complete: bencode_response.complete,
            incomplete: bencode_response.incomplete,
            peers,
            warning_message: bencode_response.warning_message,
            failure_reason: None,
        })
    }

    /// Parse compact peer format
    fn parse_peers(&self, peers_data: &[u8]) -> Vec<TrackerPeer> {
        let mut peers = Vec::new();

    
        for chunk in peers_data.chunks(6) {
            if chunk.len() == 6 {
                let ip = Ipv4Addr::new(chunk[0], chunk[1], chunk[2], chunk[3]);
                // 端口号由后两个字节组成，使用大端序
                let port = ((chunk[4] as u16) << 8) | (chunk[5] as u16);
                
                peers.push(TrackerPeer {
                    peer_id: None,
                    ip: IpAddr::V4(ip),
                    port,
                });
            }
        }

        peers
    }
}
