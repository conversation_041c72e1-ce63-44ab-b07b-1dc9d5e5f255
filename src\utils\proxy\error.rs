use std::error::Error;
use std::fmt;
use std::io;

/// 代理检测过程中可能发生的错误类型
#[derive(Debug)]
pub enum ProxyError {
    /// 环境变量错误
    EnvError(String),
    /// 操作系统特定错误
    OsError(String),
    /// URL 解析错误
    UrlParseError(String),
    /// IO 错误
    IoError(io::Error),
    /// 注册表错误（Windows 特定）
    #[cfg(target_os = "windows")]
    RegistryError(String),
    /// 其他错误
    Other(String),
}

impl fmt::Display for ProxyError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ProxyError::EnvError(msg) => write!(f, "环境变量代理错误: {}", msg),
            ProxyError::OsError(msg) => write!(f, "操作系统代理错误: {}", msg),
            ProxyError::UrlParseError(msg) => write!(f, "URL解析错误: {}", msg),
            ProxyError::IoError(err) => write!(f, "IO错误: {}", err),
            #[cfg(target_os = "windows")]
            ProxyError::RegistryError(msg) => write!(f, "Windows注册表错误: {}", msg),
            ProxyError::Other(msg) => write!(f, "代理错误: {}", msg),
        }
    }
}

impl Error for ProxyError {
    fn source(&self) -> Option<&(dyn Error + 'static)> {
        match self {
            ProxyError::IoError(err) => Some(err),
            _ => None,
        }
    }
}

impl From<io::Error> for ProxyError {
    fn from(err: io::Error) -> Self {
        ProxyError::IoError(err)
    }
}

impl From<url::ParseError> for ProxyError {
    fn from(err: url::ParseError) -> Self {
        ProxyError::UrlParseError(err.to_string())
    }
}

// 删除这个实现，避免与通用的 From<io::Error> 实现冲突
// 在需要将 IO 错误转换为注册表错误的地方，使用显式的错误转换
// 例如: .map_err(|e| ProxyError::RegistryError(format!("注册表操作失败: {}", e)))
// #[cfg(target_os = "windows")]
// impl From<std::io::Error> for ProxyError {
//     fn from(err: std::io::Error) -> Self {
//         ProxyError::RegistryError(format!("注册表操作失败: {}", err))
//     }
// }

// 删除这个实现，因为winreg库使用std::io::Error作为其错误类型，而不是RegError
// #[cfg(target_os = "windows")]
// impl From<winreg::RegError> for ProxyError {
//     fn from(err: winreg::RegError) -> Self {
//         ProxyError::RegistryError(err.to_string())
//     }
// }
