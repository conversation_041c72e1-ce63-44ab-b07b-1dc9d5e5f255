//! HTTP download protocol implementation

mod protocol;
mod downloader;
mod core;
mod utils;

// 各个功能模块
mod file_utils;
mod buffer_manager;
mod http_client;
mod resume_manager;
mod speed_tracker;
mod cancellation;
mod chunk_manager;
mod multi_thread_downloader;
mod metadata_manager;
mod performance_optimizer;

#[cfg(test)]
mod tests;

// 重新导出公共接口
pub use downloader::HttpDownloader;

// 导出内部模块，供其他模块使用
pub(crate) mod internal {
    pub use super::downloader::*;
    pub use super::core::*;
    pub use super::utils::*;
    pub use super::file_utils::*;
    pub use super::buffer_manager::*;
    pub use super::http_client::*;
    pub use super::resume_manager::*;
    pub use super::speed_tracker::*;
    pub use super::cancellation::*;
    pub use super::chunk_manager::*;
    pub use super::multi_thread_downloader::*;
    pub use super::metadata_manager::*;
    pub use super::performance_optimizer::*;
}

// 导出公共内部工具
pub use utils::*;
