//! Tonitru 下载器 CLI 入口点
//!
//! 这是Tonitru下载器的命令行界面入口点，负责启动CLI应用程序并处理顶级错误。

use std::process;
use std::time::Instant;
use anyhow::Result;
use log::{error, info, debug};
use colored::Colorize;

use tonitru_downloader::cli::utils::diagnostics::{generate_diagnostic_report, collect_system_info, save_diagnostic_report_to_file};
use tonitru_downloader::cli::utils::debug::print_colored_debug;

/// 主函数
/// 
/// 使用tokio运行时执行异步CLI程序，并处理顶级错误。
/// 如果发生错误，将打印错误信息并以非零状态码退出。
#[tokio::main]
async fn main() -> Result<()> {
    // 记录启动时间
    let start_time = Instant::now();
    debug!("CLI 主程序启动");
    
    // 设置panic钩子，以便更好地处理崩溃
    std::panic::set_hook(Box::new(|panic_info| {
        let message = if let Some(s) = panic_info.payload().downcast_ref::<String>() {
            s.clone()
        } else if let Some(s) = panic_info.payload().downcast_ref::<&str>() {
            s.to_string()
        } else {
            "未知错误".to_string()
        };
        
        let location = if let Some(location) = panic_info.location() {
            format!("在 {} 的第 {} 行", location.file(), location.line())
        } else {
            "位置未知".to_string()
        };
        
        eprintln!("{}", "程序崩溃！".bright_red().bold());
        eprintln!("{}: {}", "错误信息".bright_yellow(), message);
        eprintln!("{}: {}", "位置".bright_yellow(), location);
        
        // 收集系统信息用于诊断
        let _system_info = collect_system_info();
        
        // 生成诊断报告
        let report = generate_diagnostic_report(true);
        
        // 将报告保存到文件
        let _report_path = match save_diagnostic_report_to_file(report, &format!("CRASH: {}", message)) {
            Ok(path) => {
                eprintln!("{}: {}", "诊断报告已保存到".bright_green(), path);
                eprintln!("请在提交问题报告时附上此文件");
            },
            Err(e) => {
                eprintln!("{}: {}", "无法生成诊断报告".bright_red(), e);
            }
        };
        
        eprintln!("{}", "请报告此问题：https://github.com/your-repo/tonitru_downloader_rust/issues".bright_blue());
    }));
    
    // 运行CLI并处理错误
    let result = match tonitru_downloader::cli::run().await {
        Ok(_) => {
            let elapsed = start_time.elapsed();
            info!("CLI执行成功完成，总耗时: {:?}", elapsed);
            // 只在性能分析模式下输出总运行时间
            if tonitru_downloader::cli::utils::profiler::is_profiling_enabled() {
                print_colored_debug(&format!("总运行时间: {:?}", elapsed), colored::Color::BrightGreen);
            }
            Ok(())
        },
        Err(e) => {
            let elapsed = start_time.elapsed();
            error!("CLI执行失败: {}，耗时: {:?}", e, elapsed);
            eprintln!("{}: {}", "错误".bright_red().bold(), e);
            
            // 收集错误链信息
            let mut error_chain = Vec::new();
            let mut current_error = e.source();
            while let Some(source) = current_error {
                error_chain.push(source.to_string());
                current_error = source.source();
            }
            
            // 打印错误链
            if !error_chain.is_empty() {
                eprintln!("{}", "错误链:".bright_yellow());
                for (i, err) in error_chain.iter().enumerate() {
                    eprintln!("  {}. {}", i + 1, err);
                }
            }
            
            // 生成诊断报告
            let _system_info = collect_system_info();
            let report = generate_diagnostic_report(true);
            match save_diagnostic_report_to_file(report, &format!("ERROR: {}", e)) {
                Ok(report_path) => {
                    eprintln!("{}: {}", "诊断报告已保存到".bright_green(), report_path);
                },
                Err(report_err) => {
                    eprintln!("{}: {}", "无法生成诊断报告".bright_red(), report_err);
                }
            }
            
            // 以非零状态码退出
            process::exit(1);
        }
    };
    
    result
}