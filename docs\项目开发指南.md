# Tonitru 下载器项目开发指南

## 1. 项目概述

### 1.1 项目简介

Tonitru 是一个高性能、分布式的下载工具，支持多种下载协议，包括 HTTP、HTTPS、FTP、BitTorrent 等。它采用 Rust 语言开发后端，Vue3 开发前端，旨在提供高效、稳定、易用的下载体验。

### 1.2 核心特性

- 多协议支持：HTTP(S)、FTP、BitTorrent 等
- 高性能下载引擎：基于 Rust 和 Tokio 的异步 I/O
- 分布式架构：支持多节点协作下载
- 断点续传：支持中断后继续下载
- 速度控制：精细的带宽管理
- 插件系统：可扩展的功能模块
- 多平台支持：Windows、macOS、Linux
- 友好的用户界面：基于 Vue3 的现代 Web 界面
- 完整的 API：支持第三方集成

### 1.3 技术栈

#### 1.3.1 后端技术栈

- **语言**：Rust
- **异步运行时**：Tokio
- **Web 框架**：Axum
- **数据库**：SQLite/PostgreSQL
- **序列化**：Serde
- **日志**：tracing
- **配置**：config-rs
- **HTTP 客户端**：reqwest
- **WebSocket**：tokio-tungstenite

#### 1.3.2 前端技术栈

- **框架**：Vue 3
- **状态管理**：Pinia
- **路由**：Vue Router
- **UI 组件**：Element Plus
- **HTTP 客户端**：Axios
- **WebSocket**：native WebSocket API
- **构建工具**：Vite
- **CSS 预处理器**：SCSS

## 2. 架构设计

### 2.1 系统架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        前端应用 (Vue3)                          │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                         API 网关 (Axum)                         │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                ┌───────────────┴───────────────┐
                │                               │
                ▼                               ▼
┌───────────────────────────┐   ┌───────────────────────────┐
│      下载管理模块        │   │      配置管理模块        │
└───────────┬───────────────┘   └───────────────────────────┘
            │
            │
┌───────────▼───────────────┐   ┌───────────────────────────┐
│      协议处理模块        │──▶│      存储管理模块        │
└───────────────────────────┘   └───────────────────────────┘
            │
            │
┌───────────▼───────────────┐   ┌───────────────────────────┐
│      速度控制模块        │   │      插件系统            │
└───────────────────────────┘   └───────────────────────────┘
```

### 2.2 模块设计

#### 2.2.1 核心模块

- **下载管理模块**：负责创建、管理和监控下载任务
- **协议处理模块**：实现各种下载协议的处理逻辑
- **存储管理模块**：负责文件存储和管理
- **配置管理模块**：处理系统配置和用户设置，包括读取、更新、保存和重置配置
- **速度控制模块**：实现带宽管理和速度限制
- **插件系统**：提供扩展功能的接口和管理

#### 2.2.2 API 模块

- **HTTP API**：提供 RESTful API 接口
- **WebSocket API**：提供实时通信接口
- **CLI API**：提供命令行接口

### 2.3 数据流设计

```
┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐
│  用户请求 │───▶│  API 层  │───▶│ 业务逻辑层│───▶│ 数据存储层│
└───────────┘    └───────────┘    └───────────┘    └───────────┘
                       │                │               │
                       │                │               │
                       ▼                ▼               ▼
                  ┌───────────┐    ┌───────────┐   ┌───────────┐
                  │ 事件通知 │◀───│ 下载引擎 │───│ 文件系统 │
                  └───────────┘    └───────────┘   └───────────┘
```

## 3. 设计原则

### 3.1 代码设计原则

- **模块化**：功能分离为独立模块，降低耦合度
- **可测试性**：代码设计便于单元测试和集成测试
- **错误处理**：统一的错误处理机制
- **异步优先**：充分利用异步 I/O 提高性能
- **资源管理**：严格控制资源使用，避免泄漏
- **接口设计**：清晰、一致的 API 设计

### 3.2 性能设计原则

- **并发下载**：支持文件分片并发下载
- **异步 I/O**：使用非阻塞 I/O 操作
- **资源池化**：复用连接和其他资源
- **内存管理**：控制内存使用，避免过度分配
- **批处理**：合并小操作减少开销

### 3.3 安全设计原则

- **输入验证**：验证所有外部输入
- **权限控制**：基于角色的访问控制
- **安全通信**：使用 TLS 加密通信
- **敏感数据保护**：加密存储敏感信息
- **日志审计**：记录关键操作日志

## 4. 项目结构

```
.
├── Cargo.toml                 # 项目依赖配置
├── Cargo.lock                 # 依赖锁定文件
├── .github/                   # GitHub 配置
├── .gitignore                 # Git 忽略文件
├── docs/                      # 项目文档
├── src/                       # 源代码
│   ├── main.rs                # 程序入口
│   ├── api/                   # API 模块
│   │   ├── http.rs            # HTTP API 实现
│   │   ├── websocket.rs       # WebSocket API 实现
│   │   └── mod.rs             # 模块导出
│   ├── config/                # 配置模块
│   │   ├── manager.rs         # 配置管理器
│   │   ├── models.rs          # 配置数据模型
│   │   └── mod.rs             # 模块导出
│   ├── download/              # 下载模块
│   │   ├── manager.rs         # 下载管理器
│   │   ├── task.rs            # 下载任务
│   │   ├── protocols/         # 协议实现
│   │   │   ├── http.rs        # HTTP 协议
│   │   │   ├── ftp.rs         # FTP 协议
│   │   │   ├── bittorrent.rs  # BitTorrent 协议
│   │   │   └── mod.rs         # 模块导出
│   │   └── mod.rs             # 模块导出
│   ├── storage/               # 存储模块
│   │   ├── manager.rs         # 存储管理器
│   │   ├── file.rs            # 文件操作
│   │   └── mod.rs             # 模块导出
│   ├── speed/                 # 速度控制模块
│   │   ├── limiter.rs         # 速度限制器
│   │   ├── scheduler.rs       # 调度器
│   │   └── mod.rs             # 模块导出
│   ├── plugin/                # 插件模块
│   │   ├── manager.rs         # 插件管理器
│   │   ├── loader.rs          # 插件加载器
│   │   └── mod.rs             # 模块导出
│   ├── event/                 # 事件模块
│   │   ├── manager.rs         # 事件管理器
│   │   ├── models.rs          # 事件数据模型
│   │   └── mod.rs             # 模块导出
│   ├── utils/                 # 工具模块
│   │   ├── error.rs           # 错误处理
│   │   ├── logger.rs          # 日志工具
│   │   └── mod.rs             # 模块导出
│   └── cli/                   # CLI 模块
│       ├── commands.rs        # 命令实现
│       ├── app.rs             # CLI 应用
│       └── mod.rs             # 模块导出
├── tests/                     # 集成测试
├── benches/                   # 性能测试
└── web/                       # 前端代码
    ├── package.json           # 前端依赖配置
    ├── vite.config.js         # Vite 配置
    ├── index.html             # HTML 入口
    ├── public/                # 静态资源
    └── src/                   # 前端源代码
        ├── main.js            # 前端入口
        ├── App.vue            # 根组件
        ├── assets/            # 资源文件
        ├── components/        # 组件
        ├── views/             # 页面
        ├── router/            # 路由
        ├── store/             # 状态管理
        ├── api/               # API 客户端
        └── utils/             # 工具函数
```

## 5. 开发计划

### 5.1 第一阶段：基础架构（已完成）

- [x] 项目初始化和基础架构搭建
- [x] 核心模块设计和实现
- [x] HTTP 下载功能实现
- [x] 基础 API 接口定义和实现
- [x] 配置管理系统实现
- [x] 单元测试框架搭建

### 5.2 第二阶段：功能扩展（进行中）

- [x] BitTorrent 协议重构和优化
- [x] WebSeed 功能实现
- [x] 任务更新功能实现
- [x] 断点续传功能完善
- [x] 速度控制系统实现
- [x] 事件通知系统实现
- [x] CLI 工具实现
- [x] 前端基础界面实现

### 5.3 第三阶段：性能优化与集成

- [ ] 多协议下载引擎优化
- [ ] 分布式节点协作功能
- [ ] 插件系统实现
- [ ] 高级 UI 功能实现
- [ ] 完整的文档和示例
- [ ] 性能测试和优化
- [ ] 安全审计和修复

### 5.4 第四阶段：扩展功能

- [ ] 云存储集成
- [ ] 移动端适配
- [ ] 国际化支持
- [ ] 高级调度功能
- [ ] 数据分析和报告
- [ ] 第三方服务集成

## 6. 开发指南

### 6.1 环境搭建

#### 6.1.1 后端环境

```bash
# 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 克隆仓库
git clone https://github.com/your-username/tonitru_downloader_rust.git
cd tonitru_downloader_rust

# 安装开发工具
rustup component add rustfmt clippy

# 编译项目
cargo build

# 运行测试
cargo test
```

#### 6.1.2 前端环境

```bash
# 进入前端目录
cd web

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 6.2 代码规范

#### 6.2.1 Rust 代码规范

- 使用 `rustfmt` 格式化代码
- 使用 `clippy` 进行静态分析
- 遵循 Rust API 指南的命名约定
- 为公共 API 编写文档注释
- 使用 `thiserror` 定义错误类型
- 使用 `tracing` 进行日志记录

#### 6.2.2 前端代码规范

- 使用 ESLint 和 Prettier 格式化代码
- 遵循 Vue 风格指南
- 使用 TypeScript 类型注解
- 组件使用 PascalCase 命名
- 使用 Composition API
- 编写单元测试

### 6.3 测试指南

#### 6.3.1 单元测试

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_download_task_creation() {
        let task = DownloadTask::new("https://example.com/file.zip", "/tmp/file.zip");
        assert_eq!(task.url, "https://example.com/file.zip");
        assert_eq!(task.output_path, "/tmp/file.zip");
        assert_eq!(task.status, TaskStatus::Created);
    }
    
    #[tokio::test]
    async fn test_download_manager() {
        let manager = DownloadManager::new().await.unwrap();
        let task_id = manager.create_task("https://example.com/file.zip", Some("/tmp")).await.unwrap();
        assert!(manager.get_task(&task_id).await.is_some());
    }
}
```

#### 6.3.2 集成测试

```rust
// tests/api_tests.rs
use tonitru_downloader::api::client::ApiClient;
use tonitru_downloader::download::models::TaskStatus;

#[tokio::test]
async fn test_api_create_task() {
    // 启动测试服务器
    let server = spawn_test_server().await;
    let client = ApiClient::new("http://localhost:8080");
    
    // 创建下载任务
    let task_id = client.create_task("https://example.com/file.zip", None).await.unwrap();
    
    // 获取任务状态
    let task = client.get_task(&task_id).await.unwrap();
    assert_eq!(task.status, TaskStatus::Created);
    
    // 关闭测试服务器
    server.shutdown().await;
}
```

### 6.4 贡献指南

#### 6.4.1 提交 Pull Request

1. Fork 仓库并克隆到本地
2. 创建新的分支：`git checkout -b feature/your-feature-name`
3. 实现功能或修复 bug
4. 确保通过所有测试：`cargo test`
5. 提交代码：`git commit -m "Add feature: your feature name"`
6. 推送到 Fork 仓库：`git push origin feature/your-feature-name`
7. 创建 Pull Request

#### 6.4.2 代码审查标准

- 代码必须通过 CI 测试
- 代码必须遵循项目代码规范
- 新功能必须有单元测试
- 文档必须更新
- 提交信息必须清晰明了

## 7. 常见问题与解决方案

### 7.1 编译问题

#### 7.1.1 依赖冲突

**问题**：依赖版本冲突导致编译失败

**解决方案**：
1. 更新 `Cargo.lock`：`cargo update`
2. 检查依赖版本约束：`cargo tree -d`
3. 调整依赖版本范围

#### 7.1.2 平台特定问题

**问题**：在特定平台上编译失败

**解决方案**：
1. 使用条件编译：`#[cfg(target_os = "windows")]`
2. 添加平台特定依赖
3. 检查环境变量和工具链

### 7.2 运行时问题

#### 7.2.1 性能问题

**问题**：下载速度慢或 CPU 使用率高

**解决方案**：
1. 使用性能分析工具：`cargo flamegraph`
2. 优化内存分配和复制
3. 调整并发参数
4. 使用缓冲池

#### 7.2.2 内存泄漏

**问题**：长时间运行后内存使用增加

**解决方案**：
1. 使用 `drop` 显式释放资源
2. 检查循环引用
3. 使用弱引用 `Weak<T>`
4. 使用内存分析工具

## 8. 参考资料

### 8.1 官方文档

- [Rust 官方文档](https://doc.rust-lang.org/book/)
- [Tokio 文档](https://tokio.rs/docs/)
- [Axum 文档](https://docs.rs/axum/)
- [Vue 3 文档](https://v3.vuejs.org/)

### 8.2 相关规范

- [HTTP/1.1 规范 (RFC 7230-7235)](https://tools.ietf.org/html/rfc7230)
- [BitTorrent 规范](http://www.bittorrent.org/beps/bep_0003.html)
- [WebSeed 规范 (BEP 19)](http://www.bittorrent.org/beps/bep_0019.html)
- [RESTful API 设计指南](https://restfulapi.net/)
