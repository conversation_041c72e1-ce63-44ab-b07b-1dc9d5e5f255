use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use tokio::sync::RwLock;
use tracing::{debug, warn};

/// STUN服务器状态
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum ServerStatus {
    /// 未知状态
    Unknown,
    /// 可用
    Available,
    /// 不可用
    Unavailable,
}

/// STUN服务器信息
#[derive(Debug, Clone)]
struct ServerInfo {
    /// 服务器地址
    addr: SocketAddr,
    /// 服务器状态
    status: ServerStatus,
    /// 上次检查时间
    last_check: Instant,
    /// 失败次数
    failure_count: u32,
}

/// STUN服务器池
pub struct StunServerPool {
    /// 服务器列表
    servers: RwLock<Vec<ServerInfo>>,
    /// 当前服务器索引
    current_index: RwLock<usize>,
}

impl StunServerPool {
    /// 创建新的STUN服务器池
    pub fn new(server_addrs: Vec<String>) -> Self {
        let mut servers = Vec::with_capacity(server_addrs.len());

        for addr_str in server_addrs {
            if let Ok(addr) = addr_str.parse() {
                servers.push(ServerInfo {
                    addr,
                    status: ServerStatus::Unknown,
                    last_check: Instant::now(),
                    failure_count: 0,
                });
            } else {
                warn!("Invalid STUN server address: {}", addr_str);
            }
        }

        // 如果没有提供服务器，添加默认服务器
        if servers.is_empty() {
            let default_servers = [
                "stun.l.google.com:19302",
                "stun1.l.google.com:19302",
                "stun2.l.google.com:19302",
                "stun.ekiga.net:3478",
                "stun.ideasip.com:3478",
                "stun.schlund.de:3478",
            ];

            for &addr_str in &default_servers {
                if let Ok(addr) = addr_str.parse() {
                    servers.push(ServerInfo {
                        addr,
                        status: ServerStatus::Unknown,
                        last_check: Instant::now(),
                        failure_count: 0,
                    });
                }
            }
        }

        Self {
            servers: RwLock::new(servers),
            current_index: RwLock::new(0),
        }
    }

    /// 获取下一个可用的STUN服务器
    pub async fn get_server(&self) -> Result<SocketAddr> {
        let servers = self.servers.read().await;

        if servers.is_empty() {
            return Err(anyhow!("No STUN servers available"));
        }

        // 获取当前索引
        let mut current_index = self.current_index.write().await;

        // 尝试找到一个可用的服务器
        let start_index = *current_index;
        let mut index = start_index;

        loop {
            let server = &servers[index];

            // 如果服务器状态未知或可用，或者上次检查时间超过5分钟，则使用该服务器
            if server.status != ServerStatus::Unavailable ||
               server.last_check.elapsed() > Duration::from_secs(300) {
                *current_index = (index + 1) % servers.len();
                return Ok(server.addr);
            }

            // 移动到下一个服务器
            index = (index + 1) % servers.len();

            // 如果已经尝试了所有服务器，则返回错误
            if index == start_index {
                return Err(anyhow!("No available STUN servers"));
            }
        }
    }

    /// 更新服务器状态
    pub async fn update_server_status(&self, addr: SocketAddr, success: bool) {
        let mut servers = self.servers.write().await;

        for server in servers.iter_mut() {
            if server.addr == addr {
                server.last_check = Instant::now();

                if success {
                    server.status = ServerStatus::Available;
                    server.failure_count = 0;
                } else {
                    server.failure_count += 1;

                    // 如果连续失败3次，则标记为不可用
                    if server.failure_count >= 3 {
                        server.status = ServerStatus::Unavailable;
                    }
                }

                break;
            }
        }
    }

    /// 添加服务器
    pub async fn add_server(&self, addr: SocketAddr) {
        let mut servers = self.servers.write().await;

        // 检查服务器是否已存在
        for server in servers.iter() {
            if server.addr == addr {
                return;
            }
        }

        // 添加新服务器
        servers.push(ServerInfo {
            addr,
            status: ServerStatus::Unknown,
            last_check: Instant::now(),
            failure_count: 0,
        });
    }

    /// 获取所有服务器
    pub async fn get_all_servers(&self) -> Vec<SocketAddr> {
        let servers = self.servers.read().await;
        servers.iter().map(|s| s.addr).collect()
    }

    /// 获取备用服务器（与指定服务器不同的服务器）
    pub async fn get_alternate_server(&self, current_server: SocketAddr) -> Result<SocketAddr> {
        let servers = self.servers.read().await;

        if servers.len() <= 1 {
            return Err(anyhow!("No alternate STUN servers available"));
        }

        // 尝试找到一个与当前服务器不同且可用的服务器
        for server in servers.iter() {
            if server.addr != current_server &&
               (server.status != ServerStatus::Unavailable ||
                server.last_check.elapsed() > Duration::from_secs(300)) {
                return Ok(server.addr);
            }
        }

        // 如果没有找到可用的备用服务器，返回错误
        Err(anyhow!("No available alternate STUN servers"))
    }
}
