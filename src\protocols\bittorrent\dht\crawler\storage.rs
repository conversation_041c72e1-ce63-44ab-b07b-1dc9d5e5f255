use std::collections::{HashMap, HashSet, VecDeque};
use std::net::SocketAddr;
use std::path::Path;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use anyhow::Result;
use tokio::fs;
use tokio::sync::{Mutex, RwLock};
use tracing::{info, warn};
use serde::{Serialize, Deserialize};

use crate::protocols::bittorrent::dht::node::DHTNode;

/// 信息哈希元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InfoHashMetadata {
    /// 信息哈希
    pub infohash: [u8; 20],
    /// 首次发现时间
    pub first_seen: u64,
    /// 最后发现时间
    pub last_seen: u64,
    /// 发现次数
    pub seen_count: u64,
    /// 发现该信息哈希的节点
    pub seen_from: Vec<SocketAddr>,
}

impl InfoHashMetadata {
    /// 创建新的信息哈希元数据
    pub fn new(infohash: [u8; 20], node: SocketAddr) -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_secs();

        Self {
            infohash,
            first_seen: now,
            last_seen: now,
            seen_count: 1,
            seen_from: vec![node],
        }
    }

    /// 更新元数据
    pub fn update(&mut self, node: SocketAddr) {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_secs();

        self.last_seen = now;
        self.seen_count += 1;

        // 添加节点（如果不存在）
        if !self.seen_from.contains(&node) {
            self.seen_from.push(node);

            // 限制节点列表大小
            if self.seen_from.len() > 10 {
                self.seen_from.remove(0);
            }
        }
    }
}

/// DHT爬虫存储
pub struct DHTCrawlerStorage {
    /// 待爬取的节点队列
    pending_nodes: Mutex<VecDeque<DHTNode>>,
    /// 已爬取的节点集合
    crawled_nodes: RwLock<HashSet<SocketAddr>>,
    /// 已发现的信息哈希集合
    infohashes: RwLock<HashSet<[u8; 20]>>,
    /// 信息哈希元数据
    metadata: RwLock<HashMap<[u8; 20], InfoHashMetadata>>,
    /// 最大信息哈希数
    max_infohashes: usize,
    /// 是否保存元数据
    save_metadata: bool,
    /// 元数据存储路径
    metadata_path: Option<String>,
    /// 上次保存时间
    last_save: Mutex<Instant>,
    /// 保存间隔（秒）
    save_interval: u64,
}

impl DHTCrawlerStorage {
    /// 创建新的DHT爬虫存储
    pub fn new(
        max_infohashes: usize,
        save_metadata: bool,
        metadata_path: Option<String>,
    ) -> Self {
        Self {
            pending_nodes: Mutex::new(VecDeque::new()),
            crawled_nodes: RwLock::new(HashSet::new()),
            infohashes: RwLock::new(HashSet::new()),
            metadata: RwLock::new(HashMap::new()),
            max_infohashes,
            save_metadata,
            metadata_path,
            last_save: Mutex::new(Instant::now()),
            save_interval: 300, // 5分钟
        }
    }

    /// 添加待爬取的节点
    pub async fn add_pending_node(&self, node: DHTNode) {
        let mut pending_nodes = self.pending_nodes.lock().await;

        // 检查节点是否已经在队列中
        if !pending_nodes.iter().any(|n| n.addr() == node.addr()) {
            pending_nodes.push_back(node);
        }
    }

    /// 添加多个待爬取的节点
    pub async fn add_pending_nodes(&self, nodes: Vec<DHTNode>) {
        let mut pending_nodes = self.pending_nodes.lock().await;

        for node in nodes {
            // 检查节点是否已经在队列中
            if !pending_nodes.iter().any(|n| n.addr() == node.addr()) {
                pending_nodes.push_back(node);
            }
        }
    }

    /// 添加多个待爬取的节点（引用版本）
    pub async fn add_pending_nodes_ref(&self, nodes: &[DHTNode]) {
        let mut pending_nodes = self.pending_nodes.lock().await;

        for node in nodes {
            // 检查节点是否已经在队列中
            if !pending_nodes.iter().any(|n| n.addr() == node.addr()) {
                pending_nodes.push_back(node.clone());
            }
        }
    }

    /// 获取下一个待爬取的节点
    pub async fn get_next_node(&self) -> Option<DHTNode> {
        let mut pending_nodes = self.pending_nodes.lock().await;

        // 移除已爬取的节点
        while let Some(node) = pending_nodes.front() {
            let crawled_nodes = self.crawled_nodes.read().await;
            if crawled_nodes.contains(&node.addr()) {
                pending_nodes.pop_front();
            } else {
                break;
            }
        }

        pending_nodes.pop_front()
    }

    /// 标记节点为已爬取
    pub async fn mark_node_crawled(&self, addr: SocketAddr) {
        let mut crawled_nodes = self.crawled_nodes.write().await;
        crawled_nodes.insert(addr);
    }

    /// 添加信息哈希
    pub async fn add_infohash(&self, infohash: [u8; 20], node: SocketAddr) -> bool {
        let mut infohashes = self.infohashes.write().await;

        // 检查是否已达到最大数量
        if infohashes.len() >= self.max_infohashes {
            return false;
        }

        let is_new = infohashes.insert(infohash);

        // 更新元数据
        if self.save_metadata {
            let mut metadata = self.metadata.write().await;

            if let Some(meta) = metadata.get_mut(&infohash) {
                meta.update(node);
            } else {
                metadata.insert(infohash, InfoHashMetadata::new(infohash, node));
            }
        }

        // 检查是否需要保存
        if self.save_metadata {
            let last_save = self.last_save.lock().await;
            if last_save.elapsed().as_secs() >= self.save_interval {
                drop(last_save); // 释放锁
                drop(infohashes); // 释放锁

                if let Err(e) = self.save_metadata_to_file().await {
                    warn!("Failed to save metadata: {}", e);
                }

                *self.last_save.lock().await = Instant::now();
            }
        }

        is_new
    }

    /// 获取信息哈希数量
    pub async fn get_infohash_count(&self) -> usize {
        let infohashes = self.infohashes.read().await;
        infohashes.len()
    }

    /// 获取待爬取节点数量
    pub async fn get_pending_node_count(&self) -> usize {
        let pending_nodes = self.pending_nodes.lock().await;
        pending_nodes.len()
    }

    /// 获取已爬取节点数量
    pub async fn get_crawled_node_count(&self) -> usize {
        let crawled_nodes = self.crawled_nodes.read().await;
        crawled_nodes.len()
    }

    /// 保存元数据到文件
    pub async fn save_metadata_to_file(&self) -> Result<()> {
        if !self.save_metadata {
            return Ok(());
        }

        if let Some(path) = &self.metadata_path {
            let metadata = self.metadata.read().await;

            // 创建目录（如果不存在）
            let path = Path::new(path);
            if let Some(parent) = path.parent() {
                if !parent.exists() {
                    fs::create_dir_all(parent).await?;
                }
            }

            // 序列化元数据
            let serialized = serde_json::to_string(&*metadata)?;

            // 保存到文件
            fs::write(path, serialized).await?;

            info!("Saved {} infohash metadata entries to {}", metadata.len(), path.display());
        }

        Ok(())
    }

    /// 加载元数据从文件
    pub async fn load_metadata_from_file(&self) -> Result<()> {
        if !self.save_metadata {
            return Ok(());
        }

        if let Some(path) = &self.metadata_path {
            let path = Path::new(path);

            if fs::metadata(path).await.is_ok() {
                // 读取文件
                let data = fs::read_to_string(path).await?;

                // 反序列化元数据
                let loaded: HashMap<[u8; 20], InfoHashMetadata> = serde_json::from_str(&data)?;

                // 更新元数据和信息哈希集合
                let mut metadata = self.metadata.write().await;
                let mut infohashes = self.infohashes.write().await;

                *metadata = loaded;

                // 更新信息哈希集合
                for key in metadata.keys() {
                    infohashes.insert(*key);

                    // 检查是否已达到最大数量
                    if infohashes.len() >= self.max_infohashes {
                        break;
                    }
                }

                info!("Loaded {} infohash metadata entries from {}", metadata.len(), path.display());
            }
        }

        Ok(())
    }

    /// 清除所有数据
    pub async fn clear(&self) {
        {
            let mut pending_nodes = self.pending_nodes.lock().await;
            pending_nodes.clear();
        }

        {
            let mut crawled_nodes = self.crawled_nodes.write().await;
            crawled_nodes.clear();
        }

        {
            let mut infohashes = self.infohashes.write().await;
            infohashes.clear();
        }

        {
            let mut metadata = self.metadata.write().await;
            metadata.clear();
        }
    }
}
