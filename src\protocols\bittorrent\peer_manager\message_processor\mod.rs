//! 消息处理器模块
//! 
//! 这个模块负责处理 BitTorrent 协议中的对等点消息，包括握手、位图、请求、片段等消息。
//! 它处理消息的解析、验证和响应等功能。

use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::sync::RwLock;

use tracing::{debug, error, info, trace, warn};

use crate::core::Peer;
use crate::protocols::bittorrent::message::BitTorrentMessage;
use crate::protocols::bittorrent::peer_quality::PeerQualityManager;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

type Result<T> = std::result::Result<T, BitTorrentError>;

/// 消息处理器
/// 
/// 负责处理 BitTorrent 协议中的对等点消息。
pub struct MessageProcessor {
    /// 对等点质量管理器
    quality_manager: Arc<RwLock<PeerQualityManager>>,
    
    /// 种子信息哈希
    info_hash: Option<Vec<u8>>,
}

impl MessageProcessor {
    /// 创建新的消息处理器
    pub fn new(quality_manager: Arc<RwLock<PeerQualityManager>>) -> Self {
        Self {
            quality_manager,
            info_hash: None,
        }
    }
    
    /// 设置种子信息哈希
    pub fn set_info_hash(&mut self, info_hash: Vec<u8>) {
        self.info_hash = Some(info_hash);
    }
    
    /// 处理对等点消息
    pub async fn process_messages(
        &self,
        peers: &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>
    ) -> Result<()> {
        // 处理每个对等点的消息
        for (addr, peer) in peers {
            // 接收消息
            // 先获取 peer 的锁，再调用 receive_message
            let mut peer_guard = peer.lock().await;
            if let Ok(message) = peer_guard.receive_message().await {
                if !message.is_empty() {
                    debug!("从 {} 接收到消息，长度: {}", addr, message.len());
                    
                    // 处理消息
                    if let Err(e) = self.process_message(peer, addr, &message).await {
                        warn!("处理来自 {} 的消息时出错: {}", addr, e);
                        
                        // 记录消息处理失败的违规行为
                        let mut manager = self.quality_manager.write().await;
                        manager.record_violation(
                            addr,
                            "message_processing_failed",
                            "消息处理失败",
                            2, // 轻微严重程度
                        ).await;
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// 处理单个消息
    async fn process_message(
        &self,
        peer: &Arc<tokio::sync::Mutex<dyn Peer>>,
        addr: &SocketAddr,
        message_data: &[u8]
    ) -> Result<()> {
        // 解析消息
        let message_opt = BitTorrentMessage::decode(&message_data)?;
        // 如果是Keep-alive消息或未知消息，直接返回
        if message_opt.is_none() {
            debug!("收到来自 {} 的保持连接消息", addr);
            return Ok(());
        }
        // 根据消息类型处理
        match message_opt.unwrap() {
            // 注意：BitTorrentMessage 枚举中没有 Handshake 变体
            // 握手消息应该在连接建立时由 HandshakeHandler 处理
            // 这里保留其他消息类型的处理
            // Keep-alive 消息已在前面处理（当 decode 返回 None 时）
            BitTorrentMessage::Choke => {
                debug!("对等点 {} 阻塞了我们", addr);
                if let Err(e) = peer.lock().await.set_peer_choking(true).await {
                    warn!("设置对等点阻塞状态失败: {}", e);
                }
                Ok(())
            },
            BitTorrentMessage::Unchoke => {
                debug!("对等点 {} 解除了对我们的阻塞", addr);
                if let Err(e) = peer.lock().await.set_peer_choking(false).await {
                    warn!("设置对等点解除阻塞状态失败: {}", e);
                }
                Ok(())
            },
            BitTorrentMessage::Interested => {
                debug!("对等点 {} 对我们感兴趣", addr);
                if let Err(e) = peer.lock().await.set_peer_interested(true).await {
                    warn!("设置对等点感兴趣状态失败: {}", e);
                }
                Ok(())
            },
            BitTorrentMessage::NotInterested => {
                debug!("对等点 {} 对我们不感兴趣", addr);
                if let Err(e) = peer.lock().await.set_peer_interested(false).await {
                    warn!("设置对等点不感兴趣状态失败: {}", e);
                }
                Ok(())
            },
            BitTorrentMessage::Have(piece_index) => {
                self.handle_have(peer, addr, piece_index).await
            },
            BitTorrentMessage::Bitfield(bitfield) => {
                self.handle_bitfield(peer, addr, bitfield).await
            },
            BitTorrentMessage::Request(piece_index, offset, length) => {
                self.handle_request(peer, addr, piece_index, offset, length).await
            },
            BitTorrentMessage::Piece(piece_index, offset, data) => {
                self.handle_piece(peer, addr, piece_index, offset, data).await
            },
            BitTorrentMessage::Cancel(piece_index, offset, length) => {
                self.handle_cancel(peer, addr, piece_index, offset, length).await
            },
            BitTorrentMessage::Port(port) => {
                debug!("对等点 {} 设置了DHT端口: {}", addr, port);
                Ok(())
            },
            BitTorrentMessage::Extended(id, payload) => {
                self.handle_extension(peer, addr, id, payload).await
            },
            _ => {
                warn!("收到未知消息类型");
                Ok(())
            }
        }
    }
    
    // 注意：握手消息处理已移至 HandshakeHandler 类中
    // 这里不再需要 handle_handshake 方法
    
    /// 处理have消息
    async fn handle_have(
        &self,
        peer: &Arc<tokio::sync::Mutex<dyn Peer>>,
        addr: &SocketAddr,
        piece_index: u32
    ) -> Result<()> {
        debug!("对等点 {} 拥有分片 {}", addr, piece_index);
        // 调用 Peer trait 的 add_piece 方法
        let mut peer = peer.lock().await;
        peer.add_piece(piece_index).await?;
        Ok(())
    }
    
    /// 处理bitfield消息
    async fn handle_bitfield(
        &self,
        peer: &Arc<tokio::sync::Mutex<dyn Peer>>,
        addr: &SocketAddr,
        bitfield: Vec<u8>
    ) -> Result<()> {
        debug!("对等点 {} 发送了bitfield，长度 {}", addr, bitfield.len());
        // 调用 Peer trait 的 set_bitfield 方法
        let mut peer = peer.lock().await;
        peer.set_bitfield(bitfield).await?;
        Ok(())
    }
    
    /// 处理request消息
    async fn handle_request(
        &self,
        peer: &Arc<tokio::sync::Mutex<dyn Peer>>,
        addr: &SocketAddr,
        piece_index: u32,
        offset: u32,
        length: u32
    ) -> Result<()> {
        debug!("对等点 {} 请求分片 {} 偏移 {} 长度 {}", addr, piece_index, offset, length);
        // 调用 Peer trait 的 handle_request 方法
        let mut peer = peer.lock().await;
        // peer.handle_request(piece_index, offset, length)?; // 已移除无效调用
        Ok(())
    }

    /// 处理piece消息
    async fn handle_piece(
        &self,
        peer: &Arc<tokio::sync::Mutex<dyn Peer>>,
        addr: &SocketAddr,
        piece_index: u32,
        offset: u32,
        data: Vec<u8>
    ) -> Result<()> {
        debug!("对等点 {} 发送了分片 {} 偏移 {} 数据长度 {}", addr, piece_index, offset, data.len());
        // Peer trait 没有 handle_piece，调用 add_piece 作为最优方案
        let mut peer = peer.lock().await;
        peer.add_piece(piece_index).await?;
        Ok(())
    }

    /// 处理cancel消息
    async fn handle_cancel(
        &self,
        peer: &Arc<tokio::sync::Mutex<dyn Peer>>,
        addr: &SocketAddr,
        piece_index: u32,
        offset: u32,
        length: u32
    ) -> Result<()> {
        debug!("对等点 {} 取消请求分片 {} 偏移 {} 长度 {}", addr, piece_index, offset, length);
        // Peer trait 未定义 handle_cancel，如需支持请在 Peer trait 中添加
        // let mut peer = peer.lock().await;
        // peer.handle_cancel(piece_index, offset, length).await?;
        Ok(())
    }
    
    /// 处理扩展消息
    async fn handle_extension(
        &self,
        peer: &Arc<tokio::sync::Mutex<dyn Peer>>,
        addr: &SocketAddr,
        id: u8,
        payload: Vec<u8>
    ) -> Result<()> {
        debug!("收到来自 {} 的扩展消息，ID: {}, 长度: {}", addr, id, payload.len());
        // 这里只记录扩展消息，实际处理应由 ExtensionManager 实现
        Ok(())
    }
}