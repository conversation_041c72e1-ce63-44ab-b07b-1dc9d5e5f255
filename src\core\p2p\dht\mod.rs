use anyhow::Result;
use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Duration;

/// DHT配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DHTConfig {
    /// DHT监听端口
    pub port: u16,
    /// 引导节点列表
    pub bootstrap_nodes: Vec<String>,
    /// 路由表大小
    pub routing_table_size: usize,
    /// 节点超时时间（秒）
    pub node_timeout: u64,
    /// 查询超时时间（秒）
    pub query_timeout: u64,
    /// 是否启用DHT
    pub enabled: bool,
    /// 是否启用IPv6
    #[serde(default = "default_enable_ipv6")]
    pub enable_ipv6: bool,
}

/// 默认启用IPv6
fn default_enable_ipv6() -> bool {
    true
}

impl Default for DHTConfig {
    fn default() -> Self {
        Self {
            port: 6881,
            bootstrap_nodes: vec![
                "router.bittorrent.com:6881".to_string(),
                "dht.transmissionbt.com:6881".to_string(),
                "router.utorrent.com:6881".to_string(),
            ],
            routing_table_size: 8,
            node_timeout: 900,
            query_timeout: 30,
            enabled: true,
            enable_ipv6: true,
        }
    }
}

/// DHT事件类型
#[derive(Debug, Clone)]
pub enum DHTEvent {
    /// 发现新的对等点
    PeersFound {
        /// 信息哈希
        info_hash: [u8; 20],
        /// 对等点列表
        peers: Vec<SocketAddr>,
    },
    /// 节点状态更新
    NodeStateChanged {
        /// 节点数量
        node_count: usize,
        /// 是否已引导
        bootstrapped: bool,
    },
    /// 错误事件
    Error {
        /// 错误消息
        message: String,
    },
}

/// DHT事件监听器
#[async_trait]
pub trait DHTEventListener: Send + Sync {
    /// 处理DHT事件
    async fn on_event(&self, event: DHTEvent) -> Result<()>;
}

/// DHT接口
#[async_trait]
pub trait DHT: Send + Sync {
    /// 初始化DHT
    async fn init(&mut self) -> Result<()>;

    /// 启动DHT服务
    async fn start(&mut self) -> Result<()>;

    /// 停止DHT服务
    async fn stop(&mut self) -> Result<()>;

    /// 查找拥有特定info_hash的对等点
    async fn get_peers(&self, info_hash: [u8; 20]) -> Result<Vec<SocketAddr>>;

    /// 宣布自己拥有特定info_hash
    async fn announce_peer(&self, info_hash: [u8; 20], port: u16) -> Result<()>;

    /// 添加事件监听器
    async fn add_event_listener(&mut self, listener: Arc<dyn DHTEventListener>) -> Result<()>;

    /// 移除事件监听器
    async fn remove_event_listener(&mut self, listener_id: usize) -> Result<()>;

    /// 获取DHT状态
    async fn get_status(&self) -> Result<DHTStatus>;
}

/// DHT状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DHTStatus {
    /// 是否已初始化
    pub initialized: bool,
    /// 是否正在运行
    pub running: bool,
    /// 节点ID
    pub node_id: Option<String>,
    /// 监听端口
    pub port: u16,
    /// 路由表中的节点数量
    pub node_count: usize,
    /// 活跃查询数量
    pub active_queries: usize,
    /// 已发现的对等点数量
    pub discovered_peers: usize,
    /// 运行时间
    pub uptime: Duration,
    /// 是否支持IPv6
    pub ipv6_enabled: bool,
    /// IPv6节点数量
    pub ipv6_node_count: usize,
}
