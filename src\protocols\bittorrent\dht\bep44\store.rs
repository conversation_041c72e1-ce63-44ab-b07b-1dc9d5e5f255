use std::collections::HashMap;
use std::path::Path;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use async_trait::async_trait;
use tokio::fs;
use tokio::sync::RwLock;
use tracing::debug;
use serde::{Serialize, Deserialize};

use super::value::DHTValue;

/// 存储配置
#[derive(Debug, Clone)]
pub struct DHTStoreConfig {
    /// 最大存储项数
    pub max_items: usize,
    /// 项目生存时间（秒）
    pub item_ttl: u64,
    /// 清理间隔（秒）
    pub cleanup_interval: u64,
    /// 是否持久化
    pub persistent: bool,
    /// 持久化文件路径
    pub storage_path: Option<String>,
    /// 最大值大小（字节）
    pub max_value_size: usize,
}

impl Default for DHTStoreConfig {
    fn default() -> Self {
        Self {
            max_items: 1000,
            item_ttl: 7200, // 2小时
            cleanup_interval: 300, // 5分钟
            persistent: false,
            storage_path: None,
            max_value_size: 1000, // 1KB
        }
    }
}

/// 存储项
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
struct StoreItem {
    /// 值
    value: DHTValue,
    /// 创建时间
    created_at: u64,
    /// 最后访问时间
    last_accessed: u64,
    /// 访问次数
    access_count: u64,
}

impl StoreItem {
    /// 创建新的存储项
    fn new(value: DHTValue) -> Self {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_secs();

        Self {
            value,
            created_at: now,
            last_accessed: now,
            access_count: 0,
        }
    }

    /// 更新访问信息
    fn update_access(&mut self) {
        self.last_accessed = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_secs();
        self.access_count += 1;
    }

    /// 检查是否过期
    fn is_expired(&self, ttl: u64) -> bool {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_secs();

        now - self.created_at > ttl
    }
}

/// DHT存储接口
#[async_trait]
pub trait DHTStore: Send + Sync {
    /// 存储值
    async fn put(&self, key: [u8; 20], value: DHTValue) -> Result<()>;

    /// 获取值
    async fn get(&self, key: &[u8; 20]) -> Result<Option<DHTValue>>;

    /// 删除值
    async fn delete(&self, key: &[u8; 20]) -> Result<()>;

    /// 获取所有键
    async fn get_all_keys(&self) -> Result<Vec<[u8; 20]>>;

    /// 清理过期项
    async fn cleanup(&self) -> Result<usize>;

    /// 持久化存储
    async fn persist(&self) -> Result<()>;

    /// 加载存储
    async fn load(&self) -> Result<()>;
}

/// 内存DHT存储
pub struct MemoryDHTStore {
    /// 存储映射
    storage: RwLock<HashMap<[u8; 20], StoreItem>>,
    /// 配置
    config: DHTStoreConfig,
    /// 上次清理时间
    last_cleanup: RwLock<Instant>,
}

impl MemoryDHTStore {
    /// 创建新的内存DHT存储
    pub fn new(config: DHTStoreConfig) -> Self {
        Self {
            storage: RwLock::new(HashMap::new()),
            config,
            last_cleanup: RwLock::new(Instant::now()),
        }
    }

    /// 检查是否需要清理
    async fn check_cleanup(&self) -> Result<()> {
        let now = Instant::now();
        let last_cleanup = self.last_cleanup.write().await;

        if now.duration_since(*last_cleanup).as_secs() >= self.config.cleanup_interval {
            drop(last_cleanup); // 释放锁
            let cleaned = self.cleanup().await?;
            debug!("Cleaned up {} expired items from DHT store", cleaned);

            *self.last_cleanup.write().await = now;
        }

        Ok(())
    }
}

#[async_trait]
impl DHTStore for MemoryDHTStore {
    async fn put(&self, key: [u8; 20], value: DHTValue) -> Result<()> {
        // 检查值大小
        if value.size() > self.config.max_value_size {
            return Err(anyhow!("Value too large: {} bytes (max: {} bytes)",
                value.size(), self.config.max_value_size));
        }

        // 验证可变值
        if let DHTValue::Mutable(ref mutable) = value {
            if !mutable.verify()? {
                return Err(anyhow!("Invalid signature for mutable value"));
            }
        }

        // 检查是否需要清理
        self.check_cleanup().await?;

        let mut storage = self.storage.write().await;

        // 检查存储大小
        if storage.len() >= self.config.max_items && !storage.contains_key(&key) {
            // 移除最旧的项
            let oldest_key = storage.iter()
                .min_by_key(|(_, item)| item.last_accessed)
                .map(|(k, _)| *k);

            if let Some(oldest_key) = oldest_key {
                storage.remove(&oldest_key);
            }
        }

        // 检查是否已存在可变值
        if let DHTValue::Mutable(ref mutable) = value {
            if let Some(existing) = storage.get(&key) {
                if let DHTValue::Mutable(ref existing_mutable) = existing.value {
                    // 只有序列号更高的值才能替换现有值
                    if mutable.sequence <= existing_mutable.sequence {
                        return Err(anyhow!("Sequence number too low: {} (current: {})",
                            mutable.sequence, existing_mutable.sequence));
                    }
                }
            }
        }

        // 存储值
        storage.insert(key, StoreItem::new(value));

        // 如果配置为持久化，则保存存储
        if self.config.persistent {
            drop(storage); // 释放锁
            self.persist().await?;
        }

        Ok(())
    }

    async fn get(&self, key: &[u8; 20]) -> Result<Option<DHTValue>> {
        // 检查是否需要清理
        self.check_cleanup().await?;

        let mut storage = self.storage.write().await;

        if let Some(item) = storage.get_mut(key) {
            // 检查是否过期
            if item.is_expired(self.config.item_ttl) {
                storage.remove(key);
                return Ok(None);
            }

            // 更新访问信息
            item.update_access();

            return Ok(Some(item.value.clone()));
        }

        Ok(None)
    }

    async fn delete(&self, key: &[u8; 20]) -> Result<()> {
        let mut storage = self.storage.write().await;
        storage.remove(key);

        // 如果配置为持久化，则保存存储
        if self.config.persistent {
            drop(storage); // 释放锁
            self.persist().await?;
        }

        Ok(())
    }

    async fn get_all_keys(&self) -> Result<Vec<[u8; 20]>> {
        let storage = self.storage.read().await;
        let keys = storage.keys().cloned().collect();
        Ok(keys)
    }

    async fn cleanup(&self) -> Result<usize> {
        let mut storage = self.storage.write().await;
        let before_count = storage.len();

        // 移除过期项
        storage.retain(|_, item| {
            !item.is_expired(self.config.item_ttl)
        });

        let cleaned = before_count - storage.len();

        // 如果配置为持久化，则保存存储
        if self.config.persistent && cleaned > 0 {
            drop(storage); // 释放锁
            self.persist().await?;
        }

        Ok(cleaned)
    }

    async fn persist(&self) -> Result<()> {
        if let Some(path) = &self.config.storage_path {
            let storage = self.storage.read().await;

            // 序列化存储
            let serialized = serde_json::to_string(&*storage)?;

            // 保存到文件
            fs::write(path, serialized).await?;

            debug!("Persisted DHT store to {}", path);
        }

        Ok(())
    }

    async fn load(&self) -> Result<()> {
        if let Some(path) = &self.config.storage_path {
            let path = Path::new(path);

            if fs::metadata(path).await.is_ok() {
                // 读取文件
                let data = fs::read_to_string(path).await?;

                // 反序列化存储
                let loaded: HashMap<[u8; 20], StoreItem> = serde_json::from_str(&data)?;

                // 更新存储
                let mut storage = self.storage.write().await;
                *storage = loaded;

                debug!("Loaded DHT store from {} with {} items", path.display(), storage.len());
            }
        }

        Ok(())
    }
}
