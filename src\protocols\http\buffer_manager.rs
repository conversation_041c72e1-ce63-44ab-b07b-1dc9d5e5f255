//! HTTP下载器缓冲区管理模块

use std::time::Instant;
use anyhow::Result;
use tokio::io::AsyncWriteExt;
use tracing::debug;

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// 刷新缓冲区到文件
    pub(crate) async fn flush_buffer(&mut self, file: &mut tokio::fs::File) -> Result<()> {
        if !self.buffer.is_empty() {
            debug!("Flushing {} bytes from buffer to file", self.buffer.len());
            file.write_all(&self.buffer).await?;
            file.flush().await?;
            self.buffer.clear();
            self.last_flush_time = Some(Instant::now());
        }
        Ok(())
    }
    
    /// 写入数据到缓冲区，如果缓冲区满或达到刷新间隔则刷新到文件
    pub(crate) async fn write_to_buffer(&mut self, data: &[u8], file: &mut tokio::fs::File) -> Result<()> {
        // 如果数据加上当前缓冲区大小超过了缓冲区容量，先刷新缓冲区
        if self.buffer.len() + data.len() > self.buffer_size {
            self.flush_buffer(file).await?
        }
        
        // 如果单个数据块大于缓冲区容量，直接写入文件
        if data.len() > self.buffer_size {
            debug!("Data chunk size ({}) exceeds buffer size, writing directly to file", data.len());
            file.write_all(data).await?;
            file.flush().await?;
            self.last_flush_time = Some(Instant::now());
        } else {
            // 否则添加到缓冲区
            self.buffer.extend_from_slice(data);
            
            // 检查是否需要定期刷新
            let should_flush = if let Some(last_flush) = self.last_flush_time {
                Instant::now().duration_since(last_flush) >= self.flush_interval
            } else {
                true
            };
            
            if should_flush {
                self.flush_buffer(file).await?
            }
        }
        
        Ok(())
    }
}
