use anyhow::{Result, anyhow};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{debug, info, warn};
use url::Url;

use crate::config::Settings;
use crate::utils::HttpClient;

/// Mirror information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MirrorInfo {
    pub url: String,
    pub latency: Option<Duration>,
    #[serde(skip, default = "Instant::now")]
    pub last_checked: Instant,
    #[serde(skip, default = "SystemTime::now")]
    pub last_checked_timestamp: SystemTime,
    pub is_available: bool,
}

impl Default for MirrorInfo {
    fn default() -> Self {
        Self {
            url: String::new(),
            latency: None,
            last_checked: Instant::now(),
            last_checked_timestamp: SystemTime::now(),
            is_available: false,
        }
    }
}

/// Mirror manager interface
#[async_trait]
pub trait MirrorManager: Send + Sync {
    /// Add a mirror
    async fn add_mirror(&self, url: String) -> Result<()>;

    /// Remove a mirror
    async fn remove_mirror(&self, url: &str) -> Result<()>;

    /// Get all mirrors
    async fn get_all_mirrors(&self) -> Result<Vec<MirrorInfo>>;

    /// Get the best mirror for a URL
    async fn get_best_mirror(&self, url: &str) -> Result<Option<String>>;

    /// Check the health of all mirrors
    async fn check_mirrors(&self) -> Result<()>;

    /// Start the mirror health check loop
    async fn start_health_check_loop(&self) -> Result<()>;
}

/// Mirror manager implementation
pub struct MirrorManagerImpl {
    settings: Settings,
    mirrors: Arc<RwLock<HashMap<String, MirrorInfo>>>,
    client: HttpClient,
}

impl MirrorManagerImpl {
    /// Create a new mirror manager
    pub fn new(settings: Settings) -> Result<Self> {
        let client = HttpClient::new()?;

        Ok(Self {
            settings,
            mirrors: Arc::new(RwLock::new(HashMap::new())),
            client,
        })
    }

    /// Check the health of a mirror
    /// 
    /// 此方法会检查镜像的健康状态，包括可访问性、响应时间和状态码
    /// 
    /// # 参数
    /// * `url` - 要检查的镜像 URL
    /// 
    /// # 返回值
    /// * `Result<MirrorInfo>` - 镜像信息，包括可访问性、延迟等
    async fn check_mirror(&self, url: &str) -> Result<MirrorInfo> {
        // 使用 check_url_accessibility 获取详细的可访问性信息
        let accessibility_result = self.client.check_url_accessibility(url).await;
        
        // 从结果中提取信息
        let is_available = accessibility_result.is_accessible;
        let latency = accessibility_result.response_time;
        
        // 如果有错误，记录日志
        if let Some(error) = &accessibility_result.error {
            warn!("镜像访问失败: {}, 错误: {}", url, error);
        }
        
        // 如果有状态码但不是成功状态，记录日志
        if let Some(status_code) = accessibility_result.status_code {
            if !is_available {
                warn!("镜像不可用: {}, 状态码: {}", url, status_code);
            } else {
                debug!("镜像可用: {}, 状态码: {}", url, status_code);
            }
        }

        let now = Instant::now();

        Ok(MirrorInfo {
            url: url.to_string(),
            latency,
            last_checked: now,
            last_checked_timestamp: SystemTime::now(),
            is_available,
        })
    }

    /// Generate a mirror URL for a given URL
    fn generate_mirror_url(&self, base_mirror: &str, original_url: &str) -> Result<String> {
        let original = Url::parse(original_url)?;
        let mirror = Url::parse(base_mirror)?;

        // Extract the path and query from the original URL
        let path = original.path();
        let query = original.query();

        // Combine with the mirror URL
        let mut mirror_url = mirror.to_string();
        if !mirror_url.ends_with('/') && !path.starts_with('/') {
            mirror_url.push('/');
        }
        mirror_url.push_str(path);

        if let Some(q) = query {
            mirror_url.push('?');
            mirror_url.push_str(q);
        }

        Ok(mirror_url)
    }
}

#[async_trait]
impl MirrorManager for MirrorManagerImpl {
    async fn add_mirror(&self, url: String) -> Result<()> {
        // Check if the mirror is valid
        let mirror_info = self.check_mirror(&url).await?;

        if !mirror_info.is_available {
            return Err(anyhow!("Mirror is not available: {}", url));
        }

        // Add the mirror to the list
        self.mirrors.write().await.insert(url.clone(), mirror_info);

        info!("Added mirror: {}", url);

        Ok(())
    }

    async fn remove_mirror(&self, url: &str) -> Result<()> {
        // Remove the mirror from the list
        let removed = self.mirrors.write().await.remove(url).is_some();

        if removed {
            info!("Removed mirror: {}", url);
            Ok(())
        } else {
            Err(anyhow!("Mirror not found: {}", url))
        }
    }

    async fn get_all_mirrors(&self) -> Result<Vec<MirrorInfo>> {
        let mirrors = self.mirrors.read().await;

        Ok(mirrors.values().cloned().collect())
    }

    async fn get_best_mirror(&self, url: &str) -> Result<Option<String>> {
        let mirrors = self.mirrors.read().await;

        if mirrors.is_empty() {
            return Ok(None);
        }

        // Find the mirror with the lowest latency
        let best_mirror = mirrors.values()
            .filter(|m| m.is_available)
            .min_by_key(|m| m.latency.unwrap_or(Duration::from_secs(u64::MAX)));

        match best_mirror {
            Some(mirror) => {
                let mirror_url = self.generate_mirror_url(&mirror.url, url)?;
                Ok(Some(mirror_url))
            },
            None => Ok(None),
        }
    }

    async fn check_mirrors(&self) -> Result<()> {
        let mut mirrors = self.mirrors.write().await;

        for (url, mirror) in mirrors.iter_mut() {
            match self.check_mirror(url).await {
                Ok(info) => {
                    mirror.latency = info.latency;
                    mirror.last_checked = info.last_checked;
                    mirror.is_available = info.is_available;

                    debug!("Mirror check: url={}, available={}, latency={:?}",
                           url, info.is_available, info.latency);
                },
                Err(e) => {
                    mirror.latency = None;
                    mirror.last_checked = Instant::now();
                    mirror.is_available = false;

                    warn!("Failed to check mirror: url={}, error={}", url, e);
                },
            }
        }

        Ok(())
    }

    async fn start_health_check_loop(&self) -> Result<()> {
        let mirrors = self.mirrors.clone();
        let check_interval = Duration::from_secs(self.settings.mirror.health_check_interval_secs);

        tokio::spawn(async move {
            let mut interval = interval(check_interval);

            loop {
                interval.tick().await;

                let mirror_urls: Vec<String> = {
                    let mirrors = mirrors.read().await;
                    mirrors.keys().cloned().collect()
                };

                for url in mirror_urls {
                    let mut mirrors = mirrors.write().await;
                    if let Some(mirror) = mirrors.get_mut(&url) {
                        let start = Instant::now();
                        let client = HttpClient::new().expect("Failed to create HTTP client");
                        let is_available = client.is_accessible(&url).await;
                        let latency = if is_available {
                            Some(start.elapsed())
                        } else {
                            None
                        };

                        let now = Instant::now();
                        mirror.latency = latency;
                        mirror.last_checked = now;
                        mirror.last_checked_timestamp = SystemTime::now();
                        mirror.is_available = is_available;

                        debug!("Mirror health check: url={}, available={}, latency={:?}",
                               url, is_available, latency);
                    }
                }
            }
        });

        info!("Started mirror health check loop");

        Ok(())
    }
}
