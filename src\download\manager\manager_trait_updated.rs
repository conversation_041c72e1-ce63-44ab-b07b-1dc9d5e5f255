use anyhow::Result;
use async_trait::async_trait;
use uuid::Uuid;

use crate::download::Downloader;
use super::models::{TaskInfo, DownloadStats};

/// Download manager interface
#[async_trait]
pub trait DownloadManager: Send + Sync {
    /// Add a new download task
    async fn add_task(&self, task: TaskInfo) -> Result<Uuid>;
    
    /// Get task information by ID
    async fn get_task(&self, task_id: Uuid) -> Result<TaskInfo>;
    
    /// Update task information
    async fn update_task(&self, task_id: Uuid, task: TaskInfo) -> Result<()>;
    
    /// Start a download task
    async fn start_task(&self, task_id: Uuid) -> Result<()>;
    
    /// Pause a download task
    async fn pause_task(&self, task_id: Uuid) -> Result<()>;
    
    /// Resume a paused download task
    async fn resume_task(&self, task_id: Uuid) -> Result<()>;
    
    /// Cancel a download task
    async fn cancel_task(&self, task_id: Uuid) -> Result<()>;
    
    /// Get all download tasks
    async fn get_all_tasks(&self) -> Result<Vec<TaskInfo>>;
    
    /// Remove a download task
    async fn remove_task(&self, task_id: Uuid) -> Result<()>;
    
    /// Get download statistics
    async fn get_download_stats(&self) -> DownloadStats;
    
    /// Create a downloader for the given URL
    async fn create_downloader(&self, url: &str, output_path: &str, task_id: Uuid) -> Result<Box<dyn Downloader>>;
}