use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Duration;
use anyhow::{Result, anyhow};
use tokio::net::UdpSocket;
use tokio::sync::{RwLock, Mutex};
use tracing::{debug, warn};

use super::node::NodeId;
use super::routing::RoutingTable;
use super::message::{DHTMessage, DHTMessageType, MessageCodec, TokenManager};
use super::query::{DHTQuery, QueryManager};
use super::event::{DHTEvent, DHTEventDispatcher};

/// DHT对等点查找器
pub struct DHTPeerFinder {
    /// 节点ID
    node_id: NodeId,
    /// UDP套接字
    socket: Arc<UdpSocket>,
    /// 路由表
    routing_table: Arc<RwLock<RoutingTable>>,
    /// 查询管理器
    query_manager: Arc<RwLock<QueryManager>>,
    /// 令牌管理器
    token_manager: Arc<Mutex<TokenManager>>,
    /// 事件分发器
    event_dispatcher: Arc<DHTEventDispatcher>,
    /// 查询超时时间（秒）
    query_timeout: u64,
    /// 并行查询数
    parallel_queries: usize,
    /// 是否正在运行
    running: bool,
}

impl DHTPeerFinder {
    /// 创建新的DHT对等点查找器
    pub fn new(
        node_id: NodeId,
        socket: Arc<UdpSocket>,
        routing_table: Arc<RwLock<RoutingTable>>,
        query_manager: Arc<RwLock<QueryManager>>,
        token_manager: Arc<Mutex<TokenManager>>,
        event_dispatcher: Arc<DHTEventDispatcher>,
        query_timeout: u64,
        parallel_queries: usize,
    ) -> Self {
        Self {
            node_id,
            socket,
            routing_table,
            query_manager,
            token_manager,
            event_dispatcher,
            query_timeout,
            parallel_queries,
            running: false,
        }
    }

    /// 设置运行状态
    pub fn set_running(&mut self, running: bool) {
        self.running = running;
    }

    /// 获取对等点
    pub async fn get_peers(&self, info_hash: [u8; 20]) -> Result<Vec<SocketAddr>> {
        if !self.running {
            return Err(anyhow!("DHT peer finder is not running"));
        }

        debug!("Getting peers for info hash: {:?}", info_hash);

        // 创建目标ID
        let target_id = NodeId::from_bytes(info_hash);

        // 尝试获取对等点，最多重试3次
        let mut peers = Vec::new();
        let mut retry_count = 0;
        let max_retries = 3;

        while peers.is_empty() && retry_count < max_retries {
            if retry_count > 0 {
                debug!("Retrying get_peers for info hash: {:?} (attempt {}/{})", info_hash, retry_count + 1, max_retries);
            }

            // 生成随机事务ID
            let transaction_id: Vec<u8> = (0..4).map(|_| rand::random::<u8>()).collect();

            // 创建获取对等点请求
            let message = DHTMessage {
                message_type: DHTMessageType::GetPeers,
                transaction_id: transaction_id.clone(),
                sender_id: self.node_id,
                target_id: Some(target_id),
                nodes: None,
                peers: None,
                token: None,
                port: None,
                error_code: None,
                error_message: None,
            };

            // 创建查询
            let query = DHTQuery::new(
                transaction_id.clone(),
                target_id,
                DHTMessageType::GetPeers,
            );

            // 添加到活跃查询
            {
                let mut query_manager = self.query_manager.write().await;
                query_manager.add_query(query)?;
            }

            // 获取路由表中最接近目标ID的节点
            let closest_nodes = {
                let table = self.routing_table.read().await;
                table.get_closest_nodes(&target_id, self.parallel_queries)
            };

            if closest_nodes.is_empty() {
                warn!("No nodes available to query for peers");
                retry_count += 1;

                // 如果是最后一次重试，直接返回空结果
                if retry_count >= max_retries {
                    return Ok(Vec::new());
                }

                // 等待一段时间再重试
                tokio::time::sleep(Duration::from_secs(1)).await;
                continue;
            }

            // 向最接近的节点发送请求
            let encoded = MessageCodec::encode(&message)?;
            let mut sent_count = 0;

            for node in &closest_nodes {
                let addr = node.addr();
                if let Err(e) = self.socket.send_to(&encoded, addr).await {
                    warn!("Failed to send get_peers message to {}: {}", addr, e);
                } else {
                    debug!("Sent get_peers message to {}", addr);
                    sent_count += 1;

                    // 更新查询状态
                    let mut query_manager = self.query_manager.write().await;
                    if let Some(query) = query_manager.get_query_mut(&transaction_id) {
                        query.add_queried_node(node.id);
                    }
                }
            }

            if sent_count == 0 {
                warn!("Failed to send get_peers message to any node");
                retry_count += 1;

                // 如果是最后一次重试，直接返回空结果
                if retry_count >= max_retries {
                    return Ok(Vec::new());
                }

                // 等待一段时间再重试
                tokio::time::sleep(Duration::from_secs(1)).await;
                continue;
            }

            // 等待一段时间，收集对等点
            tokio::time::sleep(Duration::from_secs(self.query_timeout)).await;

            // 获取查询结果
            peers = {
                let mut query_manager = self.query_manager.write().await;
                if let Some(query) = query_manager.remove_query(&transaction_id) {
                    query.found_peers
                } else {
                    Vec::new()
                }
            };

            // 如果没有找到对等点，增加重试计数
            if peers.is_empty() {
                retry_count += 1;

                // 如果不是最后一次重试，等待一段时间再重试
                if retry_count < max_retries {
                    tokio::time::sleep(Duration::from_secs(2)).await;
                }
            }
        }

        debug!("Found {} peers for info hash: {:?} after {} attempts", peers.len(), info_hash, retry_count + 1);

        // 发送事件
        if !peers.is_empty() {
            self.event_dispatcher.dispatch(DHTEvent::PeersFound {
                info_hash,
                peers: peers.clone(),
            }).await?;
        }

        Ok(peers)
    }

    /// 宣布对等点
    pub async fn announce_peer(&self, info_hash: [u8; 20], port: u16) -> Result<()> {
        if !self.running {
            return Err(anyhow!("DHT peer finder is not running"));
        }

        debug!("Announcing peer for info hash: {:?}, port: {}", info_hash, port);

        // 首先获取对等点，这样我们可以获取令牌
        let target_id = NodeId::from_bytes(info_hash);

        // 生成随机事务ID
        let get_peers_transaction_id: Vec<u8> = (0..4).map(|_| rand::random::<u8>()).collect();

        // 创建获取对等点请求
        let get_peers_message = DHTMessage {
            message_type: DHTMessageType::GetPeers,
            transaction_id: get_peers_transaction_id.clone(),
            sender_id: self.node_id,
            target_id: Some(target_id),
            nodes: None,
            peers: None,
            token: None,
            port: None,
            error_code: None,
            error_message: None,
        };

        // 创建查询
        let query = DHTQuery::new(
            get_peers_transaction_id.clone(),
            target_id,
            DHTMessageType::GetPeers,
        );

        // 添加到活跃查询
        {
            let mut query_manager = self.query_manager.write().await;
            query_manager.add_query(query)?;
        }

        // 获取路由表中最接近目标ID的节点
        let closest_nodes = {
            let table = self.routing_table.read().await;
            table.get_closest_nodes(&target_id, self.parallel_queries)
        };

        if closest_nodes.is_empty() {
            warn!("No nodes available to announce peer");
            return Ok(());
        }

        // 向最接近的节点发送获取对等点请求
        let encoded = MessageCodec::encode(&get_peers_message)?;
        for node in &closest_nodes {
            let addr = node.addr();
            if let Err(e) = self.socket.send_to(&encoded, addr).await {
                warn!("Failed to send get_peers message to {}: {}", addr, e);
            } else {
                debug!("Sent get_peers message to {}", addr);

                // 更新查询状态
                let mut query_manager = self.query_manager.write().await;
                if let Some(query) = query_manager.get_query_mut(&get_peers_transaction_id) {
                    query.add_queried_node(node.id);
                }
            }
        }

        // 等待一段时间，获取令牌
        tokio::time::sleep(Duration::from_secs(5)).await;

        // 获取查询结果和响应节点
        let responded_nodes = {
            let query_manager = self.query_manager.read().await;
            if let Some(query) = query_manager.get_query(&get_peers_transaction_id) {
                query.responded_nodes.clone()
            } else {
                return Ok(());
            }
        };

        // 移除查询
        {
            let mut query_manager = self.query_manager.write().await;
            query_manager.remove_query(&get_peers_transaction_id);
        }

        // 向响应的节点发送宣布对等点请求
        let responded_nodes_count = responded_nodes.len();
        for node_id in &responded_nodes {
            // 为每个节点生成令牌
            let token = {
                let token_manager = self.token_manager.lock().await;
                // 查找节点地址
                let table = self.routing_table.read().await;
                let mut node_addr = None;
                for bucket in &table.buckets {
                    for node in &bucket.nodes {
                        if node.id == *node_id {
                            node_addr = Some(node.addr());
                            break;
                        }
                    }
                    if node_addr.is_some() {
                        break;
                    }
                }

                if let Some(addr) = node_addr {
                    token_manager.generate_token(&addr)
                } else {
                    continue;
                }
            };

            // 生成新的事务ID
            let announce_transaction_id: Vec<u8> = (0..4).map(|_| rand::random::<u8>()).collect();

            // 创建宣布对等点请求
            let announce_message = DHTMessage {
                message_type: DHTMessageType::AnnouncePeer,
                transaction_id: announce_transaction_id,
                sender_id: self.node_id,
                target_id: Some(target_id),
                nodes: None,
                peers: None,
                token: Some(token),
                port: Some(port),
                error_code: None,
                error_message: None,
            };

            // 查找节点地址
            let node_addr = {
                let table = self.routing_table.read().await;
                let mut addr = None;
                for bucket in &table.buckets {
                    for node in &bucket.nodes {
                        if node.id == *node_id {
                            addr = Some(node.addr());
                            break;
                        }
                    }
                    if addr.is_some() {
                        break;
                    }
                }
                addr
            };

            if let Some(addr) = node_addr {
                // 发送请求
                let encoded = MessageCodec::encode(&announce_message)?;
                if let Err(e) = self.socket.send_to(&encoded, addr).await {
                    warn!("Failed to send announce_peer message to {}: {}", addr, e);
                } else {
                    debug!("Sent announce_peer message to {}", addr);
                }
            }
        }

        debug!("Announced peer for info hash: {:?} to {} nodes", info_hash, responded_nodes_count);

        Ok(())
    }
}
