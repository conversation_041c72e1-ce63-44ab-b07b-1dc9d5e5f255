use std::time::{Duration, Instant};

/// WebSeed源性能指标
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct WebSeedPerformance {
    /// 平均下载速度（字节/秒）
    pub avg_speed: f64,
    /// 成功率（0.0-1.0）
    pub success_rate: f64,
    /// 平均响应时间（毫秒）
    pub avg_response_time: f64,
    /// 总下载字节数
    pub total_bytes: u64,
    /// 总下载时间（毫秒）
    pub total_time: u64,
    /// 成功请求数
    pub success_count: u32,
    /// 失败请求数
    pub failure_count: u32,
    /// 上次更新时间
    pub last_updated: Instant,
    /// 是否可用
    pub available: bool,
    /// 上次可用性检查时间
    pub last_availability_check: Option<Instant>,
}

impl Default for WebSeedPerformance {
    fn default() -> Self {
        Self {
            avg_speed: 0.0,
            success_rate: 1.0, // 初始假设为100%成功率
            avg_response_time: 0.0,
            total_bytes: 0,
            total_time: 0,
            success_count: 0,
            failure_count: 0,
            last_updated: Instant::now(),
            available: true, // 初始假设为可用
            last_availability_check: None,
        }
    }
}

impl WebSeedPerformance {
    /// 创建新的WebSeed性能指标
    pub fn new() -> Self {
        Self::default()
    }

    /// 更新性能指标
    pub fn update(&mut self, download_time: Duration, bytes_downloaded: usize, success: bool) {
        // 更新总计数据
        self.total_bytes += bytes_downloaded as u64;
        self.total_time += download_time.as_millis() as u64;
        
        if success {
            self.success_count += 1;
        } else {
            self.failure_count += 1;
        }
        
        // 计算成功率
        let total_requests = self.success_count + self.failure_count;
        if total_requests > 0 {
            self.success_rate = self.success_count as f64 / total_requests as f64;
        }
        
        // 计算平均速度
        if self.total_time > 0 {
            self.avg_speed = (self.total_bytes as f64 / self.total_time as f64) * 1000.0;
        }
        
        // 计算平均响应时间
        if total_requests > 0 {
            self.avg_response_time = self.total_time as f64 / total_requests as f64;
        }
        
        self.last_updated = Instant::now();
    }

    /// 更新可用性
    pub fn update_availability(&mut self, available: bool) {
        self.available = available;
        self.last_availability_check = Some(Instant::now());
    }

    /// 计算优先级分数（0-100）
    /// 60% 基于速度，30% 基于成功率，10% 基于响应时间
    pub fn calculate_priority(&self) -> f64 {
        if !self.available {
            return 0.0;
        }
        
        // 如果没有足够的数据，返回默认优先级
        if self.success_count + self.failure_count < 5 {
            return 50.0;
        }
        
        // 速度分数（最高10MB/s获得满分）
        let speed_score = (self.avg_speed / (10.0 * 1024.0 * 1024.0)).min(1.0) * 60.0;
        
        // 成功率分数
        let success_score = self.success_rate * 30.0;
        
        // 响应时间分数（最低50ms获得满分，最高2000ms获得最低分）
        let response_time_normalized = ((2000.0 - self.avg_response_time.min(2000.0)) / 1950.0).max(0.0);
        let response_score = response_time_normalized * 10.0;
        
        speed_score + success_score + response_score
    }

    /// 检查是否需要更新可用性
    pub fn needs_availability_check(&self, interval: Duration) -> bool {
        match self.last_availability_check {
            Some(last_check) => last_check.elapsed() >= interval,
            None => true,
        }
    }
}
