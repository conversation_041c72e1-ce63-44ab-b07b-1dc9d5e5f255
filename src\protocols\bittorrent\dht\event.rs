use std::net::SocketAddr;
use std::sync::Arc;
use anyhow::Result;
use async_trait::async_trait;
use tokio::sync::RwLock;
use tracing::warn;

/// DHT事件
#[derive(Debug, <PERSON>lone)]
pub enum DHTEvent {
    /// 节点状态变更
    NodeStateChanged {
        /// 节点数量
        node_count: usize,
        /// 是否已引导
        bootstrapped: bool,
    },
    /// 找到对等点
    PeersFound {
        /// 信息哈希
        info_hash: [u8; 20],
        /// 对等点列表
        peers: Vec<SocketAddr>,
    },
}

/// DHT事件监听器接口
#[async_trait]
pub trait DHTEventListener: Send + Sync {
    /// 处理DHT事件
    async fn on_event(&self, event: DHTEvent) -> Result<()>;
}

/// DHT事件分发器
pub struct DHTEventDispatcher {
    /// 事件监听器列表
    listeners: Arc<RwLock<Vec<Arc<dyn DHTEventListener>>>>,
}

impl DHTEventDispatcher {
    /// 创建新的DHT事件分发器
    pub fn new() -> Self {
        Self {
            listeners: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 添加事件监听器
    pub async fn add_listener(&self, listener: Arc<dyn DHTEventListener>) -> Result<()> {
        let mut listeners = self.listeners.write().await;
        listeners.push(listener);
        Ok(())
    }

    /// 移除事件监听器
    pub async fn remove_listener(&self, listener_id: usize) -> Result<()> {
        let mut listeners = self.listeners.write().await;
        if listener_id < listeners.len() {
            listeners.remove(listener_id);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Invalid listener ID"))
        }
    }

    /// 分发事件
    pub async fn dispatch(&self, event: DHTEvent) -> Result<()> {
        let listeners = self.listeners.read().await;
        for listener in listeners.iter() {
            if let Err(e) = listener.on_event(event.clone()).await {
                warn!("Error sending DHT event to listener: {}", e);
            }
        }
        Ok(())
    }

    /// 获取监听器数量
    pub async fn listener_count(&self) -> usize {
        let listeners = self.listeners.read().await;
        listeners.len()
    }
}
