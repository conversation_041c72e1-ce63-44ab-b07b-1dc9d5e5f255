use std::sync::Arc;
use std::time::Duration;
use anyhow::{Result, anyhow};
use rand::{Rng, RngCore};
use tokio::sync::{mpsc, Mutex, RwLock, Semaphore};
use tokio::time::timeout;
use tracing::{debug, info, warn};

use crate::protocols::bittorrent::dht::client::DHTClient;
use crate::protocols::bittorrent::dht::config::DHTClientConfig;
use crate::protocols::bittorrent::dht::{DHTMessageType, DHTQuery};
use crate::protocols::bittorrent::dht::message::DHTResponse;
use crate::protocols::bittorrent::dht::node::DHTNode;
use crate::protocols::bittorrent::dht::node::NodeId;

use super::config::DHTCrawlerConfig;
use super::stats::SafeDHTCrawlerStats;
use super::storage::DHTCrawlerStorage;

/// DHT爬虫
pub struct DHTCrawler {
    /// 配置
    config: DHTCrawlerConfig,
    /// 存储
    storage: Arc<DHTCrawlerStorage>,
    /// 统计
    stats: Arc<SafeDHTCrawlerStats>,
    /// DHT客户端
    client: Arc<DHTClient>,
    /// 并发限制信号量
    semaphore: Arc<Semaphore>,
    /// 是否正在运行
    running: RwLock<bool>,
    /// 任务句柄
    task_handle: Mutex<Option<tokio::task::JoinHandle<()>>>,
}

impl DHTCrawler {
    /// 创建新的DHT爬虫
    pub async fn new(config: DHTCrawlerConfig) -> Result<Self> {
        // 创建DHT客户端配置
        let dht_config = DHTClientConfig {
            port: config.listen_addr.port(),
            bootstrap_nodes: config.bootstrap_nodes.clone(),
            routing_table_config: Default::default(),
            query_timeout: config.request_timeout,
            parallel_queries: config.max_concurrent_requests,
            enable_ipv6: config.enable_ipv6,
        };

        // 创建DHT客户端
        let client = Arc::new(DHTClient::new(dht_config).await?);

        // 创建存储
        let storage = Arc::new(DHTCrawlerStorage::new(
            config.max_infohashes,
            config.save_metadata,
            config.metadata_path.clone(),
        ));

        // 创建统计
        let stats = Arc::new(SafeDHTCrawlerStats::new());

        // 创建信号量
        let semaphore = Arc::new(Semaphore::new(config.max_concurrent_requests));

        Ok(Self {
            config,
            storage,
            stats,
            client,
            semaphore,
            running: RwLock::new(false),
            task_handle: Mutex::new(None),
        })
    }

    /// 启动爬虫
    pub async fn start(&self) -> Result<()> {
        let mut running = self.running.write().await;

        if *running {
            return Ok(());
        }

        *running = true;

        // 加载元数据
        if self.config.save_metadata {
            if let Err(e) = self.storage.load_metadata_from_file().await {
                warn!("Failed to load metadata: {}", e);
            }
        }

        // 添加引导节点
        for addr in &self.config.bootstrap_nodes {
            let node = DHTNode::new(
                NodeId::new_random(),
                addr.ip(),
                addr.port()
            );
            self.storage.add_pending_node(node).await;
        }

        // 创建爬虫任务
        let config = self.config.clone();
        let storage = self.storage.clone();
        let stats = self.stats.clone();
        let client = self.client.clone();
        let semaphore = self.semaphore.clone();

        // 创建一个布尔值副本，用于在任务中访问
        let running_copy = *running;
        drop(running); // 释放写锁

        let handle = tokio::spawn(async move {
            info!("DHT crawler started");

            // 创建通道
            let (tx, mut rx) = mpsc::channel(1000);

            loop {
                // 检查是否有节点可爬取
                if let Some(node) = storage.get_next_node().await {
                    // 获取信号量许可
                    let permit_result = semaphore.try_acquire();
                    if permit_result.is_err() {
                        // 没有可用的许可，等待一段时间后继续
                        tokio::time::sleep(Duration::from_millis(10)).await;
                        continue;
                    }
                    let _permit = permit_result.unwrap();

                    // 创建爬取任务
                    let task_tx = tx.clone();
                    let task_client = client.clone();
                    let task_stats = stats.clone();
                    let task_config = config.clone();
                    let task_storage = storage.clone();
                    let node_addr = node.addr();
                    let node_clone = node.clone();

                    // 在这里生成随机目标ID，而不是在crawl_node中
                    let mut target_id = [0u8; 20];
                    rand::rngs::OsRng.fill(&mut target_id);

                    // 生成随机事务ID
                    let mut transaction_id = vec![0u8; 4];
                    rand::rngs::OsRng.fill_bytes(&mut transaction_id);

                    tokio::spawn(async move {
                        // 标记节点为已爬取
                        task_storage.mark_node_crawled(node_addr).await;
                        task_stats.increment_nodes_crawled(1).await;

                        // 爬取节点，传入预生成的随机ID
                        if let Err(e) = crawl_node_with_ids(
                            &task_client,
                            &node_clone,
                            &task_storage,
                            &task_stats,
                            &task_config,
                            task_tx,
                            target_id,
                            transaction_id,
                        ).await {
                            debug!("Failed to crawl node {}: {}", node_addr, e);
                        }

                        // 不需要手动释放信号量许可，它会在任务结束时自动释放
                    });
                }

                // 处理发现的节点
                while let Ok(nodes) = rx.try_recv() {
                    let count = nodes.len() as u64;
                    storage.add_pending_nodes_ref(&nodes).await;
                    stats.increment_nodes_discovered(count).await;
                }

                // 等待一段时间
                tokio::time::sleep(Duration::from_millis(config.crawl_interval)).await;

                // 检查是否应该停止
                if !running_copy {
                    break;
                }
            }

            // 爬虫停止
            info!("DHT crawler stopped");
        });

        // 保存任务句柄
        let mut task_handle = self.task_handle.lock().await;
        *task_handle = Some(handle);

        info!("DHT crawler started");

        Ok(())
    }

    /// 停止爬虫
    pub async fn stop(&self) -> Result<()> {
        let mut running = self.running.write().await;

        if !*running {
            return Ok(());
        }

        *running = false;

        // 取消任务
        let mut task_handle = self.task_handle.lock().await;
        if let Some(handle) = task_handle.take() {
            handle.abort();
        }

        // 保存元数据
        if self.config.save_metadata {
            if let Err(e) = self.storage.save_metadata_to_file().await {
                warn!("Failed to save metadata: {}", e);
            }
        }

        info!("DHT crawler stopped");

        Ok(())
    }

    /// 获取统计
    pub async fn get_stats(&self) -> Result<super::stats::DHTCrawlerStats> {
        Ok(self.stats.get_stats().await)
    }

    /// 重置统计
    pub async fn reset_stats(&self) -> Result<()> {
        self.stats.reset().await;
        Ok(())
    }

    /// 清除数据
    pub async fn clear_data(&self) -> Result<()> {
        self.storage.clear().await;
        Ok(())
    }

    /// 是否正在运行
    pub async fn is_running(&self) -> bool {
        *self.running.read().await
    }
}

/// 爬取节点（使用预生成的随机ID）
async fn crawl_node_with_ids(
    client: &DHTClient,
    node: &DHTNode,
    storage: &DHTCrawlerStorage,
    stats: &SafeDHTCrawlerStats,
    config: &DHTCrawlerConfig,
    tx: mpsc::Sender<Vec<DHTNode>>,
    target_id: [u8; 20],
    transaction_id: Vec<u8>,
) -> Result<()> {
    // 使用传入的随机ID，避免在异步上下文中使用 thread_rng
    let query = DHTQuery::new(
        transaction_id,
        NodeId::from_bytes(target_id),
        DHTMessageType::GetPeers
    );

    // 发送查询
    stats.increment_requests_processed(1).await;

    match timeout(
        Duration::from_secs(config.request_timeout),
        client.send_query(node.addr(), DHTMessageType::GetPeers, query)
    ).await {
        Ok(Ok(response)) => {
            stats.increment_requests_succeeded(1).await;

            // 处理响应
            match response {
                DHTResponse::GetPeers { id, token: _, values, nodes } => {
                    // 处理返回的信息哈希（values）
                    if let Some(values) = values {
                        for _value in values {
                            // 添加信息哈希
                            if storage.add_infohash(target_id, node.addr()).await {
                                stats.increment_infohashes_discovered(1).await;
                            }
                        }
                    }

                    // 处理返回的节点
                    if let Some(nodes) = nodes {
                        let mut discovered_nodes = Vec::new();

                        for node_info in nodes {
                            // 创建节点ID的20字节数组
                            let mut id_bytes = [0u8; 20];
                            id_bytes.copy_from_slice(&node_info.id.as_bytes()[0..20]);
                            let discovered_node = DHTNode::new(NodeId::from_bytes(id_bytes), node_info.addr().ip(), node_info.addr().port());
                            discovered_nodes.push(discovered_node);
                        }

                        // 发送发现的节点
                        if !discovered_nodes.is_empty() {
                            let _ = tx.send(discovered_nodes).await;
                        }
                    }

                    // 发送sample_infohashes查询
                    if let Some(_id) = id {
                        // 生成新的随机事务ID
                        let mut sample_transaction_id = vec![0u8; 4];
                        rand::rngs::OsRng.fill_bytes(&mut sample_transaction_id);
                        let sample_query = DHTQuery::new(
                            sample_transaction_id,
                            NodeId::from_bytes(target_id),
                            DHTMessageType::GetPeers
                        );

                        stats.increment_requests_processed(1).await;

                        match timeout(
                            Duration::from_secs(config.request_timeout),
                            client.send_query(node.addr(), DHTMessageType::GetPeers, sample_query)
                        ).await {
                            Ok(Ok(sample_response)) => {
                                stats.increment_requests_succeeded(1).await;

                                // 处理sample_infohashes响应
                                if let DHTResponse::SampleInfohashes { samples, nodes, .. } = sample_response {
                                    // 处理返回的样本
                                    if let Some(samples) = samples {
                                        for sample in samples {
                                            // 添加信息哈希
                                            if storage.add_infohash(sample, node.addr()).await {
                                                stats.increment_infohashes_discovered(1).await;
                                            }
                                        }
                                    }

                                    // 处理返回的节点
                                    if let Some(nodes) = nodes {
                                        let mut discovered_nodes = Vec::new();

                                        for node_info in nodes {
                                            // 创建节点ID的20字节数组
                                            let mut id_bytes = [0u8; 20];
                                            id_bytes.copy_from_slice(&node_info.id.as_bytes()[0..20]);
                                            let discovered_node = DHTNode::new(NodeId::from_bytes(id_bytes), node_info.addr().ip(), node_info.addr().port());
                                            discovered_nodes.push(discovered_node);
                                        }

                                        // 发送发现的节点
                                        if !discovered_nodes.is_empty() {
                                            let _ = tx.send(discovered_nodes).await;
                                        }
                                    }
                                }
                            },
                            Ok(Err(e)) => {
                                stats.increment_requests_failed(1).await;
                                debug!("Failed to send sample_infohashes query to {}: {}", node.addr(), e);
                            },
                            Err(_) => {
                                stats.increment_requests_timeout(1).await;
                                debug!("sample_infohashes query to {} timed out", node.addr());
                            },
                        }
                    }
                },
                _ => {
                    debug!("Unexpected response type from {}", node.addr());
                }
            }

            Ok(())
        },
        Ok(Err(e)) => {
            stats.increment_requests_failed(1).await;
            Err(anyhow!("Failed to send query: {}", e))
        },
        Err(_) => {
            stats.increment_requests_timeout(1).await;
            Err(anyhow!("Query timed out"))
        },
    }
}
