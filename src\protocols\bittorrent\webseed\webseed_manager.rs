use anyhow::{anyhow, Result};
use bytes::Bytes;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;
use tracing::{debug, error, info, warn};
use futures::future;
use uuid::Uuid;

use crate::download::bandwidth_scheduler::BandwidthScheduler;

use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::utils::HttpClient;

use super::config::WebSeedConfig;
use super::http_seed::HttpSeed;
use super::url_seed::UrlSeed;
use super::webseed_source::WebSeedSource;
use super::cache::WebSeedCache;

#[async_trait::async_trait]
pub trait WebSeedManagerTrait: Send + Sync {
    async fn add_webseed(&mut self, url: &str);
    async fn remove_webseed(&mut self, url: &str);
    async fn start_all(&mut self);
    async fn stop_all(&mut self);
    // ...可扩展其它接口...
}

/// WebSeed管理器
// 移除自动派生的Clone
pub struct WebSeedManager {
    /// WebSeed源列表
    sources: Mutex<Vec<Box<dyn WebSeedSource>>>,
    /// HTTP客户端
    http_client: HttpClient,
    /// WebSeed配置
    config: WebSeedConfig,
    /// 种子信息
    torrent_info: Arc<TorrentInfo>,
    /// 缓存
    cache: Arc<WebSeedCache>,
    /// 带宽调度器
    bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
    /// 任务ID
    task_id: Uuid,
}

// 手动实现Clone trait
impl Clone for WebSeedManager {
    fn clone(&self) -> Self {
        // 创建一个新的空sources
        let sources = Mutex::new(Vec::new());
        
        Self {
            sources,
            http_client: self.http_client.clone(),
            config: self.config.clone(),
            torrent_info: self.torrent_info.clone(),
            cache: self.cache.clone(),
            bandwidth_scheduler: self.bandwidth_scheduler.clone(),
            task_id: self.task_id,
        }
    }
}

impl WebSeedManager {
    /// 创建新的WebSeed管理器
    pub fn new(http_client: HttpClient, config: WebSeedConfig,
               torrent_info: Arc<TorrentInfo>,
               bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
               task_id: Uuid) -> Self {
        // 创建缓存
        let cache = Arc::new(WebSeedCache::new(config.cache_config.clone()));

        Self {
            sources: Mutex::new(Vec::new()),
            http_client,
            config,
            torrent_info,
            cache,
            bandwidth_scheduler,
            task_id,
        }
    }

    /// 从种子元数据添加WebSeed源
    pub async fn add_sources_from_torrent(&self) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let mut sources = self.sources.lock().await;

        // 添加BEP 19源（url-list）
        if let Some(url_list) = &self.torrent_info.url_list {
            for url in url_list {
                match UrlSeed::new(
                    url.clone(),
                    self.http_client.clone(),
                    self.torrent_info.clone(),
                ) {
                    Ok(url_seed) => {
                        info!("Added URL Seed: {}", url);
                        sources.push(Box::new(url_seed));
                    },
                    Err(e) => {
                        warn!("Failed to add URL Seed {}: {}", url, e);
                    }
                }
            }
        }

        // 添加BEP 17源（httpseeds）
        if let Some(http_seeds) = &self.torrent_info.http_seeds {
            for url in http_seeds {
                match HttpSeed::new(
                    url.clone(),
                    self.http_client.clone(),
                    self.torrent_info.clone(),
                ) {
                    Ok(http_seed) => {
                        info!("Added HTTP Seed: {}", url);
                        sources.push(Box::new(http_seed));
                    },
                    Err(e) => {
                        warn!("Failed to add HTTP Seed {}: {}", url, e);
                    }
                }
            }
        }

        debug!("Added {} WebSeed sources", sources.len());

        Ok(())
    }

    /// 添加URL Seed（BEP 19）
    pub async fn add_url_seed(&self, url: &str) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let url_seed = UrlSeed::new(
            url.to_string(),
            self.http_client.clone(),
            self.torrent_info.clone(),
        )?;

        let mut sources = self.sources.lock().await;
        sources.push(Box::new(url_seed));

        info!("Added URL Seed: {}", url);

        Ok(())
    }

    /// 添加HTTP Seed（BEP 17）
    pub async fn add_http_seed(&self, url: &str) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let http_seed = HttpSeed::new(
            url.to_string(),
            self.http_client.clone(),
            self.torrent_info.clone(),
        )?;

        let mut sources = self.sources.lock().await;
        sources.push(Box::new(http_seed));

        info!("Added HTTP Seed: {}", url);

        Ok(())
    }

    /// 从最佳可用的WebSeed源下载分片
    pub async fn download_piece(&self, piece_index: usize) -> Result<Bytes> {
        if !self.config.enabled {
            return Err(anyhow!("WebSeed is disabled"));
        }

        // 生成缓存键
        let cache_key = format!("piece:{}:{}", self.torrent_info.info_hash_hex, piece_index);

        // 检查缓存
        if let Some(cached_data) = self.cache.get(&cache_key).await {
            debug!("Cache hit for piece {}", piece_index);
            return Ok(cached_data);
        }

        let sources = self.sources.lock().await;

        if sources.is_empty() {
            return Err(anyhow!("No WebSeed sources available"));
        }

        // 获取分片长度
        let piece_length = self.get_piece_length(piece_index)?;

        // 选择最佳源
        let best_source = self.select_best_source(&sources)?;

        debug!("Downloading piece {} from WebSeed: {}", piece_index, best_source.url());

        // 下载分片
        let result = best_source.download_piece(piece_index, piece_length, 0, piece_length).await;

        match result {
            Ok(data) => {
                debug!("Successfully downloaded piece {} from WebSeed", piece_index);

                // 应用带宽限制
                if let Some(scheduler) = &self.bandwidth_scheduler {
                    if let Err(e) = scheduler.wait_for_download_quota(self.task_id, data.len()).await {
                        warn!("Failed to apply bandwidth limit: {}", e);
                    }
                }

                // 缓存数据
                if let Err(e) = self.cache.set(cache_key, data.clone()).await {
                    warn!("Failed to cache piece {}: {}", piece_index, e);
                }

                Ok(data)
            },
            Err(e) => {
                error!("Failed to download piece {} from WebSeed: {}", piece_index, e);
                Err(anyhow!("Failed to download piece from WebSeed: {}", e))
            }
        }
    }

    /// 选择最佳源
    fn select_best_source<'a>(&self, sources: &'a [Box<dyn WebSeedSource>]) -> Result<&'a dyn WebSeedSource> {
        // 按优先级排序并选择第一个可用源
        sources.iter()
            .filter(|s| s.performance().available)
            .max_by(|a, b| a.priority().partial_cmp(&b.priority()).unwrap_or(std::cmp::Ordering::Equal))
            .map(|s| s.as_ref())
            .ok_or_else(|| anyhow!("No available WebSeed sources"))
    }

    /// 获取分片长度
    fn get_piece_length(&self, piece_index: usize) -> Result<usize> {
        if piece_index >= self.torrent_info.pieces.len() {
            return Err(anyhow!("Invalid piece index: {}", piece_index));
        }

        // 对于最后一个分片，长度可能不同
        if piece_index == self.torrent_info.pieces.len() - 1 {
            let remaining = self.torrent_info.length as usize % self.torrent_info.piece_length as usize;
            if remaining > 0 {
                return Ok(remaining);
            }
        }

        Ok(self.torrent_info.piece_length as usize)
    }

    /// 检查所有源的可用性
    pub async fn check_sources_availability(&self) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let mut sources = self.sources.lock().await;

        for source in sources.iter_mut() {
            // 检查是否需要更新可用性
            let check_interval = Duration::from_secs(self.config.availability_check_interval);
            if source.performance().needs_availability_check(check_interval) {
                let available = source.is_available().await;
                source.performance_mut().update_availability(available);

                if available {
                    debug!("WebSeed source {} is available", source.url());
                } else {
                    warn!("WebSeed source {} is not available", source.url());
                }
            }
        }

        Ok(())
    }

    /// 更新源性能指标
    pub async fn update_source_performance(&self, url: &str,
                                    download_time: Duration,
                                    bytes_downloaded: usize,
                                    success: bool) -> Result<()> {
        let mut sources = self.sources.lock().await;

        if let Some(source) = sources.iter_mut().find(|s| s.url() == url) {
            source.update_performance(download_time, bytes_downloaded, success);
            Ok(())
        } else {
            Err(anyhow!("WebSeed source not found: {}", url))
        }
    }

    /// 获取所有可用源
    pub async fn available_sources(&self) -> Vec<String> {
        let sources = self.sources.lock().await;

        sources.iter()
            .filter(|s| s.performance().available)
            .map(|s| s.url().to_string())
            .collect()
    }

    /// 获取源数量
    pub async fn source_count(&self) -> usize {
        let sources = self.sources.lock().await;
        sources.len()
    }

    /// 获取可用源数量
    pub async fn available_source_count(&self) -> usize {
        let sources = self.sources.lock().await;

        sources.iter()
            .filter(|s| s.performance().available)
            .count()
    }

    /// 是否有可用源
    pub async fn has_available_sources(&self) -> bool {
        self.available_source_count().await > 0
    }

    /// 是否启用WebSeed
    pub fn is_enabled(&self) -> bool {
        self.config.enabled
    }

    /// 是否优先使用WebSeed
    pub fn prefer_webseed(&self) -> bool {
        self.config.prefer_webseed
    }

    /// 并发下载多个分片
    pub async fn download_pieces_concurrent(&self, piece_indices: &[usize]) -> Result<Vec<(usize, Bytes)>> {
        if !self.config.enabled {
            return Err(anyhow!("WebSeed is disabled"));
        }

        let sources = self.sources.lock().await;

        if sources.is_empty() {
            return Err(anyhow!("No WebSeed sources available"));
        }

        // 限制并发数量
        let max_concurrent = self.config.max_connections.min(piece_indices.len());

        // 创建任务
        let mut tasks = Vec::with_capacity(piece_indices.len());
        let mut results = Vec::with_capacity(piece_indices.len());

        // 为每个分片创建下载任务
        for &piece_index in piece_indices {
            // 生成缓存键
            let cache_key = format!("piece:{}:{}", self.torrent_info.info_hash_hex, piece_index);

            // 检查缓存
            if let Some(cached_data) = self.cache.get(&cache_key).await {
                debug!("Cache hit for piece {}", piece_index);
                results.push((piece_index, cached_data));
                continue;
            }

            // 获取分片长度
            let piece_length = match self.get_piece_length(piece_index) {
                Ok(length) => length,
                Err(e) => {
                    warn!("Failed to get piece length for piece {}: {}", piece_index, e);
                    continue;
                }
            };

            // 选择最佳源
            let best_source = match self.select_best_source(&sources) {
                Ok(source) => source.clone_box(),
                Err(e) => {
                    warn!("Failed to select best source for piece {}: {}", piece_index, e);
                    continue;
                }
            };

            // 克隆源以便在任务中使用
            let source_url = best_source.url().to_string();

            // 克隆缓存引用
            let cache = Arc::clone(&self.cache);
            let cache_key_clone = cache_key.clone();

            // 克隆带宽调度器引用
            let bandwidth_scheduler = self.bandwidth_scheduler.clone();
            let task_id = self.task_id;

            // 创建下载任务
            let task = tokio::spawn(async move {
                debug!("Starting concurrent download of piece {} from WebSeed: {}", piece_index, source_url);

                // 下载分片
                match best_source.download_piece(piece_index, piece_length, 0, piece_length).await {
                    Ok(data) => {
                        debug!("Successfully downloaded piece {} from WebSeed", piece_index);

                        // 应用带宽限制
                        if let Some(scheduler) = &bandwidth_scheduler {
                            if let Err(e) = scheduler.wait_for_download_quota(task_id, data.len()).await {
                                warn!("Failed to apply bandwidth limit: {}", e);
                            }
                        }

                        // 缓存数据
                        if let Err(e) = cache.set(cache_key_clone, data.clone()).await {
                            warn!("Failed to cache piece {}: {}", piece_index, e);
                        }

                        Some((piece_index, data))
                    },
                    Err(e) => {
                        error!("Failed to download piece {} from WebSeed: {}", piece_index, e);
                        None
                    }
                }
            });

            tasks.push(task);
        }

        // 限制并发数量
        let mut i = 0;
        while i * max_concurrent < tasks.len() {
            let start = i * max_concurrent;
            let end = ((i + 1) * max_concurrent).min(tasks.len());

            // 创建一个新的任务向量
            let mut chunk_tasks = Vec::with_capacity(end - start);

            // 移动任务到新的向量中
            for j in start..end {
                if j < tasks.len() {
                    chunk_tasks.push(tasks.remove(start));
                }
            }

            // 执行任务
            let chunk_results = future::join_all(chunk_tasks).await;

            // 处理结果
            for result in chunk_results {
                if let Ok(Some((piece_index, data))) = result {
                    results.push((piece_index, data));
                }
            }

            i += 1;
        }

        if results.is_empty() {
            return Err(anyhow!("Failed to download any pieces from WebSeed"));
        }

        Ok(results)
    }
}
