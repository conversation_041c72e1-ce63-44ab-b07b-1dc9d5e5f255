use std::collections::HashMap;
use std::time::{Duration, Instant};
use anyhow::Result;
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{debug, info};

use crate::protocols::bittorrent::dht::message::DHTResponse;
use crate::protocols::bittorrent::dht::DHTQuery;

/// 缓存配置
#[derive(Debug, Clone)]
pub struct MessageCacheConfig {
    /// 缓存生存时间（秒）
    pub ttl: u64,
    /// 最大缓存项数
    pub max_items: usize,
    /// 清理间隔（秒）
    pub cleanup_interval: u64,
}

impl Default for MessageCacheConfig {
    fn default() -> Self {
        Self {
            ttl: 60, // 1分钟
            max_items: 10000,
            cleanup_interval: 300, // 5分钟
        }
    }
}

/// 缓存项
#[derive(Clone)]
struct CacheItem<T: Clone> {
    /// 值
    value: T,
    /// 创建时间
    created_at: Instant,
    /// 访问次数
    access_count: u64,
}



impl<T: Clone> CacheItem<T> {
    /// 创建新的缓存项
    fn new(value: T) -> Self {
        Self {
            value,
            created_at: Instant::now(),
            access_count: 0,
        }
    }

    /// 更新访问信息
    fn update_access(&mut self) {
        self.access_count += 1;
    }

    /// 检查是否过期
    fn is_expired(&self, ttl: Duration) -> bool {
        self.created_at.elapsed() > ttl
    }
}

/// DHT消息缓存
pub struct DHTMessageCache {
    /// 查询缓存
    query_cache: RwLock<HashMap<String, CacheItem<DHTQuery>>>,
    /// 响应缓存
    response_cache: RwLock<HashMap<String, CacheItem<DHTResponse>>>,
    /// 配置
    config: MessageCacheConfig,
    /// 是否正在运行
    running: RwLock<bool>,
    /// 任务句柄
    task_handle: RwLock<Option<tokio::task::JoinHandle<()>>>,
    /// 缓存命中计数
    hits: RwLock<u64>,
    /// 缓存未命中计数
    misses: RwLock<u64>,
}

impl DHTMessageCache {
    /// 创建新的DHT消息缓存
    pub fn new(config: MessageCacheConfig) -> Self {
        Self {
            query_cache: RwLock::new(HashMap::new()),
            response_cache: RwLock::new(HashMap::new()),
            config,
            running: RwLock::new(false),
            task_handle: RwLock::new(None),
            hits: RwLock::new(0),
            misses: RwLock::new(0),
        }
    }

    /// 启动缓存
    pub async fn start(&self) -> Result<()> {
        let mut running = self.running.write().await;

        if *running {
            return Ok(());
        }

        *running = true;

        // 创建清理任务
        let config = self.config.clone();

        // 创建新的缓存实例，而不是引用现有的
        let query_cache = RwLock::new(HashMap::new());
        let response_cache = RwLock::new(HashMap::new());

        // 复制缓存数据
        {
            let src_cache = self.query_cache.read().await;
            let mut dst_cache = query_cache.write().await;
            for (k, v) in src_cache.iter() {
                dst_cache.insert(k.clone(), v.clone());
            }
        }

        {
            let src_cache = self.response_cache.read().await;
            let mut dst_cache = response_cache.write().await;
            for (k, v) in src_cache.iter() {
                dst_cache.insert(k.clone(), v.clone());
            }
        }

        let handle = tokio::spawn(async move {
            let mut interval_timer = interval(Duration::from_secs(config.cleanup_interval));
            let ttl = Duration::from_secs(config.ttl);

            loop {
                interval_timer.tick().await;

                // 清理查询缓存
                {
                    let mut cache = query_cache.write().await;
                    let before_count = cache.len();

                    cache.retain(|_, item| !item.is_expired(ttl));

                    let cleaned = before_count - cache.len();
                    if cleaned > 0 {
                        debug!("Cleaned {} expired items from query cache", cleaned);
                    }
                }

                // 清理响应缓存
                {
                    let mut cache = response_cache.write().await;
                    let before_count = cache.len();

                    cache.retain(|_, item| !item.is_expired(ttl));

                    let cleaned = before_count - cache.len();
                    if cleaned > 0 {
                        debug!("Cleaned {} expired items from response cache", cleaned);
                    }
                }
            }
        });

        // 保存任务句柄
        let mut task_handle = self.task_handle.write().await;
        *task_handle = Some(handle);

        info!("DHT message cache started");

        Ok(())
    }

    /// 停止缓存
    pub async fn stop(&self) -> Result<()> {
        let mut running = self.running.write().await;

        if !*running {
            return Ok(());
        }

        *running = false;

        // 取消任务
        let mut task_handle = self.task_handle.write().await;
        if let Some(handle) = task_handle.take() {
            handle.abort();
        }

        // 清空缓存
        {
            let mut query_cache = self.query_cache.write().await;
            query_cache.clear();
        }

        {
            let mut response_cache = self.response_cache.write().await;
            response_cache.clear();
        }

        info!("DHT message cache stopped");

        Ok(())
    }

    /// 缓存查询
    pub async fn cache_query(&self, key: &str, query: DHTQuery) -> Result<()> {
        let mut cache = self.query_cache.write().await;

        // 检查缓存大小
        if cache.len() >= self.config.max_items && !cache.contains_key(key) {
            // 移除最旧的项
            let oldest_key = cache.iter()
                .min_by_key(|(_, item)| item.created_at)
                .map(|(k, _)| k.clone());

            if let Some(oldest_key) = oldest_key {
                cache.remove(&oldest_key);
            }
        }

        // 添加到缓存
        cache.insert(key.to_string(), CacheItem::new(query));

        Ok(())
    }

    /// 获取缓存的查询
    pub async fn get_query(&self, key: &str) -> Option<DHTQuery> {
        let mut cache = self.query_cache.write().await;

        if let Some(item) = cache.get_mut(key) {
            // 检查是否过期
            if item.is_expired(Duration::from_secs(self.config.ttl)) {
                cache.remove(key);

                // 增加未命中计数
                let mut misses = self.misses.write().await;
                *misses += 1;

                return None;
            }

            // 更新访问信息
            item.update_access();

            // 增加命中计数
            let mut hits = self.hits.write().await;
            *hits += 1;

            return Some(item.value.clone());
        }

        // 增加未命中计数
        let mut misses = self.misses.write().await;
        *misses += 1;

        None
    }

    /// 缓存响应
    pub async fn cache_response(&self, key: &str, response: DHTResponse) -> Result<()> {
        let mut cache = self.response_cache.write().await;

        // 检查缓存大小
        if cache.len() >= self.config.max_items && !cache.contains_key(key) {
            // 移除最旧的项
            let oldest_key = cache.iter()
                .min_by_key(|(_, item)| item.created_at)
                .map(|(k, _)| k.clone());

            if let Some(oldest_key) = oldest_key {
                cache.remove(&oldest_key);
            }
        }

        // 添加到缓存
        cache.insert(key.to_string(), CacheItem::new(response));

        Ok(())
    }

    /// 获取缓存的响应
    pub async fn get_response(&self, key: &str) -> Option<DHTResponse> {
        let mut cache = self.response_cache.write().await;

        if let Some(item) = cache.get_mut(key) {
            // 检查是否过期
            if item.is_expired(Duration::from_secs(self.config.ttl)) {
                cache.remove(key);

                // 增加未命中计数
                let mut misses = self.misses.write().await;
                *misses += 1;

                return None;
            }

            // 更新访问信息
            item.update_access();

            // 增加命中计数
            let mut hits = self.hits.write().await;
            *hits += 1;

            return Some(item.value.clone());
        }

        // 增加未命中计数
        let mut misses = self.misses.write().await;
        *misses += 1;

        None
    }

    /// 清除缓存
    pub async fn clear(&self) {
        {
            let mut query_cache = self.query_cache.write().await;
            query_cache.clear();
        }

        {
            let mut response_cache = self.response_cache.write().await;
            response_cache.clear();
        }
    }

    /// 获取缓存统计
    pub async fn get_stats(&self) -> (usize, usize, u64, u64) {
        let query_cache_size = self.query_cache.read().await.len();
        let response_cache_size = self.response_cache.read().await.len();
        let hits = *self.hits.read().await;
        let misses = *self.misses.read().await;

        (query_cache_size, response_cache_size, hits, misses)
    }

    /// 获取缓存命中率
    pub async fn get_hit_rate(&self) -> f64 {
        let hits = *self.hits.read().await;
        let misses = *self.misses.read().await;

        let total = hits + misses;
        if total == 0 {
            return 0.0;
        }

        hits as f64 / total as f64
    }
}
