<script setup lang="ts">
import { ref } from 'vue'
import { useThemeStore } from '../stores/theme'
import { useAppStore } from '../stores/app'

const themeStore = useThemeStore()
const appStore = useAppStore()

// 窗口操作
const handleMinimize = () => {
  // 在实际的Electron环境中，这里会调用窗口最小化API
  console.log('最小化窗口')
}

const handleMaximize = () => {
  // 在实际的Electron环境中，这里会调用窗口最大化API
  console.log('最大化窗口')
}

const handleClose = () => {
  // 在实际的Electron环境中，这里会调用窗口关闭API
  console.log('关闭窗口')
}
</script>

<template>
  <div class="title-bar">
    <div class="title draggable">
      <span>{{ appStore.appName }}</span>
    </div>
    <div class="window-actions">
      <div class="action-button non-draggable" @click="handleMinimize">
        <el-icon><Minus /></el-icon>
      </div>
      <div class="action-button non-draggable" @click="handleMaximize">
        <el-icon><FullScreen /></el-icon>
      </div>
      <div class="action-button close-button non-draggable" @click="handleClose">
        <el-icon><Close /></el-icon>
      </div>
    </div>
  </div>
</template>

<style scoped>
.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  background-color: var(--subnav-background);
  -webkit-app-region: drag;
  user-select: none;
}

.title {
  padding-left: 12px;
  font-size: 12px;
  color: var(--subnav-text-color);
}

.window-actions {
  display: flex;
  height: 100%;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 100%;
  -webkit-app-region: no-drag;
  cursor: pointer;
}

.action-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.close-button:hover {
  background-color: #e81123;
  color: white;
}

.draggable {
  -webkit-app-region: drag;
  user-select: none;
}

.non-draggable {
  -webkit-app-region: no-drag;
}
</style>
