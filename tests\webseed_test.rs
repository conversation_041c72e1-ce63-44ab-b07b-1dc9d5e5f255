#[cfg(test)]
mod tests {
    use anyhow::Result;
    use std::sync::Arc;
    use uuid::Uuid;

    use tonitru_downloader::config::Settings;
    use tonitru_downloader::protocols::bittorrent::webseed::{
        WebSeedConfig, WebSeedManager
    };
    use tonitru_downloader::protocols::bittorrent::webseed::cache::WebSeedCacheConfig;
    use tonitru_downloader::protocols::bittorrent::torrent::TorrentInfo;
    use tonitru_downloader::utils::HttpClient;

    // 创建一个测试用的TorrentInfo
    fn create_test_torrent_info() -> TorrentInfo {
        TorrentInfo {
            name: "test".to_string(),
            announce: Some("http://example.com/announce".to_string()),
            announce_list: Some(vec![vec!["http://example.com/announce".to_string()]]),
            creation_date: Some(0),
            comment: Some("test".to_string()),
            created_by: Some("test".to_string()),
            encoding: Some("UTF-8".to_string()),
            piece_length: 16384,
            pieces: vec![vec![0; 20]],
            files: Some(vec![]),
            length: 16384,
            info_hash: vec![0; 20],
            info_hash_hex: "0000000000000000000000000000000000000000".to_string(),
            info_hash_encoded: "%00%00%00%00%00%00%00%00%00%00%00%00%00%00%00%00%00%00%00%00".to_string(),
            total_size: 16384,
            url_list: Some(vec!["http://example.com/files/".to_string()]),
            http_seeds: Some(vec!["http://example.com/seed/".to_string()]),
        }
    }

    #[tokio::test]
    async fn test_webseed_manager_creation() -> Result<()> {
        // 创建设置
        let settings = Settings::default();

        // 创建HTTP客户端
        let http_client = HttpClient::from_settings(&settings)?;

        // 创建WebSeed缓存配置
        let cache_config = WebSeedCacheConfig {
            enabled: true,
            max_items: 1000,
            ttl: 3600,
            cleanup_interval: 300,
        };

        // 创建WebSeed配置
        let webseed_config = WebSeedConfig {
            enabled: true,
            max_connections: 4,
            connection_timeout: 30,
            read_timeout: 60,
            availability_check_interval: 300,
            min_piece_size: 16384,
            prefer_webseed: false,
            retry_count: 3,
            retry_delay: 5,
            cache_config,
        };

        // 创建TorrentInfo
        let torrent_info = create_test_torrent_info();

        // 创建任务ID
        let task_id = Uuid::new_v4();

        // 创建WebSeed管理器
        let webseed_manager = WebSeedManager::new(
            http_client,
            webseed_config,
            Arc::new(torrent_info),
            None, // 带宽调度器
            task_id,
        );

        // 添加WebSeed源
        webseed_manager.add_sources_from_torrent().await?;

        // 检查源数量
        assert_eq!(webseed_manager.source_count().await, 2);

        Ok(())
    }

    #[tokio::test]
    async fn test_webseed_source_types() -> Result<()> {
        // 创建设置
        let settings = Settings::default();

        // 创建HTTP客户端
        let http_client = HttpClient::from_settings(&settings)?;

        // 创建TorrentInfo
        let torrent_info = create_test_torrent_info();

        // 创建WebSeed缓存配置
        let cache_config = WebSeedCacheConfig {
            enabled: true,
            max_items: 1000,
            ttl: 3600,
            cleanup_interval: 300,
        };

        // 创建WebSeed配置
        let webseed_config = WebSeedConfig {
            enabled: true,
            max_connections: 4,
            connection_timeout: 30,
            read_timeout: 60,
            availability_check_interval: 300,
            min_piece_size: 16384,
            prefer_webseed: false,
            retry_count: 3,
            retry_delay: 5,
            cache_config,
        };

        // 创建任务ID
        let task_id = Uuid::new_v4();

        // 创建WebSeed管理器
        let webseed_manager = WebSeedManager::new(
            http_client,
            webseed_config,
            Arc::new(torrent_info),
            None, // 带宽调度器
            task_id,
        );

        // 添加WebSeed源
        webseed_manager.add_sources_from_torrent().await?;

        // 获取源列表
        let sources = webseed_manager.available_sources().await;

        // 检查源类型
        assert_eq!(sources.len(), 2);
        assert!(sources.iter().any(|s| s.contains("example.com/files/")));
        assert!(sources.iter().any(|s| s.contains("example.com/seed/")));

        Ok(())
    }
}
