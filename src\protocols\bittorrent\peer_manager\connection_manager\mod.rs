//! 连接管理器模块
//! 
//! 这个模块负责管理 BitTorrent 协议中的对等点连接，包括连接建立、维护和断开。
//! 它处理对等点的连接状态、连接超时和连接限制等功能。

use std::collections::{HashMap, HashSet, VecDeque};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};

use anyhow::{anyhow, Result};
use tokio::sync::RwLock;
use tracing::{debug, warn};

use crate::core::p2p::peer::Peer;
use crate::protocols::bittorrent::dht_manager::DHTManager;
use crate::protocols::bittorrent::BitTorrentPeer;
use crate::protocols::bittorrent::security::SecurityManager;
use crate::protocols::bittorrent::peer_quality::PeerQualityManager;
use crate::protocols::bittorrent::torrent::TorrentInfo;

/// 连接管理器
/// 
/// 负责管理对等点的连接，包括连接建立、维护和断开。
pub struct ConnectionManager {
    /// 已连接的对等点
    peers: HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>,
    
    /// 等待连接的对等点队列
    peer_queue: VecDeque<SocketAddr>,
    
    /// 最大对等点数量
    max_peers: usize,
    
    /// 安全管理器
    security_manager: Option<Arc<RwLock<SecurityManager>>>,
    
    /// 对等点质量管理器
    quality_manager: Arc<RwLock<PeerQualityManager>>,
    
    /// 本地对等点ID
    local_peer_id: Vec<u8>,
    
    /// 种子信息哈希
    info_hash: Option<Vec<u8>>,

    /// 总分片数量
    num_pieces: u32,

    /// DHT管理器
    dht_manager: Option<Arc<DHTManager>>,
    
    /// 对等点握手超时时间（秒）
    peer_handshake_timeout_secs: u64,
    /// 最后清理时间
    last_cleanup: Instant,
    /// 种子信息
    torrent_info: Option<TorrentInfo>,
    /// 输出路径
    output_path: Option<String>,
}

impl ConnectionManager {
    /// 创建新的连接管理器
    pub fn new(
        local_peer_id: Vec<u8>,
        max_peers: usize,
        security_manager: Option<Arc<RwLock<SecurityManager>>>,
        quality_manager: Arc<RwLock<PeerQualityManager>>,
        num_pieces: u32,
        dht_manager: Option<Arc<DHTManager>>,
        peer_handshake_timeout_secs: u64,
    ) -> Self {
        Self {
            peers: HashMap::new(),
            peer_queue: VecDeque::new(),
            max_peers,
            security_manager,
            quality_manager,
            local_peer_id,
            info_hash: None,
            num_pieces,
            dht_manager,
            peer_handshake_timeout_secs,
            last_cleanup: Instant::now(),
            torrent_info: None,
            output_path: None,
        }
}
    
    /// 设置种子信息哈希
    pub fn set_info_hash(&mut self, info_hash: Vec<u8>) {
        self.info_hash = Some(info_hash);

    }
    
    /// 获取已连接的对等点数量
    pub fn get_connected_peer_count(&self) -> usize {
        self.peers.len()
    }
    
    /// 获取对等点队列长度
    pub fn get_queue_length(&self) -> usize {
        self.peer_queue.len()
    }
    
    /// 获取所有已连接的对等点
    pub fn get_peers(&self) -> &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>> {
        &self.peers
    }
    
    /// 获取可变的对等点集合
    pub fn get_peers_mut(&mut self) -> &mut HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>> {
        &mut self.peers
    }
    
    /// 添加对等点到队列
    pub async fn add_peer_to_queue(&mut self, addr: SocketAddr) -> Result<()> {
        // 检查对等点是否已经在连接中
        if self.peers.contains_key(&addr) {
            return Ok(());
        }
        
        // 检查对等点是否已经在队列中
        if self.peer_queue.contains(&addr) {
            return Ok(());
        }
        
        // 检查对等点质量
        let manager = self.quality_manager.read().await;
        if !manager.filter_peer(&addr).await? {
            debug!("对等点 {} 被质量管理器拒绝", addr);
            return Ok(());
        }
        
        // 检查安全管理器
        if let Some(security_manager) = &self.security_manager {
            let manager = security_manager.read().await;
            if (*manager).is_blacklisted(&addr).await {
                debug!("对等点 {} 在黑名单中", addr);
                return Ok(());
            }
        }
        
        // 添加到队列
        self.peer_queue.push_back(addr);
        debug!("对等点 {} 已添加到队列", addr);
        
        Ok(())
    }
    
    /// 处理对等点队列，尝试连接新的对等点
    pub async fn process_peer_queue(&mut self) -> Result<()> {
        // 检查是否达到最大对等点数量
        if self.peers.len() >= self.max_peers {
            return Ok(());
        }
        
        // 检查info_hash是否已设置
        let info_hash = match &self.info_hash {
            Some(hash) => hash.clone(),
            None => {
                debug!("尚未设置info_hash，无法连接对等点");
                return Ok(());
            }
        };
        
        // 计算可以连接的对等点数量
        let available_slots = self.max_peers - self.peers.len();
        let connect_count = std::cmp::min(available_slots, self.peer_queue.len());
        
        // 尝试连接对等点
        for _ in 0..connect_count {
            if let Some(addr) = self.peer_queue.pop_front() {
                // 再次检查对等点是否已经在连接中
                if self.peers.contains_key(&addr) {
                    continue;
                }
                
                // 再次检查安全管理器
                if let Some(security_manager) = &self.security_manager {
                    let manager = security_manager.read().await;
                    if (*manager).is_blacklisted(&addr).await {
                        debug!("对等点 {} 在黑名单中", addr);
                        continue;
                    }
                }
                
                // 创建新的BitTorrent对等点
                match BitTorrentPeer::new(
                     addr,
                     &info_hash,
                     &self.local_peer_id,
                     self.peer_handshake_timeout_secs,
                     self.num_pieces,
                     self.dht_manager.clone(),
                     self.torrent_info.clone(),
                     self.output_path.clone()
                 ).await {
                     Ok(peer) => {
                         // 添加到对等点集合
                         self.peers.insert(addr, Arc::new(tokio::sync::Mutex::new(peer)));
                         debug!("成功连接对等点 {}", addr);
                     },
                     Err(e) => {
                         warn!("连接对等点 {} 失败: {}", addr, e);
                         // 记录连接失败的违规行为
                         let mut manager = self.quality_manager.write().await;
                         manager.record_violation(
                             &addr,
                             "connection_failed",
                             "连接失败",
                             2, // 轻微严重程度
                         ).await;
                     }
                 }
            }
        }
        
        Ok(())
    }
    
    /// 移除失败的对等点
    pub async fn remove_failed_peers(&mut self) -> Result<()> {
        let now = Instant::now();
        
        // 如果距离上次清理不足30秒，则跳过
        if now.duration_since(self.last_cleanup) < Duration::from_secs(30) {
            return Ok(());
        }
        
        // 更新最后清理时间
        self.last_cleanup = now;
        
        // 收集需要移除的对等点
        let mut to_remove = Vec::new();
        
        for (addr, peer) in &self.peers {
            // 检查对等点是否已断开连接
            if !peer.lock().await.is_connected() {
                to_remove.push(*addr);
                
                // 记录连接超时的违规行为
                if let Ok(mut manager) = self.quality_manager.try_write() {
                    manager.record_violation(
                        &addr,
                        "connection_timeout",
                        "连接断开或超时",
                        3, // 中等严重程度
                    ).await;
                }
            }
        }
        
        // 移除失败的对等点
        for addr in to_remove {
            self.peers.remove(&addr);
            debug!("移除失败的对等点 {}", addr);
        }
        
        Ok(())
    }
    
    /// 处理新的对等点列表
    pub async fn process_peers(&mut self, peers: Vec<SocketAddr>) -> Result<()> {
        debug!("处理 {} 个新对等点", peers.len());
        
        // 过滤并添加新对等点到队列
        for addr in peers {
            self.add_peer_to_queue(addr).await?;
        }
        
        // 处理对等点队列
        self.process_peer_queue().await?;
        
        Ok(())
    }
    
    /// 断开所有对等点连接
    pub async fn disconnect_all(&mut self) -> Result<()> {
        // 清空对等点集合
        self.peers.clear();
        
        // 清空对等点队列
        self.peer_queue.clear();
        
        debug!("已断开所有对等点连接");
        
        Ok(())
    }
    /// 获取最大对等点数量
    pub fn max_peers(&self) -> usize {
        self.max_peers
    }
    /// 获取分片总数
    pub fn num_pieces(&self) -> u32 {
        self.num_pieces
    }
    /// 获取 DHT 管理器
    pub fn dht_manager(&self) -> Option<Arc<DHTManager>> {
        self.dht_manager.clone()
    }
    /// 获取对等点握手超时时间
    pub fn peer_handshake_timeout_secs(&self) -> u64 {
        self.peer_handshake_timeout_secs
    }
    
    /// 设置种子信息
    pub fn set_torrent_info(&mut self, torrent_info: TorrentInfo) {
        self.torrent_info = Some(torrent_info);
    }
    
    /// 设置输出路径
    pub fn set_output_path(&mut self, output_path: String) {
        self.output_path = Some(output_path);
    }
    
    /// 设置最大对等点数量
    pub fn set_max_peers(&mut self, max_peers: usize) {
        self.max_peers = max_peers;
    }
}