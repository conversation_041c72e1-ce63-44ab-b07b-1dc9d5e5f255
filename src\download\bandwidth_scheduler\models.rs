use chrono::{DateTime, Utc, Timelike, Datelike};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

/// Task priority level
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum TaskPriority {
    /// Low priority
    Low = 0,
    /// Normal priority
    Normal = 1,
    /// High priority
    High = 2,
    /// Critical priority
    Critical = 3,
}

impl Default for TaskPriority {
    fn default() -> Self {
        Self::Normal
    }
}

/// Bandwidth statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BandwidthStats {
    /// Total bytes downloaded
    pub total_downloaded: u64,
    /// Total bytes uploaded
    pub total_uploaded: u64,
    /// Current download rate (bytes/second)
    pub download_rate: u64,
    /// Current upload rate (bytes/second)
    pub upload_rate: u64,
    /// Last update time
    pub last_update: DateTime<Utc>,
}

impl Default for BandwidthStats {
    fn default() -> Self {
        Self {
            total_downloaded: 0,
            total_uploaded: 0,
            download_rate: 0,
            upload_rate: 0,
            last_update: Utc::now(),
        }
    }
}

/// Time-based speed rule
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TimeBasedSpeedRule {
    /// Rule ID
    pub id: Uuid,
    /// Rule name
    pub name: String,
    /// Start hour (0-23)
    pub start_hour: u8,
    /// Start minute (0-59)
    pub start_minute: u8,
    /// End hour (0-23)
    pub end_hour: u8,
    /// End minute (0-59)
    pub end_minute: u8,
    /// Days of week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
    pub days_of_week: Vec<u8>,
    /// Download speed limit in bytes per second
    pub download_limit: Option<u64>,
    /// Upload speed limit in bytes per second
    pub upload_limit: Option<u64>,
}

impl TimeBasedSpeedRule {
    /// Create a new time-based speed rule
    pub fn new(
        name: String,
        start_hour: u8,
        start_minute: u8,
        end_hour: u8,
        end_minute: u8,
        days_of_week: Vec<u8>,
        download_limit: Option<u64>,
        upload_limit: Option<u64>,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            name,
            start_hour,
            start_minute,
            end_hour,
            end_minute,
            days_of_week,
            download_limit,
            upload_limit,
        }
    }

    /// Check if the rule is active at the given time
    pub fn is_active_at(&self, time: &DateTime<Utc>) -> bool {
        // Check day of week
        let day_of_week = time.weekday().num_days_from_sunday() as u8;
        if !self.days_of_week.contains(&day_of_week) {
            return false;
        }

        // Check time
        let current_time_minutes = time.hour() as u16 * 60 + time.minute() as u16;
        let start_time_minutes = self.start_hour as u16 * 60 + self.start_minute as u16;
        let end_time_minutes = self.end_hour as u16 * 60 + self.end_minute as u16;

        if start_time_minutes <= end_time_minutes {
            // Simple case: start time is before end time
            current_time_minutes >= start_time_minutes && current_time_minutes <= end_time_minutes
        } else {
            // Complex case: end time is on the next day
            current_time_minutes >= start_time_minutes || current_time_minutes <= end_time_minutes
        }
    }
}
