//! BitTorrent对等点状态模块

use std::collections::{HashSet, VecDeque};
use std::sync::Arc;

use crate::protocols::bittorrent::dht_manager::DHTManager;
use crate::protocols::bittorrent::extension_handler::ExtensionHandler;
use crate::protocols::bittorrent::handshake::HandshakeHandler;
use crate::protocols::bittorrent::upload_manager::UploadManager;

/// 对等点状态
pub struct PeerState {
    /// 是否已完成握手
    pub handshaked: bool,
    /// 握手处理器
    pub handshake_handler: Handshake<PERSON><PERSON><PERSON>,
    /// 扩展处理器
    pub extension_handler: ExtensionHandler,
    /// 上传管理器
    pub upload_manager: UploadManager,
    /// 对等点是否阻塞我
    pub peer_choking: bool,
    /// 我是否对对等点感兴趣
    pub am_interested: bool,
    /// 对等点拥有的块
    pub pieces_have: HashSet<u32>,
    /// 已请求的块
    pub requested_blocks: HashSet<(u32, u32)>,
    /// 对等点的位域
    pub bitfield: Option<Vec<u8>>,
    /// 允许的快速片段
    pub allowed_fast_pieces: HashSet<u32>,
    /// 上传队列
    pub upload_queue: VecDeque<(u32, u32, u32)>,
}

impl PeerState {
    /// 创建新的对等点状态
    pub fn new(
        info_hash: &[u8],
        local_peer_id: &[u8],
        supports_fast: bool,
        supports_extensions: bool,
        dht_manager: Option<Arc<DHTManager>>,
    ) -> Self {
        Self {
            handshaked: false,
            handshake_handler: HandshakeHandler::new(info_hash, local_peer_id),
            extension_handler: ExtensionHandler::new(supports_extensions),
            upload_manager: UploadManager::new(supports_fast),
            peer_choking: true,
            am_interested: false,
            pieces_have: HashSet::new(),
            requested_blocks: HashSet::new(),
            bitfield: None,
            allowed_fast_pieces: HashSet::new(),
            upload_queue: VecDeque::new(),
        }
    }
}