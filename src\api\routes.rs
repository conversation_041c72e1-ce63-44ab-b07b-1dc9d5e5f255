use axum::{
    Router,
    routing::{get, post, delete, put},
};

use crate::api::handlers::{
    download::{add_download, start_download, pause_download, resume_download, cancel_download, get_download_stats},
    task::{get_task, get_all_tasks, remove_task, update_task},
    status::get_status,
    speed::{
        set_global_download_limit, get_global_download_limit,
        set_global_upload_limit, get_global_upload_limit,
        set_task_download_limit, get_task_download_limit,
        set_task_upload_limit, get_task_upload_limit,
    },
    time_rule::{add_time_rule, get_all_time_rules, remove_time_rule},
    config::{get_config, update_config, get_config_item, update_config_item, reset_config},
};
use crate::api::websocket::ws_handler;
use crate::api::state::AppState;

/// Create the API router
pub fn create_router(
    app_state: AppState,
) -> Router {
    // 创建下载路由
    let download_router = Router::new()
        .route("/downloads", post(add_download))
        .route("/downloads/:id/start", post(start_download))
        .route("/downloads/:id/pause", post(pause_download))
        .route("/downloads/:id/resume", post(resume_download))
        .route("/downloads/:id/cancel", post(cancel_download))
        .route("/download/stats", get(get_download_stats))
        .route("/download/list", get(get_all_tasks)); // 添加新的路由

    // 创建任务路由
    let task_router = Router::new()
        .route("/tasks/:id", get(get_task))
        .route("/tasks", get(get_all_tasks))
        .route("/tasks/:id", put(update_task)) // 添加更新任务路由
        .route("/tasks/:id", delete(remove_task));

    // 创建状态路由
    let status_router = Router::new()
        .route("/status", get(get_status));

    // 创建WebSocket路由
    let ws_router = Router::new()
        .route("/ws", get(ws_handler));

    // 创建速度控制路由
    let speed_router = Router::new()
        .route("/speed/download", post(set_global_download_limit))
        .route("/speed/download", get(get_global_download_limit))
        .route("/speed/upload", post(set_global_upload_limit))
        .route("/speed/upload", get(get_global_upload_limit))
        .route("/tasks/:id/speed/download", post(set_task_download_limit))
        .route("/tasks/:id/speed/download", get(get_task_download_limit))
        .route("/tasks/:id/speed/upload", post(set_task_upload_limit))
        .route("/tasks/:id/speed/upload", get(get_task_upload_limit))
        .route("/speed/time-rules", post(add_time_rule))
        .route("/speed/time-rules", get(get_all_time_rules))
        .route("/speed/time-rules/:id", delete(remove_time_rule));

    // 创建配置路由
    let config_router = Router::new()
        .route("/config", get(get_config))
        .route("/config", post(update_config))
        .route("/config/reset", post(reset_config))
        .route("/config/:key", get(get_config_item))
        .route("/config/:key", post(update_config_item));

    // 合并所有路由
    let api_router = Router::new()
        .merge(download_router)
        .merge(task_router)
        .merge(status_router)
        .route("/version", get(get_status))
        .merge(speed_router)
        .merge(config_router)
        .route("/download/settings", get(get_config))
        .merge(ws_router);

    // 应用API前缀
    let router = Router::new()
        .nest("/api/v1", api_router)
        .with_state(app_state);

    // 返回路由
    router
}
