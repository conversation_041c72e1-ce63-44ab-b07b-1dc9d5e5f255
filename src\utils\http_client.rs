use reqwest::{C<PERSON>, C<PERSON><PERSON>uilder, Response, Proxy, NoProxy};
use std::time::Duration;
use anyhow::{Result, anyhow};
use tracing::{debug, info};
use crate::config::constants::{CONNECTION_TIMEOUT, READ_TIMEOUT};
use crate::config::{Settings, ConfigManager};
use crate::utils::proxy_detector::ProxyDetector;

/// URL 可访问性检查结果
#[derive(Debug, Clone)]
pub struct UrlAccessibilityResult {
    /// URL 是否可访问
    pub is_accessible: bool,
    /// HTTP 状态码（如果有）
    pub status_code: Option<u16>,
    /// 响应时间（如果成功）
    pub response_time: Option<std::time::Duration>,
    /// 错误信息（如果失败）
    pub error: Option<String>,
}

/// A wrapper around reqwest::Client with custom configuration
#[derive(Clone)]
pub struct HttpClient {
    client: Client,
}

use async_trait::async_trait;

/// HTTP client trait
#[async_trait]
pub trait HttpClientTrait: Send + Sync + 'static {
    /// Send a GET request
    async fn get(&self, url: &str) -> Result<reqwest::Response>;
    
    /// Send a HEAD request
    async fn head(&self, url: &str) -> Result<reqwest::Response>;
    
    /// Check if a URL is accessible
    async fn is_accessible(&self, url: &str) -> bool;
    
    /// Check URL accessibility with detailed result
    async fn check_url_accessibility(&self, url: &str) -> UrlAccessibilityResult;
    
    /// Get the content length of a resource
    async fn get_content_length(&self, url: &str) -> Result<Option<u64>>;
    
    /// Check if a URL supports range requests
    async fn supports_range(&self, url: &str) -> Result<bool>;
    
    /// Send a POST request
    async fn post(&self, url: &str, body: Vec<u8>) -> Result<reqwest::Response>;
    
    /// Send a PUT request
    async fn put(&self, url: &str, body: Vec<u8>) -> Result<reqwest::Response>;
    
    /// Send a DELETE request
    async fn delete(&self, url: &str) -> Result<reqwest::Response>;
    
    /// Send a GET request with custom headers
    async fn get_with_headers(&self, url: &str, headers: reqwest::header::HeaderMap) -> Result<reqwest::Response>;
    
    /// Get the underlying client
    fn inner(&self) -> &Client;
}

#[async_trait]
impl HttpClientTrait for HttpClient {
    async fn get(&self, url: &str) -> Result<reqwest::Response> {
        HttpClient::get(self, url).await
    }

    async fn head(&self, url: &str) -> Result<reqwest::Response> {
        HttpClient::head(self, url).await
    }

    async fn is_accessible(&self, url: &str) -> bool {
        HttpClient::is_accessible(self, url).await
    }
    
    async fn check_url_accessibility(&self, url: &str) -> UrlAccessibilityResult {
        HttpClient::check_url_accessibility(self, url).await
    }

    async fn get_content_length(&self, url: &str) -> Result<Option<u64>> {
        HttpClient::get_content_length(self, url).await
    }

    async fn supports_range(&self, url: &str) -> Result<bool> {
        HttpClient::supports_range(self, url).await
    }

    async fn post(&self, url: &str, body: Vec<u8>) -> Result<reqwest::Response> {
        HttpClient::post(self, url, body).await
    }

    async fn put(&self, url: &str, body: Vec<u8>) -> Result<reqwest::Response> {
        HttpClient::put(self, url, body).await
    }

    async fn delete(&self, url: &str) -> Result<reqwest::Response> {
        HttpClient::delete(self, url).await
    }

    async fn get_with_headers(&self, url: &str, headers: reqwest::header::HeaderMap) -> Result<reqwest::Response> {
        HttpClient::get_with_headers(self, url, headers).await
    }

    fn inner(&self) -> &Client {
        &self.client
    }
}

impl HttpClient {
    /// Create a new HttpClient with default configuration
    pub fn new() -> Result<Self> {
        let mut builder = ClientBuilder::new()
            .timeout(Duration::from_secs(CONNECTION_TIMEOUT))
            .connect_timeout(Duration::from_secs(CONNECTION_TIMEOUT))
            .pool_idle_timeout(Duration::from_secs(READ_TIMEOUT))
            .pool_max_idle_per_host(10)
            .user_agent(concat!(
                env!("CARGO_PKG_NAME"),
                "/",
                env!("CARGO_PKG_VERSION")
            ));

        // 尝试自动检测系统代理
        if let Some(proxy_config) = Self::auto_detect_proxy() {
            builder = Self::apply_proxy_config(builder, &proxy_config)?;
        }

        let client = builder.build()?;
        Ok(Self { client })
    }

    /// 自动检测系统代理设置
    fn auto_detect_proxy() -> Option<crate::config::settings::ProxyConfig> {
        ProxyDetector::detect_system_proxy()
    }

    /// Create a new HttpClient from settings
    pub fn from_settings(settings: &Settings) -> Result<Self> {
        let mut builder = ClientBuilder::new()
            .timeout(Duration::from_secs(CONNECTION_TIMEOUT))
            .connect_timeout(Duration::from_secs(CONNECTION_TIMEOUT))
            .pool_idle_timeout(Duration::from_secs(READ_TIMEOUT))
            .pool_max_idle_per_host(10)
            .user_agent(concat!(
                env!("CARGO_PKG_NAME"),
                "/",
                env!("CARGO_PKG_VERSION")
            ));

        // 添加代理配置
        if let Some(proxy_config) = &settings.proxy {
            if proxy_config.enabled && !proxy_config.url.is_empty() {
                builder = Self::apply_proxy_config(builder, proxy_config)?;
            }
        } else {
            // 如果设置中没有代理配置，尝试自动检测
            if let Some(auto_proxy) = Self::auto_detect_proxy() {
                info!("Using auto-detected proxy: {}", auto_proxy.url);
                builder = Self::apply_proxy_config(builder, &auto_proxy)?;
            }
        }

        let client = builder.build()?;
        Ok(Self { client })
    }

    /// 应用代理配置到 ClientBuilder
    fn apply_proxy_config(mut builder: ClientBuilder, proxy_config: &crate::config::settings::ProxyConfig) -> Result<ClientBuilder> {
        if !proxy_config.url.is_empty() {
            let mut proxy = match proxy_config.proxy_type.as_str() {
                "http" => Proxy::http(&proxy_config.url)?,
                "https" => Proxy::https(&proxy_config.url)?,
                "socks5" => Proxy::all(&proxy_config.url)?,
                _ => return Err(anyhow!("Unsupported proxy type: {}", proxy_config.proxy_type)),
            };

            // 添加代理认证
            if let (Some(username), Some(password)) = (&proxy_config.username, &proxy_config.password) {
                if !username.is_empty() && !password.is_empty() {
                    proxy = proxy.basic_auth(username, password);
                }
            }

            // 添加 no_proxy 配置
            if !proxy_config.no_proxy.is_empty() {
                debug!("No proxy domains: {:?}", proxy_config.no_proxy);

                // 将 no_proxy 列表转换为字符串
                let no_proxy_str = proxy_config.no_proxy.join(",");

                // 使用 NoProxy::from_string 创建 NoProxy 实例
                if let Some(no_proxy) = NoProxy::from_string(&no_proxy_str) {
                    proxy = proxy.no_proxy(Some(no_proxy));
                    debug!("No proxy configuration applied");
                }
            }

            builder = builder.proxy(proxy);
        }

        Ok(builder)
    }

    /// Create a new HttpClient with custom configuration
    pub fn with_config(
        timeout: Duration,
        connect_timeout: Duration,
        user_agent: Option<String>,
    ) -> Result<Self> {
        let mut builder = ClientBuilder::new()
            .timeout(timeout)
            .connect_timeout(connect_timeout);

        if let Some(ua) = user_agent {
            builder = builder.user_agent(ua);
        }

        // 尝试自动检测系统代理
        if let Some(proxy_config) = Self::auto_detect_proxy() {
            builder = Self::apply_proxy_config(builder, &proxy_config)?;
        }

        let client = builder.build()?;
        Ok(Self { client })
    }
    
    /// Create a new HttpClient from ConfigManager
    pub async fn from_config_manager(config_manager: &ConfigManager) -> Result<Self> {
        // 从ConfigManager获取Settings
        let settings = config_manager.get_settings().await;
        
        // 调用现有的from_settings方法
        Self::from_settings(&settings)
    }

    /// Send a GET request to the specified URL
    pub async fn get(&self, url: &str) -> Result<Response> {
        let response = self.client.get(url).send().await?;
        Ok(response)
    }

    /// Send a HEAD request to the specified URL
    pub async fn head(&self, url: &str) -> Result<Response> {
        let response = self.client.head(url).send().await?;
        Ok(response)
    }

    // UrlAccessibilityResult 结构体已移至模块级别

    /// 检查 URL 可访问性并返回详细结果
    /// 
    /// 此方法会发送 HEAD 请求检查 URL 是否可访问，并返回详细的可访问性信息
    /// 
    /// # 参数
    /// * `url` - 要检查的 URL
    /// 
    /// # 返回值
    /// * `UrlAccessibilityResult` - URL 可访问性的详细结果
    pub async fn check_url_accessibility(&self, url: &str) -> UrlAccessibilityResult {
        use tracing::{debug, warn};
        use std::time::Instant;
        
        debug!("检查 URL 可访问性: {}", url);
        let start_time = Instant::now();
        
        match self.head(url).await {
            Ok(response) => {
                let elapsed = start_time.elapsed();
                let status = response.status();
                let status_code = status.as_u16();
                let is_success = status.is_success();
                
                if is_success {
                    debug!("URL 可访问: {}, 状态码: {}, 响应时间: {:?}", url, status, elapsed);
                } else {
                    warn!("URL 不可访问: {}, 状态码: {}, 响应时间: {:?}", url, status, elapsed);
                }
                
                UrlAccessibilityResult {
                    is_accessible: is_success,
                    status_code: Some(status_code),
                    response_time: Some(elapsed),
                    error: None,
                }
            },
            Err(e) => {
                warn!("URL 访问失败: {}, 错误: {}", url, e);
                UrlAccessibilityResult {
                    is_accessible: false,
                    status_code: None,
                    response_time: None,
                    error: Some(e.to_string()),
                }
            },
        }
    }

    /// Check if a URL is accessible
    /// 检查 URL 是否可访问，返回可访问状态
    /// 
    /// 此方法会发送 HEAD 请求检查 URL 是否可访问，并记录详细日志
    /// 
    /// # 参数
    /// * `url` - 要检查的 URL
    /// 
    /// # 返回值
    /// * `bool` - URL 是否可访问
    pub async fn is_accessible(&self, url: &str) -> bool {
        let result = self.check_url_accessibility(url).await;
        result.is_accessible
    }

    /// Get the content length of a resource
    pub async fn get_content_length(&self, url: &str) -> Result<Option<u64>> {
        let response = self.head(url).await?;
        let content_length = response
            .headers()
            .get(reqwest::header::CONTENT_LENGTH)
            .and_then(|h| h.to_str().ok())
            .and_then(|s| s.parse::<u64>().ok());

        Ok(content_length)
    }

    /// Check if a URL supports range requests
    /// 检查 URL 是否支持范围请求
    /// 
    /// 此方法会发送 HEAD 请求检查 URL 是否支持范围请求，并记录详细日志
    /// 
    /// # 参数
    /// * `url` - 要检查的 URL
    /// 
    /// # 返回值
    /// * `Result<bool>` - URL 是否支持范围请求
    pub async fn supports_range(&self, url: &str) -> Result<bool> {
        use tracing::{debug, warn};
        
        debug!("检查 URL 是否支持范围请求: {}", url);
        
        let response = self.head(url).await?;
        let accept_ranges = response
            .headers()
            .get(reqwest::header::ACCEPT_RANGES)
            .and_then(|h| h.to_str().ok())
            .map(|s| s.contains("bytes"))
            .unwrap_or(false);

        if accept_ranges {
            debug!("URL 支持范围请求: {}", url);
        } else {
            debug!("URL 不支持范围请求: {}", url);
        }

        Ok(accept_ranges)
    }

    /// Send a POST request to the specified URL
    pub async fn post(&self, url: &str, body: Vec<u8>) -> Result<Response> {
        let response = self.client.post(url).body(body).send().await?;
        Ok(response)
    }

    /// Send a GET request to the specified URL with custom headers
    pub async fn get_with_headers(&self, url: &str, headers: reqwest::header::HeaderMap) -> Result<Response> {
        let response = self.client.get(url).headers(headers).send().await?;
        Ok(response)
    }

    /// Send a PUT request to the specified URL
    pub async fn put(&self, url: &str, body: Vec<u8>) -> Result<Response> {
        let response = self.client.put(url).body(body).send().await?;
        Ok(response)
    }

    /// Send a DELETE request to the specified URL
    pub async fn delete(&self, url: &str) -> Result<Response> {
        let response = self.client.delete(url).send().await?;
        Ok(response)
    }

    /// Get the underlying reqwest client
    pub fn inner(&self) -> &Client {
        &self.client
    }
}
