//! CLI 性能分析工具模块
//!
//! 提供CLI的性能分析功能，用于监控和优化程序性能。

use std::collections::HashMap;
use std::time::{Duration, Instant};
use log::{debug, info, trace};
use colored::Colorize;

/// 性能分析级别
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ProfileLevel {
    /// 基本分析
    Basic,
    /// 详细分析
    Detailed,
    /// 完整分析
    Full,
}

/// 性能分析器
pub struct Profiler {
    /// 分析器名称
    name: String,
    /// 开始时间
    start: Instant,
    /// 检查点时间
    checkpoints: HashMap<String, Duration>,
    /// 分析级别
    level: ProfileLevel,
    /// 是否启用
    enabled: bool,
}

/// 全局性能分析开关
static mut PROFILING_ENABLED: bool = false;

/// 设置全局性能分析开关
pub fn set_profiling_enabled(enabled: bool) {
    unsafe {
        PROFILING_ENABLED = enabled;
    }
}

/// 获取全局性能分析开关状态
pub fn is_profiling_enabled() -> bool {
    unsafe {
        PROFILING_ENABLED
    }
}

impl Profiler {
    /// 创建新的性能分析器
    pub fn new(name: &str, level: ProfileLevel) -> Self {
        // 只在debug模式下启用性能分析，并且只有当全局开关打开时才启用
        let enabled = cfg!(debug_assertions) && is_profiling_enabled();
        
        if enabled {
            debug!("开始性能分析: {}", name);
        }
        
        Self {
            name: name.to_string(),
            start: Instant::now(),
            checkpoints: HashMap::new(),
            level,
            enabled,
        }
    }

    /// 记录检查点
    pub fn checkpoint(&mut self, name: &str) {
        if !self.enabled {
            return;
        }
        
        let elapsed = self.start.elapsed();
        match self.level {
            ProfileLevel::Basic => {
                debug!("性能检查点 {}: {} - 耗时: {:?}", self.name, name, elapsed);
            },
            ProfileLevel::Detailed => {
                trace!("性能检查点 {}: {} - 耗时: {:?}", self.name, name, elapsed);
            },
            ProfileLevel::Full => {
                trace!("性能检查点 {}: {} - 耗时: {:?} (详细)", self.name, name, elapsed);
                // 在完整分析模式下，记录内存使用情况等更多信息
                #[cfg(target_os = "linux")]
                {
                    if let Ok(mem_info) = std::fs::read_to_string("/proc/self/status") {
                        if let Some(vm_line) = mem_info.lines().find(|l| l.starts_with("VmRSS:")) {
                            trace!("内存使用: {}", vm_line.trim());
                        }
                    }
                }
            },
        }
        
        self.checkpoints.insert(name.to_string(), elapsed);
    }

    /// 结束分析并返回总耗时
    pub fn stop(self) -> Duration {
        if !self.enabled {
            return Duration::from_secs(0);
        }
        
        let elapsed = self.start.elapsed();
        
        match self.level {
            ProfileLevel::Basic => {
                info!("性能分析结束: {} - 总耗时: {:?}", self.name, elapsed);
            },
            ProfileLevel::Detailed | ProfileLevel::Full => {
                debug!("性能分析结束: {} - 总耗时: {:?}", self.name, elapsed);
                
                // 打印所有检查点的耗时
                if !self.checkpoints.is_empty() {
                    debug!("检查点详情:");
                    let mut checkpoints: Vec<_> = self.checkpoints.iter().collect();
                    checkpoints.sort_by_key(|(_, duration)| *duration);
                    
                    for (name, duration) in checkpoints {
                        let percentage = duration.as_secs_f64() / elapsed.as_secs_f64() * 100.0;
                        debug!("  {} - {:?} ({:.2}%)", name, duration, percentage);
                    }
                }
            },
        }
        
        elapsed
    }

    /// 获取从开始到现在的耗时
    pub fn elapsed(&self) -> Duration {
        self.start.elapsed()
    }

    /// 获取特定检查点的耗时
    pub fn checkpoint_elapsed(&self, name: &str) -> Option<Duration> {
        self.checkpoints.get(name).cloned()
    }
}

/// 打印性能报告
pub fn print_performance_report(title: &str, report: &HashMap<String, Duration>) {
    info!("{}", title.green().bold());
    
    // 找出最长的操作名称，用于对齐输出
    let max_len = report.keys().map(|k| k.len()).max().unwrap_or(0);
    
    // 找出总执行时间
    let total_time = report.get("总执行时间").cloned().unwrap_or_default();
    
    // 按耗时排序
    let mut items: Vec<_> = report.iter().collect();
    items.sort_by(|a, b| b.1.cmp(a.1));
    
    // 计算总耗时的毫秒数，用于更精确的百分比计算
    let total_ms = total_time.as_millis() as f64;
    
    for (operation, duration) in items {
        let duration_ms = duration.as_millis() as f64;
        let percentage = if total_ms > 0.0 {
            (duration_ms / total_ms) * 100.0
        } else {
            0.0
        };
        
        // 根据百分比选择颜色
        let color = if percentage > 20.0 {
            "red"
        } else if percentage > 10.0 {
            "yellow"
        } else if percentage > 5.0 {
            "cyan"
        } else {
            "green"
        };
        
        // 格式化持续时间，根据大小选择合适的单位
        let duration_str = if duration.as_secs() > 1 {
            format!("{:.2}s", duration.as_secs_f64())
        } else if duration.as_millis() > 1 {
            format!("{:.2}ms", duration.as_millis() as f64)
        } else {
            format!("{:.2}μs", duration.as_micros() as f64)
        };
        
        let percentage_str = format!("{:.2}%", percentage);
        
        // 添加进度条可视化
        let bar_width = 20;
        let filled_width = (percentage * bar_width as f64 / 100.0).round() as usize;
        let bar = format!(
            "[{}{}]",
            "=".repeat(filled_width),
            " ".repeat(bar_width - filled_width)
        );
        
        info!(
            "{:width$} - {} {} ({})",
            operation,
            duration_str.color(color),
            bar.color(color),
            percentage_str.color(color),
            width = max_len
        );
    }
    
    // 添加总结信息
    info!("{}", "=".repeat(max_len + 40).dimmed());
    info!(
        "总计: {} 操作, 总耗时: {}", 
        report.len(), 
        format!("{:.2}s", total_time.as_secs_f64()).green().bold()
    );
}

/// 性能监控上下文
pub struct PerformanceContext {
    /// 操作名称
    operation: String,
    /// 开始时间
    start: Instant,
    /// 是否已完成
    completed: bool,
}

impl PerformanceContext {
    /// 创建新的性能监控上下文
    pub fn new(operation: &str) -> Self {
        trace!("开始操作: {}", operation);
        Self {
            operation: operation.to_string(),
            start: Instant::now(),
            completed: false,
        }
    }

    /// 完成操作并返回耗时
    pub fn complete(mut self) -> Duration {
        let elapsed = self.start.elapsed();
        trace!("完成操作: {} - 耗时: {:?}", self.operation, elapsed);
        self.completed = true;
        elapsed
    }
}

impl Drop for PerformanceContext {
    fn drop(&mut self) {
        if !self.completed {
            let elapsed = self.start.elapsed();
            debug!("操作未正常完成: {} - 耗时: {:?}", self.operation, elapsed);
        }
    }
}