#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Settings;
    use crate::storage::storage_impl::LocalStorage;
    use crate::download::resume::{ResumeManager, ResumeManagerImpl, ResumePoint};
    use crate::download::manager::TaskInfo;
    use chrono::Utc;
    use std::sync::Arc;
    use std::path::PathBuf;
    use tokio::fs;
    use uuid::Uuid;

    // 创建临时目录
    async fn create_temp_dir() -> PathBuf {
        let temp_dir = PathBuf::from("./target/test_temp");
        let _ = fs::create_dir_all(&temp_dir).await;
        temp_dir
    }

    // 清理临时目录
    async fn cleanup_temp_dir(temp_dir: &PathBuf) {
        let _ = fs::remove_dir_all(temp_dir).await;
    }

    // 创建测试任务
    fn create_test_task() -> TaskInfo {
        TaskInfo {
            id: Uuid::new_v4(),
            url: "https://example.com/test.zip".to_string(),
            output_path: "./target/test_temp/test.zip".to_string(),
            status: crate::download::manager::TaskStatus::Downloading,
            progress: 50.0,
            speed: 1024,
            total_size: Some(1024 * 1024),
            downloaded_size: 512 * 1024,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            error_message: None,
        }
    }

    #[tokio::test]
    async fn test_save_and_load_resume_point() {
        // 创建临时目录
        let temp_dir = create_temp_dir().await;

        // 创建设置
        let mut settings = Settings::default();
        settings.download.path = temp_dir.to_string_lossy().to_string();

        // 创建存储
        let storage = Arc::new(LocalStorage::new(&temp_dir));

        // 创建恢复管理器
        let resume_manager = ResumeManagerImpl::new(settings, storage);
        resume_manager.init().await.unwrap();

        // 创建测试任务
        let task = create_test_task();

        // 保存恢复点
        resume_manager.save_resume_point(&task).await.unwrap();

        // 加载恢复点
        let resume_point = resume_manager.load_resume_point(task.id).await.unwrap();

        // 验证恢复点
        assert!(resume_point.is_some());
        let resume_point = resume_point.unwrap();
        assert_eq!(resume_point.task_id, task.id);
        assert_eq!(resume_point.url, task.url);
        assert_eq!(resume_point.output_path, task.output_path);
        assert_eq!(resume_point.downloaded_size, task.downloaded_size);
        assert_eq!(resume_point.total_size, task.total_size);

        // 删除恢复点
        resume_manager.delete_resume_point(task.id).await.unwrap();

        // 验证恢复点已删除
        let resume_point = resume_manager.load_resume_point(task.id).await.unwrap();
        assert!(resume_point.is_none());

        // 清理临时目录
        cleanup_temp_dir(&temp_dir).await;
    }

    #[tokio::test]
    async fn test_get_all_resume_points() {
        // 创建临时目录
        let temp_dir = create_temp_dir().await;

        // 创建设置
        let mut settings = Settings::default();
        settings.download.path = temp_dir.to_string_lossy().to_string();

        // 创建存储
        let storage = Arc::new(LocalStorage::new(&temp_dir));

        // 创建恢复管理器
        let resume_manager = ResumeManagerImpl::new(settings, storage);
        resume_manager.init().await.unwrap();

        // 创建多个测试任务
        let task1 = create_test_task();
        let task2 = create_test_task();
        let task3 = create_test_task();

        // 保存恢复点
        resume_manager.save_resume_point(&task1).await.unwrap();
        resume_manager.save_resume_point(&task2).await.unwrap();
        resume_manager.save_resume_point(&task3).await.unwrap();

        // 获取所有恢复点
        let resume_points = resume_manager.get_all_resume_points().await.unwrap();

        // 验证恢复点数量
        assert_eq!(resume_points.len(), 3);

        // 清理临时目录
        cleanup_temp_dir(&temp_dir).await;
    }
}
