use anyhow::Result;
use async_trait::async_trait;
use uuid::Uuid;

use super::models::{TimeBasedSpeedRule, TaskPriority, BandwidthStats};

/// Bandwidth scheduler interface
#[async_trait]
pub trait BandwidthScheduler: Send + Sync {
    /// Set global download speed limit
    async fn set_global_download_limit(&self, bytes_per_second: Option<u64>) -> Result<()>;

    /// Get global download speed limit
    async fn get_global_download_limit(&self) -> Result<Option<u64>>;

    /// Set task download speed limit
    async fn set_task_download_limit(&self, task_id: Uuid, bytes_per_second: Option<u64>) -> Result<()>;

    /// Get task download speed limit
    async fn get_task_download_limit(&self, task_id: Uuid) -> Result<Option<u64>>;

    /// Wait for download quota
    async fn wait_for_download_quota(&self, task_id: Uuid, bytes: usize) -> Result<()>;

    /// Set global upload speed limit
    async fn set_global_upload_limit(&self, bytes_per_second: Option<u64>) -> Result<()>;

    /// Get global upload speed limit
    async fn get_global_upload_limit(&self) -> Result<Option<u64>>;

    /// Set task upload speed limit
    async fn set_task_upload_limit(&self, task_id: Uuid, bytes_per_second: Option<u64>) -> Result<()>;

    /// Get task upload speed limit
    async fn get_task_upload_limit(&self, task_id: Uuid) -> Result<Option<u64>>;

    /// Wait for upload quota
    async fn wait_for_upload_quota(&self, task_id: Uuid, bytes: usize) -> Result<()>;

    /// Add a time-based speed limit rule
    async fn add_time_based_rule(&self, rule: TimeBasedSpeedRule) -> Result<Uuid>;

    /// Remove a time-based speed limit rule
    async fn remove_time_based_rule(&self, rule_id: Uuid) -> Result<()>;

    /// Get all time-based speed limit rules
    async fn get_time_based_rules(&self) -> Result<Vec<TimeBasedSpeedRule>>;

    /// Update speed limits based on time
    async fn update_time_based_limits(&self) -> Result<()>;

    /// Update download statistics
    async fn update_download_stats(&self, task_id: Uuid, bytes: u64) -> Result<()>;

    /// Update upload statistics
    async fn update_upload_stats(&self, task_id: Uuid, bytes: u64) -> Result<()>;
    
    /// Get bandwidth statistics for a task
    async fn get_task_stats(&self, task_id: Uuid) -> Result<BandwidthStats>;
    
    /// Get global bandwidth statistics
    async fn get_global_stats(&self) -> Result<BandwidthStats>;
    
    /// Set task priority
    async fn set_task_priority(&self, task_id: Uuid, priority: TaskPriority) -> Result<()>;
    
    /// Get task priority
    async fn get_task_priority(&self, task_id: Uuid) -> Result<TaskPriority>;
}
