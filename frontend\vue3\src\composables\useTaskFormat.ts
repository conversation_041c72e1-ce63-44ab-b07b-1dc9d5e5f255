import type { Task } from '../stores/task';
import { statusMap } from '../stores/task';

/**
 * 任务格式化相关的composable
 */
export function useTaskFormat() {
  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化后的大小字符串
   */
  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    if (isNaN(bytes) || !isFinite(bytes)) return '-';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
  };

  /**
   * 计算任务进度百分比
   * @param task 任务对象
   * @returns 进度百分比
   */
  const calculatePercentage = (task: Task): number => {
    const total = Number(task.totalLength);
    const completed = Number(task.completedLength);
    
    if (total === 0 || isNaN(total) || isNaN(completed)) {
      return 0;
    }
    
    const percentage = (completed / total) * 100;
    return Math.min(100, Math.max(0, percentage));
  };

  /**
   * 格式化任务进度
   * @param task 任务对象
   * @returns 格式化后的进度字符串
   */
  const formatProgress = (task: Task): string => {
    const total = Number(task.totalLength);
    const completed = Number(task.completedLength);
    
    if (total === 0 || isNaN(total) || isNaN(completed)) {
      return '未知';
    }
    
    const percentage = (completed / total) * 100;
    return `${percentage.toFixed(1)}%`;
  };

  /**
   * 格式化任务状态
   * @param status 任务状态
   * @returns 格式化后的状态字符串
   */
  const formatStatus = (status: string): string => {
    return statusMap[status as keyof typeof statusMap] || status;
  };

  /**
   * 格式化速度
   * @param bytes 字节数
   * @returns 格式化后的速度字符串
   */
  const formatSpeed = (bytes: number): string => {
    if (bytes === 0) return '0 B/s';
    if (isNaN(bytes) || !isFinite(bytes)) return '-';
    
    const units = ['B/s', 'KB/s', 'MB/s', 'GB/s', 'TB/s'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
  };

  return {
    formatSize,
    calculatePercentage,
    formatProgress,
    formatStatus,
    formatSpeed
  };
}