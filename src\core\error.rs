use thiserror::<PERSON>rror;
use std::io;

/// Core error types for the application
#[derive(Erro<PERSON>, Debug)]
pub enum CoreError {
    #[error("IO error: {0}")]
    Io(#[from] io::Error),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Download error: {0}")]
    Download(String),

    #[error("Protocol error: {0}")]
    Protocol(String),

    #[error("Storage error: {0}")]
    Storage(String),

    #[error("Task error: {0}")]
    Task(String),

    #[error("Network error: {0}")]
    Network(String),

    #[error("Authentication error: {0}")]
    Auth(String),

    #[error("Serialization error: {0}")]
    Serialization(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Already exists: {0}")]
    AlreadyExists(String),

    #[error("Invalid argument: {0}")]
    InvalidArgument(String),

    #[error("Timeout: {0}")]
    Timeout(String),

    #[error("Unsupported: {0}")]
    Unsupported(String),

    #[error("Internal error: {0}")]
    Internal(String),

    #[error("Plugin error: {0}")]
    Plugin(String),

    #[error("Invalid state: {0}")]
    InvalidState(String),

    #[error("Parse error: {0}")]
    Parse(String),

    #[error("IO error: {0}")]
    IO(String),

    #[error("Not implemented: {0}")]
    NotImplemented(String),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("{0}")]
    Other(String),
}

/// Result type alias using CoreError
pub type CoreResult<T> = Result<T, CoreError>;

/// Convert anyhow::Error to CoreError
impl From<anyhow::Error> for CoreError {
    fn from(err: anyhow::Error) -> Self {
        CoreError::Other(err.to_string())
    }
}

/// Convert String to CoreError
impl From<String> for CoreError {
    fn from(err: String) -> Self {
        CoreError::Other(err)
    }
}

/// Convert &str to CoreError
impl From<&str> for CoreError {
    fn from(err: &str) -> Self {
        CoreError::Other(err.to_string())
    }
}

/// Convert reqwest::Error to CoreError
impl From<reqwest::Error> for CoreError {
    fn from(err: reqwest::Error) -> Self {
        if err.is_timeout() {
            CoreError::Timeout(err.to_string())
        } else if err.is_connect() {
            CoreError::Network(err.to_string())
        } else {
            CoreError::Other(err.to_string())
        }
    }
}

/// Convert serde_json::Error to CoreError
impl From<serde_json::Error> for CoreError {
    fn from(err: serde_json::Error) -> Self {
        CoreError::Serialization(err.to_string())
    }
}

/// Convert config::ConfigError to CoreError
impl From<config::ConfigError> for CoreError {
    fn from(err: config::ConfigError) -> Self {
        CoreError::Config(err.to_string())
    }
}

/// Convert sqlx::Error to CoreError
impl From<sqlx::Error> for CoreError {
    fn from(err: sqlx::Error) -> Self {
        match err {
            sqlx::Error::RowNotFound => CoreError::NotFound("Database record not found".to_string()),
            sqlx::Error::Database(e) => CoreError::Storage(e.to_string()),
            sqlx::Error::Io(e) => CoreError::Io(e),
            _ => CoreError::Storage(err.to_string()),
        }
    }
}
