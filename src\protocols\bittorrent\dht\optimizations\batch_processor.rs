use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::Result;
use futures::future::join_all;
use tokio::sync::{mpsc, Mutex, RwLock};
use tokio::time::interval;
use tracing::info;

use crate::protocols::bittorrent::dht::message::DHTResponse;
use crate::protocols::bittorrent::dht::{DHTMessageType, DHTQuery};
use crate::protocols::bittorrent::dht::client::DHTClient;

/// 批处理配置
#[derive(Debug, Clone)]
pub struct BatchProcessorConfig {
    /// 批处理间隔（毫秒）
    pub batch_interval: u64,
    /// 最大批处理大小
    pub max_batch_size: usize,
    /// 最大队列大小
    pub max_queue_size: usize,
}

impl Default for BatchProcessorConfig {
    fn default() -> Self {
        Self {
            batch_interval: 50, // 50毫秒
            max_batch_size: 100,
            max_queue_size: 10000,
        }
    }
}

/// 查询项
struct QueryItem {
    /// 目标地址
    addr: SocketAddr,
    /// 消息类型
    message_type: DHTMessageType,
    /// 查询
    query: DHTQuery,
    /// 响应通道
    response_tx: mpsc::Sender<Result<DHTResponse>>,
    /// 创建时间
    created_at: Instant,
}

/// DHT批处理器
pub struct DHTBatchProcessor {
    /// DHT客户端
    client: Arc<DHTClient>,
    /// 查询队列
    queue: Arc<Mutex<Vec<QueryItem>>>,
    /// 配置
    config: BatchProcessorConfig,
    /// 是否正在运行
    running: RwLock<bool>,
    /// 任务句柄
    task_handle: Mutex<Option<tokio::task::JoinHandle<()>>>,
}

impl DHTBatchProcessor {
    /// 创建新的DHT批处理器
    pub fn new(client: Arc<DHTClient>, config: BatchProcessorConfig) -> Self {
        Self {
            client,
            queue: Arc::new(Mutex::new(Vec::new())),
            config,
            running: RwLock::new(false),
            task_handle: Mutex::new(None),
        }
    }

    /// 启动批处理器
    pub async fn start(&self) -> Result<()> {
        let mut running = self.running.write().await;

        if *running {
            return Ok(());
        }

        *running = true;

        // 创建批处理任务
        let client = self.client.clone();
        let queue = Arc::clone(&self.queue);
        let config = self.config.clone();

        let handle = tokio::spawn(async move {
            let mut interval_timer = interval(Duration::from_millis(config.batch_interval));

            loop {
                interval_timer.tick().await;

                // 获取批处理项
                let batch = {
                    let mut queue = queue.lock().await;

                    // 取出最多max_batch_size个项
                    let batch_size = std::cmp::min(queue.len(), config.max_batch_size);
                    let batch: Vec<_> = queue.drain(0..batch_size).collect();

                    batch
                };

                if batch.is_empty() {
                    continue;
                }

                // 按地址分组
                let mut groups: HashMap<SocketAddr, Vec<(DHTMessageType, DHTQuery, mpsc::Sender<Result<DHTResponse>>)>> = HashMap::new();

                for item in batch {
                    groups.entry(item.addr)
                        .or_insert_with(Vec::new)
                        .push((item.message_type, item.query, item.response_tx));
                }

                // 处理每个组
                let mut tasks = Vec::new();

                for (addr, queries) in groups {
                    let client = client.clone();

                    let task = tokio::spawn(async move {
                        for (message_type, query, response_tx) in queries {
                            // 发送查询
                            let result = client.send_query(addr, message_type, query).await;

                            // 发送响应
                            let _ = response_tx.send(result).await;
                        }
                    });

                    tasks.push(task);
                }

                // 等待所有任务完成
                join_all(tasks).await;
            }
        });

        // 保存任务句柄
        let mut task_handle = self.task_handle.lock().await;
        *task_handle = Some(handle);

        info!("DHT batch processor started");

        Ok(())
    }

    /// 停止批处理器
    pub async fn stop(&self) -> Result<()> {
        let mut running = self.running.write().await;

        if !*running {
            return Ok(());
        }

        *running = false;

        // 取消任务
        let mut task_handle = self.task_handle.lock().await;
        if let Some(handle) = task_handle.take() {
            handle.abort();
        }

        // 清空队列
        let mut queue = self.queue.lock().await;
        queue.clear();

        info!("DHT batch processor stopped");

        Ok(())
    }

    /// 发送查询
    pub async fn send_query(&self, addr: SocketAddr, message_type: DHTMessageType, query: DHTQuery) -> Result<DHTResponse> {
        // 创建响应通道
        let (tx, mut rx) = mpsc::channel(1);

        // 添加到队列
        {
            let mut queue = self.queue.lock().await;

            // 检查队列大小
            if queue.len() >= self.config.max_queue_size {
                return Err(anyhow::anyhow!("Query queue is full"));
            }

            queue.push(QueryItem {
                addr,
                message_type,
                query,
                response_tx: tx,
                created_at: Instant::now(),
            });
        }

        // 等待响应
        match rx.recv().await {
            Some(result) => result,
            None => Err(anyhow::anyhow!("Response channel closed")),
        }
    }

    /// 获取队列大小
    pub async fn get_queue_size(&self) -> usize {
        let queue = self.queue.lock().await;
        queue.len()
    }

    /// 是否正在运行
    pub async fn is_running(&self) -> bool {
        *self.running.read().await
    }
}
