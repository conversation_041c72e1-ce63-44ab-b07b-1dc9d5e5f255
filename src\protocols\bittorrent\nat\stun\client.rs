use std::collections::HashMap;
use std::net::{IpAddr, SocketAddr};
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use tokio::net::UdpSocket;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use super::message::{StunMessage, StunMessageType};
use super::server_pool::StunServerPool;
use crate::protocols::bittorrent::nat::nat_detector::NATType;

/// STUN客户端配置
#[derive(Debug, Clone)]
pub struct StunClientConfig {
    /// STUN服务器列表
    pub servers: Vec<String>,
    /// 请求超时时间（秒）
    pub request_timeout: u64,
    /// 缓存有效期（秒）
    pub cache_ttl: u64,
}

impl Default for StunClientConfig {
    fn default() -> Self {
        Self {
            servers: Vec::new(),
            request_timeout: 5,
            cache_ttl: 300, // 5分钟
        }
    }
}

/// STUN客户端
pub struct StunClient {
    /// 服务器池
    servers: Arc<StunServerPool>,
    /// 绑定缓存
    binding_cache: RwLock<HashMap<SocketAddr, (SocketAddr, Instant)>>,
    /// 配置
    config: StunClientConfig,
}

impl StunClient {
    /// 创建新的STUN客户端
    pub fn new(config: StunClientConfig) -> Self {
        let servers = Arc::new(StunServerPool::new(config.servers.clone()));

        Self {
            servers,
            binding_cache: RwLock::new(HashMap::new()),
            config,
        }
    }

    /// 发现公网地址映射
    pub async fn discover_binding(&self, local_addr: SocketAddr) -> Result<SocketAddr> {
        // 检查缓存
        {
            let cache = self.binding_cache.read().await;
            if let Some((mapped_addr, timestamp)) = cache.get(&local_addr) {
                if timestamp.elapsed().as_secs() < self.config.cache_ttl {
                    debug!("Using cached STUN binding: {} -> {}", local_addr, mapped_addr);
                    return Ok(*mapped_addr);
                }
            }
        }

        // 创建UDP套接字
        let socket = UdpSocket::bind(local_addr).await?;

        // 获取STUN服务器
        let server_addr = self.servers.get_server().await?;

        // 创建STUN请求
        let request = StunMessage::new_binding_request();

        // 发送请求
        socket.send_to(&request.encode()?, server_addr).await?;
        debug!("Sent STUN binding request to {}", server_addr);

        // 等待响应
        let mut buf = vec![0u8; 1024];
        let timeout = Duration::from_secs(self.config.request_timeout);

        let (len, _) = match tokio::time::timeout(timeout, socket.recv_from(&mut buf)).await {
            Ok(Ok(result)) => result,
            Ok(Err(e)) => {
                self.servers.update_server_status(server_addr, false).await;
                return Err(anyhow!("Failed to receive STUN response: {}", e));
            },
            Err(_) => {
                self.servers.update_server_status(server_addr, false).await;
                return Err(anyhow!("STUN request timed out"));
            },
        };

        // 解码响应
        let response = match StunMessage::decode(&buf[..len]) {
            Ok(msg) => msg,
            Err(e) => {
                self.servers.update_server_status(server_addr, false).await;
                return Err(anyhow!("Failed to decode STUN response: {}", e));
            },
        };

        // 检查响应类型
        if response.message_type != StunMessageType::BindingResponse {
            self.servers.update_server_status(server_addr, false).await;
            return Err(anyhow!("Unexpected STUN response type: {:?}", response.message_type));
        }

        // 提取映射地址
        let mapped_addr = match response.get_mapped_address() {
            Ok(addr) => addr,
            Err(e) => {
                self.servers.update_server_status(server_addr, false).await;
                return Err(anyhow!("Failed to get mapped address from STUN response: {}", e));
            },
        };

        // 更新服务器状态
        self.servers.update_server_status(server_addr, true).await;

        // 更新缓存
        {
            let mut cache = self.binding_cache.write().await;
            cache.insert(local_addr, (mapped_addr, Instant::now()));
        }

        info!("Discovered STUN binding: {} -> {}", local_addr, mapped_addr);

        Ok(mapped_addr)
    }

    /// 检测NAT类型
    pub async fn detect_nat_type(&self) -> Result<NATType> {
        // 创建UDP套接字
        let socket1 = UdpSocket::bind("0.0.0.0:0").await?;
        let local_addr1 = socket1.local_addr()?;

        // 获取STUN服务器
        let server_addr = self.servers.get_server().await?;

        // 第一次测试：获取映射地址
        let request1 = StunMessage::new_binding_request();
        socket1.send_to(&request1.encode()?, server_addr).await?;

        let mut buf = vec![0u8; 1024];
        let timeout = Duration::from_secs(self.config.request_timeout);

        let (len, _) = match tokio::time::timeout(timeout, socket1.recv_from(&mut buf)).await {
            Ok(Ok(result)) => result,
            Ok(Err(e)) => return Err(anyhow!("Failed to receive STUN response: {}", e)),
            Err(_) => return Err(anyhow!("STUN request timed out")),
        };

        let response1 = StunMessage::decode(&buf[..len])?;
        let mapped_addr1 = response1.get_mapped_address()?;

        // 创建第二个套接字
        let socket2 = UdpSocket::bind("0.0.0.0:0").await?;
        let local_addr2 = socket2.local_addr()?;

        // 第二次测试：使用不同的本地端口
        let request2 = StunMessage::new_binding_request();
        socket2.send_to(&request2.encode()?, server_addr).await?;

        let (len, _) = match tokio::time::timeout(timeout, socket2.recv_from(&mut buf)).await {
            Ok(Ok(result)) => result,
            Ok(Err(e)) => return Err(anyhow!("Failed to receive STUN response: {}", e)),
            Err(_) => return Err(anyhow!("STUN request timed out")),
        };

        let response2 = StunMessage::decode(&buf[..len])?;
        let mapped_addr2 = response2.get_mapped_address()?;

        // 分析NAT类型
        if mapped_addr1.ip() == local_addr1.ip() {
            // 没有NAT
            debug!("Detected NAT type: Open Internet (No NAT)");
            return Ok(NATType::Open);
        } else if mapped_addr1.port() != mapped_addr2.port() {
            // 对称NAT
            debug!("Detected NAT type: Symmetric NAT");
            return Ok(NATType::Symmetric);
        }

        // 如果到这里，可能是完全锥形、受限锥形或端口受限锥形NAT
        // 需要进一步测试

        // 尝试获取第二个STUN服务器
        let server_addr2 = match self.servers.get_alternate_server(server_addr).await {
            Ok(addr) => addr,
            Err(_) => {
                // 如果没有第二个服务器，我们无法区分完全锥形和受限锥形
                // 假设是完全锥形（最宽松的）
                debug!("No alternate STUN server available, assuming Full Cone NAT");
                return Ok(NATType::FullCone);
            }
        };

        // 第三次测试：使用不同的服务器地址
        let request3 = StunMessage::new_binding_request();
        socket1.send_to(&request3.encode()?, server_addr2).await?;

        let result = tokio::time::timeout(timeout, socket1.recv_from(&mut buf)).await;

        match result {
            Ok(Ok((len, _))) => {
                let response3 = StunMessage::decode(&buf[..len])?;
                let mapped_addr3 = response3.get_mapped_address()?;

                if mapped_addr3.port() == mapped_addr1.port() {
                    // 完全锥形NAT
                    debug!("Detected NAT type: Full Cone NAT");
                    return Ok(NATType::FullCone);
                } else {
                    // 对称NAT（不同服务器得到不同映射）
                    debug!("Detected NAT type: Symmetric NAT (different mapping for different servers)");
                    return Ok(NATType::Symmetric);
                }
            },
            _ => {
                // 如果无法从第二个服务器获取响应，我们需要进行更多测试
                // 创建第三个套接字用于测试受限锥形和端口受限锥形
                let socket3 = UdpSocket::bind("0.0.0.0:0").await?;

                // 发送请求到第一个服务器，获取映射地址
                let request4 = StunMessage::new_binding_request();
                socket3.send_to(&request4.encode()?, server_addr).await?;

                let (len, _) = match tokio::time::timeout(timeout, socket3.recv_from(&mut buf)).await {
                    Ok(Ok(result)) => result,
                    Ok(Err(e)) => return Err(anyhow!("Failed to receive STUN response: {}", e)),
                    Err(_) => return Err(anyhow!("STUN request timed out")),
                };

                let response4 = StunMessage::decode(&buf[..len])?;
                let mapped_addr4 = response4.get_mapped_address()?;

                // 创建一个变更请求，要求服务器从不同的IP发送响应（测试受限锥形NAT）
                let change_request_ip = StunMessage::new_binding_request_with_change(true, false);
                socket3.send_to(&change_request_ip.encode()?, server_addr).await?;

                // 尝试接收响应（测试IP限制）
                let ip_change_result = tokio::time::timeout(timeout, socket3.recv_from(&mut buf)).await;

                match ip_change_result {
                    Ok(Ok(_)) => {
                        // 如果收到响应，说明NAT允许来自不同IP的响应
                        // 现在测试是否允许来自不同端口的响应

                        // 创建一个变更请求，要求服务器从不同的端口发送响应
                        let change_request_port = StunMessage::new_binding_request_with_change(false, true);
                        socket3.send_to(&change_request_port.encode()?, server_addr).await?;

                        // 尝试接收响应（测试端口限制）
                        let port_change_result = tokio::time::timeout(timeout, socket3.recv_from(&mut buf)).await;

                        match port_change_result {
                            Ok(Ok(_)) => {
                                // 如果收到响应，说明是完全锥形NAT（允许来自任何IP和端口的响应）
                                debug!("Detected NAT type: Full Cone NAT (alternative test)");
                                return Ok(NATType::FullCone);
                            },
                            _ => {
                                // 如果没有收到响应，说明是受限锥形NAT（只允许来自特定IP的响应）
                                debug!("Detected NAT type: Restricted Cone NAT");
                                return Ok(NATType::RestrictedCone);
                            }
                        }
                    },
                    _ => {
                        // 如果没有收到来自不同IP的响应，测试是否允许来自相同IP但不同端口的响应

                        // 创建一个变更请求，要求服务器从相同IP但不同端口发送响应
                        let change_request_port_only = StunMessage::new_binding_request_with_change(false, true);
                        socket3.send_to(&change_request_port_only.encode()?, server_addr).await?;

                        // 尝试接收响应
                        let port_only_result = tokio::time::timeout(timeout, socket3.recv_from(&mut buf)).await;

                        match port_only_result {
                            Ok(Ok(_)) => {
                                // 如果收到响应，说明是受限锥形NAT（允许来自相同IP但不同端口的响应）
                                debug!("Detected NAT type: Restricted Cone NAT (alternative test)");
                                return Ok(NATType::RestrictedCone);
                            },
                            _ => {
                                // 如果没有收到响应，说明是端口受限锥形NAT（只允许来自特定IP和端口的响应）
                                debug!("Detected NAT type: Port Restricted Cone NAT");
                                return Ok(NATType::PortRestrictedCone);
                            }
                        }
                    }
                }
            }
        }
    }

    /// 获取服务器池
    pub fn get_server_pool(&self) -> Arc<StunServerPool> {
        self.servers.clone()
    }

    /// 清除缓存
    pub async fn clear_cache(&self) {
        let mut cache = self.binding_cache.write().await;
        cache.clear();
    }
}
