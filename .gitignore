# Generated by Cargo
/target/

# Remove Cargo.lock from gitignore if creating an executable, leave it for libraries
# More information here https://doc.rust-lang.org/cargo/guide/cargo-toml-vs-cargo-lock.html
Cargo.lock

# These are backup files generated by rustfmt
**/*.rs.bk

# MSVC Windows builds of rustc generate these, which store debugging information
*.pdb

# Downloaded files
/downloads/

# Database files
*.db
*.db-shm
*.db-wal

# IDE files
.idea/
.vscode/
*.iml

# OS specific files
.DS_Store
Thumbs.db

