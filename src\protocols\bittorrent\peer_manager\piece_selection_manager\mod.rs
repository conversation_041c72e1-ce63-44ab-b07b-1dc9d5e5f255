//! 片段选择管理器模块
//! 
//! 这个模块负责选择要下载的片段，实现各种片段选择策略，如稀有优先、随机优先、顺序等。
//! 它处理片段的选择、优先级和稀有度计算等功能。

use std::collections::{HashMap, HashSet};
use std::net::SocketAddr;
use std::sync::Arc;

use anyhow::{Result};
use rand::{seq::SliceRandom, thread_rng};
use tracing::{debug};

use crate::core::Peer;
use crate::core::p2p::piece::PieceState;
use crate::protocols::bittorrent::piece_manager::PieceSelectionStrategy;

/// 片段选择管理器
/// 
/// 负责选择要下载的片段。
pub struct PieceSelectionManager {
    /// 片段选择策略
    strategy: PieceSelectionStrategy,
    
    /// 结束游戏阈值
    end_game_threshold: usize,
    
    /// 随机优先的片段数量
    random_first_pieces: usize,
    
    /// 总片段数量
    total_pieces: usize,
    
    /// 已选择的随机片段
    random_pieces: HashSet<u32>,
    
    /// 片段稀有度
    piece_rarities: HashMap<u32, usize>,
}

impl PieceSelectionManager {
    /// 创建新的片段选择管理器
    pub fn new(
        strategy: PieceSelectionStrategy,
        end_game_threshold: usize,
        random_first_pieces: usize,
        total_pieces: usize
    ) -> Self {
        Self {
            strategy,
            end_game_threshold,
            random_first_pieces,
            total_pieces,
            random_pieces: HashSet::new(),
            piece_rarities: HashMap::new(),
        }
    }
    
    /// 设置片段选择策略
    pub fn set_strategy(&mut self, strategy: PieceSelectionStrategy) {
        self.strategy = strategy;
    }
    
    /// 设置结束游戏阈值
    pub fn set_end_game_threshold(&mut self, threshold: usize) {
        self.end_game_threshold = threshold;
    }
    
    /// 设置随机优先的片段数量
    pub fn set_random_first_pieces(&mut self, count: usize) {
        self.random_first_pieces = count;
    }
    
    /// 设置总片段数量
    pub fn set_total_pieces(&mut self, count: usize) {
        self.total_pieces = count;
    }
    
    /// 更新片段稀有度
    pub async fn update_piece_rarities(
        &mut self,
        peers: &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>
    ) -> Result<()> {
        // 重置稀有度
        self.piece_rarities.clear();
        
        // 统计每个片段的拥有数量
        for (_, peer) in peers {
            let peer = peer.lock().await;
            let pieces = peer.pieces();
            
            // 更新稀有度
            for piece_index in pieces {
                let count = self.piece_rarities.entry(piece_index).or_insert(0);
                *count += 1;
            }
        }
        
        debug!("更新了 {} 个片段的稀有度", self.piece_rarities.len());
        
        Ok(())
    }
    
    /// 选择要下载的片段
    pub async fn select_pieces(
        &mut self,
        peers: &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Result<Vec<(u32, SocketAddr)>> {
        // 根据策略选择片段
        match self.strategy {
            PieceSelectionStrategy::RarestFirst => {
                self.select_rarest_first(peers, piece_states, requested_pieces).await
            },
            PieceSelectionStrategy::RandomFirst => {
                self.select_random_first(peers, piece_states, requested_pieces).await
            },
            PieceSelectionStrategy::Sequential => {
                self.select_sequential(peers, piece_states, requested_pieces).await
            },
            PieceSelectionStrategy::EndGame => {
                self.select_end_game(peers, piece_states, requested_pieces).await
            },
        }
    }
    
    /// 稀有优先策略
    async fn select_rarest_first(
        &mut self,
        peers: &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Result<Vec<(u32, SocketAddr)>> {
        self.update_piece_rarities(peers).await?;
        let mut available_pieces = Vec::new();
        for (addr, peer) in peers {
            let peer = peer.lock().await;
            let peer_pieces = peer.pieces();
            for piece_index in peer_pieces {
                if piece_index as usize >= piece_states.len() {
                    continue;
                }
                let state = &piece_states[piece_index as usize];
                if *state == PieceState::Missing || *state == PieceState::Downloading {
                    if !requested_pieces.contains(&piece_index) {
                        let rarity = self.piece_rarities.get(&piece_index).unwrap_or(&0);
                        available_pieces.push((piece_index, *addr, *rarity));
                    }
                }
            }
        }
        
        // 按稀有度排序（升序，稀有度越低越稀有）
        available_pieces.sort_by(|a, b| a.2.cmp(&b.2));
        
        // 转换为结果格式
        let result: Vec<(u32, SocketAddr)> = available_pieces.iter()
            .map(|(piece, addr, _)| (*piece, *addr))
            .collect();
        
        debug!("稀有优先策略选择了 {} 个片段", result.len());
        
        Ok(result)
    }
    
    /// 随机优先策略
    async fn select_random_first(
        &mut self,
        peers: &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Result<Vec<(u32, SocketAddr)>> {
        if self.random_pieces.len() >= self.random_first_pieces {
            return self.select_rarest_first(peers, piece_states, requested_pieces).await;
        }
        let mut available_pieces = Vec::new();
        for (addr, peer) in peers {
            let peer = peer.lock().await;
            let peer_pieces = peer.pieces();
            for piece_index in peer_pieces {
                if piece_index as usize >= piece_states.len() {
                    continue;
                }
                let state = &piece_states[piece_index as usize];
                if (*state == PieceState::Missing || *state == PieceState::Downloading)
                    && !requested_pieces.contains(&piece_index)
                    && !self.random_pieces.contains(&piece_index) {
                    available_pieces.push((piece_index, *addr));
                }
            }
        }
        let mut rng = thread_rng();
        available_pieces.shuffle(&mut rng);
        let mut result = Vec::new();
        let needed = self.random_first_pieces - self.random_pieces.len();
        for (piece, addr) in available_pieces.iter().take(needed) {
            result.push((*piece, *addr));
            self.random_pieces.insert(*piece);
        }
        debug!("随机优先策略选择了 {} 个片段", result.len());
        Ok(result)
    }

    /// 顺序策略
    async fn select_sequential(
        &mut self,
        peers: &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Result<Vec<(u32, SocketAddr)>> {
        let mut available_pieces = Vec::new();
        for (addr, peer) in peers {
            let peer = peer.lock().await;
            let peer_pieces = peer.pieces();
            for piece_index in 0..piece_states.len() as u32 {
                if !peer_pieces.contains(&piece_index) {
                    continue;
                }
                let state = &piece_states[piece_index as usize];
                if (*state == PieceState::Missing || *state == PieceState::Downloading)
                    && !requested_pieces.contains(&piece_index) {
                    available_pieces.push((piece_index, *addr));
                }
            }
        }
        available_pieces.sort_by(|a, b| a.0.cmp(&b.0));
        debug!("顺序策略选择了 {} 个片段", available_pieces.len());
        Ok(available_pieces)
    }

    /// 结束游戏策略
    async fn select_end_game(
        &mut self,
        peers: &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>,
        piece_states: &[PieceState],
        requested_pieces: &HashSet<u32>
    ) -> Result<Vec<(u32, SocketAddr)>> {
        let missing_count = piece_states.iter().filter(|state| **state == PieceState::Missing).count();
        if missing_count > self.end_game_threshold {
            return self.select_rarest_first(peers, piece_states, requested_pieces).await;
        }
        let mut missing_pieces = Vec::new();
        for (i, state) in piece_states.iter().enumerate() {
            if *state == PieceState::Missing {
                missing_pieces.push(i as u32);
            }
        }
        let mut result = Vec::new();
        for piece_index in missing_pieces {
            for (addr, peer) in peers {
                let peer = peer.lock().await;
                let peer_pieces = peer.pieces();
                if peer_pieces.contains(&piece_index) {
                    result.push((piece_index, *addr));
                }
            }
        }
        debug!("结束游戏策略选择了 {} 个片段", result.len());
        Ok(result)
    }
}