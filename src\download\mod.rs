// 导出下载相关模块
// 这些模块实际位于protocols目录下，应该使用use语句导入
// pub mod http;
// pub mod bt;
// pub mod p2p;
// pub mod r2;
pub mod resume;
pub mod manager;
pub mod manager_ws;
pub mod downloader_factory_impl;
pub mod bandwidth_scheduler;
pub mod event_notifier;
pub mod mirror;
pub mod speed_limiter;
pub mod task_manager;

// 重新导出protocols目录下的模块
pub use crate::protocols::http;
pub use crate::protocols::bittorrent as bt;
pub use crate::protocols::custom_p2p as p2p;
// pub use crate::protocols::r2; // 将在未来整合


// 重新导出核心接口
pub use crate::core::interfaces::downloader::{Downloader, DownloaderFactory, DownloadProgress, DownloadStatus};
pub use downloader_factory_impl::DownloaderFactoryImpl;
