use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use tokio::net::UdpSocket;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use super::node::NodeId;
use super::routing::RoutingTable;
use super::message::{DHTMessage, DHTMessageType, MessageCodec};
use super::query::{DHTQuery, QueryManager};
use super::event::{DHTEvent, DHTEventDispatcher};

/// DHT引导器
pub struct DHTBootstrapper {
    /// 节点ID
    node_id: NodeId,
    /// UDP套接字
    socket: Arc<UdpSocket>,
    /// 路由表
    routing_table: Arc<RwLock<RoutingTable>>,
    /// 查询管理器
    query_manager: Arc<RwLock<QueryManager>>,
    /// 事件分发器
    event_dispatcher: Arc<DHTEventDispatcher>,
    /// 引导节点列表
    bootstrap_nodes: Vec<SocketAddr>,
    /// 最大并行查询数
    max_parallel_queries: usize,
    /// 引导超时时间（秒）
    bootstrap_timeout: u64,
    /// 是否已引导
    bootstrapped: bool,
    /// 引导开始时间
    bootstrap_start_time: Option<Instant>,
}

impl DHTBootstrapper {
    /// 创建新的DHT引导器
    pub fn new(
        node_id: NodeId,
        socket: Arc<UdpSocket>,
        routing_table: Arc<RwLock<RoutingTable>>,
        query_manager: Arc<RwLock<QueryManager>>,
        event_dispatcher: Arc<DHTEventDispatcher>,
        bootstrap_nodes: Vec<SocketAddr>,
    ) -> Self {
        Self {
            node_id,
            socket,
            routing_table,
            query_manager,
            event_dispatcher,
            bootstrap_nodes,
            max_parallel_queries: 3,
            bootstrap_timeout: 60,
            bootstrapped: false,
            bootstrap_start_time: None,
        }
    }

    /// 创建新的DHT引导器，带配置
    pub fn with_config(
        node_id: NodeId,
        socket: Arc<UdpSocket>,
        routing_table: Arc<RwLock<RoutingTable>>,
        query_manager: Arc<RwLock<QueryManager>>,
        event_dispatcher: Arc<DHTEventDispatcher>,
        bootstrap_nodes: Vec<SocketAddr>,
        max_parallel_queries: usize,
        bootstrap_timeout: u64,
    ) -> Self {
        Self {
            node_id,
            socket,
            routing_table,
            query_manager,
            event_dispatcher,
            bootstrap_nodes,
            max_parallel_queries,
            bootstrap_timeout,
            bootstrapped: false,
            bootstrap_start_time: None,
        }
    }

    /// 执行引导过程
    pub async fn bootstrap(&mut self) -> Result<()> {
        debug!("Starting DHT bootstrap process");

        // 如果没有引导节点，无法引导
        if self.bootstrap_nodes.is_empty() {
            warn!("No bootstrap nodes available");
            return Err(anyhow!("No bootstrap nodes available"));
        }

        // 记录引导开始时间
        self.bootstrap_start_time = Some(Instant::now());

        // 向所有引导节点发送查找节点请求
        let mut successful_queries = 0;

        for &addr in &self.bootstrap_nodes {
            // 生成随机事务ID
            let transaction_id: Vec<u8> = (0..4).map(|_| rand::random::<u8>()).collect();

            // 创建查找节点请求
            let message = DHTMessage {
                message_type: DHTMessageType::FindNode,
                transaction_id: transaction_id.clone(),
                sender_id: self.node_id,
                target_id: Some(NodeId::new_random()), // 查找随机节点ID
                nodes: None,
                peers: None,
                token: None,
                port: None,
                error_code: None,
                error_message: None,
            };

            // 创建查询
            let query = DHTQuery::new(
                transaction_id.clone(),
                message.target_id.unwrap(),
                DHTMessageType::FindNode,
            );

            // 添加查询到查询管理器
            {
                let mut query_manager = self.query_manager.write().await;
                query_manager.add_query(query)?;
            }

            // 发送消息
            let encoded = MessageCodec::encode(&message)?;
            match self.socket.send_to(&encoded, addr).await {
                Ok(_) => {
                    debug!("Sent FindNode request to bootstrap node: {}", addr);
                    successful_queries += 1;
                },
                Err(e) => {
                    warn!("Failed to send FindNode request to bootstrap node {}: {}", addr, e);
                }
            }

            // 限制并行查询数
            if successful_queries >= self.max_parallel_queries {
                break;
            }
        }

        if successful_queries == 0 {
            warn!("Failed to send FindNode requests to any bootstrap nodes");
            return Err(anyhow!("Failed to bootstrap"));
        }

        // 等待引导完成
        self.wait_for_bootstrap().await?;

        Ok(())
    }

    /// 等待引导完成
    async fn wait_for_bootstrap(&mut self) -> Result<()> {
        let start_time = self.bootstrap_start_time.unwrap_or_else(Instant::now);
        let timeout = Duration::from_secs(self.bootstrap_timeout);

        // 等待直到路由表中有足够的节点或超时
        let mut check_interval = tokio::time::interval(Duration::from_millis(500));

        loop {
            check_interval.tick().await;

            // 检查是否超时
            if start_time.elapsed() > timeout {
                warn!("Bootstrap timed out after {} seconds", self.bootstrap_timeout);
                break;
            }

            // 检查路由表中的节点数
            let node_count = {
                let routing_table = self.routing_table.read().await;
                routing_table.node_count()
            };

            debug!("Bootstrap in progress, current node count: {}", node_count);

            // 如果路由表中有足够的节点，认为引导成功
            if node_count >= 8 {
                info!("Bootstrap successful, found {} nodes", node_count);
                self.bootstrapped = true;

                // 发送状态变更事件
                self.event_dispatcher.dispatch(DHTEvent::NodeStateChanged {
                    node_count,
                    bootstrapped: true,
                }).await?;

                return Ok(());
            }
        }

        // 即使没有达到理想的节点数，如果有任何节点，也认为引导部分成功
        let node_count = {
            let routing_table = self.routing_table.read().await;
            routing_table.node_count()
        };

        if node_count > 0 {
            info!("Bootstrap partially successful, found {} nodes", node_count);
            self.bootstrapped = true;

            // 发送状态变更事件
            self.event_dispatcher.dispatch(DHTEvent::NodeStateChanged {
                node_count,
                bootstrapped: true,
            }).await?;

            return Ok(());
        }

        warn!("Bootstrap failed, no nodes found");
        Err(anyhow!("Bootstrap failed"))
    }

    /// 发送DHT消息
    async fn send_message(&self, message: &DHTMessage, addr: SocketAddr) -> Result<()> {
        // 编码消息
        let encoded = MessageCodec::encode(message)?;

        // 发送消息
        self.socket.send_to(&encoded, addr).await?;

        Ok(())
    }

    /// 是否已引导
    pub fn is_bootstrapped(&self) -> bool {
        self.bootstrapped
    }
}
