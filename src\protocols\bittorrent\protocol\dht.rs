use anyhow::Result;
use tracing::{info, warn};
use std::net::SocketAddr;

use super::core::BitTorrentProtocol;

impl BitTorrentProtocol {
    /// Use DHT to find peers
    pub(crate) async fn find_peers_via_dht(&mut self) -> Result<()> {
        // 如果启用了DHT，使用DHT查找对等点
        if let Some(dht_manager) = &self.dht_manager {
            if dht_manager.is_enabled() {
                if let Some(torrent_info) = &self.torrent_info {
                    // 确保info_hash是20字节
                    if torrent_info.info_hash.len() == 20 {
                        // 将Vec<u8>转换为[u8; 20]
                        let mut info_hash_array = [0u8; 20];
                        info_hash_array.copy_from_slice(&torrent_info.info_hash);

                        // 宣布我们拥有这个info_hash
                        if let Err(e) = dht_manager.announce_peer(info_hash_array, 6881).await {
                            warn!("Failed to announce peer to DHT: {}", e);
                        }

                        // 查找拥有这个info_hash的对等点
                        match dht_manager.get_peers(info_hash_array).await {
                            Ok(peers) => {
                                if !peers.is_empty() {
                                    info!("DHT found {} peers for info_hash {}", peers.len(), hex::encode(&torrent_info.info_hash));

                                    // 转换为Tracker响应格式
                                    let dht_response = dht_manager.convert_to_tracker_response(peers);

                                    // 连接到DHT找到的对等点
                                    if let Some(peer_manager) = &mut self.peer_manager {
                                          let socket_addrs: Vec<SocketAddr> = dht_response.peers.into_iter().map(|p| SocketAddr::new(p.ip, p.port)).collect();
                                          peer_manager.process_peers(socket_addrs).await?;
                                    }
                                }
                            },
                            Err(e) => {
                                warn!("Failed to get peers from DHT: {}", e);
                            }
                        }
                    } else {
                        warn!("Invalid info_hash length: {}, expected 20 bytes", torrent_info.info_hash.len());
                    }
                }
            }
        }

        Ok(())
    }
}
