import { defineStore } from 'pinia';
import axios from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { TaskAPI, DownloadAPI } from '../utils/apiConstants';

// 任务接口定义
export interface Task {
  id: string;
  fileName: string;
  status: string;
  totalLength: string;
  completedLength: string;
  downloadSpeed: string;
  uploadSpeed: string;
  connections: string;
  errorMessage?: string;
  gid: string;
  dir: string;
  files: any[];
}

// 任务状态类型
export type TaskStatus = 'all' | 'active' | 'waiting' | 'paused' | 'error' | 'complete' | 'removed';

// 任务状态映射
export const statusMap = {
  all: '全部',
  active: '下载中',
  waiting: '等待中',
  paused: '已暂停',
  error: '出错',
  complete: '已完成',
  removed: '已删除'
};

// 任务Store定义
export const useTaskStore = defineStore('task', {
  state: () => ({
    taskList: [] as Task[],
    loading: false,
    selectedTasks: [] as string[],
    previousSelectedTasks: [] as string[],
    currentStatus: 'all' as TaskStatus,
    refreshTimer: null as number | null
  }),

  getters: {
    // 获取当前状态的任务列表
    filteredTaskList: (state) => {
      if (state.currentStatus === 'all') {
        return state.taskList;
      }
      return state.taskList.filter(task => task.status === state.currentStatus);
    },
    
    // 获取可暂停的任务（状态为active或waiting）
    pausableTasks: (state) => {
      return state.taskList.filter(task => 
        state.selectedTasks.includes(task.id) && 
        (task.status === 'active' || task.status === 'waiting')
      );
    },
    
    // 获取可恢复的任务（状态为paused）
    resumableTasks: (state) => {
      return state.taskList.filter(task => 
        state.selectedTasks.includes(task.id) && 
        task.status === 'paused'
      );
    }
  },

  actions: {
    // 设置当前任务状态过滤器
    setTaskStatus(status: TaskStatus) {
      this.currentStatus = status;
      this.fetchTaskList();
    },
    
    // 获取任务列表
    async fetchTaskList() {
      this.loading = true;
      try {
        // 保存当前选中的任务，以便在刷新后恢复选择
        this.previousSelectedTasks = [...this.selectedTasks];
        
        const response = await axios.get(TaskAPI.GET_BY_STATUS(this.currentStatus));
        this.taskList = response.data.tasks || [];
        
        // 恢复之前选中的任务
        this.restoreSelection();
      } catch (error) {
        console.error('获取任务列表失败:', error);
        ElMessage.error('获取任务列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 恢复之前选中的任务
    restoreSelection() {
      if (this.previousSelectedTasks.length > 0) {
        // 过滤出仍然存在于当前任务列表中的任务ID
        this.selectedTasks = this.previousSelectedTasks.filter(id => 
          this.taskList.some(task => task.id === id)
        );
      }
    },
    
    // 暂停单个任务
    async pauseTask(taskId: string) {
      try {
        const response = await axios.post(DownloadAPI.PAUSE(taskId));
        if (response.data && response.data.success) {
          ElMessage.success('任务已暂停');
          this.fetchTaskList();
        } else {
          ElMessage.error(response.data?.error?.message || '暂停任务失败');
        }
      } catch (error: any) {
        console.error('暂停任务失败:', error);
        ElMessage.error(error.response?.data?.error?.message || '暂停任务失败');
      }
    },
    
    // 恢复单个任务
    async resumeTask(taskId: string) {
      try {
        const response = await axios.post(DownloadAPI.RESUME(taskId));
        if (response.data && response.data.success) {
          ElMessage.success('任务已恢复');
          this.fetchTaskList();
        } else {
          ElMessage.error(response.data?.error?.message || '恢复任务失败');
        }
      } catch (error: any) {
        console.error('恢复任务失败:', error);
        ElMessage.error(error.response?.data?.error?.message || '恢复任务失败');
      }
    },
    
    // 删除单个任务
    async removeTask(taskId: string) {
      try {
        await ElMessageBox.confirm('确定要删除此任务吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        const response = await axios.delete(TaskAPI.DELETE(taskId));
        if (response.data && response.data.success) {
          ElMessage.success('任务已删除');
          this.fetchTaskList();
        } else {
          ElMessage.error(response.data?.error?.message || '删除任务失败');
        }
      } catch (error: any) {
        if (error !== 'cancel') {
          console.error('删除任务失败:', error);
          ElMessage.error(error.response?.data?.error?.message || '删除任务失败');
        }
      }
    },
    
    // 批量暂停任务
    async batchPause() {
      if (this.selectedTasks.length === 0) {
        ElMessage.warning('请选择要暂停的任务');
        return;
      }

      try {
        let successCount = 0;
        let errorMessages: string[] = [];

        // 如果没有可暂停的任务，提示用户
        if (this.pausableTasks.length === 0) {
          ElMessage.warning('选中的任务都不在可暂停状态（只有下载中或等待中的任务可以暂停）');
          return;
        }
        
        // 暂停可暂停的任务
        for (const task of this.pausableTasks) {
          try {
            const response = await axios.post(DownloadAPI.PAUSE(task.id));
            if (response.data && response.data.success) {
              successCount++;
            } else {
              // 收集错误信息
              const errorMsg = response.data?.error?.message || `任务 ${task.id} 暂停失败`;
              errorMessages.push(errorMsg);
            }
          } catch (err: any) {
            // 收集HTTP错误信息
            const errorMsg = err.response?.data?.error?.message || `任务 ${task.id} 暂停失败`;
            errorMessages.push(errorMsg);
            console.error(`暂停任务失败 (${task.id}):`, err);
          }
        }

        // 显示结果
        if (successCount > 0) {
          ElMessage.success(`成功暂停 ${successCount} 个任务`);
          this.fetchTaskList();
        }
        
        // 如果有错误，显示错误信息
        if (errorMessages.length > 0) {
          // 如果错误太多，只显示前3个
          const displayErrors = errorMessages.length > 3 
            ? errorMessages.slice(0, 3).concat([`...等共 ${errorMessages.length} 个错误`])
            : errorMessages;
          
          ElMessage({
            message: displayErrors.join('\n'),
            type: 'error',
            duration: 5000,
            showClose: true,
          });
        }
      } catch (error: any) {
        console.error('批量暂停失败:', error);
        ElMessage.error('批量暂停失败');
      }
    },
    
    // 批量恢复任务
    async batchResume() {
      if (this.selectedTasks.length === 0) {
        ElMessage.warning('请选择要恢复的任务');
        return;
      }

      try {
        let successCount = 0;
        let errorMessages: string[] = [];

        // 筛选可恢复的任务（状态为paused）
        if (this.resumableTasks.length === 0) {
          ElMessage.warning('选中的任务都不在可恢复状态（只有已暂停的任务可以恢复）');
          return;
        }
        
        // 恢复可恢复的任务
        for (const task of this.resumableTasks) {
          try {
            const response = await axios.post(DownloadAPI.RESUME(task.id));
            if (response.data && response.data.success) {
              successCount++;
            } else {
              // 收集错误信息
              const errorMsg = response.data?.error?.message || `任务 ${task.id} 恢复失败`;
              errorMessages.push(errorMsg);
            }
          } catch (err: any) {
            // 收集HTTP错误信息
            const errorMsg = err.response?.data?.error?.message || `任务 ${task.id} 恢复失败`;
            errorMessages.push(errorMsg);
            console.error(`恢复任务失败 (${task.id}):`, err);
          }
        }

        // 显示结果
        if (successCount > 0) {
          ElMessage.success(`成功恢复 ${successCount} 个任务`);
          this.fetchTaskList();
        }
        
        // 如果有错误，显示错误信息
        if (errorMessages.length > 0) {
          // 如果错误太多，只显示前3个
          const displayErrors = errorMessages.length > 3 
            ? errorMessages.slice(0, 3).concat([`...等共 ${errorMessages.length} 个错误`])
            : errorMessages;
          
          ElMessage({
            message: displayErrors.join('\n'),
            type: 'error',
            duration: 5000,
            showClose: true,
          });
        }
      } catch (error: any) {
        console.error('批量恢复失败:', error);
        ElMessage.error('批量恢复失败');
      }
    },
    
    // 批量删除任务
    async batchRemove() {
      if (this.selectedTasks.length === 0) {
        ElMessage.warning('请选择要删除的任务');
        return;
      }

      try {
        await ElMessageBox.confirm(`确定要删除选中的 ${this.selectedTasks.length} 个任务吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        console.log('开始批量删除任务:', this.selectedTasks);
        
        // 显示加载状态
        const loadingMessage = ElMessage({
          message: `正在删除 ${this.selectedTasks.length} 个任务...`,
          type: 'info',
          duration: 0
        });

        let successCount = 0;
        let failedTasks = [];
        
        for (const taskId of this.selectedTasks) {
          try {
            console.log(`删除任务 ${taskId}, API端点:`, TaskAPI.DELETE(taskId));
            // 直接删除任务，不依赖取消操作
            const deleteResponse = await axios.delete(TaskAPI.DELETE(taskId));
            console.log(`任务 ${taskId} 删除响应:`, deleteResponse);
            
            if (deleteResponse.data && deleteResponse.data.success) {
              successCount++;
            } else {
              failedTasks.push(taskId);
              console.error(`任务 ${taskId} 删除失败 - 响应:`, deleteResponse.data);
            }
          } catch (err: any) {
            failedTasks.push(taskId);
            console.error(`删除任务失败 (${taskId}):`, err);
          }
        }

        loadingMessage.close();

        if (successCount > 0) {
          let message = `成功删除 ${successCount} 个任务`;
          if (failedTasks.length > 0) {
            message += `，${failedTasks.length} 个任务删除失败`;
          }
          ElMessage.success(message);
          console.log('批量删除完成，刷新列表');
          this.fetchTaskList();
          this.selectedTasks = [];
        } else {
          ElMessage.error('所有任务删除失败');
          console.error('失败的任务ID:', failedTasks);
        }
      } catch (error: any) {
        if (error !== 'cancel') {
          console.error('批量删除任务失败:', error);
          ElMessage.error('批量删除任务失败');
        }
      }
    },
    
    // 显示错误详情
    showErrorDetails(errorMessage: string) {
      ElMessageBox.alert(errorMessage, '错误详情', {
        confirmButtonText: '确定',
        customClass: 'error-details-dialog'
      });
    },
    
    // 启动定时刷新（仅当查看活动任务时）
    startAutoRefresh() {
      // 清除可能存在的旧定时器
      this.stopAutoRefresh();
      
      // 只有在查看活动任务时才启动定时刷新
      if (this.currentStatus === 'active') {
        this.refreshTimer = setInterval(() => {
          this.fetchTaskList();
        }, 3000) as unknown as number;
      }
    },
    
    // 停止定时刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    }
  }
});