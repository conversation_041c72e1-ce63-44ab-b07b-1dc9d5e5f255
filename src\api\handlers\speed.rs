use axum::{
    extract::{Path, State},
    Json,
};
use uuid::Uuid;
use serde::{Deserialize, Serialize};

use crate::api::response::{ApiResponse, ApiError};
use crate::api::state::AppState;

/// 速度限制请求
#[derive(Debug, Deserialize)]
pub struct SpeedLimitRequest {
    /// 速度限制（字节/秒），None表示不限速
    pub limit: Option<u64>,
}

/// 速度限制响应
#[derive(Debug, Serialize)]
pub struct SpeedLimitResponse {
    /// 速度限制（字节/秒），None表示不限速
    pub limit: Option<u64>,
}

/// 设置全局下载速度限制
pub async fn set_global_download_limit(
    State(app_state): State<AppState>,
    Json(request): Json<SpeedLimitRequest>,
) -> Result<Json<ApiResponse<SpeedLimitResponse>>, ApiError> {
    let bandwidth_scheduler = match app_state.bandwidth_scheduler {
        Some(scheduler) => scheduler,
        None => return Err(ApiError::internal_error("Bandwidth scheduler not available".to_string())),
    };

    // 设置全局下载速度限制
    bandwidth_scheduler.set_global_download_limit(request.limit).await
        .map_err(|e| ApiError::internal_error(format!("Failed to set global download limit: {}", e)))?;

    // 返回响应
    Ok(Json(ApiResponse::success(SpeedLimitResponse {
        limit: request.limit,
    })))
}

/// 获取全局下载速度限制
pub async fn get_global_download_limit(
    State(app_state): State<AppState>,
) -> Result<Json<ApiResponse<SpeedLimitResponse>>, ApiError> {
    let bandwidth_scheduler = match app_state.bandwidth_scheduler {
        Some(scheduler) => scheduler,
        None => return Err(ApiError::internal_error("Bandwidth scheduler not available".to_string())),
    };

    // 获取全局下载速度限制
    let limit = bandwidth_scheduler.get_global_download_limit().await
        .map_err(|e| ApiError::internal_error(format!("Failed to get global download limit: {}", e)))?;

    // 返回响应
    Ok(Json(ApiResponse::success(SpeedLimitResponse {
        limit,
    })))
}

/// 设置全局上传速度限制
pub async fn set_global_upload_limit(
    State(app_state): State<AppState>,
    Json(request): Json<SpeedLimitRequest>,
) -> Result<Json<ApiResponse<SpeedLimitResponse>>, ApiError> {
    let bandwidth_scheduler = match app_state.bandwidth_scheduler {
        Some(scheduler) => scheduler,
        None => return Err(ApiError::internal_error("Bandwidth scheduler not available".to_string())),
    };

    // 设置全局上传速度限制
    bandwidth_scheduler.set_global_upload_limit(request.limit).await
        .map_err(|e| ApiError::internal_error(format!("Failed to set global upload limit: {}", e)))?;

    // 返回响应
    Ok(Json(ApiResponse::success(SpeedLimitResponse {
        limit: request.limit,
    })))
}

/// 获取全局上传速度限制
pub async fn get_global_upload_limit(
    State(app_state): State<AppState>,
) -> Result<Json<ApiResponse<SpeedLimitResponse>>, ApiError> {
    let bandwidth_scheduler = match app_state.bandwidth_scheduler {
        Some(scheduler) => scheduler,
        None => return Err(ApiError::internal_error("Bandwidth scheduler not available".to_string())),
    };

    // 获取全局上传速度限制
    let limit = bandwidth_scheduler.get_global_upload_limit().await
        .map_err(|e| ApiError::internal_error(format!("Failed to get global upload limit: {}", e)))?;

    // 返回响应
    Ok(Json(ApiResponse::success(SpeedLimitResponse {
        limit,
    })))
}

/// 设置任务下载速度限制
pub async fn set_task_download_limit(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
    Json(request): Json<SpeedLimitRequest>,
) -> Result<Json<ApiResponse<SpeedLimitResponse>>, ApiError> {
    let bandwidth_scheduler = match app_state.bandwidth_scheduler {
        Some(scheduler) => scheduler,
        None => return Err(ApiError::internal_error("Bandwidth scheduler not available".to_string())),
    };

    // 设置任务下载速度限制
    bandwidth_scheduler.set_task_download_limit(task_id, request.limit).await
        .map_err(|e| ApiError::internal_error(format!("Failed to set task download limit: {}", e)))?;

    // 返回响应
    Ok(Json(ApiResponse::success(SpeedLimitResponse {
        limit: request.limit,
    })))
}

/// 获取任务下载速度限制
pub async fn get_task_download_limit(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
) -> Result<Json<ApiResponse<SpeedLimitResponse>>, ApiError> {
    let bandwidth_scheduler = match app_state.bandwidth_scheduler {
        Some(scheduler) => scheduler,
        None => return Err(ApiError::internal_error("Bandwidth scheduler not available".to_string())),
    };

    // 获取任务下载速度限制
    let limit = bandwidth_scheduler.get_task_download_limit(task_id).await
        .map_err(|e| ApiError::internal_error(format!("Failed to get task download limit: {}", e)))?;

    // 返回响应
    Ok(Json(ApiResponse::success(SpeedLimitResponse {
        limit,
    })))
}

/// 设置任务上传速度限制
pub async fn set_task_upload_limit(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
    Json(request): Json<SpeedLimitRequest>,
) -> Result<Json<ApiResponse<SpeedLimitResponse>>, ApiError> {
    let bandwidth_scheduler = match app_state.bandwidth_scheduler {
        Some(scheduler) => scheduler,
        None => return Err(ApiError::internal_error("Bandwidth scheduler not available".to_string())),
    };

    // 设置任务上传速度限制
    bandwidth_scheduler.set_task_upload_limit(task_id, request.limit).await
        .map_err(|e| ApiError::internal_error(format!("Failed to set task upload limit: {}", e)))?;

    // 返回响应
    Ok(Json(ApiResponse::success(SpeedLimitResponse {
        limit: request.limit,
    })))
}

/// 获取任务上传速度限制
pub async fn get_task_upload_limit(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
) -> Result<Json<ApiResponse<SpeedLimitResponse>>, ApiError> {
    let bandwidth_scheduler = match app_state.bandwidth_scheduler {
        Some(scheduler) => scheduler,
        None => return Err(ApiError::internal_error("Bandwidth scheduler not available".to_string())),
    };

    // 获取任务上传速度限制
    let limit = bandwidth_scheduler.get_task_upload_limit(task_id).await
        .map_err(|e| ApiError::internal_error(format!("Failed to get task upload limit: {}", e)))?;

    // 返回响应
    Ok(Json(ApiResponse::success(SpeedLimitResponse {
        limit,
    })))
}
