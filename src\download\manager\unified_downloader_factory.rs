use anyhow::{anyhow, Context};
use std::path::Path;
use std::sync::Arc;
use tracing::{info, debug, warn};
use uuid::Uuid;
use async_trait::async_trait;

use crate::analyzer::LinkAnalyzer;
use crate::config::{ConfigManager, Settings};
use crate::core::error::{CoreError, CoreResult};
use crate::core::interfaces::{Downloader, DownloaderFactory, ProtocolType};
use crate::protocols::http::HttpDownloader;
use crate::protocols::r2::R2Downloader;
use crate::protocols::bittorrent::BitTorrentFactory;
use crate::protocols::custom_p2p::{CustomP2PFactory, P2PProtocol};
use crate::download::protocol_adapter::P2PProtocolAdapter;
use crate::download::resume::ResumeManager;
use crate::download::bandwidth_scheduler::BandwidthScheduler;
use crate::utils::HttpClient;

/// 统一下载器工厂，合并了 DownloaderFactoryImpl 和 DownloaderCreator 的功能
pub struct UnifiedDownloaderFactory {
    config_manager: Arc<ConfigManager>,
    link_analyzer: Arc<dyn LinkAnalyzer>,
    bt_factory: BitTorrentFactory,
    p2p_factory: CustomP2PFactory,
    resume_manager: Arc<dyn ResumeManager>,
    bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
}

impl UnifiedDownloaderFactory {
    /// 创建新的统一下载器工厂
    pub fn new(
        config_manager: Arc<ConfigManager>,
        link_analyzer: Arc<dyn LinkAnalyzer>,
        resume_manager: Arc<dyn ResumeManager>,
    ) -> Self {
        let bt_factory = BitTorrentFactory::new(config_manager.clone());
        let p2p_factory = CustomP2PFactory::new(config_manager.clone());

        Self {
            config_manager,
            link_analyzer,
            bt_factory,
            p2p_factory,
            resume_manager,
            bandwidth_scheduler: None,
        }
    }

    /// 设置带宽调度器
    pub fn with_bandwidth_scheduler(mut self, bandwidth_scheduler: Arc<dyn BandwidthScheduler>) -> Self {
        self.bandwidth_scheduler = Some(bandwidth_scheduler);
        self
    }
    
    /// 分析URL协议并处理重定向
    async fn analyze_url_protocol(&self, url: &str) -> CoreResult<(String, String, String)> {
        // 分析URL协议
        info!("Analyzing URL protocol for {}", url);
        let protocol = self.link_analyzer.analyze(url).await
            .map_err(|e| CoreError::from(anyhow!(e)))?;
        info!("URL protocol for {} is {}", url, protocol);

        // 处理HTTP/HTTPS协议的重定向
        if protocol == "http" || protocol == "https" {
            // 跟踪重定向
            info!("Following redirects for {}", url);
            let redirect_result = self.link_analyzer.follow_redirects(url).await
                .map_err(|e| CoreError::from(anyhow!(e)))?;

            // 检查是否是循环重定向
            if redirect_result.is_redirect_loop {
                warn!("Detected redirect loop for URL: {}", url);
                return Err(CoreError::Protocol("Redirect loop detected".to_string()));
            }

            // 获取最终URL和文件名
            let final_url = redirect_result.final_url;
            let file_name = redirect_result.file_name.unwrap_or_else(|| {
                // 从URL中提取文件名
                let url_parts: Vec<&str> = final_url.split('/').collect();
                url_parts.last().unwrap_or(&"download").to_string()
            });

            info!("Final URL: {}, file name: {}", final_url, file_name);
            Ok((protocol, final_url, file_name))
        } else {
            // 非HTTP/HTTPS协议，直接使用原始URL
            let url_parts: Vec<&str> = url.split('/').collect();
            let file_name = url_parts.last().unwrap_or(&"download").to_string();
            info!("Using original URL: {}, file name: {}", url, file_name);
            Ok((protocol, url.to_string(), file_name))
        }
    }

    /// 确保输出目录存在
    async fn ensure_output_directory_exists(&self, output_path: &str) -> CoreResult<()> {
        let output_dir = Path::new(output_path).parent();
        if let Some(dir) = output_dir {
            if !dir.exists() {
                info!("Creating output directory: {}", dir.display());
                tokio::fs::create_dir_all(dir).await
                    .context(format!("Failed to create directory: {}", dir.display()))
                    .map_err(|e| CoreError::from(anyhow!(e)))?;
            }
        }
        Ok(())
    }
}

#[async_trait]
impl DownloaderFactory for UnifiedDownloaderFactory {
    async fn create_downloader(
        &self,
        url: &str,
        output_path: &str,
        task_id: Uuid,
    ) -> CoreResult<Box<dyn Downloader>> {
        // 确保输出目录存在
        self.ensure_output_directory_exists(output_path).await?;
        
        // 分析URL协议
        let (protocol, final_url, _file_name) = self.analyze_url_protocol(url).await?;

        // 根据协议创建下载器
        info!("Creating downloader for protocol: {}", protocol);
        match protocol.as_str() {
            "http" | "https" => {
                // 验证URL格式
                if !final_url.starts_with("http://") && !final_url.starts_with("https://") {
                    return Err(CoreError::InvalidArgument(format!("无效的HTTP/HTTPS URL格式: {}", final_url)));
                }
                
                // 创建HTTP下载器
                let mut downloader = HttpDownloader::new(
                    final_url.to_string(),
                    output_path.to_string(),
                    self.config_manager.clone(),
                    task_id,
                );
                
                // 创建自定义的 HttpClient
                let http_client = match HttpClient::from_config_manager(&self.config_manager).await {
                    Ok(client) => Arc::new(client),
                    Err(e) => {
                        debug!("Failed to create custom HTTP client: {}", e);
                        // 使用默认客户端
                        Arc::new(HttpClient::new().expect("Failed to create default HTTP client"))
                    }
                };
                
                // 设置自定义的 HTTP 客户端
                downloader = downloader.with_http_client(http_client);
                
                // 设置恢复管理器
                downloader = downloader.with_resume_manager(self.resume_manager.clone());
                
                // 如果有带宽调度器，设置到下载器中
                if let Some(scheduler) = &self.bandwidth_scheduler {
                    downloader = downloader.with_bandwidth_scheduler(scheduler.clone());
                }
                
                // 设置下载速度限制
                let settings = self.config_manager.get_settings().await;
                if let Some(download_limit) = settings.download.speed_limit {
                    info!("Setting download speed limit to {} bytes/s", download_limit);
                    downloader.set_download_limit(Some(download_limit)).await?;
                }
                
                // 设置上传速度限制
                if let Some(upload_limit) = settings.download.upload_speed_limit {
                    info!("Setting upload speed limit to {} bytes/s", upload_limit);
                    downloader.set_upload_limit(Some(upload_limit)).await?;
                }

                // 直接返回 HttpDownloader，不需要适配器
                Ok(Box::new(downloader) as Box<dyn Downloader>)
            },
            "bt" | "magnet" => {
                // 使用BitTorrent工厂创建下载器
                if self.bt_factory.supports_url(&final_url) {
                    let mut bt_factory = self.bt_factory.clone();

                    // 设置恢复管理器
                    bt_factory = bt_factory.with_resume_manager(self.resume_manager.clone());

                    // 如果有带宽调度器，设置到工厂中
                    if let Some(scheduler) = &self.bandwidth_scheduler {
                        bt_factory = bt_factory.with_bandwidth_scheduler(scheduler.clone());
                    }

                    // 创建BitTorrent下载器
                    let downloader = bt_factory.create_downloader(&final_url, output_path, task_id);
                    
                    // 直接返回 BitTorrentDownloader，不需要适配器
                    Ok(Box::new(downloader) as Box<dyn Downloader>)
                } else {
                    Err(CoreError::Unsupported(format!("BitTorrent工厂不支持URL: {}，请检查URL格式是否正确", final_url)))
                }
            },
            "p2p" => {
                // 使用自定义P2P协议工厂创建下载器
                if self.p2p_factory.supports_url(&final_url) {
                    // 尝试创建P2P协议，如果失败会返回详细错误
                    let p2p_protocol = match self.p2p_factory.create_protocol(&final_url, output_path, task_id).await {
                        Ok(protocol) => protocol,
                        Err(e) => return Err(CoreError::Protocol(format!("创建P2P协议失败: {}，URL: {}", e, final_url)))
                    };

                    // 创建适配器，将P2PProtocol转换为Downloader
                    let p2p_adapter = P2PProtocolAdapter::new(
                        p2p_protocol,
                        task_id,
                        final_url.to_string(),
                        output_path.to_string(),
                    );
                    
                    // 返回P2P适配器
                    Ok(Box::new(p2p_adapter) as Box<dyn Downloader>)
                } else {
                    Err(CoreError::Unsupported(format!("P2P工厂不支持URL: {}，请检查URL格式是否正确", final_url)))
                }
            },
            "r2" => {
                // 验证URL格式
                if !final_url.starts_with("r2://") {
                    return Err(CoreError::InvalidArgument(format!("无效的R2 URL格式: {}，R2 URL应以'r2://'开头", final_url)));
                }
                
                // 创建R2下载器
                let downloader = R2Downloader::new(
                    final_url.to_string(),
                    output_path.to_string(),
                    self.config_manager.clone(),
                    task_id,
                );
                
                // 直接返回 R2Downloader，不需要适配器
                Ok(Box::new(downloader) as Box<dyn Downloader>)
            },
            _ => Err(CoreError::Unsupported(format!("不支持的协议类型: {}，支持的协议有: HTTP, HTTPS, BitTorrent, Magnet, P2P, R2", protocol))),
        }
    }
    
    /// 获取支持的协议类型
    fn supported_protocols(&self) -> Vec<ProtocolType> {
        vec![
            ProtocolType::Http,
            ProtocolType::Https,
            ProtocolType::BitTorrent,
            ProtocolType::Magnet,
            ProtocolType::P2P,
            ProtocolType::R2,
        ]
    }
}