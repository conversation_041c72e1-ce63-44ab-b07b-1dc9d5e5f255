use anyhow::{Result, anyhow};
use bytes::{BytesMut, BufMut};
use tracing::{debug, warn};

/// BitTorrent消息类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum BitTorrentMessage {
    <PERSON><PERSON>,
    Unchoke,
    Interested,
    NotInterested,
    Have(u32),
    Bitfield(Vec<u8>),
    Request(u32, u32, u32),
    Piece(u32, u32, Vec<u8>),
    Cancel(u32, u32, u32),
    Port(u16),
    Extended(u8, Vec<u8>),
    // Fast Extension消息 (BEP 6)
    HaveAll,           // 表示拥有所有分片
    HaveNone,          // 表示没有任何分片
    Suggest(u32),      // 建议下载某个分片
    Reject(u32, u32, u32), // 拒绝请求 (index, begin, length)
    AllowedFast(u32),  // 允许即使在choked状态下也可以请求的分片
}

impl BitTorrentMessage {
    /// 将消息编码为字节
    /// 
    /// # 实现说明
    /// - 支持快速扩展（supports_fast=true），如需区分可扩展参数可后续扩展
    /// - 编码失败时返回空向量，并记录详细日志
    /// - 可根据实际需求扩展为返回 Result 类型
    pub fn to_bytes(&self) -> Vec<u8> {
        let mut buf = BytesMut::new();
        // 这里假设所有消息都支持快速扩展，实际可根据 peer 能力调整
        match self.encode(true) {
            Ok(bytes_mut) => bytes_mut.freeze().to_vec(),
            Err(e) => {
                // 记录详细错误日志，便于后续排查
                warn!(target: "bittorrent::message", "BitTorrentMessage 编码失败: {:?}", e);
                // 返回空向量，实际可考虑返回特定错误码或 Result
                Vec::new()
            }
        }
    }

    pub fn encode(&self, supports_fast: bool) -> Result<BytesMut> {
        let mut buf = BytesMut::new();

        match self {
            BitTorrentMessage::Choke => {
                buf.put_u32(1); // 长度
                buf.put_u8(0); // ID
            },
            BitTorrentMessage::Unchoke => {
                buf.put_u32(1); // 长度
                buf.put_u8(1); // ID
            },
            BitTorrentMessage::Interested => {
                buf.put_u32(1); // 长度
                buf.put_u8(2); // ID
            },
            BitTorrentMessage::NotInterested => {
                buf.put_u32(1); // 长度
                buf.put_u8(3); // ID
            },
            BitTorrentMessage::Have(index) => {
                buf.put_u32(5); // 长度
                buf.put_u8(4); // ID
                buf.put_u32(*index);
            },
            BitTorrentMessage::Bitfield(bitfield) => {
                buf.put_u32(1 + bitfield.len() as u32); // 长度
                buf.put_u8(5); // ID
                buf.put_slice(bitfield);
            },
            BitTorrentMessage::Request(index, begin, length) => {
                buf.put_u32(13); // 长度
                buf.put_u8(6); // ID
                buf.put_u32(*index);
                buf.put_u32(*begin);
                buf.put_u32(*length);
            },
            BitTorrentMessage::Piece(index, begin, block) => {
                buf.put_u32(9 + block.len() as u32); // 长度
                buf.put_u8(7); // ID
                buf.put_u32(*index);
                buf.put_u32(*begin);
                buf.put_slice(block);
            },
            BitTorrentMessage::Cancel(index, begin, length) => {
                buf.put_u32(13); // 长度
                buf.put_u8(8); // ID
                buf.put_u32(*index);
                buf.put_u32(*begin);
                buf.put_u32(*length);
            },
            BitTorrentMessage::Port(port) => {
                buf.put_u32(3); // 长度
                buf.put_u8(9); // ID
                buf.put_u16(*port);
            },
            BitTorrentMessage::Extended(ext_id, payload) => {
                buf.put_u32(2 + payload.len() as u32); // 长度
                buf.put_u8(20); // ID
                buf.put_u8(*ext_id);
                buf.put_slice(payload);
            },
            // Fast Extension消息 (BEP 6)
            BitTorrentMessage::HaveAll => {
                if !supports_fast {
                    return Err(anyhow!("Peer does not support Fast Extension"));
                }
                buf.put_u32(1); // 长度
                buf.put_u8(0x0E); // ID = 14
            },
            BitTorrentMessage::HaveNone => {
                if !supports_fast {
                    return Err(anyhow!("Peer does not support Fast Extension"));
                }
                buf.put_u32(1); // 长度
                buf.put_u8(0x0F); // ID = 15
            },
            BitTorrentMessage::Suggest(index) => {
                if !supports_fast {
                    return Err(anyhow!("Peer does not support Fast Extension"));
                }
                buf.put_u32(5); // 长度
                buf.put_u8(0x0D); // ID = 13
                buf.put_u32(*index);
            },
            BitTorrentMessage::Reject(index, begin, length) => {
                if !supports_fast {
                    return Err(anyhow!("Peer does not support Fast Extension"));
                }
                buf.put_u32(13); // 长度
                buf.put_u8(0x10); // ID = 16
                buf.put_u32(*index);
                buf.put_u32(*begin);
                buf.put_u32(*length);
            },
            BitTorrentMessage::AllowedFast(index) => {
                if !supports_fast {
                    return Err(anyhow!("Peer does not support Fast Extension"));
                }
                buf.put_u32(5); // 长度
                buf.put_u8(0x11); // ID = 17
                buf.put_u32(*index);
            },
        }

        Ok(buf)
    }

    /// 从字节解码消息
    pub fn decode(message: &[u8]) -> Result<Option<Self>> {
        if message.is_empty() {
            // Keep-alive消息
            return Ok(None);
        }

        let message_id = message[0];
        let payload = &message[1..];

        match message_id {
            0 => {
                // Choke
                Ok(Some(BitTorrentMessage::Choke))
            },
            1 => {
                // Unchoke
                Ok(Some(BitTorrentMessage::Unchoke))
            },
            2 => {
                // Interested
                Ok(Some(BitTorrentMessage::Interested))
            },
            3 => {
                // Not interested
                Ok(Some(BitTorrentMessage::NotInterested))
            },
            4 => {
                // Have
                if payload.len() != 4 {
                    return Err(anyhow!("Invalid have message"));
                }
                let index = u32::from_be_bytes([payload[0], payload[1], payload[2], payload[3]]);
                Ok(Some(BitTorrentMessage::Have(index)))
            },
            5 => {
                // Bitfield
                Ok(Some(BitTorrentMessage::Bitfield(payload.to_vec())))
            },
            6 => {
                // Request
                if payload.len() != 12 {
                    return Err(anyhow!("Invalid request message"));
                }
                let index = u32::from_be_bytes([payload[0], payload[1], payload[2], payload[3]]);
                let begin = u32::from_be_bytes([payload[4], payload[5], payload[6], payload[7]]);
                let length = u32::from_be_bytes([payload[8], payload[9], payload[10], payload[11]]);
                Ok(Some(BitTorrentMessage::Request(index, begin, length)))
            },
            7 => {
                // Piece
                if payload.len() < 8 {
                    return Err(anyhow!("Invalid piece message"));
                }
                let index = u32::from_be_bytes([payload[0], payload[1], payload[2], payload[3]]);
                let begin = u32::from_be_bytes([payload[4], payload[5], payload[6], payload[7]]);
                let block = payload[8..].to_vec();
                Ok(Some(BitTorrentMessage::Piece(index, begin, block)))
            },
            8 => {
                // Cancel
                if payload.len() != 12 {
                    return Err(anyhow!("Invalid cancel message"));
                }
                let index = u32::from_be_bytes([payload[0], payload[1], payload[2], payload[3]]);
                let begin = u32::from_be_bytes([payload[4], payload[5], payload[6], payload[7]]);
                let length = u32::from_be_bytes([payload[8], payload[9], payload[10], payload[11]]);
                Ok(Some(BitTorrentMessage::Cancel(index, begin, length)))
            },
            9 => {
                // Port
                if payload.len() != 2 {
                    return Err(anyhow!("Invalid port message"));
                }
                let port = u16::from_be_bytes([payload[0], payload[1]]);
                Ok(Some(BitTorrentMessage::Port(port)))
            },
            20 => {
                // Extended
                if payload.is_empty() {
                    return Err(anyhow!("Invalid extended message"));
                }
                let ext_id = payload[0];
                let ext_payload = payload[1..].to_vec();
                Ok(Some(BitTorrentMessage::Extended(ext_id, ext_payload)))
            },
            // Fast Extension消息 (BEP 6)
            0x0D => {
                // Suggest
                if payload.len() != 4 {
                    return Err(anyhow!("Invalid suggest message"));
                }
                let index = u32::from_be_bytes([payload[0], payload[1], payload[2], payload[3]]);
                debug!("Peer suggests piece {}", index);
                Ok(Some(BitTorrentMessage::Suggest(index)))
            },
            0x0E => {
                // HaveAll
                debug!("Peer has all pieces");
                Ok(Some(BitTorrentMessage::HaveAll))
            },
            0x0F => {
                // HaveNone
                debug!("Peer has no pieces");
                Ok(Some(BitTorrentMessage::HaveNone))
            },
            0x10 => {
                // Reject
                if payload.len() != 12 {
                    return Err(anyhow!("Invalid reject message"));
                }
                let index = u32::from_be_bytes([payload[0], payload[1], payload[2], payload[3]]);
                let begin = u32::from_be_bytes([payload[4], payload[5], payload[6], payload[7]]);
                let length = u32::from_be_bytes([payload[8], payload[9], payload[10], payload[11]]);
                debug!("Peer rejected request for piece {} (offset {}, length {})", index, begin, length);
                Ok(Some(BitTorrentMessage::Reject(index, begin, length)))
            },
            0x11 => {
                // AllowedFast
                if payload.len() != 4 {
                    return Err(anyhow!("Invalid allowed fast message"));
                }
                let index = u32::from_be_bytes([payload[0], payload[1], payload[2], payload[3]]);
                debug!("Peer allows fast request for piece {}", index);
                Ok(Some(BitTorrentMessage::AllowedFast(index)))
            },
            _ => {
                warn!("Unknown message ID: {}", message_id);
                Ok(None)
            }
        }
    }
}
