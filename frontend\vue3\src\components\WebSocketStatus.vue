<template>
  <div class="websocket-status" :title="statusTooltip">
    <div class="status-indicator" :class="statusClass"></div>
    <span v-if="showText" class="status-text">{{ statusText }}</span>
    <el-popover
      v-if="showReconnect"
      placement="top"
      :width="100"
      trigger="hover"
      content="WebSocket连接已断开，点击重新连接"
    >
      <template #reference>
        <div class="reconnect-button" @click="reconnect">
          <i class="el-icon-refresh"></i>
        </div>
      </template>
    </el-popover>
    <el-dialog
      v-model="logDialogVisible"
      title="WebSocket事件日志"
      width="80%"
      :before-close="closeLogDialog"
    >
      <div class="event-log">
        <div class="log-container">
          <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
            <span class="log-time">{{ formatTime(log.time) }}</span>
            <span class="log-event" :class="getEventClass(log.event)">{{ log.event }}</span>
            <span class="log-payload">{{ JSON.stringify(log.payload) }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue';
import {
  initWebSocket,
  addEventListener,
  removeEventListener,
  getWebSocketState,
  closeWebSocket
} from '@/utils/websocket';

// WebSocket 连接状态
enum ConnectionState {
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
  RECONNECTING = 4,
}

export default defineComponent({
  name: 'WebSocketStatus',
  props: {
    showLog: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const connectionStatus = ref<string>('disconnected');
    const eventLogs = ref<Array<{event: string, payload: any, time: number}>>([]);
    const maxLogs = 50; // 最多显示50条日志
    const logDialogVisible = ref(false);
    const showText = ref(true); // 是否显示状态文本

    // 计算状态样式
    const statusClass = computed(() => {
      switch (connectionStatus.value) {
        case 'connected':
          return 'status-connected';
        case 'connecting':
        case 'reconnecting':
          return 'status-connecting';
        default:
          return 'status-disconnected';
      }
    });

    // 计算状态文本
    const statusText = computed(() => {
      switch (connectionStatus.value) {
        case 'connected':
          return '已连接';
        case 'connecting':
          return '连接中...';
        case 'reconnecting':
          return '重新连接中...';
        default:
          return '未连接';
      }
    });

    // 计算状态提示文本
    const statusTooltip = computed(() => {
      switch (connectionStatus.value) {
        case 'connected':
          return 'WebSocket连接已建立，与服务器通信正常';
        case 'connecting':
          return 'WebSocket正在连接中...';
        case 'reconnecting':
          return 'WebSocket正在尝试重新连接...';
        default:
          return 'WebSocket连接已断开，点击重新连接';
      }
    });

    // 是否显示重连按钮
    const showReconnect = computed(() => {
      return connectionStatus.value === 'disconnected';
    });

    // 关闭日志对话框
    const closeLogDialog = () => {
      logDialogVisible.value = false;
    };

    // 处理连接状态变化
    const handleConnectionChange = (payload: any) => {
      connectionStatus.value = payload.status;
      addEventLog('connection', payload);
    };

    // 处理任务事件
    const handleTaskEvent = (event: string, payload: any) => {
      addEventLog(event, payload);
    };

    // 添加事件日志
    const addEventLog = (event: string, payload: any) => {
      eventLogs.value.unshift({
        event,
        payload,
        time: Date.now()
      });

      // 限制日志数量
      if (eventLogs.value.length > maxLogs) {
        eventLogs.value = eventLogs.value.slice(0, maxLogs);
      }
    };

    // 格式化时间
    const formatTime = (timestamp: number) => {
      const date = new Date(timestamp);
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
    };

    // 获取事件样式
    const getEventClass = (event: string) => {
      if (event.includes('error')) {
        return 'event-error';
      } else if (event.includes('complete')) {
        return 'event-success';
      } else if (event.includes('start')) {
        return 'event-start';
      } else if (event.includes('pause')) {
        return 'event-pause';
      } else {
        return 'event-info';
      }
    };

    // 重新连接
    const reconnect = () => {
      connectionStatus.value = 'connecting';
      initWebSocket();
    };

    // 组件挂载时
    onMounted(() => {
      // 根据当前WebSocket状态设置初始状态
      const state = getWebSocketState();
      switch (state) {
        case ConnectionState.OPEN:
          connectionStatus.value = 'connected';
          break;
        case ConnectionState.CONNECTING:
        case ConnectionState.RECONNECTING:
          connectionStatus.value = 'connecting';
          break;
        default:
          connectionStatus.value = 'disconnected';
      }

      // 添加事件监听器
      addEventListener('connection', handleConnectionChange);

      // 监听任务事件
      const taskEvents = [
        'task.start', 'task.pause', 'task.resume', 'task.cancel',
        'task.complete', 'task.error', 'task.progress', 'task.speed'
      ];

      taskEvents.forEach(event => {
        addEventListener(event, (payload) => handleTaskEvent(event, payload));
      });
    });

    // 组件卸载时
    onUnmounted(() => {
      // 移除事件监听器
      removeEventListener('connection', handleConnectionChange);

      // 移除任务事件监听器
      const taskEvents = [
        'task.start', 'task.pause', 'task.resume', 'task.cancel',
        'task.complete', 'task.error', 'task.progress', 'task.speed'
      ];

      taskEvents.forEach(event => {
        removeEventListener(event, (payload) => handleTaskEvent(event, payload));
      });
    });

    return {
      connectionStatus,
      statusClass,
      statusText,
      statusTooltip,
      showReconnect,
      showText,
      eventLogs,
      logDialogVisible,
      reconnect,
      formatTime,
      getEventClass,
      closeLogDialog
    };
  }
});
</script>

<style scoped>
.websocket-status {
  display: flex;
  align-items: center;
  padding: 4px;
  border-radius: 4px;
  background-color: rgba(245, 245, 245, 0.7);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-connected {
  background-color: #4caf50;
}

.status-connecting {
  background-color: #ff9800;
  animation: blink 1s infinite;
}

.status-disconnected {
  background-color: #f44336;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  color: #555;
}

.reconnect-button {
  margin-left: 4px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2196f3;
  cursor: pointer;
  font-size: 12px;
}

.reconnect-button:hover {
  color: #1976d2;
}

.event-log {
  width: 100%;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.log-item {
  font-family: monospace;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: flex-start;
}

.log-time {
  color: #757575;
  margin-right: 8px;
  flex-shrink: 0;
}

.log-event {
  font-weight: bold;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 120px;
}

.log-payload {
  color: #616161;
  word-break: break-all;
  flex-grow: 1;
}

.event-error {
  color: #f44336;
}

.event-success {
  color: #4caf50;
}

.event-start {
  color: #2196f3;
}

.event-pause {
  color: #ff9800;
}

.event-info {
  color: #607d8b;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
</style>
