use std::process::Command;
use tracing::{debug, info};

use crate::config::settings::ProxyConfig;
use super::super::detector::{ProxyDetector, parse_auth_from_url, parse_no_proxy};
use super::super::error::ProxyError;

/// Linux 代理检测器
/// 
/// 负责从 Linux 系统设置中检测代理设置
pub struct LinuxProxyDetector;

impl LinuxProxyDetector {
    /// 创建新的 Linux 代理检测器实例
    pub fn new() -> Self {
        LinuxProxyDetector {}
    }
    
    /// 从 GNOME 设置中检测代理
    fn detect_from_gnome(&self) -> Result<Option<ProxyConfig>, ProxyError> {
        debug!("检查 GNOME 设置中的代理");
        
        // 检查 gsettings 命令是否可用
        if !Command::new("which").arg("gsettings").status().map_or(false, |status| status.success()) {
            debug!("gsettings 命令不可用");
            return Ok(None);
        }
        
        // 检查代理模式
        let mode_output = Command::new("gsettings")
            .args(["get", "org.gnome.system.proxy", "mode"])
            .output()?;
            
        if !mode_output.status.success() {
            return Err(ProxyError::OsError("无法获取 GNOME 代理模式".to_string()));
        }
        
        let mode = String::from_utf8_lossy(&mode_output.stdout).trim().trim_matches('\'').to_string();
        
        if mode != "manual" {
            debug!("GNOME 代理模式不是手动模式: {}", mode);
            return Ok(None);
        }
        
        // 获取 HTTP 代理设置
        let host_output = Command::new("gsettings")
            .args(["get", "org.gnome.system.proxy.http", "host"])
            .output()?;
            
        if !host_output.status.success() {
            return Err(ProxyError::OsError("无法获取 GNOME HTTP 代理主机".to_string()));
        }
        
        let host = String::from_utf8_lossy(&host_output.stdout).trim().trim_matches('\'').to_string();
        
        if host.is_empty() {
            debug!("GNOME HTTP 代理主机为空");
            return Ok(None);
        }
        
        // 获取端口
        let port_output = Command::new("gsettings")
            .args(["get", "org.gnome.system.proxy.http", "port"])
            .output()?;
            
        if !port_output.status.success() {
            return Err(ProxyError::OsError("无法获取 GNOME HTTP 代理端口".to_string()));
        }
        
        let port = String::from_utf8_lossy(&port_output.stdout).trim().parse::<u16>().unwrap_or(80);
        
        // 检查是否启用了 HTTPS
        let use_https_output = Command::new("gsettings")
            .args(["get", "org.gnome.system.proxy.http", "use-authentication"])
            .output()?;
            
        let use_https = String::from_utf8_lossy(&use_https_output.stdout).trim() == "true";
        
        // 构建代理 URL
        let proxy_type = if use_https { "https" } else { "http" };
        let proxy_url = format!("{protocol}://{host}:{port}", 
            protocol = proxy_type,
            host = host,
            port = port
        );
        
        // 获取用户名和密码（如果有）
        let mut username = None;
        let mut password = None;
        
        let use_auth_output = Command::new("gsettings")
            .args(["get", "org.gnome.system.proxy.http", "use-authentication"])
            .output()?;
            
        let use_auth = String::from_utf8_lossy(&use_auth_output.stdout).trim() == "true";
        
        if use_auth {
            let auth_user_output = Command::new("gsettings")
                .args(["get", "org.gnome.system.proxy.http", "authentication-user"])
                .output()?;
                
            let auth_user = String::from_utf8_lossy(&auth_user_output.stdout).trim().trim_matches('\'').to_string();
            
            if !auth_user.is_empty() {
                username = Some(auth_user);
                
                let auth_password_output = Command::new("gsettings")
                    .args(["get", "org.gnome.system.proxy.http", "authentication-password"])
                    .output()?;
                    
                let auth_password = String::from_utf8_lossy(&auth_password_output.stdout).trim().trim_matches('\'').to_string();
                
                if !auth_password.is_empty() {
                    password = Some(auth_password);
                }
            }
        }
        
        // 获取 no_proxy 列表
        let ignore_hosts_output = Command::new("gsettings")
            .args(["get", "org.gnome.system.proxy", "ignore-hosts"])
            .output()?;
            
        let ignore_hosts_str = String::from_utf8_lossy(&ignore_hosts_output.stdout).trim().to_string();
        let mut no_proxy = Vec::new();
        
        if !ignore_hosts_str.is_empty() && ignore_hosts_str != "[]" {
            // 解析 GNOME 格式的忽略主机列表，通常是 ['localhost', '127.0.0.1', '::1'] 格式
            let cleaned = ignore_hosts_str
                .trim_start_matches('[')
                .trim_end_matches(']')
                .replace('\'', "");
                
            no_proxy = cleaned
                .split(',')
                .map(|s| s.trim().to_string())
                .filter(|s| !s.is_empty())
                .collect();
        }
        
        info!("从 GNOME 设置检测到代理: {}", proxy_url);
        Ok(Some(ProxyConfig {
            enabled: true,
            url: proxy_url,
            username,
            password,
            no_proxy,
            proxy_type: proxy_type.to_string(),
        }))
    }
    
    /// 从 KDE 设置中检测代理
    fn detect_from_kde(&self) -> Result<Option<ProxyConfig>, ProxyError> {
        debug!("检查 KDE 设置中的代理");
        // KDE 代理设置通常存储在 ~/.config/kioslaverc 中
        // 这里仅为占位实现，实际实现会更复杂
        Ok(None)
    }
}

impl ProxyDetector for LinuxProxyDetector {
    fn detect(&self) -> Result<Option<ProxyConfig>, ProxyError> {
        debug!("检查 Linux 系统设置中的代理设置");
        
        // 首先尝试 GNOME 设置
        if let Ok(Some(config)) = self.detect_from_gnome() {
            return Ok(Some(config));
        }
        
        // 然后尝试 KDE 设置
        if let Ok(Some(config)) = self.detect_from_kde() {
            return Ok(Some(config));
        }
        
        // 可以添加更多桌面环境的检测
        
        debug!("未在 Linux 系统设置中检测到代理");
        Ok(None)
    }
}

#[cfg(test)]
mod tests {
    // Linux 代理测试需要在 Linux 环境中运行
    // 这里只提供基本的测试框架
}