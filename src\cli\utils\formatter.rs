use anyhow::Result;
use clap::ValueEnum;
use prettytable::{Table, Row, Cell, format};
use serde_json::{Value};
use std::collections::HashMap;

/// Output format
#[derive(Debug, <PERSON>lone, Co<PERSON>, PartialEq, Eq, ValueEnum)]
pub enum OutputFormat {
    /// Text format
    Text,
    /// Table format
    Table,
    /// JSON format
    Json,
    /// YAML format
    Yaml,
}

/// Format output
pub fn format_output(data: Value, format: OutputFormat) {
    match format {
        OutputFormat::Text => format_as_text(data),
        OutputFormat::Table => format_as_table(data),
        OutputFormat::Json => format_as_json(data),
        OutputFormat::Yaml => format_as_yaml(data),
    }
}

/// Format as text
fn format_as_text(data: Value) {
    match data {
        Value::Object(map) => {
            for (key, value) in map {
                println!("{}: {}", key, value_to_string(&value));
            }
        },
        Value::Array(array) => {
            for (i, item) in array.iter().enumerate() {
                println!("[{}]:", i + 1);
                match item {
                    Value::Object(map) => {
                        for (key, value) in map {
                            println!("  {}: {}", key, value_to_string(value));
                        }
                        println!();
                    },
                    _ => println!("  {}", value_to_string(item)),
                }
            }
        },
        _ => println!("{}", value_to_string(&data)),
    }
}

/// Format as table
fn format_as_table(data: Value) {
    match data {
        Value::Array(array) => {
            if array.is_empty() {
                println!("No data to display.");
                return;
            }
            
            // 获取所有可能的列名
            let mut all_columns = Vec::new();
            for item in &array {
                if let Value::Object(map) = item {
                    for key in map.keys() {
                        if !all_columns.contains(key) {
                            all_columns.push(key.clone());
                        }
                    }
                }
            }
            
            // 如果没有列，返回
            if all_columns.is_empty() {
                println!("No columns to display.");
                return;
            }
            
            // 创建表格
            let mut table = Table::new();
            table.set_format(*format::consts::FORMAT_BOX_CHARS);
            
            // 添加表头
            let header_row = Row::new(all_columns.iter().map(|c| Cell::new(c)).collect());
            table.add_row(header_row);
            
            // 添加数据行
            for item in array {
                if let Value::Object(map) = item {
                    let mut row = Vec::new();
                    for column in &all_columns {
                        let cell_value = map.get(column).map_or("".to_string(), |v| value_to_string(v));
                        row.push(Cell::new(&cell_value));
                    }
                    table.add_row(Row::new(row));
                }
            }
            
            // 打印表格
            table.printstd();
        },
        Value::Object(map) => {
            // 创建表格
            let mut table = Table::new();
            table.set_format(*format::consts::FORMAT_BOX_CHARS);
            
            // 添加表头
            table.add_row(Row::new(vec![Cell::new("Key"), Cell::new("Value")]));
            
            // 添加数据行
            for (key, value) in map {
                table.add_row(Row::new(vec![Cell::new(&key), Cell::new(&value_to_string(&value))]));
            }
            
            // 打印表格
            table.printstd();
        },
        _ => println!("{}", value_to_string(&data)),
    }
}

/// Format as JSON
fn format_as_json(data: Value) {
    println!("{}", serde_json::to_string_pretty(&data).unwrap_or_else(|_| "Error formatting JSON".to_string()));
}

/// Format as YAML
fn format_as_yaml(data: Value) {
    println!("{}", serde_yaml::to_string(&data).unwrap_or_else(|_| "Error formatting YAML".to_string()));
}

/// Convert JSON value to string
fn value_to_string(value: &Value) -> String {
    match value {
        Value::Null => "null".to_string(),
        Value::Bool(b) => b.to_string(),
        Value::Number(n) => n.to_string(),
        Value::String(s) => s.clone(),
        Value::Array(a) => {
            let items: Vec<String> = a.iter().map(|v| value_to_string(v)).collect();
            format!("[{}]", items.join(", "))
        },
        Value::Object(_) => "[Object]".to_string(),
    }
}