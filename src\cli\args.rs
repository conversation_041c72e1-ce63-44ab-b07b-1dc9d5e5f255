//! CLI 参数定义模块
//!
//! 定义命令行接口的参数结构。

use clap::{Parser, Subcommand};
use std::path::PathBuf;

use crate::cli::commands::config::ConfigCommands;
use crate::cli::commands::download::DownloadCommands;
use crate::cli::commands::speed::SpeedCommands;
use crate::cli::commands::status::StatusCommands;
use crate::cli::utils::formatter::OutputFormat;

/// CLI 运行模式
#[derive(clap::ValueEnum, Clone, Debug)]
pub enum CliMode {
    /// 通过 API 与服务器通信
    Api,
    /// 直接集成到主程序
    Direct,
}

/// Tonitru 下载器 CLI
#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
pub struct Cli {
    /// 配置文件路径
    #[arg(short, long)]
    pub config: Option<PathBuf>,

    /// API 服务器 URL
    #[arg(short, long, default_value = "http://localhost:3000")]
    pub api: String,

    /// 运行模式
    #[arg(short, long, value_enum, default_value = "direct")]
    pub mode: CliMode,

    /// 输出格式
    #[arg(short, long, value_enum)]
    pub format: Option<OutputFormat>,

    /// 详细输出
    #[arg(short, long)]
    pub verbose: bool,

    /// 安静模式
    #[arg(short, long)]
    pub quiet: bool,

    /// 调试模式（比详细输出提供更多信息）
    #[arg(short = 'D', long)]
    pub debug: bool,

    /// 启用性能分析（仅在调试模式下有效）
    #[arg(short = 'P', long)]
    pub profile: bool,

    /// 子命令
    #[command(subcommand)]
    pub command: Commands,
}

/// CLI 子命令
#[derive(Subcommand, Debug)]
pub enum Commands {
    /// 下载管理
    #[command(subcommand)]
    Download(DownloadCommands),

    /// 速度控制
    #[command(subcommand)]
    Speed(SpeedCommands),

    /// 配置管理
    #[command(subcommand)]
    Config(ConfigCommands),

    /// 状态查询
    #[command(subcommand)]
    Status(StatusCommands),
}