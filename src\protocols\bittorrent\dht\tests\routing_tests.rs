#[cfg(test)]
mod routing_tests {
    use std::net::{IpAddr, Ipv4Addr};
    use std::time::Duration;
    use crate::protocols::bittorrent::dht::node::{NodeId, DHTNode};
    use crate::protocols::bittorrent::dht::routing::{KBucket, RoutingTable, RoutingTableConfig};

    #[test]
    fn test_kbucket_new() {
        let capacity = 8;
        let prefix_len = 5;
        let max_failures = 3;
        let replace_questionable = true;

        let bucket = KBucket::new(capacity, prefix_len, max_failures, replace_questionable);

        assert_eq!(bucket.capacity, capacity);
        assert_eq!(bucket.prefix_len, prefix_len);
        assert_eq!(bucket.max_failures, max_failures);
        assert_eq!(bucket.replace_questionable, replace_questionable);
        assert!(bucket.nodes.is_empty());
    }

    #[test]
    fn test_kbucket_add_node() {
        let mut bucket = KBucket::new(2, 0, 3, true);
        let node_id1 = NodeId::new_random();
        let node_id2 = NodeId::new_random();
        let node_id3 = NodeId::new_random();

        let node1 = DHTNode::new(node_id1, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
        let node2 = DHTNode::new(node_id2, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);
        let node3 = DHTNode::new(node_id3, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 3)), 6883);

        // 添加第一个节点
        assert!(bucket.add_node(node1.clone()));
        assert_eq!(bucket.len(), 1);

        // 添加第二个节点
        assert!(bucket.add_node(node2.clone()));
        assert_eq!(bucket.len(), 2);

        // 桶已满，但实现可能允许替换，所以不测试返回值
        bucket.add_node(node3.clone());
        // 确保桶中仍然只有2个节点
        assert_eq!(bucket.len(), 2);

        // 更新已有节点
        let mut updated_node1 = node1.clone();
        updated_node1.port = 7000;
        assert!(bucket.add_node(updated_node1));
        assert_eq!(bucket.len(), 2);

        // 验证节点已更新
        // 节点可能在任何位置，所以我们需要遍历所有节点
        let found = bucket.nodes.iter().any(|n| n.port == 7000);
        assert!(found, "Updated node with port 7000 not found");
    }

    #[test]
    fn test_kbucket_get_closest_nodes() {
        let mut bucket = KBucket::new(8, 0, 3, true);

        // 创建一些节点，ID有特定距离
        let target_id = NodeId::from_bytes([0u8; 20]);

        let mut id1 = [0u8; 20];
        id1[0] = 1; // 距离 = 1
        let node1 = DHTNode::new(NodeId::from_bytes(id1), IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        let mut id2 = [0u8; 20];
        id2[0] = 2; // 距离 = 2
        let node2 = DHTNode::new(NodeId::from_bytes(id2), IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);

        let mut id3 = [0u8; 20];
        id3[0] = 3; // 距离 = 3
        let node3 = DHTNode::new(NodeId::from_bytes(id3), IpAddr::V4(Ipv4Addr::new(127, 0, 0, 3)), 6883);

        // 添加节点（顺序打乱）
        bucket.add_node(node3.clone());
        bucket.add_node(node1.clone());
        bucket.add_node(node2.clone());

        // 获取最接近的2个节点
        let closest = bucket.get_closest_nodes(&target_id, 2);

        // 应该按距离排序
        assert_eq!(closest.len(), 2);
        assert_eq!(closest[0].id, node1.id); // 距离 = 1
        assert_eq!(closest[1].id, node2.id); // 距离 = 2
    }

    #[test]
    fn test_kbucket_remove_expired_nodes() {
        let mut bucket = KBucket::new(8, 0, 3, true);

        let node_id1 = NodeId::new_random();
        let node_id2 = NodeId::new_random();

        let mut node1 = DHTNode::new(node_id1, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
        let node2 = DHTNode::new(node_id2, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);

        // 更新node1的最后响应时间
        node1.update_last_seen();

        // 添加节点
        bucket.add_node(node1);
        bucket.add_node(node2);
        assert_eq!(bucket.len(), 2);

        // 移除过期节点（node2没有last_seen，应该被移除）
        bucket.remove_expired_nodes(Duration::from_secs(1));
        assert_eq!(bucket.len(), 1);
    }

    #[test]
    fn test_routing_table_new() {
        let node_id = NodeId::new_random();
        let config = RoutingTableConfig::default();

        let table = RoutingTable::new(node_id, config.clone());

        assert_eq!(table.node_id, node_id);
        assert_eq!(table.config.k, config.k);
        assert_eq!(table.buckets.len(), 160);
        assert_eq!(table.node_count, 0);
    }

    #[test]
    fn test_routing_table_add_node() {
        let local_id = NodeId::from_bytes([0u8; 20]);
        let config = RoutingTableConfig::default();

        let mut table = RoutingTable::new(local_id, config);

        // 创建一个节点，ID与本地节点有特定距离
        let mut remote_id = [0u8; 20];
        remote_id[0] = 0b10000000; // 第0位不同，应该进入第0个桶
        let node = DHTNode::new(NodeId::from_bytes(remote_id), IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        // 添加节点
        assert!(table.add_node(node.clone()));
        assert_eq!(table.node_count, 1);

        // 验证节点在正确的桶中
        assert_eq!(table.buckets[0].len(), 1);
        assert_eq!(table.buckets[0].nodes[0].id, node.id);

        // 不应该添加自己
        assert!(!table.add_node(DHTNode::new(local_id, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881)));
        assert_eq!(table.node_count, 1);
    }

    #[test]
    fn test_routing_table_get_closest_nodes() {
        let local_id = NodeId::from_bytes([0u8; 20]);
        let config = RoutingTableConfig::default();

        let mut table = RoutingTable::new(local_id, config);

        // 创建一些节点，ID有特定距离
        let target_id = NodeId::from_bytes([0x80u8; 20]); // 第0位为1，其余为0

        let mut id1 = [0u8; 20];
        id1[0] = 0x80; // 与target_id相同
        let node1 = DHTNode::new(NodeId::from_bytes(id1), IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        let mut id2 = [0u8; 20];
        id2[0] = 0xC0; // 与target_id第1位不同
        let node2 = DHTNode::new(NodeId::from_bytes(id2), IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);

        let mut id3 = [0u8; 20];
        id3[0] = 0xA0; // 与target_id第2位不同
        let node3 = DHTNode::new(NodeId::from_bytes(id3), IpAddr::V4(Ipv4Addr::new(127, 0, 0, 3)), 6883);

        // 添加节点
        table.add_node(node1.clone());
        table.add_node(node2.clone());
        table.add_node(node3.clone());

        // 获取最接近的节点
        let closest = table.get_closest_nodes(&target_id, 3);

        // 应该按距离排序
        assert_eq!(closest.len(), 3);
        assert_eq!(closest[0].id, node1.id); // 完全匹配
        assert_eq!(closest[1].id, node3.id); // 第2位不同
        assert_eq!(closest[2].id, node2.id); // 第1位不同
    }

    #[test]
    fn test_routing_table_get_bucket_index() {
        let local_id = NodeId::from_bytes([0u8; 20]);
        let config = RoutingTableConfig::default();

        let table = RoutingTable::new(local_id, config);

        // 创建一些节点ID，与本地节点有特定距离
        let mut id1 = [0u8; 20];
        id1[0] = 0x80; // 第0位不同
        let node_id1 = NodeId::from_bytes(id1);

        let mut id2 = [0u8; 20];
        id2[0] = 0x40; // 第1位不同
        let node_id2 = NodeId::from_bytes(id2);

        let mut id3 = [0u8; 20];
        id3[0] = 0x20; // 第2位不同
        let node_id3 = NodeId::from_bytes(id3);

        // 验证桶索引
        assert_eq!(table.get_bucket_index(&node_id1), 0);
        assert_eq!(table.get_bucket_index(&node_id2), 1);
        assert_eq!(table.get_bucket_index(&node_id3), 2);
    }

    #[test]
    fn test_routing_table_remove_expired_nodes() {
        let local_id = NodeId::new_random();
        let config = RoutingTableConfig::default();

        let mut table = RoutingTable::new(local_id, config);

        // 创建一些节点
        let node_id1 = NodeId::new_random();
        let node_id2 = NodeId::new_random();

        let mut node1 = DHTNode::new(node_id1, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
        let node2 = DHTNode::new(node_id2, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);

        // 更新node1的最后响应时间
        node1.update_last_seen();

        // 添加节点
        table.add_node(node1);
        table.add_node(node2);
        assert_eq!(table.node_count, 2);

        // 移除过期节点
        table.remove_expired_nodes();
        assert_eq!(table.node_count, 1);
    }

    #[test]
    fn test_routing_table_update_node() {
        let local_id = NodeId::new_random();
        let config = RoutingTableConfig::default();

        let mut table = RoutingTable::new(local_id, config);

        // 创建一个节点
        let node_id = NodeId::new_random();
        let node = DHTNode::new(node_id, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        // 添加节点
        table.add_node(node);

        // 更新节点状态（成功）
        assert!(table.update_node(&node_id, true).is_ok());

        // 更新节点状态（失败）
        assert!(table.update_node(&node_id, false).is_ok());

        // 更新不存在的节点
        let non_existent_id = NodeId::new_random();
        assert!(table.update_node(&non_existent_id, true).is_err());
    }
}
