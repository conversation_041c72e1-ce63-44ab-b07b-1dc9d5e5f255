# 对等点质量评分系统设计与实现

## 1. 系统概述

对等点质量评分系统是 Tonitru 下载器中的一个核心组件，用于评估、管理和过滤 BitTorrent 网络中的对等点（Peers）。该系统通过多维度的评分指标，帮助下载器识别高质量的对等点，提高下载效率，同时避免恶意或低质量对等点带来的问题。

### 1.1 设计目标

- **提高下载效率**：优先连接高质量、高速度的对等点
- **增强安全性**：识别并过滤恶意或可疑的对等点
- **优化资源利用**：合理分配连接资源，避免浪费在低质量对等点上
- **自适应评估**：根据对等点的历史表现动态调整评分
- **多维度评价**：综合考虑速度、稳定性、地理位置等多方面因素

### 1.2 核心功能

- **对等点评分**：基于多维度指标计算对等点质量评分
- **对等点过滤**：根据评分和规则过滤低质量对等点
- **违规记录**：记录对等点的违规行为
- **黑名单集成**：与安全管理器集成，支持黑名单功能
- **地理位置评估**：基于地理位置评估对等点质量
- **历史记录**：维护对等点的历史评分记录

## 2. 系统架构

### 2.1 核心组件

对等点质量评分系统由以下核心组件组成：

1. **EnhancedPeerScore**：增强的对等点评分结构体，包含多维度评分指标
2. **PeerQualityManager**：对等点质量管理器，负责评分、过滤和违规记录
3. **PeerFilter**：对等点过滤器接口及其实现
4. **GeoLocationService**：地理位置服务接口

### 2.2 组件关系

```
+-------------------+      +-------------------+
| PeerManager       |----->| PeerQualityManager|
+-------------------+      +-------------------+
         |                          |
         v                          v
+-------------------+      +-------------------+
| PeerMessageHandler|      | EnhancedPeerScore |
+-------------------+      +-------------------+
                                    |
                                    v
                           +-------------------+
                           | PeerFilter        |
                           +-------------------+
                                    |
                           +-------------------+
                           | SecurityManager   |
                           +-------------------+
```

## 3. 评分指标

### 3.1 基础性能指标

- **下载速度**：对等点提供的下载速度（bytes/s）
- **上传速度**：对等点的上传速度（bytes/s）
- **成功率**：请求成功的比率（0.0-1.0）
- **响应时间**：对等点响应请求的时间（ms）

### 3.2 高级指标

- **连接稳定性**：对等点连接的稳定程度（0.0-1.0）
- **地理位置评分**：基于地理位置的评分（0.0-1.0）
- **协议兼容性**：对等点支持的协议扩展程度（0.0-1.0）

### 3.3 安全指标

- **可疑行为计数**：记录对等点的可疑行为次数
- **违规历史**：记录对等点的违规行为历史

### 3.4 元数据

- **首次发现时间**：首次发现对等点的时间
- **最后更新时间**：最后更新对等点评分的时间
- **更新次数**：评分更新的次数
- **历史评分**：对等点的历史评分记录

## 4. 评分计算

### 4.1 综合评分计算

综合评分通过加权平均的方式计算，权重可以通过配置调整：

```rust
self.overall_score = download_score * 0.3 +
                    upload_score * 0.1 +
                    self.success_rate * 0.15 +
                    response_score * 0.1 +
                    self.connection_stability * 0.1 +
                    self.geo_score * 0.05 +
                    self.protocol_compatibility * 0.05 +
                    security_score * 0.15;
```

### 4.2 指标归一化

各项指标在计算前会进行归一化处理，转换为 0.0-1.0 的范围：

- **下载速度**：假设 5MB/s 是满分
- **上传速度**：假设 1MB/s 是满分
- **响应时间**：50ms 以下是满分，1000ms 以上是 0 分
- **安全评分**：基于违规严重程度计算

## 5. 过滤机制

### 5.1 过滤器类型

系统实现了多种过滤器：

1. **BlacklistFilter**：基于黑名单过滤对等点
2. **PerformanceFilter**：基于性能评分过滤对等点
3. **GeoFilter**：基于地理位置过滤对等点
4. **CompositeFilter**：组合多个过滤器

### 5.2 过滤流程

1. 对等点管理器在处理新对等点时，会先通过质量管理器的过滤器进行过滤
2. 连接对等点前，会再次检查对等点是否应该被过滤
3. 添加对等点到连接队列前，也会进行过滤检查

## 6. 违规记录

### 6.1 违规类型

系统记录多种违规行为：

- **连接失败**：对等点连接失败
- **连接超时**：对等点连接超时
- **连接错误**：对等点连接出错
- **低下载速度**：对等点下载速度过低
- **高响应时间**：对等点响应时间过长
- **低成功率**：对等点成功率过低
- **过多拒绝**：对等点过多拒绝请求

### 6.2 违规严重程度

违规行为按严重程度分级（1-10）：

- **低严重度（1-3）**：如低下载速度、高响应时间
- **中等严重度（4-6）**：如连接超时、过多拒绝
- **高严重度（7-10）**：如恶意行为、协议违规

### 6.3 违规处理

- 记录违规行为到对等点的违规历史
- 更新对等点的可疑行为计数
- 重新计算对等点的综合评分
- 严重违规（严重度 >= 8）会将对等点添加到黑名单

## 7. 系统集成

### 7.1 与对等点管理器集成

对等点质量评分系统与对等点管理器（PeerManager）紧密集成：

- 在 PeerManager 初始化时创建 PeerQualityManager
- 在处理新对等点时使用质量管理器过滤低质量对等点
- 在连接对等点前检查对等点质量和黑名单状态
- 在更新对等点统计时更新对等点质量评分
- 在移除失败对等点时记录违规行为

### 7.2 与消息处理器集成

对等点质量评分系统与消息处理器（PeerMessageHandler）集成：

- 在处理各类消息时更新对等点质量评分
- 在处理拒绝消息时记录违规行为
- 在处理位图消息时更新连接稳定性和协议兼容性评分

### 7.3 与安全管理器集成

对等点质量评分系统与安全管理器（SecurityManager）集成：

- 使用安全管理器的黑名单功能
- 严重违规时将对等点添加到黑名单
- 通过 BlacklistFilter 过滤黑名单中的对等点

## 8. 配置与调优

### 8.1 评分权重配置

可以通过 PeerQualityConfig 配置各项指标的权重：

```rust
weights.insert("download_speed".to_string(), 0.3);
weights.insert("upload_speed".to_string(), 0.1);
weights.insert("success_rate".to_string(), 0.15);
// ...
```

### 8.2 评分参数配置

可以配置评分计算中使用的参数：

```rust
scoring_params.insert("max_download_speed".to_string(), 5 * 1024.0 * 1024.0); // 5MB/s
scoring_params.insert("max_upload_speed".to_string(), 1024.0 * 1024.0); // 1MB/s
// ...
```

### 8.3 过滤器配置

可以配置过滤器的参数：

```rust
filter_params.insert("performance_threshold".to_string(), "0.3".to_string());
```

## 9. 性能优化

### 9.1 定期清理

系统会定期清理过期的评分记录，避免内存占用过大：

```rust
fn cleanup_expired_scores(&mut self) {
    let max_age = Duration::from_secs(86400); // 24小时
    // ...
}
```

### 9.2 历史记录限制

历史评分记录数量有限制，避免无限增长：

```rust
// 保持历史记录在合理范围内
if self.historical_scores.len() > 10 {
    self.historical_scores.pop_front();
}
```

## 10. 未来扩展

### 10.1 机器学习集成

未来可以考虑集成机器学习算法，基于历史数据自动调整评分权重和参数。

### 10.2 P2P 信誉系统

可以考虑实现基于区块链的 P2P 信誉系统，让多个客户端共享对等点评分信息。

### 10.3 高级地理位置分析

可以集成更高级的地理位置分析，考虑网络拓扑、延迟等因素。

### 10.4 行为模式分析

实现对等点行为模式分析，识别潜在的恶意行为模式。

## 11. 总结

对等点质量评分系统通过多维度评估对等点质量，帮助 Tonitru 下载器优化对等点选择，提高下载效率和安全性。该系统与下载器的其他组件紧密集成，形成了一个完整的对等点质量管理解决方案。