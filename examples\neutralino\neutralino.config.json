{"applicationId": "com.lumen.downloader", "version": "1.0.0", "defaultMode": "window", "port": 0, "documentRoot": "/resources/", "url": "/", "enableServer": true, "enableNativeAPI": true, "tokenSecurity": "one-time", "logging": {"enabled": true, "writeToLogFile": true}, "nativeAllowList": ["app.*", "os.*", "filesystem.*", "extensions.*", "clipboard.*", "window.*", "events.*"], "modes": {"window": {"title": "Lu<PERSON> 下载管理器", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "center": true, "fullScreen": false, "alwaysOnTop": false, "icon": "/resources/icons/appIcon.png", "enableInspector": true, "borderless": false, "maximize": false, "hidden": false, "resizable": true, "exitProcessOnClose": true}}, "cli": {"binaryName": "lumen", "resourcesPath": "/resources/", "extensionsPath": "/extensions/", "clientLibrary": "/resources/js/neutralino.js", "binaryVersion": "4.14.1", "clientVersion": "3.12.0", "frontendLibrary": {"patchVersion": "4.14.1"}}, "extensions": [{"id": "js.neutralino.urlprotocol", "commandDictionary": [{"name": "register", "description": "Register a URL protocol handler"}, {"name": "unregister", "description": "Unregister a URL protocol handler"}, {"name": "isRegistered", "description": "Check if a URL protocol handler is registered"}]}]}