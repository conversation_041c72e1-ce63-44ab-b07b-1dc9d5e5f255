#[cfg(test)]
mod tests {
    use anyhow::Result;
    use bytes::Bytes;
    use std::sync::Arc;
    use std::time::Duration;
    use tokio::time::sleep;

    use tonitru_downloader::protocols::bittorrent::webseed::WebSeedCache;
    use tonitru_downloader::protocols::bittorrent::webseed::cache::WebSeedCacheConfig;

    #[tokio::test]
    async fn test_webseed_cache_basic() -> Result<()> {
        // 创建缓存配置
        let config = WebSeedCacheConfig {
            enabled: true,
            max_items: 10,
            ttl: 1, // 1秒过期
            cleanup_interval: 1,
        };

        // 创建缓存
        let cache = WebSeedCache::new(config);

        // 添加缓存项
        let key = "test_key".to_string();
        let data = Bytes::from("test_data");
        cache.set(key.clone(), data.clone()).await?;

        // 获取缓存项
        let cached_data = cache.get(&key).await;
        assert!(cached_data.is_some());
        assert_eq!(cached_data.unwrap(), data);

        // 等待缓存过期
        sleep(Duration::from_secs(2)).await;

        // 再次获取缓存项，应该已过期
        let cached_data = cache.get(&key).await;
        assert!(cached_data.is_none());

        Ok(())
    }

    #[tokio::test]
    async fn test_webseed_cache_eviction() -> Result<()> {
        // 创建缓存配置
        let config = WebSeedCacheConfig {
            enabled: true,
            max_items: 2, // 最多2个项
            ttl: 60,
            cleanup_interval: 60,
        };

        // 创建缓存
        let cache = WebSeedCache::new(config);

        // 添加3个缓存项，应该会淘汰最旧的
        let key1 = "key1".to_string();
        let key2 = "key2".to_string();
        let key3 = "key3".to_string();

        let data1 = Bytes::from("data1");
        let data2 = Bytes::from("data2");
        let data3 = Bytes::from("data3");

        cache.set(key1.clone(), data1.clone()).await?;
        sleep(Duration::from_millis(10)).await; // 确保时间戳不同
        cache.set(key2.clone(), data2.clone()).await?;
        sleep(Duration::from_millis(10)).await; // 确保时间戳不同
        cache.set(key3.clone(), data3.clone()).await?;

        // key1应该被淘汰
        let cached_data1 = cache.get(&key1).await;
        assert!(cached_data1.is_none());

        // key2和key3应该还在
        let cached_data2 = cache.get(&key2).await;
        let cached_data3 = cache.get(&key3).await;

        assert!(cached_data2.is_some());
        assert!(cached_data3.is_some());

        assert_eq!(cached_data2.unwrap(), data2);
        assert_eq!(cached_data3.unwrap(), data3);

        Ok(())
    }

    #[tokio::test]
    async fn test_webseed_cache_disabled() -> Result<()> {
        // 创建禁用的缓存配置
        let config = WebSeedCacheConfig {
            enabled: false,
            max_items: 10,
            ttl: 60,
            cleanup_interval: 60,
        };

        // 创建缓存
        let cache = WebSeedCache::new(config);

        // 添加缓存项
        let key = "test_key".to_string();
        let data = Bytes::from("test_data");
        cache.set(key.clone(), data.clone()).await?;

        // 获取缓存项，应该为None（因为缓存已禁用）
        let cached_data = cache.get(&key).await;
        assert!(cached_data.is_none());

        Ok(())
    }

    #[tokio::test]
    async fn test_webseed_cache_stats() -> Result<()> {
        // 创建缓存配置
        let config = WebSeedCacheConfig {
            enabled: true,
            max_items: 10,
            ttl: 60,
            cleanup_interval: 60,
        };

        // 创建缓存
        let cache = WebSeedCache::new(config);

        // 添加缓存项
        let key = "test_key".to_string();
        let data = Bytes::from("test_data");
        cache.set(key.clone(), data.clone()).await?;

        // 获取缓存项（命中）
        let _ = cache.get(&key).await;

        // 获取不存在的缓存项（未命中）
        let _ = cache.get("non_existent_key").await;

        // 检查统计信息
        let (size, hits, misses) = cache.stats().await;
        assert_eq!(size, 1); // 1个缓存项
        assert_eq!(hits, 1); // 1次命中
        assert_eq!(misses, 1); // 1次未命中

        Ok(())
    }
}
