// BitTorrent protocol module
//
// This module contains the implementation of the BitTorrent protocol.
// It is split into several sub-modules to improve maintainability and follow
// the single responsibility principle.

mod core;
mod init;
mod tracker;
mod dht;
mod webseed;
mod download;
mod upload;
mod peer;
mod stats;
mod implementation;

// Re-export the main protocol struct and implementation
pub use core::BitTorrentProtocol;
