// Import common test utilities
pub mod common;

// Import HTTP downloader tests
// mod http_basic_test;
mod http_error_handling_test;
// mod http_client_test;


mod torrent_parser_test;
mod peer_test;
mod piece_manager_test;
mod block_manager_test;

// Import storage tests
// mod storage_test;

// Import analyzer tests
// mod link_analyzer_test;

// Import bandwidth scheduler tests
mod bandwidth_scheduler_test;

// Import config tests
mod config_manager_test;


// Import integration tests (only compiled with the integration_tests feature)

#[cfg(feature = "integration_tests")]
mod http_simple_test;

#[cfg(feature = "integration_tests")]
mod http_speed_limit_test;


