use anyhow::Result;
use std::sync::Arc;
use uuid::Uuid;
use serde_json::from_str;

use crate::cli::backend::backend::CliBackend;
use crate::cli::commands::download::args::BatchTasksArgs;
use crate::download::manager::{TaskInfo, TaskStatus};

/// 获取要处理的任务ID列表
pub async fn get_task_ids_to_process(backend: &Arc<dyn CliBackend>, args: &BatchTasksArgs) -> Result<Vec<Uuid>> {
    let mut task_ids = Vec::new();
    
    // 如果指定了任务ID列表
    if let Some(ids_str) = &args.task_ids {
        for id_str in ids_str.split(',') {
            let id_str = id_str.trim();
            if !id_str.is_empty() {
                match Uuid::parse_str(id_str) {
                    Ok(id) => task_ids.push(id),
                    Err(e) => return Err(anyhow::anyhow!("无效的任务ID '{}': {}", id_str, e)),
                }
            }
        }
    }
    
    // 如果指定了状态过滤或全部任务
    if args.status.is_some() || args.all {
        let all_tasks = backend.get_all_download_tasks().await?;
        let all_task_infos: Vec<TaskInfo> = all_tasks
            .into_iter()
            .filter_map(|task_str| from_str::<TaskInfo>(&task_str).ok())
            .collect();
        
        for task in all_task_infos {
            // 如果指定了状态过滤，只处理匹配状态的任务
            if let Some(status) = &args.status {
                let task_status = match task.status {
                    TaskStatus::Pending => "pending",
                    TaskStatus::Initializing => "initializing",
                    TaskStatus::Downloading => "downloading",
                    TaskStatus::Paused => "paused",
                    TaskStatus::Completed => "completed",
                    TaskStatus::Failed => "failed",
                    TaskStatus::Cancelled => "cancelled",
                    TaskStatus::Error => "error",
                };
                
                if task_status == status && !task_ids.contains(&task.id) {
                    task_ids.push(task.id);
                }
            }
            // 如果指定了全部任务，添加所有任务ID
            else if args.all && !task_ids.contains(&task.id) {
                task_ids.push(task.id);
            }
        }
    }
    
    if task_ids.is_empty() {
        return Err(anyhow::anyhow!("没有找到要处理的任务"));
    }
    
    Ok(task_ids)
}