use config::{Config, ConfigError, File, Environment};
use serde::{Deserialize, Serialize};
use std::path::Path;

use super::constants::*;

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
}

#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]
pub struct DownloadConfig {
    pub path: String,
    pub concurrent_downloads: i64,
    pub connections_per_download: i64,
    pub chunk_size: i64,
    pub buffer_size: i64,
    pub speed_limit: Option<u64>,
    pub upload_speed_limit: Option<u64>,
    pub temp_file_extension: Option<String>,
    pub flush_interval_ms: Option<u64>,
}

#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]
pub struct DatabaseConfig {
    pub url: String,
}

#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct MirrorConfig {
    pub enabled: bool,
    pub max_mirrors: i64,
    pub health_check_interval_secs: u64,
}

#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]
pub struct DHTConfig {
    pub enabled: bool,
    pub port: u16,
    pub bootstrap_nodes: Vec<String>,
    pub routing_table_size: usize,
    pub node_timeout: u64,
    pub query_timeout: u64,
    pub auto_start: bool,
    pub maintenance_interval: u64,
    pub enable_ipv6: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ProxyConfig {
    pub enabled: bool,
    pub url: String,
    pub username: Option<String>,
    pub password: Option<String>,
    pub no_proxy: Vec<String>,
    pub proxy_type: String, // "http", "https", "socks5"
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct BitTorrentConfig {
    pub enabled: bool,
    pub port: u16,
    pub max_peers: i64,
    pub max_connections: i64,
    pub connection_timeout: u64,
    pub request_timeout: u64,
    pub default_trackers: Vec<String>,
    pub enable_dht: bool,
    pub peer_connect_timeout: u64,
    pub tracker_interval: u64,
    pub peer_handshake_timeout_secs: u64,
    pub dht_port: u16,
    pub dht_bootstrap_nodes: Vec<String>,
    pub dht_routing_table_size: i64,
    pub dht_node_timeout: u64,
    pub dht_query_timeout: u64,
    pub dht_auto_start: bool,
    pub dht_maintenance_interval: u64,
    pub dht_routing_table_path: Option<String>,
    pub dht_routing_table_save_interval: u64,
    pub dht_load_routing_table: bool,
    pub enable_ipv6: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct Settings {
    pub server: ServerConfig,
    pub download: DownloadConfig,
    pub database: DatabaseConfig,
    pub mirror: MirrorConfig,
    pub proxy: Option<ProxyConfig>,
    pub bittorrent: Option<BitTorrentConfig>,
    // Legacy fields for backward compatibility
    pub dht: Option<DHTConfig>,
    pub enable_dht: Option<bool>,
    pub dht_port: Option<u16>,
    pub dht_bootstrap_nodes: Option<Vec<String>>,
    pub dht_routing_table_size: Option<usize>,
    pub dht_node_timeout: Option<u64>,
    pub dht_query_timeout: Option<u64>,
    pub dht_auto_start: Option<bool>,
    pub dht_maintenance_interval: Option<u64>,
    pub dht_routing_table_path: Option<String>,
    pub dht_routing_table_save_interval: Option<u64>,
    pub dht_load_routing_table: Option<bool>,
    pub data_dir: Option<String>,
    // WebSeed settings
    pub webseed_enabled: Option<bool>,
    pub webseed_max_connections: Option<usize>,
    pub webseed_connection_timeout: Option<u64>,
    pub webseed_read_timeout: Option<u64>,
    pub webseed_availability_check_interval: Option<u64>,
    pub webseed_min_piece_size: Option<usize>,
    pub webseed_prefer: Option<bool>,
    pub webseed_retry_count: Option<u32>,
    pub webseed_retry_delay: Option<u64>,
    // WebSeed cache settings
    pub webseed_cache_enabled: Option<bool>,
    pub webseed_cache_max_items: Option<usize>,
    pub webseed_cache_ttl: Option<u64>,
    pub webseed_cache_cleanup_interval: Option<u64>,
    // Upload settings
    pub upload_enabled: Option<bool>,
    // IPv6 settings
    pub enable_ipv6: Option<bool>,
    // Piece selection strategy
    pub piece_selection_strategy: Option<String>,
    // Block manager settings
    pub block_size: Option<i64>,
    pub block_timeout: Option<u64>,
    // Stats manager settings
    pub stats_update_interval: Option<u64>,
}

impl Settings {
    pub fn new() -> Result<Self, ConfigError> {
        // 使用ConfigBuilder创建配置
        let builder = Config::builder()
            // 设置默认值
            .set_default("server.host", DEFAULT_HOST)?
            .set_default("server.port", DEFAULT_PORT)?
            .set_default("download.path", DEFAULT_DOWNLOAD_PATH)?
            .set_default("download.concurrent_downloads", DEFAULT_CONCURRENT_DOWNLOADS)?
            .set_default("download.connections_per_download", DEFAULT_CONNECTIONS_PER_DOWNLOAD)?
            .set_default("download.chunk_size", DEFAULT_CHUNK_SIZE)?
            .set_default("download.buffer_size", DEFAULT_BUFFER_SIZE)?
            .set_default("download.speed_limit", DEFAULT_SPEED_LIMIT)?
            .set_default("download.upload_speed_limit", DEFAULT_SPEED_LIMIT)?
            .set_default("download.temp_file_extension", ".tmp")?
            .set_default("download.flush_interval_ms", 1000)?
            .set_default("database.url", DATABASE_URL)?
            .set_default("mirror.enabled", true)?
            .set_default("mirror.max_mirrors", MAX_MIRRORS)?
            .set_default("mirror.health_check_interval_secs", MIRROR_HEALTH_CHECK_INTERVAL_SECS)?
            .set_default("proxy.enabled", DEFAULT_PROXY_ENABLED)?
            .set_default("proxy.url", DEFAULT_PROXY_URL)?
            .set_default("proxy.proxy_type", DEFAULT_PROXY_TYPE)?
            .set_default("proxy.no_proxy", Vec::<String>::new())?
            // BitTorrent settings
            .set_default("bittorrent.enabled", DEFAULT_BT_ENABLED)?
            .set_default("bittorrent.port", DEFAULT_BT_PORT)?
            .set_default("bittorrent.max_peers", DEFAULT_BT_MAX_PEERS)?
            .set_default("bittorrent.max_connections", DEFAULT_BT_MAX_CONNECTIONS)?
            .set_default("bittorrent.connection_timeout", DEFAULT_BT_CONNECTION_TIMEOUT)?
            .set_default("bittorrent.request_timeout", DEFAULT_BT_REQUEST_TIMEOUT)?
            .set_default("bittorrent.default_trackers", DEFAULT_BT_TRACKERS.to_vec())?
            .set_default("bittorrent.enable_dht", true)?
            .set_default("bittorrent.dht_port", 6881)?
            .set_default("bittorrent.dht_bootstrap_nodes", vec![
                "router.bittorrent.com:6881".to_string(),
                "dht.transmissionbt.com:6881".to_string(),
                "router.utorrent.com:6881".to_string(),
            ])?
            .set_default("bittorrent.dht_routing_table_size", 8)?
            .set_default("bittorrent.dht_node_timeout", 900)?
            .set_default("bittorrent.dht_query_timeout", 30)?
            .set_default("bittorrent.dht_auto_start", true)?
            .set_default("bittorrent.dht_maintenance_interval", 300)?
            .set_default("bittorrent.dht_routing_table_path", "data/dht_routing_table.json")?
            .set_default("bittorrent.dht_routing_table_save_interval", 600)?
            .set_default("bittorrent.dht_load_routing_table", true)?
            .set_default("bittorrent.enable_ipv6", true)?
            .set_default("bittorrent.peer_connect_timeout", DEFAULT_PEER_CONNECT_TIMEOUT)?
            .set_default("bittorrent.tracker_interval", DEFAULT_TRACKER_INTERVAL)?
            .set_default("bittorrent.peer_handshake_timeout_secs", DEFAULT_PEER_HANDSHAKE_TIMEOUT_SECS)?
            // Legacy settings for backward compatibility
            .set_default("enable_dht", true)?
            .set_default("dht_port", 6881)?
            .set_default("dht_routing_table_size", 8)?
            .set_default("dht_node_timeout", 900)?
            .set_default("dht_query_timeout", 30)?
            .set_default("dht_auto_start", true)?
            .set_default("dht_maintenance_interval", 300)?
            .set_default("dht_routing_table_path", "data/dht_routing_table.json")?
            .set_default("dht_routing_table_save_interval", 600)?
            .set_default("dht_load_routing_table", true)?
            .set_default("data_dir", "data")?
            // WebSeed settings
            .set_default("webseed_enabled", true)?
            .set_default("webseed_max_connections", 4)?
            .set_default("webseed_connection_timeout", 30)?
            .set_default("webseed_read_timeout", 60)?
            .set_default("webseed_availability_check_interval", 300)?
            .set_default("webseed_min_piece_size", 16384)?
            .set_default("webseed_prefer", false)?
            .set_default("webseed_retry_count", 3)?
            .set_default("webseed_retry_delay", 5)?
            // WebSeed cache settings
            .set_default("webseed_cache_enabled", true)?
            .set_default("webseed_cache_max_items", 1000)?
            .set_default("webseed_cache_ttl", 3600)?
            .set_default("webseed_cache_cleanup_interval", 300)?
            .set_default("upload_enabled", true)?
            .set_default("enable_ipv6", true)?
            .set_default("piece_selection_strategy", "rarest_first")?
            // Block manager settings
            .set_default("block_size", DEFAULT_BLOCK_SIZE)?
            .set_default("block_timeout", DEFAULT_BLOCK_TIMEOUT)?
            // Stats manager settings
            .set_default("stats_update_interval", DEFAULT_STATS_UPDATE_INTERVAL)?;

        // 从文件加载配置（如果存在）
        let builder = if Path::new("config/default.toml").exists() {
            builder.add_source(File::with_name("config/default"))
        } else {
            builder
        };

        // 从环境变量加载配置
        let builder = builder.add_source(Environment::with_prefix("APP").separator("__"));

        // 构建配置并转换为Settings类型
        let config = builder.build()?;
        config.try_deserialize()
    }
}

impl Default for Settings {
    fn default() -> Self {
        Self::new().expect("Failed to load default settings")
    }
}
