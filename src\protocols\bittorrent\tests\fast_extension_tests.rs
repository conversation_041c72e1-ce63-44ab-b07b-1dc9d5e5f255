#[cfg(test)]
mod tests {
    use std::net::{IpAddr, Ipv4Addr};
    use std::sync::Arc;
    use std::collections::HashSet;
    use anyhow::Result;
    use tokio::net::TcpListener;
    use tokio::io::{AsyncReadExt, AsyncWriteExt};
    use bytes::{BytesMut, BufMut};
    use crate::core::p2p::peer::Peer;

    use crate::protocols::bittorrent::fast_extension::calculate_allowed_fast;
    use crate::protocols::bittorrent::peer::BitTorrentPeer;
    use crate::protocols::bittorrent::message::BitTorrentMessage;
    use crate::core::p2p::piece::{PieceManager, PieceInfo};

    // 模拟PieceManager实现
    struct MockPieceManager {
        pieces: HashSet<u32>,
        piece_size: u64,
    }

    #[async_trait::async_trait]
    impl PieceManager for MockPieceManager {
        async fn init(&mut self) -> Result<()> {
            Ok(())
        }

        async fn get_piece_info(&self, index: u32) -> Result<Option<PieceInfo>> {
            Ok(Some(PieceInfo {
                index,
                size: self.piece_size,
                hash: Some(vec![0; 20]),
                state: if self.pieces.contains(&index) {
                    crate::core::p2p::piece::PieceState::Verified
                } else {
                    crate::core::p2p::piece::PieceState::Missing
                },
                priority: 0,
                progress: 0.0,
            }))
        }

        async fn next_piece(&mut self) -> Result<Option<PieceInfo>> {
            Ok(None)
        }

        async fn add_piece_data(&mut self, _index: u32, _data: Vec<u8>) -> Result<bool> {
            Ok(true)
        }

        async fn piece_state(&self, index: u32) -> Result<crate::core::p2p::piece::PieceState> {
            if self.pieces.contains(&index) {
                Ok(crate::core::p2p::piece::PieceState::Verified)
            } else {
                Ok(crate::core::p2p::piece::PieceState::Missing)
            }
        }

        async fn progress(&self) -> Result<f64> {
            Ok(0.0)
        }

        async fn downloaded_size(&self) -> Result<u64> {
            Ok(0)
        }

        async fn total_size(&self) -> Result<u64> {
            Ok(0)
        }

        async fn save_data(&self) -> Result<()> {
            Ok(())
        }

        async fn verify_piece(&mut self, _index: u32) -> Result<bool> {
            Ok(true)
        }

        async fn is_complete(&self) -> Result<bool> {
            Ok(false)
        }

        async fn all_piece_states(&self) -> Result<Vec<crate::core::p2p::piece::PieceState>> {
            Ok(vec![crate::core::p2p::piece::PieceState::Missing; 1000])
        }

        async fn get_requested_pieces(&self) -> Result<HashSet<u32>> {
            Ok(HashSet::new())
        }

        async fn get_piece_rarities(&self) -> Result<Vec<usize>> {
            Ok(vec![0; 1000])
        }
    }

    // 为测试添加额外的方法
    impl MockPieceManager {
        async fn has_piece(&self, index: u32) -> Result<bool> {
            Ok(self.pieces.contains(&index))
        }

        async fn read_block(&self, index: u32, begin: u32, length: u32) -> Result<Vec<u8>> {
            println!("MockPieceManager.read_block called with index={}, begin={}, length={}", index, begin, length);
            if !self.pieces.contains(&index) {
                println!("Piece {} not found in MockPieceManager", index);
                return Err(anyhow::anyhow!("Piece not found"));
            }
            println!("Returning data block of length {}", length);
            Ok(vec![0; length as usize])
        }

        async fn mark_piece_complete(&mut self, index: u32) -> Result<()> {
            self.pieces.insert(index);
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_calculate_allowed_fast() {
        let peer_ip = IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1));
        let info_hash = [0u8; 20]; // 全零的信息哈希用于测试
        let total_pieces = 1000;
        let allowed_count = 10;

        let result = calculate_allowed_fast(&peer_ip, &info_hash, total_pieces, allowed_count);

        // 验证结果
        assert_eq!(result.len(), allowed_count);
        for &piece in &result {
            assert!(piece < total_pieces);
        }

        // 验证结果是确定性的
        let result2 = calculate_allowed_fast(&peer_ip, &info_hash, total_pieces, allowed_count);
        assert_eq!(result, result2);
    }

    #[tokio::test]
    async fn test_fast_extension_handshake() {
        // 创建一个模拟的TCP服务器
        let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
        let addr = listener.local_addr().unwrap();

        // 在另一个任务中接受连接
        let handle = tokio::spawn(async move {
            let (mut socket, _) = listener.accept().await.unwrap();

            // 读取握手消息
            let mut buf = vec![0; 68];
            socket.read_exact(&mut buf).await.unwrap();

            // 验证握手消息
            assert_eq!(buf[0], 19);
            assert_eq!(&buf[1..20], b"BitTorrent protocol");

            // 检查Fast Extension标志位
            assert_eq!(buf[27] & 0x04, 0x04);

            // 发送握手响应
            let mut response = BytesMut::with_capacity(68);
            response.put_u8(19);
            response.put_slice(b"BitTorrent protocol");

            // 设置Fast Extension标志位
            let mut reserved = [0u8; 8];
            reserved[7] |= 0x04;
            response.put_slice(&reserved);

            // 信息哈希和对等点ID
            response.put_slice(&[0u8; 20]); // 信息哈希
            response.put_slice(b"MockPeerID0123456789"); // 对等点ID

            socket.write_all(&response).await.unwrap();

            // 等待allowed_fast消息
            let mut length_buf = [0u8; 4];
            socket.read_exact(&mut length_buf).await.unwrap();
            let _length = u32::from_be_bytes(length_buf);

            let mut id_buf = [0u8; 1];
            socket.read_exact(&mut id_buf).await.unwrap();
            let id = id_buf[0];

            // 验证是否为allowed_fast消息
            assert_eq!(id, 0x11);

            // 读取分片索引
            let mut index_buf = [0u8; 4];
            socket.read_exact(&mut index_buf).await.unwrap();
            let _index = u32::from_be_bytes(index_buf);

            // 成功
            true
        });

        // 创建BitTorrentPeer并连接
        let mut peer = BitTorrentPeer::new(addr, &[0u8; 20], b"ClientPeerID01234567", 5, 1000, None, None, None).await.unwrap();

        // 连接到对等点
        peer.connect().await.unwrap();

        // 执行握手
        peer.handshake().await.unwrap();

        // 验证Fast Extension支持
        assert!(peer.handshake_handler.supports_fast);

        // 等待服务器任务完成
        assert!(handle.await.unwrap());
    }

    #[tokio::test]
    async fn test_process_uploads() {
        // 创建一个模拟的TCP服务器
        let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
        let addr = listener.local_addr().unwrap();

        // 在另一个任务中接受连接
        let handle = tokio::spawn(async move {
            let (mut socket, _) = listener.accept().await.unwrap();

            // 读取握手消息
            let mut buf = vec![0; 68];
            socket.read_exact(&mut buf).await.unwrap();

            // 发送握手响应
            let mut response = BytesMut::with_capacity(68);
            response.put_u8(19);
            response.put_slice(b"BitTorrent protocol");
            response.put_slice(&[0u8; 8]); // 保留字节
            response.put_slice(&[0u8; 20]); // 信息哈希
            response.put_slice(b"MockPeerID0123456789"); // 对等点ID

            socket.write_all(&response).await.unwrap();

            // 发送请求消息
            let mut request = BytesMut::new();
            request.put_u32(13); // 长度
            request.put_u8(6); // ID = 6 (Request)
            request.put_u32(0); // index
            request.put_u32(0); // begin
            request.put_u32(16384); // length

            socket.write_all(&request).await.unwrap();

            // 等待解除阻塞消息
            let mut length_buf = [0u8; 4];
            socket.read_exact(&mut length_buf).await.unwrap();
            let _length = u32::from_be_bytes(length_buf);

            let mut id_buf = [0u8; 1];
            socket.read_exact(&mut id_buf).await.unwrap();
            let id = id_buf[0];

            // 验证是否为解除阻塞消息
            assert_eq!(id, 1, "Expected unchoke message (1), got message type {}", id);

            // 等待分片消息
            socket.read_exact(&mut length_buf).await.unwrap();
            let length = u32::from_be_bytes(length_buf);

            socket.read_exact(&mut id_buf).await.unwrap();
            let id = id_buf[0];

            // 验证是否为分片消息
            assert_eq!(id, 7, "Expected piece message (7), got message type {}", id);

            // 读取分片索引和偏移
            let mut index_buf = [0u8; 4];
            socket.read_exact(&mut index_buf).await.unwrap();
            let index = u32::from_be_bytes(index_buf);

            let mut begin_buf = [0u8; 4];
            socket.read_exact(&mut begin_buf).await.unwrap();
            let begin = u32::from_be_bytes(begin_buf);

            // 读取数据
            let mut data = vec![0u8; length as usize - 9];
            socket.read_exact(&mut data).await.unwrap();

            // 验证
            assert_eq!(index, 0);
            assert_eq!(begin, 0);
            assert_eq!(data.len(), 16384);

            // 成功
            true
        });

        // 创建BitTorrentPeer并连接
        let mut peer = BitTorrentPeer::new(addr, &[0u8; 20], b"ClientPeerID01234567", 5, 1000, None, None, None).await.unwrap();

        // 连接到对等点
        peer.connect().await.unwrap();

        // 执行握手
        peer.handshake().await.unwrap();

        // 设置支持Fast Extension
        peer.handshake_handler.supports_fast = true;
        peer.upload_manager.supports_fast = true;

        // 解除阻塞对方
        peer.send_bt_message(BitTorrentMessage::Unchoke).await.unwrap();

        // 创建模拟PieceManager
        let mock_piece_manager = MockPieceManager {
            pieces: HashSet::from([0]), // 直接初始化为包含分片0
            piece_size: 16384,
        };

        // 添加请求到上传管理器
        peer.upload_manager.upload_requests.push((0, 0, 16384));

        // 打印调试信息
        println!("Peer supports_fast: {}", peer.handshake_handler.supports_fast);
        println!("Peer am_choking: {}", peer.upload_manager.am_choking);
        println!("Piece state: {:?}", mock_piece_manager.piece_state(0).await.unwrap());

        // 处理上传请求
        let piece_manager: Arc<tokio::sync::Mutex<dyn PieceManager>> = Arc::new(tokio::sync::Mutex::new(mock_piece_manager));
        peer.process_uploads(&piece_manager).await.unwrap();

        // 设置上传管理器状态
        peer.upload_manager.set_choking(false);

        // 等待服务器任务完成
        assert!(handle.await.unwrap());
    }
}
