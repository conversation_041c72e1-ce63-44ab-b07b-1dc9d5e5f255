//! CLI 核心组件初始化模块
//!
//! 提供初始化CLI所需的核心组件的功能。

use std::path::{PathBuf, Path};
use std::sync::Arc;
use anyhow::{Result, anyhow};
use config::{Config, File, Environment};

use crate::config::{ConfigManager, Settings};
use crate::download::manager::{DownloadManager, DownloadManagerImpl};
use crate::core::interfaces::storage::{Storage, StorageType, StorageFactory};
use crate::analyzer::LinkAnalyzerImpl;
use crate::download::DownloaderFactoryImpl;
use crate::storage::storage_impl::LocalStorageFactory;
use crate::download::resume::ResumeManagerImpl;
use crate::cli::storage::StorageWrapper;
use crate::download::event_notifier::NoopEventNotifier;
use crate::protocols::custom_p2p::CustomP2PFactory;

/// 初始化核心组件
pub async fn initialize_core_components(
    config_path: &Option<PathBuf>,
) -> Result<(Arc<dyn DownloadManager>, Arc<ConfigManager>, Arc<dyn Storage>)> {
    // 创建配置管理器
    let config_manager = if let Some(path) = config_path {
        // 从指定路径加载配置
        let config_dir = path.parent().unwrap_or_else(|| Path::new(""));
        let config_file = path.file_name().unwrap_or_default().to_str().unwrap_or("default");
        
        // 使用Config库从指定路径加载配置
        let builder = Config::builder()
            .add_source(File::with_name(config_dir.join(config_file).to_str().unwrap_or("config/default")))
            .add_source(Environment::with_prefix("APP").separator("__"));
            
        let config = builder.build()
            .map_err(|e| anyhow!("Failed to load config from {}: {}", path.display(), e))?;
            
        let settings = config.try_deserialize::<Settings>()
            .map_err(|e| anyhow!("Failed to deserialize settings from {}: {}", path.display(), e))?;
            
        ConfigManager::new_with_settings(settings)
    } else {
        ConfigManager::new().await?
    };
    let config_manager = Arc::new(config_manager);

    // 创建存储
    let storage_factory = LocalStorageFactory;
    let storage_impl = storage_factory.create_storage(StorageType::Local, &config_manager.get_settings().await.download.path).await?;
    // 将Box<dyn Storage>转换为Arc<dyn Storage>
    let storage = Arc::new(StorageWrapper(storage_impl));

    // 创建恢复管理器
    let resume_manager = Arc::new(ResumeManagerImpl::new(config_manager.get_settings().await, storage.clone()));

    // 创建下载管理器
    let link_analyzer = Arc::new(LinkAnalyzerImpl::new());
    let downloader_factory = Arc::new(DownloaderFactoryImpl::new(
        config_manager.clone(),
        link_analyzer.clone(),
        resume_manager.clone(),
    ));
    
    // 创建 CustomP2PFactory 实例
    let p2p_factory = CustomP2PFactory::new(Arc::new(ConfigManager::with_settings(config_manager.get_settings().await.clone())));
    
    let download_manager: Arc<dyn DownloadManager> = Arc::new(DownloadManagerImpl::new(
        Arc::new(NoopEventNotifier {}),
        config_manager.get_settings().await,
        link_analyzer,
        p2p_factory,
        resume_manager.clone(),
        downloader_factory,
    ));

    Ok((download_manager, config_manager, storage))
}
