use anyhow::Result;
use std::sync::Arc;
use log::{debug, trace, info, warn};

use crate::cli::backend::backend::CliBackend;
use crate::cli::commands::download::args::BatchTasksArgs;
use crate::cli::commands::download::utils::get_task_ids_to_process;
use crate::cli::utils::debug::{print_debug_object, DebugLevel, DebugTimer};

/// Handle batch start command
pub async fn handle_batch_start(backend: &Arc<dyn CliBackend>, args: &BatchTasksArgs) -> Result<()> {
    debug!("执行批量启动下载任务");
    print_debug_object("批量启动参数", args, DebugLevel::Verbose);
    
    let mut timer = DebugTimer::new("批量启动任务");
    
    let task_ids = get_task_ids_to_process(backend, args).await?;
    timer.checkpoint("获取任务ID列表");
    
    debug!("获取到 {} 个任务ID", task_ids.len());
    if task_ids.is_empty() {
        info!("没有找到符合条件的任务");
    }
    
    println!("正在批量启动 {} 个任务...", task_ids.len());
    
    let mut success_count = 0;
    let mut failed_tasks = Vec::new();
    
    for task_id in task_ids {
        trace!("正在启动任务: {}", task_id);
        match backend.start_download_task(task_id).await {
            Ok(_) => {
                success_count += 1;
                debug!("任务 {} 启动成功", task_id);
                println!("任务 {} 启动成功", task_id);
            },
            Err(e) => {
                warn!("任务 {} 启动失败: {}", task_id, e);
                failed_tasks.push((task_id, e.to_string()));
                eprintln!("任务 {} 启动失败: {}", task_id, e);
            }
        }
    }
    
    timer.checkpoint("处理所有任务");
    
    debug!("批量启动完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    println!("批量启动完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    
    if !failed_tasks.is_empty() {
        println!("失败的任务:");
        for (task_id, error) in failed_tasks {
            println!("  - {}: {}", task_id, error);
        }
    }
    
    let elapsed = timer.stop();
    debug!("批量启动总耗时: {:?}", elapsed);
    
    Ok(())
}

/// Handle batch pause command
pub async fn handle_batch_pause(backend: &Arc<dyn CliBackend>, args: &BatchTasksArgs) -> Result<()> {
    debug!("执行批量暂停下载任务");
    print_debug_object("批量暂停参数", args, DebugLevel::Verbose);
    
    let mut timer = DebugTimer::new("批量暂停任务");
    
    let task_ids = get_task_ids_to_process(backend, args).await?;
    timer.checkpoint("获取任务ID列表");
    
    debug!("获取到 {} 个任务ID", task_ids.len());
    if task_ids.is_empty() {
        info!("没有找到符合条件的任务");
    }
    
    println!("正在批量暂停 {} 个任务...", task_ids.len());
    
    let mut success_count = 0;
    let mut failed_tasks = Vec::new();
    
    for task_id in task_ids {
        trace!("正在暂停任务: {}", task_id);
        match backend.pause_download_task(task_id).await {
            Ok(_) => {
                success_count += 1;
                debug!("任务 {} 暂停成功", task_id);
                println!("任务 {} 暂停成功", task_id);
            },
            Err(e) => {
                warn!("任务 {} 暂停失败: {}", task_id, e);
                failed_tasks.push((task_id, e.to_string()));
                eprintln!("任务 {} 暂停失败: {}", task_id, e);
            }
        }
    }
    
    timer.checkpoint("处理所有任务");
    
    debug!("批量暂停完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    println!("批量暂停完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    
    if !failed_tasks.is_empty() {
        println!("失败的任务:");
        for (task_id, error) in failed_tasks {
            println!("  - {}: {}", task_id, error);
        }
    }
    
    let elapsed = timer.stop();
    debug!("批量暂停总耗时: {:?}", elapsed);
    
    Ok(())
}

/// Handle batch resume command
pub async fn handle_batch_resume(backend: &Arc<dyn CliBackend>, args: &BatchTasksArgs) -> Result<()> {
    debug!("执行批量恢复下载任务");
    print_debug_object("批量恢复参数", args, DebugLevel::Verbose);
    
    let mut timer = DebugTimer::new("批量恢复任务");
    
    let task_ids = get_task_ids_to_process(backend, args).await?;
    timer.checkpoint("获取任务ID列表");
    
    debug!("获取到 {} 个任务ID", task_ids.len());
    if task_ids.is_empty() {
        info!("没有找到符合条件的任务");
    }
    
    println!("正在批量恢复 {} 个任务...", task_ids.len());
    
    let mut success_count = 0;
    let mut failed_tasks = Vec::new();
    
    for task_id in task_ids {
        trace!("正在恢复任务: {}", task_id);
        match backend.resume_download_task(task_id).await {
            Ok(_) => {
                success_count += 1;
                debug!("任务 {} 恢复成功", task_id);
                println!("任务 {} 恢复成功", task_id);
            },
            Err(e) => {
                warn!("任务 {} 恢复失败: {}", task_id, e);
                failed_tasks.push((task_id, e.to_string()));
                eprintln!("任务 {} 恢复失败: {}", task_id, e);
            }
        }
    }
    
    timer.checkpoint("处理所有任务");
    
    debug!("批量恢复完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    println!("批量恢复完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    
    if !failed_tasks.is_empty() {
        println!("失败的任务:");
        for (task_id, error) in failed_tasks {
            println!("  - {}: {}", task_id, error);
        }
    }
    
    let elapsed = timer.stop();
    debug!("批量恢复总耗时: {:?}", elapsed);
    
    Ok(())
}

/// Handle batch cancel command
pub async fn handle_batch_cancel(backend: &Arc<dyn CliBackend>, args: &BatchTasksArgs) -> Result<()> {
    debug!("执行批量取消下载任务");
    print_debug_object("批量取消参数", args, DebugLevel::Verbose);
    
    let mut timer = DebugTimer::new("批量取消任务");
    
    let task_ids = get_task_ids_to_process(backend, args).await?;
    timer.checkpoint("获取任务ID列表");
    
    debug!("获取到 {} 个任务ID", task_ids.len());
    if task_ids.is_empty() {
        info!("没有找到符合条件的任务");
    }
    
    println!("正在批量取消 {} 个任务...", task_ids.len());
    
    let mut success_count = 0;
    let mut failed_tasks = Vec::new();
    
    for task_id in task_ids {
        trace!("正在取消任务: {}", task_id);
        match backend.cancel_download_task(task_id).await {
            Ok(_) => {
                success_count += 1;
                debug!("任务 {} 取消成功", task_id);
                println!("任务 {} 取消成功", task_id);
            },
            Err(e) => {
                warn!("任务 {} 取消失败: {}", task_id, e);
                failed_tasks.push((task_id, e.to_string()));
                eprintln!("任务 {} 取消失败: {}", task_id, e);
            }
        }
    }
    
    timer.checkpoint("处理所有任务");
    
    debug!("批量取消完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    println!("批量取消完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    
    if !failed_tasks.is_empty() {
        println!("失败的任务:");
        for (task_id, error) in failed_tasks {
            println!("  - {}: {}", task_id, error);
        }
    }
    
    let elapsed = timer.stop();
    debug!("批量取消总耗时: {:?}", elapsed);
    
    Ok(())
}

/// Handle batch remove command
pub async fn handle_batch_remove(backend: &Arc<dyn CliBackend>, args: &BatchTasksArgs) -> Result<()> {
    debug!("执行批量删除下载任务");
    print_debug_object("批量删除参数", args, DebugLevel::Verbose);
    
    let mut timer = DebugTimer::new("批量删除任务");
    
    let task_ids = get_task_ids_to_process(backend, args).await?;
    timer.checkpoint("获取任务ID列表");
    
    debug!("获取到 {} 个任务ID", task_ids.len());
    if task_ids.is_empty() {
        info!("没有找到符合条件的任务");
    }
    
    println!("正在批量删除 {} 个任务...", task_ids.len());
    
    let mut success_count = 0;
    let mut failed_tasks = Vec::new();
    
    for task_id in task_ids {
        trace!("正在删除任务: {}", task_id);
        match backend.remove_download_task(task_id).await {
            Ok(_) => {
                success_count += 1;
                debug!("任务 {} 删除成功", task_id);
                println!("任务 {} 删除成功", task_id);
            },
            Err(e) => {
                warn!("任务 {} 删除失败: {}", task_id, e);
                failed_tasks.push((task_id, e.to_string()));
                eprintln!("任务 {} 删除失败: {}", task_id, e);
            }
        }
    }
    
    timer.checkpoint("处理所有任务");
    
    debug!("批量删除完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    println!("批量删除完成: {} 个成功, {} 个失败", success_count, failed_tasks.len());
    
    if !failed_tasks.is_empty() {
        println!("失败的任务:");
        for (task_id, error) in failed_tasks {
            println!("  - {}: {}", task_id, error);
        }
    }
    
    let elapsed = timer.stop();
    debug!("批量删除总耗时: {:?}", elapsed);
    
    Ok(())
}