# BitTorrent 技术实现指南

## 1. BitTorrent 协议概述

BitTorrent 是一种点对点文件共享协议，允许用户直接相互分享文件，而不需要依赖中央服务器。Tonitru 下载工具实现了完整的 BitTorrent 协议，包括核心功能和多种扩展协议，以提供高效、稳定的下载体验。

### 1.1 协议特点

- **分布式架构**：无中心服务器，用户直接相互连接
- **分片下载**：文件被分割成小块，可以从多个来源并行下载
- **稀有优先**：优先下载网络中稀有的分片，提高整体效率
- **公平交换**：实现了基于贡献的公平交换机制
- **可扩展性**：通过扩展协议支持更多功能

### 1.2 核心概念

- **种子文件 (Torrent)**：包含元数据的文件，描述要下载的内容
- **信息哈希 (Info Hash)**：种子文件内容的唯一标识符
- **Tracker**：帮助对等点相互发现的服务器
- **对等点 (Peer)**：参与文件共享的用户
- **做种者 (Seeder)**：拥有完整文件的对等点
- **下载者 (Leecher)**：正在下载文件的对等点
- **分片 (Piece)**：文件被分割的基本单位
- **区块 (Block)**：分片的更小单位，是数据传输的基本单位

## 2. 核心组件设计

### 2.1 对等点架构

BitTorrent 对等点模块负责与其他 BitTorrent 客户端进行通信，实现 BitTorrent 协议的核心功能。为了提高代码的可维护性和可扩展性，我们对 `peer.rs` 文件进行了解耦，将其拆分为多个职责明确的模块。

#### 2.1.1 模块结构

解耦后的 BitTorrent 对等点架构包含以下模块：

1. **BitTorrentPeer** (`peer.rs`)
   - 主要对等点类，实现 `Peer` 接口
   - 通过组合方式使用其他专用模块
   - 负责协调各个模块的工作
   - 使用 `ConfigManager` 获取动态配置，支持配置更新

2. **消息模块** (`message.rs`)
   - 定义 `BitTorrentMessage` 枚举
   - 实现消息的编码和解码
   - 处理所有 BitTorrent 协议消息类型

3. **握手处理模块** (`handshake.rs`)
   - 负责 BitTorrent 握手过程
   - 处理协议协商
   - 管理 Fast Extension 支持

4. **扩展协议处理模块** (`extension_handler.rs`)
   - 处理 BitTorrent 扩展协议
   - 支持 PEX (Peer Exchange) 扩展
   - 管理扩展协议的状态

5. **上传管理模块** (`upload_manager.rs`)
   - 处理上传请求队列
   - 实现分片请求和响应
   - 管理上传速率限制

6. **下载管理模块** (`download_manager.rs`)
   - 管理下载队列和策略
   - 实现分片选择算法
   - 处理下载速率限制

7. **状态管理模块** (`state_manager.rs`)
   - 维护对等点状态
   - 处理阻塞/解除阻塞逻辑
   - 管理兴趣状态

8. **统计模块** (`statistics.rs`)
   - 收集性能指标
   - 计算上传/下载速率
   - 提供统计数据接口

#### 2.1.2 模块间关系

```
                  ┌─────────────────┐
                  │  BitTorrentPeer │
                  └────────┬────────┘
                           │
           ┌───────────────┼───────────────┐
           │               │               │
┌──────────▼─────────┐ ┌───▼───┐ ┌─────────▼──────────┐
│  HandshakeHandler  │ │Message│ │  ExtensionHandler  │
└──────────┬─────────┘ └───────┘ └─────────┬──────────┘
           │                               │
           │          ┌──────────────────┐ │
           └──────────► StateManager     │◄┘
                      └────────┬─────────┘
                               │
                ┌──────────────┼──────────────┐
                │              │              │
       ┌────────▼─────┐ ┌─────▼──────┐ ┌─────▼────────┐
       │UploadManager │ │DownloadMgr│ │ Statistics   │
       └──────────────┘ └────────────┘ └──────────────┘
```

#### 2.1.3 配置集成

对等点模块与配置管理器集成，支持动态配置更新：

```rust
pub struct BitTorrentPeer {
    // ... 其他字段 ...
    config: Arc<ConfigManager>,
    config_subscription: Option<ConfigSubscription>,
}

impl BitTorrentPeer {
    pub fn new(config: Arc<ConfigManager>, /* 其他参数 */) -> Self {
        let peer = Self {
            // ... 初始化其他字段 ...
            config: Arc::clone(&config),
            config_subscription: None,
        };
        
        // 订阅配置更新
        peer.subscribe_to_config_changes();
        peer
    }
    
    fn subscribe_to_config_changes(&mut self) {
        let weak_self = Arc::downgrade(&self.inner);
        self.config_subscription = Some(self.config.subscribe(move |updated_keys| {
            if let Some(inner) = weak_self.upgrade() {
                if updated_keys.contains("bittorrent.max_peers") ||
                   updated_keys.contains("bittorrent.request_queue_size") {
                    // 更新相关配置
                    let mut inner = inner.lock().unwrap();
                    inner.update_config();
                }
            }
        }));
    }
    
    fn update_config(&mut self) {
        // 从配置管理器获取最新配置
        if let Ok(max_peers) = self.config.get::<usize>("bittorrent.max_peers") {
            self.max_peers = max_peers;
        }
        
        if let Ok(queue_size) = self.config.get::<usize>("bittorrent.request_queue_size") {
            self.download_manager.set_queue_size(queue_size);
        }
        
        // ... 更新其他配置 ...
    }
}
```

### 2.2 分片管理

分片管理模块负责管理文件的分片状态、分片选择策略和数据验证。

#### 2.2.1 分片管理器设计

```rust
pub struct PieceManager {
    /// 种子文件的元数据
    metadata: TorrentMetadata,
    /// 分片状态
    piece_states: Vec<PieceState>,
    /// 分片优先级
    piece_priorities: Vec<PiecePriority>,
    /// 已完成的分片数量
    completed_pieces: usize,
    /// 总分片数量
    total_pieces: usize,
    /// 分片选择策略
    selection_strategy: Box<dyn PieceSelectionStrategy>,
    /// 文件存储接口
    storage: Arc<dyn Storage>,
    /// 分片验证器
    validator: PieceValidator,
}

#[derive(Clone, Copy, PartialEq, Eq)]
pub enum PieceState {
    /// 未下载
    Missing,
    /// 部分下载
    Partial(usize), // 已下载的字节数
    /// 已下载但未验证
    Downloaded,
    /// 已验证
    Verified,
}

#[derive(Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum PiecePriority {
    Skip,
    Low,
    Normal,
    High,
    Critical,
}
```

#### 2.2.2 分片选择策略

```rust
pub trait PieceSelectionStrategy: Send + Sync {
    /// 从可用分片中选择下一个要下载的分片
    fn select_next_piece(
        &self,
        peer_bitfield: &Bitfield,
        piece_states: &[PieceState],
        piece_priorities: &[PiecePriority],
        rarest_pieces: &[usize],
    ) -> Option<usize>;
}

/// 稀有优先策略
pub struct RarestFirstStrategy;

impl PieceSelectionStrategy for RarestFirstStrategy {
    fn select_next_piece(
        &self,
        peer_bitfield: &Bitfield,
        piece_states: &[PieceState],
        piece_priorities: &[PiecePriority],
        rarest_pieces: &[usize],
    ) -> Option<usize> {
        // 首先检查高优先级分片
        for &piece_index in rarest_pieces {
            if peer_bitfield.has_piece(piece_index) &&
               piece_states[piece_index] == PieceState::Missing &&
               piece_priorities[piece_index] == PiecePriority::Critical {
                return Some(piece_index);
            }
        }
        
        // 然后按稀有程度选择普通优先级分片
        for &piece_index in rarest_pieces {
            if peer_bitfield.has_piece(piece_index) &&
               piece_states[piece_index] == PieceState::Missing &&
               piece_priorities[piece_index] >= PiecePriority::Normal {
                return Some(piece_index);
            }
        }
        
        // 最后考虑低优先级分片
        for &piece_index in rarest_pieces {
            if peer_bitfield.has_piece(piece_index) &&
               piece_states[piece_index] == PieceState::Missing &&
               piece_priorities[piece_index] == PiecePriority::Low {
                return Some(piece_index);
            }
        }
        
        None
    }
}
```

#### 2.2.3 分片验证

```rust
pub struct PieceValidator {
    piece_length: usize,
    last_piece_length: usize,
    piece_hashes: Vec<[u8; 20]>,
}

impl PieceValidator {
    pub fn new(metadata: &TorrentMetadata) -> Self {
        Self {
            piece_length: metadata.piece_length,
            last_piece_length: metadata.calculate_last_piece_length(),
            piece_hashes: metadata.piece_hashes.clone(),
        }
    }
    
    pub fn validate_piece(&self, piece_index: usize, data: &[u8]) -> bool {
        let expected_length = if piece_index == self.piece_hashes.len() - 1 {
            self.last_piece_length
        } else {
            self.piece_length
        };
        
        if data.len() != expected_length {
            return false;
        }
        
        let mut hasher = Sha1::new();
        hasher.update(data);
        let hash = hasher.finalize();
        
        hash.as_slice() == self.piece_hashes[piece_index]
    }
}
```

### 2.3 性能优化

#### 2.3.1 性能监控指标

为了监控和优化 BitTorrent 对等点的性能，我们实现了以下性能指标收集：

1. **连接指标**
   - 活跃连接数
   - 连接建立成功率
   - 连接建立时间
   - 连接持续时间

2. **传输指标**
   - 上传/下载速率
   - 分片请求响应时间
   - 请求队列长度
   - 分片验证成功率

3. **协议指标**
   - 消息处理时间
   - 握手成功率
   - 扩展协议协商成功率
   - 消息解析错误率

#### 2.3.2 性能优化策略

1. **连接池管理**
   - 动态调整最大连接数
   - 基于性能指标优先保留高质量连接
   - 实现连接超时和重试机制

2. **请求队列优化**
   - 动态调整请求队列大小
   - 实现请求超时和重试机制
   - 基于响应时间调整请求策略

3. **内存优化**
   - 使用内存池减少分配
   - 实现零拷贝数据传输
   - 优化消息缓冲区管理

4. **CPU 优化**
   - 使用无锁数据结构
   - 实现批处理消息处理
   - 优化哈希计算

## 3. 扩展协议实现

### 3.1 DHT 网络

DHT (分布式哈希表) 是一种无需中央 Tracker 服务器即可发现对等点的技术。Tonitru 实现了 BitTorrent DHT 协议 (BEP-5)。

#### 3.1.1 DHT 节点结构

```rust
pub struct DhtNode {
    /// 节点 ID
    node_id: [u8; 20],
    /// 路由表
    routing_table: RoutingTable,
    /// 存储的值
    storage: DhtStorage,
    /// UDP 套接字
    socket: UdpSocket,
    /// 消息处理器
    message_handler: DhtMessageHandler,
    /// 节点状态
    state: DhtNodeState,
}

pub struct RoutingTable {
    /// 本节点 ID
    local_id: [u8; 20],
    /// K 桶
    buckets: Vec<KBucket>,
}

pub struct KBucket {
    /// 桶范围
    range: (u8, u8),
    /// 节点列表
    nodes: VecDeque<DhtContact>,
    /// 最后更新时间
    last_updated: Instant,
}
```

#### 3.1.2 DHT 消息类型

```rust
pub enum DhtMessage {
    Query(QueryMessage),
    Response(ResponseMessage),
    Error(ErrorMessage),
}

pub struct QueryMessage {
    transaction_id: Vec<u8>,
    query_type: QueryType,
    arguments: HashMap<String, BencodedValue>,
}

pub enum QueryType {
    Ping,
    FindNode,
    GetPeers,
    AnnouncePeer,
}
```

#### 3.1.3 DHT 实现流程

1. **启动流程**
   - 生成随机节点 ID
   - 初始化路由表
   - 绑定 UDP 端口
   - 连接引导节点

2. **节点发现**
   - 发送 find_node 查询
   - 处理响应并更新路由表
   - 定期刷新路由表

3. **对等点查找**
   - 发送 get_peers 查询
   - 处理响应并存储对等点信息
   - 如果没有找到对等点，继续查询更近的节点

4. **对等点宣告**
   - 发送 announce_peer 查询
   - 定期重新宣告

### 3.2 PEX 对等点交换

PEX (Peer Exchange) 是一种允许对等点直接交换其他对等点信息的扩展协议，减少对 Tracker 的依赖。

#### 3.2.1 PEX 消息格式

```rust
pub struct PexMessage {
    /// 已添加的对等点 (IPv4)
    added: Vec<SocketAddrV4>,
    /// 已添加的对等点标志
    added_flags: Vec<u8>,
    /// 已删除的对等点 (IPv4)
    dropped: Vec<SocketAddrV4>,
    /// 已添加的对等点 (IPv6)
    added6: Vec<SocketAddrV6>,
    /// 已添加的对等点标志 (IPv6)
    added_flags6: Vec<u8>,
    /// 已删除的对等点 (IPv6)
    dropped6: Vec<SocketAddrV6>,
}
```

#### 3.2.2 PEX 实现流程

1. **协议协商**
   - 在握手时通过扩展协议协商 PEX 支持
   - 交换支持的扩展消息 ID

2. **对等点交换**
   - 定期发送 PEX 消息，包含新发现的对等点
   - 处理接收到的 PEX 消息，更新对等点列表
   - 过滤重复和无效对等点

3. **对等点管理**
   - 维护已知对等点列表
   - 记录对等点连接状态
   - 实现对等点评分机制

### 3.3 元数据交换

元数据交换扩展 (BEP-9) 允许客户端仅通过信息哈希获取完整的种子文件元数据，无需预先下载 .torrent 文件。

#### 3.3.1 元数据消息类型

```rust
pub enum MetadataMessageType {
    Request = 0,
    Data = 1,
    Reject = 2,
}

pub struct MetadataMessage {
    msg_type: MetadataMessageType,
    piece: usize,
    total_size: Option<usize>,
    data: Option<Vec<u8>>,
}
```

#### 3.3.2 元数据交换流程

1. **协议协商**
   - 在握手时通过扩展协议协商元数据交换支持
   - 交换支持的扩展消息 ID

2. **元数据大小获取**
   - 发送扩展握手消息，包含 metadata_size 字段
   - 接收对方的扩展握手消息，获取元数据大小

3. **元数据下载**
   - 根据元数据大小计算分片数量
   - 发送元数据请求消息，请求各个分片
   - 接收元数据数据消息，组装完整元数据

4. **元数据验证**
   - 计算接收到的元数据的 SHA-1 哈希
   - 验证哈希是否与信息哈希匹配

### 3.4 NAT 穿透

NAT 穿透技术允许位于 NAT 后的对等点直接相互连接，提高连接成功率。

#### 3.4.1 NAT 穿透技术

1. **端口映射**
   - UPnP (通用即插即用)
   - NAT-PMP (NAT 端口映射协议)
   - PCP (端口控制协议)

2. **打洞技术**
   - UDP 打洞
   - TCP 打洞
   - STUN (用于 NAT 的会话遍历实用程序)

#### 3.4.2 NAT 穿透实现

```rust
pub struct NatTraversal {
    /// UPnP 客户端
    upnp: Option<UpnpClient>,
    /// NAT-PMP 客户端
    natpmp: Option<NatPmpClient>,
    /// PCP 客户端
    pcp: Option<PcpClient>,
    /// 映射的端口
    mapped_ports: HashMap<u16, MappedPort>,
    /// NAT 类型
    nat_type: NatType,
}

pub enum NatType {
    Open,
    FullCone,
    RestrictedCone,
    PortRestricted,
    Symmetric,
    Unknown,
}
```

#### 3.4.3 NAT 穿透流程

1. **NAT 类型检测**
   - 使用 STUN 服务器检测 NAT 类型
   - 根据 NAT 类型选择合适的穿透策略

2. **端口映射**
   - 尝试使用 UPnP 映射端口
   - 如果 UPnP 失败，尝试 NAT-PMP
   - 如果 NAT-PMP 失败，尝试 PCP

3. **连接协商**
   - 通过 DHT 或 Tracker 交换连接信息
   - 包括外部 IP、端口和 NAT 类型

4. **打洞过程**
   - 根据双方的 NAT 类型选择打洞策略
   - 同时向对方发送连接请求
   - 处理连接响应

### 3.5 WebSeed 实现

WebSeed 是 BitTorrent 协议的一个重要扩展，允许从 HTTP/HTTPS 服务器下载数据，结合 P2P 和 HTTP 下载的优势。

#### 3.5.1 WebSeed 类型

Tonitru 实现了两种主要的 WebSeed 规范：

1. **BEP 19 (GetRight 风格)**
   - 通过 `url-list` 字段提供直接的文件 URL
   - 支持 HTTP 范围请求
   - 适用于单文件种子和多文件种子

2. **BEP 17 (Hoffman 风格)**
   - 通过 `httpseeds` 字段提供基于种子信息哈希的 URL 模式
   - 需要服务器端支持特定的 URL 格式
   - 更适合多文件种子

#### 3.5.2 WebSeed 源接口

```rust
#[async_trait]
pub trait WebSeedSource: Send + Sync {
    /// 获取源 URL
    fn url(&self) -> &str;

    /// 获取源类型 (HTTP 或 URL)
    fn source_type(&self) -> WebSeedType;

    /// 从此 WebSeed 源下载一个分片
    async fn download_piece(&self, piece_index: usize, piece_length: usize,
                           piece_offset: usize, length: usize) -> Result<Bytes>;

    /// 检查此源是否可用
    async fn is_available(&self) -> bool;

    /// 获取此源的性能指标
    fn performance(&self) -> WebSeedPerformance;
}
```

#### 3.5.3 WebSeed 管理器

```rust
pub struct WebSeedManager {
    /// WebSeed 源列表
    sources: Vec<Box<dyn WebSeedSource>>,
    /// 源优先级
    priorities: HashMap<String, usize>,
    /// 源状态
    states: HashMap<String, WebSeedState>,
    /// HTTP 客户端
    http_client: HttpClient,
    /// 种子元数据
    metadata: Arc<TorrentMetadata>,
    /// 配置管理器
    config: Arc<ConfigManager>,
}

pub struct WebSeedState {
    /// 是否可用
    available: bool,
    /// 上次检查时间
    last_checked: Instant,
    /// 失败计数
    failure_count: usize,
    /// 当前速度 (字节/秒)
    current_speed: u64,
    /// 平均速度 (字节/秒)
    average_speed: u64,
    /// 延迟 (毫秒)
    latency: u64,
}
```

#### 3.5.4 WebSeed 集成流程

1. **源发现**
   - 从种子文件中解析 WebSeed URL
   - 创建相应类型的 WebSeed 源对象

2. **源管理**
   - 定期检查源可用性
   - 根据性能指标调整源优先级
   - 移除持续失败的源

3. **分片下载**
   - 根据分片选择策略选择要下载的分片
   - 选择最佳的 WebSeed 源
   - 发送 HTTP 范围请求下载分片
   - 验证下载的分片

4. **与 P2P 下载集成**
   - 协调 WebSeed 和 P2P 下载
   - 优先从 WebSeed 下载稀有分片
   - 在 P2P 下载速度低时增加 WebSeed 使用

## 4. 高级功能

### 4.1 加密传输

BitTorrent 协议加密 (MSE/PE) 提供了传输层加密，防止 ISP 流量整形和阻断。

#### 4.1.1 加密握手流程

1. **加密协商**
   - 生成 DH 密钥对
   - 交换公钥
   - 计算共享密钥

2. **加密方式**
   - RC4 流加密
   - 支持全加密和头部加密

3. **协议识别**
   - 使用填充和同步标记
   - 验证加密握手

### 4.2 带宽管理

#### 4.2.1 带宽分配策略

1. **全局限制**
   - 设置全局上传/下载速度限制
   - 支持基于时间的限速规则

2. **任务级限制**
   - 为每个下载任务设置独立的速度限制
   - 支持优先级调度

3. **对等点级限制**
   - 基于对等点贡献分配上传带宽
   - 实现反作弊机制

#### 4.2.2 带宽调度算法

```rust
pub struct BandwidthScheduler {
    /// 全局下载限制 (字节/秒)
    global_download_limit: u64,
    /// 全局上传限制 (字节/秒)
    global_upload_limit: u64,
    /// 任务下载限制
    task_download_limits: HashMap<String, u64>,
    /// 任务上传限制
    task_upload_limits: HashMap<String, u64>,
    /// 任务优先级
    task_priorities: HashMap<String, usize>,
    /// 时间规则
    time_rules: Vec<TimeRule>,
    /// 带宽使用统计
    stats: BandwidthStats,
}

impl BandwidthScheduler {
    /// 分配下载带宽
    pub fn allocate_download_bandwidth(&self, task_id: &str, requested: u64) -> u64 {
        // 检查时间规则
        if let Some(limit) = self.get_current_time_rule_limit(BandwidthType::Download) {
            if limit < self.global_download_limit {
                // 时间规则限制更严格，使用时间规则限制
                return self.allocate_with_limit(task_id, requested, limit, BandwidthType::Download);
            }
        }
        
        // 使用全局限制
        self.allocate_with_limit(task_id, requested, self.global_download_limit, BandwidthType::Download)
    }
    
    /// 根据限制分配带宽
    fn allocate_with_limit(&self, task_id: &str, requested: u64, global_limit: u64, bandwidth_type: BandwidthType) -> u64 {
        // 如果没有全局限制，检查任务限制
        if global_limit == 0 {
            return self.get_task_limit(task_id, requested, bandwidth_type);
        }
        
        // 计算可用带宽
        let used = self.stats.get_current_usage(bandwidth_type);
        let available = if used >= global_limit { 0 } else { global_limit - used };
        
        // 如果没有可用带宽，返回 0
        if available == 0 {
            return 0;
        }
        
        // 检查任务限制
        let task_limit = self.get_task_limit(task_id, requested, bandwidth_type);
        
        // 返回较小的限制
        std::cmp::min(available, task_limit)
    }
    
    /// 获取任务限制
    fn get_task_limit(&self, task_id: &str, requested: u64, bandwidth_type: BandwidthType) -> u64 {
        let limits = match bandwidth_type {
            BandwidthType::Download => &self.task_download_limits,
            BandwidthType::Upload => &self.task_upload_limits,
        };
        
        if let Some(&limit) = limits.get(task_id) {
            if limit > 0 && limit < requested {
                return limit;
            }
        }
        
        requested
    }
}
```

### 4.3 优先级调度

#### 4.3.1 文件优先级

```rust
pub enum FilePriority {
    Skip,
    Low,
    Normal,
    High,
}

impl PieceManager {
    /// 设置文件优先级
    pub fn set_file_priority(&mut self, file_index: usize, priority: FilePriority) {
        // 获取文件对应的分片范围
        let (start_piece, end_piece) = self.metadata.file_piece_range(file_index);
        
        // 将文件优先级转换为分片优先级
        let piece_priority = match priority {
            FilePriority::Skip => PiecePriority::Skip,
            FilePriority::Low => PiecePriority::Low,
            FilePriority::Normal => PiecePriority::Normal,
            FilePriority::High => PiecePriority::High,
        };
        
        // 更新分片优先级
        for piece_index in start_piece..=end_piece {
            self.piece_priorities[piece_index] = piece_priority;
        }
        
        // 更新分片选择策略
        self.update_piece_selection();
    }
}
```

#### 4.3.2 流式下载

```rust
impl PieceManager {
    /// 启用流式下载模式
    pub fn enable_streaming_mode(&mut self, file_index: usize) {
        // 获取文件对应的分片范围
        let (start_piece, end_piece) = self.metadata.file_piece_range(file_index);
        
        // 计算预读窗口大小 (默认为 5MB)
        let piece_length = self.metadata.piece_length;
        let prefetch_size = 5 * 1024 * 1024;
        let prefetch_pieces = std::cmp::max(1, prefetch_size / piece_length);
        
        // 设置初始窗口的分片为关键优先级
        let window_end = std::cmp::min(start_piece + prefetch_pieces, end_piece);
        for piece_index in start_piece..window_end {
            self.piece_priorities[piece_index] = PiecePriority::Critical;
        }
        
        // 设置剩余分片为高优先级
        for piece_index in window_end..=end_piece {
            self.piece_priorities[piece_index] = PiecePriority::High;
        }
        
        // 更新分片选择策略
        self.update_piece_selection();
        
        // 设置流式下载回调
        self.set_piece_completed_callback(move |pm, completed_piece| {
            if completed_piece >= start_piece && completed_piece < end_piece {
                // 移动预读窗口
                let new_critical = completed_piece + prefetch_pieces;
                if new_critical <= end_piece {
                    pm.piece_priorities[new_critical] = PiecePriority::Critical;
                }
            }
        });
    }
}
```