//! CLI 调试工具模块
//!
//! 提供CLI的调试信息功能。

use std::collections::HashMap;
use std::fmt;
use std::time::{Duration, Instant};
use log::{debug, trace};
use colored::Colorize;
use serde_json::Value;

/// 调试信息级别
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DebugLevel {
    /// 基本信息
    Basic,
    /// 详细信息
    Verbose,
    /// 完整信息
    Full,
}

/// 调试计时器
pub struct DebugTimer {
    /// 计时器名称
    name: String,
    /// 开始时间
    start: Instant,
    /// 中间点时间
    checkpoints: HashMap<String, Duration>,
}

impl DebugTimer {
    /// 创建新的调试计时器
    pub fn new(name: &str) -> Self {
        // 只在性能分析模式下输出耗时信息
        if crate::cli::utils::profiler::is_profiling_enabled() {
            debug!("开始计时: {}", name);
        }
        Self {
            name: name.to_string(),
            start: Instant::now(),
            checkpoints: HashMap::new(),
        }
    }

    /// 记录检查点
    pub fn checkpoint(&mut self, name: &str) {
        let elapsed = self.start.elapsed();
        // 只在性能分析模式下输出耗时信息
        if crate::cli::utils::profiler::is_profiling_enabled() {
            debug!("检查点 {}: {} - 耗时: {:?}", self.name, name, elapsed);
        }
        self.checkpoints.insert(name.to_string(), elapsed);
    }

    /// 结束计时并返回总耗时
    pub fn stop(self) -> Duration {
        let elapsed = self.start.elapsed();
        // 只在性能分析模式下输出耗时信息
        if crate::cli::utils::profiler::is_profiling_enabled() {
            debug!("结束计时: {} - 总耗时: {:?}", self.name, elapsed);
        }
        elapsed
    }
}

/// 打印调试信息
pub fn print_debug_info(message: &str, level: DebugLevel) {
    match level {
        DebugLevel::Basic => debug!("{}", message),
        DebugLevel::Verbose | DebugLevel::Full => trace!("{}", message),
    }
}

/// 打印调试对象
pub fn print_debug_object<T: fmt::Debug>(prefix: &str, obj: &T, level: DebugLevel) {
    match level {
        DebugLevel::Basic => debug!("{}: {:?}", prefix, obj),
        DebugLevel::Verbose => trace!("{}: {:#?}", prefix, obj),
        DebugLevel::Full => trace!("{}: {:#?}", prefix, obj),
    }
}

/// 打印调试JSON
pub fn print_debug_json(prefix: &str, json: &Value, level: DebugLevel) {
    match level {
        DebugLevel::Basic => {
            debug!("{}: {}", prefix, json.to_string());
        },
        DebugLevel::Verbose => {
            let json_str = serde_json::to_string_pretty(json).unwrap_or_else(|_| json.to_string());
            trace!("{}: {}", prefix, json_str);
        },
        DebugLevel::Full => {
            let json_str = serde_json::to_string_pretty(json).unwrap_or_else(|_| json.to_string());
            trace!("{}: {}", prefix, json_str);
        },
    }
}

/// 打印彩色调试信息
pub fn print_colored_debug(message: &str, color: colored::Color) {
    debug!("{}", message.color(color));
}

/// 打印网络调试信息
pub fn print_network_debug(url: &str, method: &str, status: Option<u16>, elapsed: Duration) {
    let status_str = status.map_or("未知".to_string(), |s| s.to_string());
    let color = if status.map_or(false, |s| s >= 200 && s < 300) {
        colored::Color::Green
    } else if status.map_or(false, |s| s >= 400) {
        colored::Color::Red
    } else {
        colored::Color::Yellow
    };
    
    // 基本信息总是显示
    let base_info = format!("网络请求: {} {} - 状态: {}", method, url, status_str.color(color));
    
    // 耗时信息只在性能分析模式下显示
    if crate::cli::utils::profiler::is_profiling_enabled() {
        debug!("{} - 耗时: {:?}", base_info, elapsed);
    } else {
        debug!("{}", base_info);
    }
}

/// 打印文件系统操作调试信息
pub fn print_fs_debug(operation: &str, path: &str, result: bool, elapsed: Duration) {
    let status_str = if result { "成功" } else { "失败" };
    let color = if result {
        colored::Color::Green
    } else {
        colored::Color::Red
    };
    
    // 基本信息总是显示
    let base_info = format!("文件系统操作: {} {} - 结果: {}", operation, path, status_str.color(color));
    
    // 耗时信息只在性能分析模式下显示
    if crate::cli::utils::profiler::is_profiling_enabled() {
        debug!("{} - 耗时: {:?}", base_info, elapsed);
    } else {
        debug!("{}", base_info);
    }
}

/// 打印系统资源使用情况
pub fn print_resource_usage() {
    // 只在性能分析模式下显示资源使用情况
    if !crate::cli::utils::profiler::is_profiling_enabled() {
        return;
    }
    
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;
        
        // 获取当前进程的资源使用情况
        if let Ok(output) = Command::new("powershell")
            .args(["-Command", "Get-Process -Id $PID | Select-Object CPU, WorkingSet, HandleCount | ConvertTo-Json"])
            .output() 
        {
            if let Ok(json_str) = String::from_utf8(output.stdout) {
                debug!("当前进程资源使用: {}", json_str.trim());
            }
        }
        
        // 获取系统总体资源使用情况
        if let Ok(output) = Command::new("powershell")
            .args(["-Command", "Get-CimInstance Win32_OperatingSystem | Select-Object FreePhysicalMemory, TotalVisibleMemorySize | ConvertTo-Json"])
            .output() 
        {
            if let Ok(json_str) = String::from_utf8(output.stdout) {
                debug!("系统内存使用: {}", json_str.trim());
            }
        }
    }
}