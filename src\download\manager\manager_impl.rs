use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{<PERSON>te<PERSON>, RwLock};
use tracing::{debug, info};
use uuid::Uuid;

use crate::analyzer::LinkAnalyzer;
use crate::config::{Settings, ConfigManager};
use crate::core::interfaces::DownloaderFactory as CoreDownloaderFactory;
use crate::download::manager::models::{TaskInfo, TaskStatus, DownloadStats};
use crate::download::resume::ResumeManager;
use crate::download::event_notifier::EventNotifier;
use crate::protocols::custom_p2p::CustomP2PFactory;
use crate::core::interfaces::Downloader;
use crate::download::downloader_factory_impl::DownloaderFactoryImpl;
use crate::download::manager::task_operations::TaskOperations;
use crate::download::manager::event_handling::EventHandler;
use crate::download::manager::resume_handling::ResumeHandler;

use super::DownloadManager;

/// Implementation of the download manager
pub struct DownloadManagerImpl {
    tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
    downloaders: Arc<Mutex<HashMap<Uuid, Box<dyn Downloader>>>>,
    task_operations: Arc<TaskOperations>,
    downloader_factory: Arc<DownloaderFactoryImpl>,
    event_handler: Arc<EventHandler>,
    resume_handler: Arc<ResumeHandler>,
}

impl DownloadManagerImpl {
    /// Create a new download manager
    #[allow(dead_code)]
    pub fn new(
        event_notifier: Arc<dyn EventNotifier>,
        settings: Settings,
        link_analyzer: Arc<dyn LinkAnalyzer>,
        p2p_factory: CustomP2PFactory,
        resume_manager: Arc<dyn ResumeManager>,
        downloader_factory: Arc<DownloaderFactoryImpl>
    ) -> Self {
        // 创建共享数据结构
        let tasks = Arc::new(RwLock::new(HashMap::new()));
        let downloaders = Arc::new(Mutex::new(HashMap::new()));
        
        // 创建事件处理器
        let event_handler = Arc::new(EventHandler::new(event_notifier.clone()));
        
        // 创建恢复处理器
        let resume_handler = Arc::new(ResumeHandler::new(
            settings.clone(),
            resume_manager,
            tasks.clone()
        ));
        
        // 创建任务操作处理器
        let task_operations = Arc::new(TaskOperations::new(
            tasks.clone(),
            downloaders.clone(),
            downloader_factory.clone(),
            event_handler.clone(),
            resume_handler.clone()
        ));
        
        // 初始化恢复处理器（在新的异步任务中）
        let resume_handler_clone = resume_handler.clone();
        tokio::spawn(async move {
            if let Err(e) = resume_handler_clone.initialize().await {
                debug!("Failed to initialize resume handler: {}", e);
            }
        });
        
        Self {
            tasks,
            downloaders,
            task_operations,
            downloader_factory,
            event_handler,
            resume_handler,
        }
    }
}

// 实现 DownloadManager trait
#[async_trait::async_trait]
impl DownloadManager for DownloadManagerImpl {
    async fn add_task(&self, task: TaskInfo) -> Result<Uuid> {
        self.task_operations.add_task(task).await
    }
    
    async fn get_task(&self, task_id: Uuid) -> Result<TaskInfo> {
        self.task_operations.get_task(task_id).await
    }
    
    async fn update_task(&self, task_id: Uuid, task: TaskInfo) -> Result<()> {
        self.task_operations.update_task(task_id, task).await
    }
    
    async fn start_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.start_task(task_id).await
    }
    
    async fn pause_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.pause_task(task_id).await
    }
    
    async fn resume_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.resume_task(task_id).await
    }
    
    async fn cancel_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.cancel_task(task_id).await
    }
    
    async fn get_all_tasks(&self) -> Result<Vec<TaskInfo>> {
        self.task_operations.get_all_tasks().await
    }
    
    async fn remove_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.remove_task(task_id).await
    }
    
    async fn get_download_stats(&self) -> DownloadStats {
        self.task_operations.get_download_stats().await.unwrap_or_default()
    }
    
    async fn create_downloader(&self, url: &str, output_path: &str, task_id: Uuid) -> Result<Box<dyn Downloader>> {
        self.downloader_factory.create_downloader(url, output_path, task_id).await
            .map_err(|e| anyhow::anyhow!(e))
    }
}