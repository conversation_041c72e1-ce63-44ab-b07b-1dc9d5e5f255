use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use anyhow::Result;
use tokio::sync::{Mutex, RwLock};
use tracing::{debug, warn, error};
use serde::{Serialize, Deserialize};

/// 安全配置
#[derive(Debug, <PERSON>lone)]
pub struct SecurityConfig {
    /// 是否启用节点验证
    pub enable_node_validation: bool,
    
    /// 是否启用速率限制
    pub enable_rate_limiting: bool,
    
    /// 是否启用黑名单
    pub enable_blacklisting: bool,
    
    /// 请求速率限制（每秒请求数）
    pub request_rate_limit: u32,
    
    /// 黑名单过期时间（秒）
    pub blacklist_expiry: u64,
}


impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            enable_node_validation: true,
            enable_rate_limiting: true,
            enable_blacklisting: true,
            request_rate_limit: 100,
            blacklist_expiry: 86400, // 24小时
        }
    }
}


/// 安全事件类型
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash)]
pub enum SecurityEventType {
    /// 无效节点ID
    InvalidNodeId,
    
    /// 速率限制超出
    RateLimitExceeded,
    
    /// 节点被加入黑名单
    NodeBlacklisted,
    
    /// 可疑活动
    SuspiciousActivity,
}


impl SecurityEventType {
    /// 获取事件类型的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::InvalidNodeId => "InvalidNodeId",
            Self::RateLimitExceeded => "RateLimitExceeded",
            Self::NodeBlacklisted => "NodeBlacklisted",
            Self::SuspiciousActivity => "SuspiciousActivity",
        }
    }
}


/// 安全事件严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum SecurityEventSeverity {
    /// 信息
    Info,
    
    /// 警告
    Warning,
    
    /// 错误
    Error,
    
    /// 严重
    Critical,
}


impl SecurityEventSeverity {
    /// 获取严重程度的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::Info => "Info",
            Self::Warning => "Warning",
            Self::Error => "Error",
            Self::Critical => "Critical",
        }
    }
}


/// 安全事件
#[derive(Debug, Clone)]
pub struct SecurityEvent {
    /// 事件类型
    pub event_type: SecurityEventType,
    
    /// 事件来源（IP地址或节点ID）
    pub source: String,
    
    /// 事件描述
    pub description: String,
    
    /// 事件时间戳
    pub timestamp: SystemTime,
    
    /// 事件严重性
    pub severity: SecurityEventSeverity,
}


/// 黑名单条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlacklistEntry {
    /// 地址
    pub addr: String,
    
    /// 添加时间（Unix时间戳，秒）
    pub added_at: u64,
    
    /// 过期时间（Unix时间戳，秒）
    pub expires_at: Option<u64>,
    
    /// 原因
    pub reason: String,
}


/// 安全管理器
/// 集成节点验证、速率限制和黑名单功能
pub struct SecurityManager {
    /// 安全配置
    config: SecurityConfig,
    
    /// 黑名单
    blacklist: Arc<RwLock<HashMap<SocketAddr, BlacklistEntry>>>,
    
    /// 速率限制器
    rate_limiters: Arc<Mutex<HashMap<SocketAddr, (u32, Instant)>>>,
    
    /// 最近的安全事件
    recent_events: Arc<RwLock<Vec<SecurityEvent>>>,
    
    /// 最大事件历史数量
    max_events: usize,
}


impl SecurityManager {
    /// 创建新的安全管理器
    pub fn new(config: SecurityConfig) -> Self {
        Self {
            config,
            blacklist: Arc::new(RwLock::new(HashMap::new())),
            rate_limiters: Arc::new(Mutex::new(HashMap::new())),
            recent_events: Arc::new(RwLock::new(Vec::new())),
            max_events: 100,
        }
    }
    
    
    /// 验证节点ID
    pub async fn validate_node_id(&self, node_id: &[u8], ip: &SocketAddr) -> Result<bool> {
        // 如果节点验证被禁用，直接返回有效
        if !self.config.enable_node_validation {
            return Ok(true);
        }
        
        
        // 简单的节点ID验证：检查节点ID长度
        if node_id.len() != 20 {
            // 记录安全事件
            self.record_event(
                SecurityEventType::InvalidNodeId,
                &ip.to_string(),
                &format!("Invalid node ID length: {}", node_id.len()),
                SecurityEventSeverity::Warning
            ).await?;
            
            return Ok(false);
        }
        
        
        // 这里可以添加更复杂的节点ID验证逻辑
        
        Ok(true)
    }
    
    
    /// 检查速率限制
    pub async fn check_rate_limit(&self, addr: &SocketAddr) -> Result<bool> {
        // 如果速率限制被禁用，直接返回允许
        if !self.config.enable_rate_limiting {
            return Ok(true);
        }
        
        
        let mut rate_limiters = self.rate_limiters.lock().await;
        let now = Instant::now();
        
        // 获取或创建速率限制器
        let (count, last_reset) = rate_limiters.entry(*addr).or_insert((0, now));

        // 检查是否需要重置计数器
        if now.duration_since(*last_reset).as_secs() >= 1 {
            *count = 1;
            *last_reset = now;
            return Ok(true);
        }
        
        
        // 检查是否超过速率限制
        if *count >= self.config.request_rate_limit {
            // 记录安全事件
            self.record_event(
                SecurityEventType::RateLimitExceeded,
                &addr.to_string(),
                &format!("Rate limit exceeded: {} requests/sec", self.config.request_rate_limit),
                SecurityEventSeverity::Warning
            ).await?;
            
            return Ok(false);
        }
        
        
        // 增加计数器
        *count += 1;
        
        Ok(true)
    }
    
    
    /// 检查对等点是否在黑名单中
    pub async fn is_blacklisted(&self, addr: &SocketAddr) -> bool {
        if !self.config.enable_blacklisting {
            return false;
        }
        let blacklist = self.blacklist.read().await;
        blacklist.contains_key(addr)
    }
    
    
    /// 检查黑名单
    pub async fn check_blacklist(&self, addr: &SocketAddr) -> Result<bool> {
        // 如果黑名单被禁用，直接返回允许
        if !self.config.enable_blacklisting {
            return Ok(true);
        }
        
        
        let blacklist = self.blacklist.read().await;
        
        // 检查地址是否在黑名单中
        if let Some(entry) = blacklist.get(addr) {
            // 检查是否过期
            if let Some(expires_at) = entry.expires_at {
                let now = SystemTime::now()
                    .duration_since(SystemTime::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs();
                
                if now > expires_at {
                    // 已过期，允许
                    return Ok(true);
                }
            }
            
            
            // 记录安全事件
            self.record_event(
                SecurityEventType::NodeBlacklisted,
                &addr.to_string(),
                &format!("Node is blacklisted: {}", entry.reason),
                SecurityEventSeverity::Warning
            ).await?;
            
            return Ok(false);
        }
        
        
        Ok(true)
    }
    
    
    /// 添加到黑名单
    pub async fn add_to_blacklist(&self, addr: &SocketAddr, reason: &str, duration: Option<Duration>) -> Result<()> {
        // 如果黑名单被禁用，直接返回
        if !self.config.enable_blacklisting {
            return Ok(());
        }
        
        
        let mut blacklist = self.blacklist.write().await;
        
        // 计算过期时间
        let now = SystemTime::now()
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        let expires_at = duration.map(|d| now + d.as_secs());
        
        // 创建黑名单条目
        let entry = BlacklistEntry {
            addr: addr.to_string(),
            added_at: now,
            expires_at,
            reason: reason.to_string(),
        };
        
        // 添加到黑名单
        blacklist.insert(*addr, entry);
        
        // 记录安全事件
        self.record_event(
            SecurityEventType::NodeBlacklisted,
            &addr.to_string(),
            reason,
            SecurityEventSeverity::Warning
        ).await?;
        
        Ok(())
    }
    
    
    /// 从黑名单移除
    pub async fn remove_from_blacklist(&self, addr: &SocketAddr) -> Result<bool> {
        let mut blacklist = self.blacklist.write().await;
        
        Ok(blacklist.remove(addr).is_some())
    }
    
    
    /// 记录安全事件
    pub async fn record_event(&self, event_type: SecurityEventType, source: &str, description: &str, severity: SecurityEventSeverity) -> Result<()> {
        let event = SecurityEvent {
            event_type,
            source: source.to_string(),
            description: description.to_string(),
            timestamp: SystemTime::now(),
            severity,
        };
        
        // 记录到日志
        match severity {
            SecurityEventSeverity::Info => {
                debug!("[Security] {}: {}", event_type.as_str(), description);
            },
            SecurityEventSeverity::Warning => {
                warn!("[Security] {}: {}", event_type.as_str(), description);
            },
            SecurityEventSeverity::Error => {
                error!("[Security] {}: {}", event_type.as_str(), description);
            },
            SecurityEventSeverity::Critical => {
                error!("[Security] CRITICAL {}: {}", event_type.as_str(), description);
            },
        }
        
        
        // 添加到最近事件
        let mut recent_events = self.recent_events.write().await;
        
        // 如果事件历史已满，移除最旧的事件
        if recent_events.len() >= self.max_events {
            recent_events.remove(0);
        }
        
        
        // 添加新事件
        recent_events.push(event);
        
        Ok(())
    }
    
    
    /// 获取最近的安全事件
    pub async fn get_recent_events(&self, count: usize) -> Result<Vec<SecurityEvent>> {
        let recent_events = self.recent_events.read().await;
        
        // 获取最近的事件
        let start = if recent_events.len() > count {
            recent_events.len() - count
        } else {
            0
        };
        
        Ok(recent_events[start..].to_vec())
    }
    
    
    /// 清理过期的黑名单条目
    pub async fn cleanup_blacklist(&self) -> Result<usize> {
        let mut blacklist = self.blacklist.write().await;
        let before_count = blacklist.len();
        
        // 获取当前时间
        let now = SystemTime::now()
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // 移除过期的条目
        blacklist.retain(|_, entry| {
            if let Some(expires_at) = entry.expires_at {
                now <= expires_at
            } else {
                true
            }
        });
        
        let removed_count = before_count - blacklist.len();
        
        if removed_count > 0 {
            debug!("Cleaned up {} expired blacklist entries", removed_count);
        }
        
        
        Ok(removed_count)
    }
}
