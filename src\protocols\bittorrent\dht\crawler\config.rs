use std::net::SocketAddr;

/// DHT爬虫配置
#[derive(Debug, <PERSON>lone)]
pub struct DHTCrawlerConfig {
    /// 最大并发请求数
    pub max_concurrent_requests: usize,
    /// 请求超时时间（秒）
    pub request_timeout: u64,
    /// 节点刷新间隔（秒）
    pub node_refresh_interval: u64,
    /// 最大存储信息哈希数
    pub max_infohashes: usize,
    /// 是否保存信息哈希元数据
    pub save_metadata: bool,
    /// 元数据存储路径
    pub metadata_path: Option<String>,
    /// 是否启用IPv6
    pub enable_ipv6: bool,
    /// 监听地址
    pub listen_addr: SocketAddr,
    /// 引导节点
    pub bootstrap_nodes: Vec<SocketAddr>,
    /// 爬取间隔（毫秒）
    pub crawl_interval: u64,
    /// 每个节点的最大样本数
    pub samples_per_node: usize,
}

impl Default for DHTCrawlerConfig {
    fn default() -> Self {
        Self {
            max_concurrent_requests: 100,
            request_timeout: 5,
            node_refresh_interval: 300, // 5分钟
            max_infohashes: 1_000_000,
            save_metadata: false,
            metadata_path: None,
            enable_ipv6: false,
            listen_addr: "0.0.0.0:6881".parse().unwrap(),
            bootstrap_nodes: vec![
                "router.bittorrent.com:6881".parse().unwrap(),
                "dht.transmissionbt.com:6881".parse().unwrap(),
                "router.utorrent.com:6881".parse().unwrap(),
            ],
            crawl_interval: 100, // 100毫秒
            samples_per_node: 8,
        }
    }
}
