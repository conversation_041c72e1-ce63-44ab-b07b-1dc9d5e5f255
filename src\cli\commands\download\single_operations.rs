use anyhow::Result;
use std::sync::Arc;
use uuid::Uuid;
use log::{debug, trace};

use crate::cli::backend::backend::CliBackend;
use crate::cli::commands::download::args::{AddArgs, TaskIdArgs};
use crate::cli::utils::debug::{print_debug_info, print_debug_object, DebugLevel};

/// Handle add command
pub async fn handle_add(backend: &Arc<dyn CliBackend>, args: &AddArgs) -> Result<()> {
    debug!("执行添加下载任务: url={}, output={:?}", args.url, args.output);
    print_debug_object("添加任务参数", args, DebugLevel::Verbose);
    
    let task_id = backend.add_download_task(&args.url, args.output.as_deref().unwrap_or("")).await?;
    
    debug!("任务添加成功: {}", task_id);
    println!("Task added successfully. Task ID: {}", task_id);
    println!("Use 'tonitru download start {}' to start the download.", task_id);
    Ok(())
}

/// Handle start command
pub async fn handle_start(backend: &Arc<dyn CliBackend>, args: &TaskIdArgs) -> Result<()> {
    debug!("执行启动下载任务: task_id={}", args.task_id);
    
    let task_id = Uuid::parse_str(&args.task_id)?;
    trace!("解析UUID: {} -> {}", args.task_id, task_id);
    
    backend.start_download_task(task_id).await?;
    
    debug!("任务启动成功: {}", task_id);
    println!("Task {} started successfully.", task_id);
    Ok(())
}

/// Handle pause command
pub async fn handle_pause(backend: &Arc<dyn CliBackend>, args: &TaskIdArgs) -> Result<()> {
    debug!("执行暂停下载任务: task_id={}", args.task_id);
    
    let task_id = Uuid::parse_str(&args.task_id)?;
    trace!("解析UUID: {} -> {}", args.task_id, task_id);
    
    backend.pause_download_task(task_id).await?;
    
    debug!("任务暂停成功: {}", task_id);
    println!("Task {} paused successfully.", task_id);
    Ok(())
}

/// Handle resume command
pub async fn handle_resume(backend: &Arc<dyn CliBackend>, args: &TaskIdArgs) -> Result<()> {
    debug!("执行恢复下载任务: task_id={}", args.task_id);
    
    let task_id = Uuid::parse_str(&args.task_id)?;
    trace!("解析UUID: {} -> {}", args.task_id, task_id);
    
    backend.resume_download_task(task_id).await?;
    
    debug!("任务恢复成功: {}", task_id);
    println!("Task {} resumed successfully.", task_id);
    Ok(())
}

/// Handle cancel command
pub async fn handle_cancel(backend: &Arc<dyn CliBackend>, args: &TaskIdArgs) -> Result<()> {
    debug!("执行取消下载任务: task_id={}", args.task_id);
    
    let task_id = Uuid::parse_str(&args.task_id)?;
    trace!("解析UUID: {} -> {}", args.task_id, task_id);
    
    backend.cancel_download_task(task_id).await?;
    
    debug!("任务取消成功: {}", task_id);
    println!("Task {} cancelled successfully.", task_id);
    Ok(())
}

/// Handle remove command
pub async fn handle_remove(backend: &Arc<dyn CliBackend>, args: &TaskIdArgs) -> Result<()> {
    debug!("执行删除下载任务: task_id={}", args.task_id);
    
    let task_id = Uuid::parse_str(&args.task_id)?;
    trace!("解析UUID: {} -> {}", args.task_id, task_id);
    
    backend.remove_download_task(task_id).await?;
    
    debug!("任务删除成功: {}", task_id);
    println!("Task {} removed successfully.", task_id);
    Ok(())
}