use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};
use std::fmt::Display;

/// API响应格式
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<ApiError>,
}

/// API错误信息
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiError {
    pub code: String,
    pub message: String,
}

impl ApiError {
    /// 创建内部错误
    pub fn internal_error(message: impl Into<String>) -> Self {
        Self {
            code: ErrorCode::InternalError.to_string(),
            message: message.into(),
        }
    }

    /// 创建无效请求错误
    pub fn bad_request(message: impl Into<String>) -> Self {
        Self {
            code: ErrorCode::InvalidState.to_string(),
            message: message.into(),
        }
    }

    /// 创建资源不存在错误
    pub fn not_found(message: impl Into<String>) -> Self {
        Self {
            code: ErrorCode::TaskNotFound.to_string(),
            message: message.into(),
        }
    }
}

/// 将ApiError转换为HTTP响应
impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        let status = match self.code.as_str() {
            "TASK_NOT_FOUND" => StatusCode::NOT_FOUND,
            "INVALID_URL" => StatusCode::BAD_REQUEST,
            "INVALID_STATE" => StatusCode::BAD_REQUEST,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        };

        let body = Json(ApiResponse::<()> {
            success: false,
            data: None,
            error: Some(self),
        });

        (status, body).into_response()
    }
}

/// 错误码枚举
#[derive(Debug)]
pub enum ErrorCode {
    InvalidUrl,
    TaskNotFound,
    DownloadFailed,
    InvalidState,
    InternalError,
}

impl Display for ErrorCode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ErrorCode::InvalidUrl => write!(f, "INVALID_URL"),
            ErrorCode::TaskNotFound => write!(f, "TASK_NOT_FOUND"),
            ErrorCode::DownloadFailed => write!(f, "DOWNLOAD_FAILED"),
            ErrorCode::InvalidState => write!(f, "INVALID_STATE"),
            ErrorCode::InternalError => write!(f, "INTERNAL_ERROR"),
        }
    }
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    /// 创建成功但无数据响应
    pub fn success_no_data() -> ApiResponse<()> {
        ApiResponse {
            success: true,
            data: None,
            error: None,
        }
    }

    /// 创建错误响应
    pub fn error<E>(code: ErrorCode, message: impl Into<String>) -> ApiResponse<E> {
        ApiResponse {
            success: false,
            data: None,
            error: Some(ApiError {
                code: code.to_string(),
                message: message.into(),
            }),
        }
    }
}

/// 将ApiResponse转换为HTTP响应
impl<T: Serialize> IntoResponse for ApiResponse<T> {
    fn into_response(self) -> Response {
        let status = if self.success {
            StatusCode::OK
        } else {
            match self.error.as_ref().map(|e| e.code.as_str()) {
                Some("TASK_NOT_FOUND") => StatusCode::NOT_FOUND,
                Some("INVALID_URL") => StatusCode::BAD_REQUEST,
                Some("INVALID_STATE") => StatusCode::BAD_REQUEST,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            }
        };

        let body = Json(self);
        (status, body).into_response()
    }
}

/// 从anyhow::Error创建API错误响应
pub fn error_response<T>(err: anyhow::Error) -> ApiResponse<T> {
    let message = err.to_string();

    // 根据错误消息判断错误类型
    if message.contains("not found") || message.contains("Task not found") {
        ApiResponse::<T>::error(ErrorCode::TaskNotFound, message)
    } else if message.contains("URL") || message.contains("url") {
        ApiResponse::<T>::error(ErrorCode::InvalidUrl, message)
    } else if message.contains("download failed") || message.contains("Failed to download") {
        ApiResponse::<T>::error(ErrorCode::DownloadFailed, message)
    } else if message.contains("state") || message.contains("status") {
        ApiResponse::<T>::error(ErrorCode::InvalidState, message)
    } else {
        ApiResponse::<T>::error(ErrorCode::InternalError, message)
    }
}
