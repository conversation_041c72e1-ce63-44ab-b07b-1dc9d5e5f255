//! 代理检测和配置模块
//!
//! 该模块提供了自动检测系统代理设置的功能，支持从环境变量和操作系统特定设置中检测代理。

pub mod detector;
pub mod env_detector;
pub mod os_detector;
pub mod error;

use tracing::{debug, info};

use crate::config::settings::ProxyConfig;
use detector::ProxyDetector;
use env_detector::EnvProxyDetector;
use os_detector::OsProxyDetector;
use error::ProxyError;

/// 代理检测器工厂，用于创建和管理代理检测器实例
pub struct ProxyDetectorFactory;

impl ProxyDetectorFactory {
    /// 检测系统代理设置
    ///
    /// 首先检查环境变量，然后检查操作系统特定的设置
    ///
    /// # 返回值
    ///
    /// 返回 `Option<ProxyConfig>`，表示检测到的代理配置（如果有）
    pub fn detect_system_proxy() -> Option<ProxyConfig> {
        debug!("检测系统代理设置...");

        // 首先检查环境变量
        match EnvProxyDetector::new().detect() {
            Ok(Some(proxy_config)) => {
                info!("从环境变量检测到代理: {}", proxy_config.url);
                return Some(proxy_config);
            },
            Ok(None) => {},
            Err(e) => {
                debug!("从环境变量检测代理时出错: {}", e);
            }
        }

        // 然后检查操作系统特定的设置
        match OsProxyDetector::new().detect() {
            Ok(Some(proxy_config)) => {
                info!("从操作系统设置检测到代理: {}", proxy_config.url);
                return Some(proxy_config);
            },
            Ok(None) => {},
            Err(e) => {
                debug!("从操作系统设置检测代理时出错: {}", e);
            }
        }

        debug!("未检测到系统代理");
        None
    }

    /// 尝试检测系统代理设置，返回结果包含错误信息
    ///
    /// 首先检查环境变量，然后检查操作系统特定的设置
    ///
    /// # 返回值
    ///
    /// 返回 `Result<Option<ProxyConfig>, ProxyError>`，表示检测结果或错误
    pub fn try_detect_system_proxy() -> Result<Option<ProxyConfig>, ProxyError> {
        debug!("尝试检测系统代理设置...");

        // 首先检查环境变量
        match EnvProxyDetector::new().detect() {
            Ok(Some(proxy_config)) => {
                info!("从环境变量检测到代理: {}", proxy_config.url);
                return Ok(Some(proxy_config));
            },
            Ok(None) => {},
            Err(e) => {
                debug!("从环境变量检测代理时出错: {}", e);
                // 继续尝试其他方法，不立即返回错误
            }
        }

        // 然后检查操作系统特定的设置
        match OsProxyDetector::new().detect() {
            Ok(Some(proxy_config)) => {
                info!("从操作系统设置检测到代理: {}", proxy_config.url);
                return Ok(Some(proxy_config));
            },
            Ok(None) => {},
            Err(e) => {
                debug!("从操作系统设置检测代理时出错: {}", e);
                return Err(e);
            }
        }

        debug!("未检测到系统代理");
        Ok(None)
    }

    /// 从环境变量中检测代理设置
    ///
    /// # 返回值
    ///
    /// 返回 `Result<Option<ProxyConfig>, ProxyError>`，表示检测结果或错误
    pub fn detect_from_env() -> Result<Option<ProxyConfig>, ProxyError> {
        EnvProxyDetector::new().detect()
    }

    /// 从操作系统设置中检测代理
    ///
    /// # 返回值
    ///
    /// 返回 `Result<Option<ProxyConfig>, ProxyError>`，表示检测结果或错误
    pub fn detect_from_os() -> Result<Option<ProxyConfig>, ProxyError> {
        OsProxyDetector::new().detect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;

    #[test]
    fn test_detect_system_proxy_env() {
        // 设置环境变量
        env::set_var("http_proxy", "http://test.com:8080");
        
        // 检测代理
        let config = ProxyDetectorFactory::detect_system_proxy();
        
        // 验证结果
        assert!(config.is_some());
        let config = config.unwrap();
        assert_eq!(config.url, "http://test.com:8080/");
        
        // 清理环境变量
        env::remove_var("http_proxy");
    }

    #[test]
    fn test_try_detect_system_proxy_env() {
        // 设置环境变量
        env::set_var("https_proxy", "https://test.com:8443");
        
        // 检测代理
        let result = ProxyDetectorFactory::try_detect_system_proxy();
        
        // 验证结果
        assert!(result.is_ok());
        let config = result.unwrap();
        assert!(config.is_some());
        let config = config.unwrap();
        assert_eq!(config.url, "https://test.com:8443/");
        
        // 清理环境变量
        env::remove_var("https_proxy");
    }

    #[test]
    fn test_detect_from_env() {
        // 设置环境变量
        env::set_var("all_proxy", "socks5://test.com:1080");
        
        // 检测代理
        let result = ProxyDetectorFactory::detect_from_env();
        
        // 验证结果
        assert!(result.is_ok());
        let config = result.unwrap();
        assert!(config.is_some());
        let config = config.unwrap();
        assert_eq!(config.url, "socks5://test.com:1080/");
        assert_eq!(config.proxy_type, "socks5");
        
        // 清理环境变量
        env::remove_var("all_proxy");
    }
}