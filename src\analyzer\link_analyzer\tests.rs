use super::*;

#[tokio::test]
async fn test_analyze_http_url() {
    let analyzer = LinkAnalyzerImpl::new();
    let protocol = analyzer.analyze("http://example.com/file.txt").await.unwrap();
    assert_eq!(protocol, PROTOCOL_HTTP);
}

#[tokio::test]
async fn test_analyze_https_url() {
    let analyzer = LinkAnalyzerImpl::new();
    let protocol = analyzer.analyze("https://example.com/file.txt").await.unwrap();
    assert_eq!(protocol, PROTOCOL_HTTPS);
}

#[tokio::test]
async fn test_analyze_torrent_url() {
    let analyzer = LinkAnalyzerImpl::new();
    let protocol = analyzer.analyze("https://example.com/file.torrent").await.unwrap();
    assert_eq!(protocol, PROTOCOL_BT);
}

#[tokio::test]
async fn test_analyze_magnet_url() {
    let analyzer = LinkAnalyzerImpl::new();
    let protocol = analyzer.analyze("magnet:?xt=urn:btih:123456").await.unwrap();
    assert_eq!(protocol, PROTOCOL_MAGNET);
}

#[tokio::test]
async fn test_analyze_p2p_url() {
    let analyzer = LinkAnalyzerImpl::new();
    let protocol = analyzer.analyze("p2p://example.com/file.txt").await.unwrap();
    assert_eq!(protocol, PROTOCOL_P2P);
}

#[tokio::test]
async fn test_analyze_r2_url() {
    let analyzer = LinkAnalyzerImpl::new();
    let protocol = analyzer.analyze("https://example.r2.dev/file.txt").await.unwrap();
    assert_eq!(protocol, PROTOCOL_R2);
}

#[tokio::test]
async fn test_extract_file_name() {
    let analyzer = LinkAnalyzerImpl::new();
    let file_name = analyzer.extract_file_name("https://example.com/path/file.txt").unwrap();
    assert_eq!(file_name, "file.txt");
}

#[tokio::test]
async fn test_extract_file_name_with_query() {
    let analyzer = LinkAnalyzerImpl::new();
    let file_name = analyzer.extract_file_name("https://example.com/path/file.txt?param=value").unwrap();
    assert_eq!(file_name, "file.txt");
}

#[tokio::test]
async fn test_extract_file_name_with_fragment() {
    let analyzer = LinkAnalyzerImpl::new();
    let file_name = analyzer.extract_file_name("https://example.com/path/file.txt#fragment").unwrap();
    assert_eq!(file_name, "file.txt");
}
