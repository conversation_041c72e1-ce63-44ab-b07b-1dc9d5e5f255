use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::Result;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use super::stun::client::StunClient;

/// NAT类型
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum NATType {
    /// 未知
    Unknown,
    /// 开放互联网（无NAT）
    Open,
    /// 完全锥形NAT
    FullCone,
    /// 受限锥形NAT
    RestrictedCone,
    /// 端口受限锥形NAT
    PortRestrictedCone,
    /// 对称NAT
    Symmetric,
}

impl std::fmt::Display for NATType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            NATType::Unknown => write!(f, "Unknown"),
            NATType::Open => write!(f, "Open Internet (No NAT)"),
            NATType::FullCone => write!(f, "Full Cone NAT"),
            NATType::RestrictedCone => write!(f, "Restricted Cone NAT"),
            NATType::PortRestrictedCone => write!(f, "Port Restricted Cone NAT"),
            NATType::Symmetric => write!(f, "Symmetric NAT"),
        }
    }
}

/// NAT类型检测器
pub struct NATTypeDetector {
    /// STUN客户端
    stun_client: Arc<StunClient>,
    /// 检测到的NAT类型
    nat_type: RwLock<NATType>,
    /// 上次检测时间
    last_detection: RwLock<Option<Instant>>,
    /// 本地地址
    local_addr: RwLock<Option<SocketAddr>>,
    /// 映射地址
    mapped_addr: RwLock<Option<SocketAddr>>,
}

impl NATTypeDetector {
    /// 创建新的NAT类型检测器
    pub fn new(stun_client: Arc<StunClient>) -> Self {
        Self {
            stun_client,
            nat_type: RwLock::new(NATType::Unknown),
            last_detection: RwLock::new(None),
            local_addr: RwLock::new(None),
            mapped_addr: RwLock::new(None),
        }
    }
    
    /// 检测NAT类型
    pub async fn detect_nat_type(&self) -> Result<NATType> {
        // 检查是否需要重新检测
        {
            let last_detection = self.last_detection.read().await;
            if let Some(time) = *last_detection {
                if time.elapsed() < Duration::from_secs(3600) { // 1小时内不重新检测
                    let nat_type = *self.nat_type.read().await;
                    if nat_type != NATType::Unknown {
                        return Ok(nat_type);
                    }
                }
            }
        }
        
        // 使用STUN客户端检测NAT类型
        let nat_type = self.stun_client.detect_nat_type().await?;
        
        // 更新状态
        {
            let mut nat_type_guard = self.nat_type.write().await;
            *nat_type_guard = nat_type;
        }
        
        {
            let mut last_detection = self.last_detection.write().await;
            *last_detection = Some(Instant::now());
        }
        
        info!("Detected NAT type: {}", nat_type);
        
        Ok(nat_type)
    }
    
    /// 获取本地地址和映射地址
    pub async fn get_addresses(&self) -> Result<(Option<SocketAddr>, Option<SocketAddr>)> {
        let local_addr = *self.local_addr.read().await;
        let mapped_addr = *self.mapped_addr.read().await;
        
        Ok((local_addr, mapped_addr))
    }
    
    /// 更新地址信息
    pub async fn update_addresses(&self, local_addr: SocketAddr, mapped_addr: SocketAddr) {
        {
            let mut local_addr_guard = self.local_addr.write().await;
            *local_addr_guard = Some(local_addr);
        }
        
        {
            let mut mapped_addr_guard = self.mapped_addr.write().await;
            *mapped_addr_guard = Some(mapped_addr);
        }
    }
    
    /// 获取NAT类型
    pub async fn get_nat_type(&self) -> NATType {
        *self.nat_type.read().await
    }
    
    /// 是否是对称NAT
    pub async fn is_symmetric_nat(&self) -> bool {
        let nat_type = self.get_nat_type().await;
        nat_type == NATType::Symmetric
    }
    
    /// 是否需要打洞
    pub async fn needs_hole_punching(&self) -> bool {
        let nat_type = self.get_nat_type().await;
        nat_type == NATType::RestrictedCone || 
        nat_type == NATType::PortRestrictedCone || 
        nat_type == NATType::Symmetric
    }
}
