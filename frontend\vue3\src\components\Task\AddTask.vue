<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'uri'
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'close'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const form = ref({
  uris: '',
  options: {
    dir: '',
    out: '',
    maxDownloadLimit: 0
  }
})

// 提交状态
const isSubmitting = ref(false)

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  uris: [
    { required: true, message: '请输入下载链接', trigger: 'blur' }
  ]
}

// 对话框标题
const dialogTitle = computed(() => {
  if (props.type === 'uri') {
    return '添加链接任务'
  } else if (props.type === 'torrent') {
    return '添加种子任务'
  } else {
    return '添加任务'
  }
})

// 监听类型变化
watch(() => props.type, () => {
  resetForm()
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.value = {
    uris: '',
    options: {
      dir: '',
      out: '',
      maxDownloadLimit: 0
    }
  }
  isSubmitting.value = false
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  emit('close')
}

// 清理URL，移除反引号和多余的空格
const cleanUrl = (url: string) => {
  return url.replace(/`/g, '').trim()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || isSubmitting.value) return

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 设置提交状态为true，防止重复提交
        isSubmitting.value = true

        // 处理URI列表
        const uriList = form.value.uris.split('\n').filter(uri => uri.trim() !== '')

        if (uriList.length === 0) {
          ElMessage.warning('请输入至少一个有效的下载链接')
          isSubmitting.value = false
          return
        }

        // 为每个URI创建一个下载任务
        const tasks = []
        const failedTasks = []
        const startFailedTasks = []

        for (const uri of uriList) {
          // 清理URL，移除反引号和多余的空格
          const cleanedUri = cleanUrl(uri)
          
          // 准备请求数据
          const requestData = {
            url: cleanedUri,
            output_path: form.value.options.dir ?
              `${form.value.options.dir}/${form.value.options.out || getFileNameFromUrl(cleanedUri)}` :
              undefined
          }

          // 发送请求
          try {
            const response = await axios.post('/downloads', requestData)
            if (response.data && response.data.success) {
              const taskId = response.data.data.task_id
              tasks.push(taskId)
              
              // 立即启动下载任务
              try {
                const startResponse = await axios.post(`/downloads/${taskId}/start`)
                if (!startResponse.data || !startResponse.data.success) {
                  console.error(`启动任务失败 (${taskId}): ${startResponse.data?.error?.message || '未知错误'}`)
                  startFailedTasks.push({ 
                    taskId, 
                    uri: cleanedUri, 
                    reason: startResponse.data?.error?.message || '启动失败' 
                  })
                }
              } catch (err: any) {
                console.error(`启动任务失败 (${taskId}):`, err)
                startFailedTasks.push({ 
                  taskId, 
                  uri: cleanedUri, 
                  reason: err.response?.data?.error?.message || '启动失败' 
                })
              }
            }
          } catch (err: any) {
            console.error(`添加任务失败 (${cleanedUri}):`, err)
            failedTasks.push({ 
              uri: cleanedUri, 
              reason: err.response?.data?.error?.message || '添加失败' 
            })
          }
        }

        if (tasks.length > 0) {
          ElMessage.success(`成功添加 ${tasks.length} 个任务`)

          // 如果有失败的任务，显示警告
          if (failedTasks.length > 0) {
            ElMessage.warning(`${failedTasks.length} 个任务添加失败`)
            console.error('添加失败的任务:', failedTasks)
          }

          // 如果有启动失败的任务，显示警告并提供重试选项
          if (startFailedTasks.length > 0) {
            ElMessage.warning(`${startFailedTasks.length} 个任务启动失败，请在任务列表中手动启动`)
            console.error('启动失败的任务:', startFailedTasks)
          }

          // 如果设置了速度限制，为每个任务设置速度限制
          if (form.value.options.maxDownloadLimit > 0) {
            for (const taskId of tasks) {
              try {
                await axios.post(`/tasks/${taskId}/speed/download`, {
                  limit: form.value.options.maxDownloadLimit * 1024
                })
              } catch (err) {
                console.error(`设置任务速度限制失败 (${taskId}):`, err)
              }
            }
          }

          handleClose()
        } else {
          ElMessage.error('所有任务添加失败')
        }
      } catch (error) {
        console.error('添加任务失败:', error)
        ElMessage.error('添加任务失败，请检查网络连接')
      } finally {
        // 无论成功或失败，都将提交状态设置为false
        isSubmitting.value = false
      }
    } else {
      return false
    }
  })
}

// 从URL中提取文件名
const getFileNameFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const segments = pathname.split('/')
    const lastSegment = segments[segments.length - 1]

    if (lastSegment) {
      return lastSegment
    }
  } catch (e) {
    // URL解析失败，尝试简单提取
    const segments = url.split('/')
    const lastSegment = segments[segments.length - 1]
    if (lastSegment) {
      return lastSegment
    }
  }

  // 无法提取文件名，返回默认名称
  return 'download'
}

// 选择下载目录
const selectDirectory = () => {
  // 在实际的Electron环境中，这里会调用选择目录的API
  console.log('选择下载目录')
  // 模拟选择了一个目录
  form.value.options.dir = 'D:\\Downloads'
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="下载链接" prop="uris">
        <el-input
          v-model="form.uris"
          type="textarea"
          :rows="4"
          placeholder="输入下载链接，每行一个"
          :disabled="isSubmitting"
        />
      </el-form-item>

      <el-form-item label="保存位置">
        <div class="directory-select">
          <el-input v-model="form.options.dir" placeholder="选择保存位置" :disabled="isSubmitting" />
          <el-button @click="selectDirectory" :disabled="isSubmitting">浏览...</el-button>
        </div>
      </el-form-item>

      <el-form-item label="文件名">
        <el-input v-model="form.options.out" placeholder="留空使用默认文件名" :disabled="isSubmitting" />
      </el-form-item>

      <el-form-item label="限速">
        <el-input-number
          v-model="form.options.maxDownloadLimit"
          :min="0"
          :step="10"
          controls-position="right"
          :disabled="isSubmitting"
        />
        <span class="unit-label">KB/s (0表示不限速)</span>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="isSubmitting">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isSubmitting" :disabled="isSubmitting">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
.directory-select {
  display: flex;
  align-items: center;
}

.directory-select .el-input {
  margin-right: 10px;
}

.unit-label {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
