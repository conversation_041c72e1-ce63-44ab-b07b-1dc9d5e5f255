use anyhow::{Result, anyhow};
use chrono::Utc;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use tracing::{debug, info, warn, error};
use uuid::Uuid;

use crate::core::interfaces::Downloader;
use crate::core::interfaces::downloader::{DownloaderFactory, DownloadProgress};
use super::models::{TaskInfo, TaskStatus, DownloadStats};
use crate::download::downloader_factory_impl::DownloaderFactoryImpl;
use super::event_handling::EventHandler;
use super::resume_handling::ResumeHandler;

/// 任务操作处理器，负责处理下载任务的各种操作
pub struct TaskOperations {
    tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
    downloaders: Arc<Mutex<HashMap<Uuid, Box<dyn Downloader>>>>,
    downloader_factory: Arc<DownloaderFactoryImpl>,
    event_handler: Arc<EventHandler>,
    resume_handler: Arc<ResumeHandler>,
}

impl TaskOperations {
    /// 创建一个新的任务操作处理器
    pub fn new(
        tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
        downloaders: Arc<Mutex<HashMap<Uuid, Box<dyn Downloader>>>>,
        downloader_factory: Arc<DownloaderFactoryImpl>,
        event_handler: Arc<EventHandler>,
        resume_handler: Arc<ResumeHandler>,
    ) -> Self {
        Self {
            tasks,
            downloaders,
            downloader_factory,
            event_handler,
            resume_handler,
        }
    }

    /// 获取下载统计信息
    pub async fn get_download_stats(&self) -> Result<DownloadStats> {
        let tasks = self.tasks.read().await;
        let mut stats = DownloadStats::default();
        
        // 统计总任务数
        stats.total_downloads = tasks.len();
        
        // 遍历所有任务，统计不同状态的任务数量和总下载/上传字节数
        for task in tasks.values() {
            match task.status {
                TaskStatus::Downloading => stats.active_downloads += 1,
                TaskStatus::Completed => stats.completed_downloads += 1,
                TaskStatus::Failed => stats.failed_downloads += 1,
                _ => {}, // 其他状态不计入特定分类
            }
            
            // 累计已下载字节数
            stats.total_downloaded_bytes += task.downloaded_size;
            
            // 统计上传字节数（对于P2P协议）
            stats.total_uploaded_bytes += task.uploaded_bytes;
        }
        
        debug!("获取下载统计信息: {:?}", stats);
        Ok(stats)
    }

    /// 添加新任务
    pub async fn add_task(&self, task: TaskInfo) -> Result<Uuid> {
        // 获取任务ID
        let task_id = task.id;
        
        // 保存任务信息
        {
            let mut tasks = self.tasks.write().await;
            tasks.insert(task_id, task.clone());
        }
        
        // 保存任务信息到恢复管理器
        self.resume_handler.save_task(&task).await?;
        debug!("Task saved to resume manager: {}", task_id);
        
        Ok(task_id)
    }

    /// 更新任务信息
    pub async fn update_task(&self, task_id: Uuid, task: TaskInfo) -> Result<()> {
        // 获取任务锁
        let mut tasks = self.tasks.write().await;
        
        // 检查任务是否存在
        if !tasks.contains_key(&task_id) {
            return Err(anyhow!("Task not found"));
        }
        
        // 更新任务信息
        tasks.insert(task_id, task);
        
        Ok(())
    }

    /// 获取任务信息
    pub async fn get_task(&self, task_id: Uuid) -> Result<TaskInfo> {
        let tasks = self.tasks.read().await;
        
        if let Some(task) = tasks.get(&task_id) {
            Ok(task.clone())
        } else {
            // 尝试从恢复管理器加载任务
            if let Ok(Some(task)) = self.resume_handler.load_task(task_id).await {
                // 将任务添加到内存中
                let mut tasks = self.tasks.write().await;
                tasks.insert(task_id, task.clone());
                
                Ok(task)
            } else {
                Err(anyhow!("Task not found: {}", task_id))
            }
        }
    }

    /// 获取所有任务
    pub async fn get_all_tasks(&self) -> Result<Vec<TaskInfo>> {
        let tasks = self.tasks.read().await;
        Ok(tasks.values().cloned().collect())
    }

    /// 更新任务状态
    pub async fn update_task_status(&self, task_id: Uuid, status: TaskStatus, error_message: Option<String>) -> Result<()> {
        let mut tasks = self.tasks.write().await;

        if let Some(task) = tasks.get_mut(&task_id) {
            let old_status = task.status.clone();
            task.status = status;
            task.updated_at = Utc::now();

            if let Some(msg) = error_message {
                task.error_message = Some(msg);
            }

            // 更新进度和速度（如果任务处于下载中状态）
            if status == TaskStatus::Downloading {
                if let Some(downloader) = self.downloaders.lock().await.get(&task_id) {
                    task.progress = downloader.progress().await.map(|p| p.progress_percentage).unwrap_or(0.0);
                    task.speed = downloader.speed().await.unwrap_or(0);
                }
            }

            // 通知任务状态变更
            let task_clone = task.clone();
            drop(tasks); // 释放锁，避免死锁
            self.event_handler.notify_task_status_changed(&task_clone, old_status).await?;

            Ok(())
        } else {
            Err(anyhow!("Task not found: {}", task_id))
        }
    }

    /// 启动任务
    pub async fn start_task(&self, task_id: Uuid) -> Result<()> {
        // 获取任务信息
        let task = self.get_task(task_id).await?;
        
        // 通知任务开始
        self.event_handler.notify_task_started(&task).await?;
        
        // 检查任务状态
        if task.status == TaskStatus::Downloading {
            info!("Task {} is already downloading.", task_id);
            return Ok(());
        }
        
        // 更新任务状态为初始化中
        self.update_task_status(task_id, TaskStatus::Initializing, None).await?;
        
        // 创建下载器
        info!("Creating downloader for task {} with URL {} and output path {}", task_id, task.url, task.output_path);
        let downloader_result = self.downloader_factory.create_downloader(&task.url, &task.output_path, task_id).await
            .map_err(|e| anyhow!("Failed to create downloader: {}", e));
        
        // 检查下载器创建结果
        if let Err(ref e) = downloader_result {
            error!("Failed to create downloader for task {}: {}", task_id, e);
            self.update_task_status(task_id, TaskStatus::Failed, Some(e.to_string())).await?;
            return Err(anyhow!("Failed to create downloader: {}", e));
        }
        
        // 克隆必要的资源
        let tasks_clone = self.tasks.clone();
        let downloaders_clone = self.downloaders.clone();
        let event_handler_clone = self.event_handler.clone();
        let resume_handler_clone = self.resume_handler.clone();
        
        // 启动下载任务
        tokio::spawn(async move {
            match downloader_result {
                Ok(mut downloader) => {
                    // 更新任务状态为下载中
                    let mut tasks = tasks_clone.write().await;
                    if let Some(task) = tasks.get_mut(&task_id) {
                        let old_status = task.status.clone();
                        task.status = TaskStatus::Downloading;
                        task.updated_at = Utc::now();
                        
                        // 通知任务状态更新
                        let task_clone = task.clone();
                        drop(tasks); // 释放锁，避免死锁
                        if let Err(e) = event_handler_clone.notify_task_status_changed(&task_clone, old_status).await {
                            error!("Failed to notify task status changed: {}", e);
                        }
                    } else {
                        drop(tasks);
                    }
                    
                    // 保存下载器实例
                    downloaders_clone.lock().await.insert(task_id, downloader.clone_box());
                    
                    // 开始下载
                    info!("Starting downloader for task {}", task_id);
                    if let Err(e) = downloader.start().await {
                        error!("Failed to start downloader: {}", e);
                        let mut tasks = tasks_clone.write().await;
                        if let Some(task) = tasks.get_mut(&task_id) {
                            let old_status = task.status.clone();
                            task.status = TaskStatus::Failed;
                            task.error_message = Some(e.to_string());
                            task.updated_at = Utc::now();
                            
                            // 保存任务状态
                            let task_clone = task.clone();
                            drop(tasks); // 释放锁，避免死锁
                            
                            if let Err(e) = resume_handler_clone.save_task(&task_clone).await {
                                error!("Failed to save task to resume manager: {}", e);
                            }
                            
                            // 通知任务失败
                            if let Err(e) = event_handler_clone.notify_task_failed(&task_clone).await {
                                error!("Failed to notify task failed: {}", e);
                            }
                            
                            // 通知任务状态变更
                            if let Err(e) = event_handler_clone.notify_task_status_changed(&task_clone, old_status).await {
                                error!("Failed to notify task status changed: {}", e);
                            }
                        }
                        return;
                    }

                    // 定期更新进度和速度
                    let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(1));
                    loop {
                        interval.tick().await;
                        let progress = match downloader.progress().await {
                            Ok(p) => p,
                            Err(e) => {
                                error!("Failed to get progress for task {}: {}", task_id, e);
                                DownloadProgress {
                                    total_size: None,
                                    downloaded_size: 0,
                                    progress_percentage: 0.0,
                                    speed: 0,
                                    eta: None,
                                }
                            }
                        };
                        let speed = match downloader.speed().await {
                            Ok(s) => s,
                            Err(e) => {
                                error!("Failed to get speed for task {}: {}", task_id, e);
                                0
                            }
                        };

                        let mut tasks = tasks_clone.write().await;
                        if let Some(task) = tasks.get_mut(&task_id) {
                            task.progress = progress.progress_percentage;
                            task.speed = speed;
                            task.updated_at = Utc::now();
                            // TODO: Update downloaded_size and total_size
                            
                            // 保存任务状态
                            let task_clone = task.clone();
                            drop(tasks); // 释放锁，避免死锁
                            
                            if let Err(e) = resume_handler_clone.save_task(&task_clone).await {
                                error!("Failed to save task to resume manager: {}", e);
                            }
                            
                            // 通知任务进度和速度
                            if let Err(e) = event_handler_clone.notify_task_progress(&task_clone).await {
                                error!("Failed to notify task progress: {}", e);
                            }
                            
                            if let Err(e) = event_handler_clone.notify_task_speed(task_id, speed).await {
                                error!("Failed to notify task speed: {}", e);
                            }
                        } else {
                            drop(tasks);
                        }

                        if progress.progress_percentage >= 1.0 {
                            info!("Task {} completed.", task_id);
                            let mut tasks = tasks_clone.write().await;
                            if let Some(task) = tasks.get_mut(&task_id) {
                                let old_status = task.status.clone();
                                task.status = TaskStatus::Completed;
                                task.updated_at = Utc::now();
                                
                                // 保存任务状态
                                let task_clone = task.clone();
                                drop(tasks); // 释放锁，避免死锁
                                
                                if let Err(e) = resume_handler_clone.save_task(&task_clone).await {
                                    error!("Failed to save task to resume manager: {}", e);
                                }
                                
                                // 通知任务完成
                                if let Err(e) = event_handler_clone.notify_task_completed(&task_clone).await {
                                    error!("Failed to notify task completed: {}", e);
                                }
                                
                                // 通知任务状态变更
                                if let Err(e) = event_handler_clone.notify_task_status_changed(&task_clone, old_status).await {
                                    error!("Failed to notify task status changed: {}", e);
                                }
                            }
                            break;
                        }

                        let tasks_read = tasks_clone.read().await;
                        if let Some(task) = tasks_read.get(&task_id) {
                            if task.status == TaskStatus::Paused || task.status == TaskStatus::Cancelled {
                                info!("Task {} paused or cancelled.", task_id);
                                break;
                            }
                        }
                    }
                },
                Err(e) => {
                    error!("Failed to get downloader for task {}: {}", task_id, e);
                    let mut tasks = tasks_clone.write().await;
                    if let Some(task) = tasks.get_mut(&task_id) {
                        task.status = TaskStatus::Failed;
                        task.error_message = Some(e.to_string());
                        task.updated_at = Utc::now();
                        
                        // 保存任务状态
                        let task_clone = task.clone();
                        drop(tasks); // 释放锁，避免死锁
                        
                        if let Err(e) = resume_handler_clone.save_task(&task_clone).await {
                            error!("Failed to save task to resume manager: {}", e);
                        }
                    }
                },
            }
        });

        Ok(())
    }

    /// 暂停任务
    pub async fn pause_task(&self, task_id: Uuid) -> Result<()> {
        let mut tasks = self.tasks.write().await;
        let task = tasks.get_mut(&task_id).ok_or_else(|| anyhow!("Task not found"))?;

        if task.status != TaskStatus::Downloading {
            warn!("Task {} is not in downloading status, cannot pause.", task_id);
            return Ok(());
        }

        let old_status = task.status.clone();
        task.status = TaskStatus::Paused;
        task.updated_at = Utc::now();

        // 保存任务信息到恢复管理器
        let task_clone = task.clone();
        drop(tasks); // 释放锁，避免死锁
        
        self.resume_handler.save_task(&task_clone).await?;
        
        // 通知任务状态变更
        self.event_handler.notify_task_status_changed(&task_clone, old_status).await?;

        Ok(())
    }

    /// 恢复任务
    pub async fn resume_task(&self, task_id: Uuid) -> Result<()> {
        let mut tasks = self.tasks.write().await;
        let task = tasks.get_mut(&task_id).ok_or_else(|| anyhow!("Task not found"))?;

        if task.status != TaskStatus::Paused {
            warn!("Task {} is not in paused status, cannot resume.", task_id);
            return Ok(());
        }

        let old_status = task.status.clone();
        task.status = TaskStatus::Downloading;
        task.updated_at = Utc::now();

        // 保存任务信息到恢复管理器
        let task_clone = task.clone();
        drop(tasks); // 释放锁，避免死锁
        
        self.resume_handler.save_task(&task_clone).await?;
        
        // 检查是否已有下载器实例
        let downloader_exists = self.downloaders.lock().await.contains_key(&task_id);
        
        if !downloader_exists {
            // 如果没有下载器实例，创建一个新的下载器并启动下载
            info!("No active downloader found for task {}, creating a new one", task_id);
            self.start_task(task_id).await?
        } else {
            // 如果已有下载器实例，尝试恢复下载
            info!("Found existing downloader for task {}, resuming download", task_id);
            let mut downloaders = self.downloaders.lock().await;
            if let Some(downloader) = downloaders.get_mut(&task_id) {
                if let Err(e) = downloader.resume().await {
                    error!("Failed to resume downloader for task {}: {}", task_id, e);
                    self.update_task_status(task_id, TaskStatus::Failed, Some(e.to_string())).await?;
                    return Err(anyhow!("Failed to resume downloader: {}", e));
                }
            }
            
            // 通知任务状态变更
            self.event_handler.notify_task_status_changed(&task_clone, old_status).await?
        }

        Ok(())
    }

    /// 取消任务
    pub async fn cancel_task(&self, task_id: Uuid) -> Result<()> {
        let mut tasks = self.tasks.write().await;
        let task = tasks.get_mut(&task_id).ok_or_else(|| anyhow!("Task not found"))?;

        if task.status == TaskStatus::Cancelled {
            info!("Task {} is already cancelled.", task_id);
            return Ok(());
        }

        let old_status = task.status.clone();
        task.status = TaskStatus::Cancelled;
        task.updated_at = Utc::now();

        // 保存任务信息到恢复管理器
        let task_clone = task.clone();
        drop(tasks); // 释放锁，避免死锁
        
        self.resume_handler.save_task(&task_clone).await?;
        
        // 通知任务状态变更
        self.event_handler.notify_task_status_changed(&task_clone, old_status).await?;

        Ok(())
    }

    /// 删除任务
    pub async fn remove_task(&self, task_id: Uuid) -> Result<()> {
        // 先获取任务信息，用于日志记录
        let task_info = {
            let tasks = self.tasks.read().await;
            tasks.get(&task_id).cloned()
        };
        
        // 如果任务正在下载中或初始化中，先尝试取消它
        if let Some(task) = &task_info {
            if task.status == TaskStatus::Downloading || task.status == TaskStatus::Initializing {
                debug!("Cancelling active task {} before removal", task_id);
                // 尝试取消任务，但不中断删除流程
                if let Err(e) = self.cancel_task(task_id).await {
                    warn!("Failed to cancel task {} before removal: {}", task_id, e);
                }
            }
        }
        
        // 从任务列表中移除任务
        let mut tasks = self.tasks.write().await;
        if tasks.remove(&task_id).is_none() {
            return Err(anyhow!("Task not found"));
        }
        
        // 从恢复管理器中删除任务
        if let Err(e) = self.resume_handler.delete_task(task_id).await {
            error!("Failed to remove task from resume manager: {}", e);
        }
        
        // 移除下载器实例（如果存在）
        if let Some(mut downloader) = self.downloaders.lock().await.remove(&task_id) {
            // 如果下载器正在运行，尝试取消它
            if let Err(e) = downloader.cancel().await {
                error!("Failed to cancel downloader for task {}: {}", task_id, e);
            }
        }
        
        info!("Task {} has been completely removed from the system", task_id);
        Ok(())
    }
}