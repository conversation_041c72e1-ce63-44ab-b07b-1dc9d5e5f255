use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::collections::HashMap;

use crate::core::error::CoreResult;
use crate::core::interfaces::downloader::{DownloadOptions, DownloadProgress, DownloadStatus, ProtocolType};

/// Task information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TaskInfo {
    pub id: Uuid,
    pub url: String,
    pub output_path: String,
    pub protocol: ProtocolType,
    pub status: DownloadStatus,
    pub progress: DownloadProgress,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
    pub options: DownloadOptions,
    pub metadata: HashMap<String, String>,
}

/// Task creation request
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateTaskRequest {
    pub url: String,
    pub output_path: Option<String>,
    pub options: Option<DownloadOptions>,
    pub metadata: Option<HashMap<String, String>>,
}

/// Task update request
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UpdateTaskRequest {
    pub output_path: Option<String>,
    pub options: Option<DownloadOptions>,
    pub metadata: Option<HashMap<String, String>>,
}

/// Task filter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskFilter {
    pub status: Option<Vec<DownloadStatus>>,
    pub protocol: Option<Vec<ProtocolType>>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
    pub metadata: Option<HashMap<String, String>>,
}

/// Task manager interface
#[async_trait]
pub trait TaskManager: Send + Sync {
    /// Create a new task
    async fn create_task(&self, request: CreateTaskRequest) -> CoreResult<Uuid>;
    
    /// Get task information
    async fn get_task(&self, id: Uuid) -> CoreResult<TaskInfo>;
    
    /// Get all tasks
    async fn get_all_tasks(&self) -> CoreResult<Vec<TaskInfo>>;
    
    /// Get tasks by filter
    async fn get_tasks_by_filter(&self, filter: TaskFilter) -> CoreResult<Vec<TaskInfo>>;
    
    /// Update task
    async fn update_task(&self, id: Uuid, request: UpdateTaskRequest) -> CoreResult<()>;
    
    /// Start task
    async fn start_task(&self, id: Uuid) -> CoreResult<()>;
    
    /// Pause task
    async fn pause_task(&self, id: Uuid) -> CoreResult<()>;
    
    /// Resume task
    async fn resume_task(&self, id: Uuid) -> CoreResult<()>;
    
    /// Cancel task
    async fn cancel_task(&self, id: Uuid) -> CoreResult<()>;
    
    /// Remove task
    async fn remove_task(&self, id: Uuid) -> CoreResult<()>;
    
    /// Get task progress
    async fn get_task_progress(&self, id: Uuid) -> CoreResult<DownloadProgress>;
    
    /// Get task status
    async fn get_task_status(&self, id: Uuid) -> CoreResult<DownloadStatus>;
}
