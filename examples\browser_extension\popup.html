<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lumen下载助手</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      width: 300px;
      padding: 15px;
      margin: 0;
    }
    
    h1 {
      font-size: 18px;
      margin-top: 0;
      color: #333;
      text-align: center;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #555;
    }
    
    input[type="text"] {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .checkbox-group {
      display: flex;
      align-items: center;
    }
    
    .checkbox-group input {
      margin-right: 8px;
    }
    
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      width: 100%;
      font-size: 14px;
    }
    
    button:hover {
      background-color: #45a049;
    }
    
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      text-align: center;
      display: none;
    }
    
    .success {
      background-color: #dff0d8;
      color: #3c763d;
    }
    
    .error {
      background-color: #f2dede;
      color: #a94442;
    }
    
    .footer {
      margin-top: 15px;
      font-size: 12px;
      color: #777;
      text-align: center;
    }
  </style>
</head>
<body>
  <h1>Lumen下载助手</h1>
  
  <div class="form-group">
    <label for="lumenAppUrl">Lumen应用URL:</label>
    <input type="text" id="lumenAppUrl" placeholder="http://localhost:9080">
  </div>
  
  <div class="form-group checkbox-group">
    <input type="checkbox" id="useProtocol" checked>
    <label for="useProtocol">使用自定义协议 (lumen://)</label>
  </div>
  
  <button id="saveBtn">保存设置</button>
  
  <div id="status" class="status"></div>
  
  <div class="footer">
    <p>右键点击链接，选择「使用Lumen下载」即可发送到下载管理器</p>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>