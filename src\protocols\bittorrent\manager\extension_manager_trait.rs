use async_trait::async_trait;
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::{Arc, Weak};
use tokio::sync::Mutex;
use anyhow::Result;

use crate::protocols::bittorrent::BitTorrentPeer;
use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

/// 扩展管理器异步trait，定义扩展相关异步操作接口
#[async_trait]
pub trait ExtensionManagerTrait: Send + Sync {
    /// 初始化扩展
    async fn init_extensions(&mut self, peer_getter: Arc<dyn Fn() -> Vec<SocketAddr> + Send + Sync>, peer_adder: Arc<dyn Fn(SocketAddr) -> Result<(), BitTorrentError> + Send + Sync>) -> Result<(), BitTorrentError>;
    /// 设置对等点管理器回调
    async fn set_peer_manager_callback(&mut self, callback: Weak<dyn Send + Sync>);
    /// 处理扩展消息
    async fn handle_extension_message(&self, peer_addr: &str, message_id: u8, payload: &[u8]) -> Result<(), BitTorrentError>;
    /// 注册元数据扩展对象
    async fn register_metadata_extension_object(&mut self, info_hash: [u8; 20]) -> Result<(), BitTorrentError>;
    /// 处理待添加的对等点
    async fn process_pending_peers(&self, torrent_info: &TorrentInfo, peers: &mut HashMap<String, Arc<Mutex<BitTorrentPeer>>>) -> Result<(), BitTorrentError>;
    // 可扩展其它扩展相关异步接口
}
