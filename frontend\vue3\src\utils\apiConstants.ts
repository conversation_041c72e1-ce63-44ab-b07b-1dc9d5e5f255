/**
 * API 路径常量
 * 集中管理所有API路径，避免硬编码和不一致
 */

// 下载任务相关API
export const DownloadAPI = {
  // 基础路径
  BASE: '/downloads',
  // 添加下载任务
  ADD: '/downloads',
  // 暂停下载任务
  PAUSE: (taskId: string) => `/downloads/${taskId}/pause`,
  // 恢复下载任务
  RESUME: (taskId: string) => `/downloads/${taskId}/resume`,
  // 取消下载任务
  CANCEL: (taskId: string) => `/downloads/${taskId}/cancel`,
} as const;

// 任务管理相关API
export const TaskAPI = {
  // 基础路径
  BASE: '/tasks',
  // 获取所有任务
  GET_ALL: '/tasks',
  // 获取特定状态的任务
  GET_BY_STATUS: (status: string) => `/tasks?status=${status}`,
  // 获取特定任务
  GET: (taskId: string) => `/tasks/${taskId}`,
  // 更新任务
  UPDATE: (taskId: string) => `/tasks/${taskId}`,
  // 删除任务
  DELETE: (taskId: string) => `/tasks/${taskId}`,
  // 设置任务下载速度限制
  SET_DOWNLOAD_SPEED: (taskId: string) => `/tasks/${taskId}/speed/download`,
} as const;

// 速度控制相关API
export const SpeedAPI = {
  // 全局下载速度限制
  DOWNLOAD: '/speed/download',
  // 全局上传速度限制
  UPLOAD: '/speed/upload',
} as const;

// 系统状态相关API
export const StatusAPI = {
  // 获取系统状态
  GET: '/status',
} as const;

// 配置相关API
export const ConfigAPI = {
  // 基础路径
  BASE: '/config',
  // 获取所有配置
  GET_ALL: '/config',
  // 更新所有配置
  UPDATE_ALL: '/config',
  // 获取特定配置项
  GET: (key: string) => `/config/${key}`,
  // 更新特定配置项
  UPDATE: (key: string) => `/config/${key}`,
} as const;

// WebSocket相关API
export const WebSocketAPI = {
  // WebSocket连接
  CONNECT: '/ws',
} as const;