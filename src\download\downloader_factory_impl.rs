use anyhow::anyhow;
use crate::core::error::{CoreError, CoreResult};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;
use log::debug;

use crate::analyzer::LinkAnalyzer;
use crate::config::ConfigManager;
use crate::download::resume::ResumeManager;
use crate::download::bandwidth_scheduler::BandwidthScheduler;
use crate::protocols::http::HttpDownloader;
// use crate::protocols::r2::R2Downloader; // 将在未来整合
use crate::protocols::{
    bittorrent::BitTorrentFactory,
    custom_p2p::CustomP2PFactory,
};
// 导入核心接口
use crate::core::interfaces::{Downloader, DownloaderFactory, ProtocolType};
use crate::core::p2p::protocol::ProtocolFactory;
use crate::utils::HttpClient;

/// 下载器工厂实现
/// 直接创建实现 Downloader trait 的下载器，不再使用 DownloadProtocol
pub struct DownloaderFactoryImpl {
    config_manager: Arc<ConfigManager>,
    link_analyzer: Arc<dyn LinkAnalyzer>,
    bt_factory: BitTorrentFactory,
    p2p_factory: CustomP2PFactory,
    resume_manager: Arc<dyn ResumeManager>,
    bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
}

impl DownloaderFactoryImpl {
    /// 创建新的下载器工厂
    pub fn new(
        config_manager: Arc<ConfigManager>,
        link_analyzer: Arc<dyn LinkAnalyzer>,
        resume_manager: Arc<dyn ResumeManager>,
    ) -> Self {
        let bt_factory = BitTorrentFactory::new(config_manager.clone());
        let p2p_factory = CustomP2PFactory::new(config_manager.clone());

        Self {
            config_manager,
            link_analyzer,
            bt_factory,
            p2p_factory,
            resume_manager,
            bandwidth_scheduler: None,
        }
    }

    /// 设置带宽调度器
    pub fn with_bandwidth_scheduler(mut self, bandwidth_scheduler: Arc<dyn BandwidthScheduler>) -> Self {
        self.bandwidth_scheduler = Some(bandwidth_scheduler);
        self
    }
}

/// 实现核心接口中的DownloaderFactory trait
#[async_trait]
impl DownloaderFactory for DownloaderFactoryImpl {
    async fn create_downloader(
        &self,
        url: &str,
        output_path: &str,
        task_id: Uuid,
    ) -> CoreResult<Box<dyn Downloader>> {
        let protocol = self.link_analyzer.analyze(url).await.map_err(|e| CoreError::from(anyhow!(e)))?;

        // 创建适当的下载器
        match protocol.as_str() {
            "http" | "https" => {
                // 验证URL格式
                if !url.starts_with("http://") && !url.starts_with("https://") {
                    return Err(CoreError::InvalidArgument(format!("无效的HTTP/HTTPS URL格式: {}", url)));
                }
                
                // 创建HTTP下载器
                let mut downloader = HttpDownloader::new(
                    url.to_string(),
                    output_path.to_string(),
                    self.config_manager.clone(),
                    task_id,
                );
                
                // 创建自定义的 HttpClient
                let http_client = match HttpClient::from_config_manager(&self.config_manager).await {
                    Ok(client) => Arc::new(client),
                    Err(e) => {
                        debug!("Failed to create custom HTTP client: {}", e);
                        // 使用默认客户端
                        Arc::new(HttpClient::new().expect("Failed to create default HTTP client"))
                    }
                };
                
                // 设置自定义的 HTTP 客户端
                downloader = downloader.with_http_client(http_client);
                
                // 设置恢复管理器
                downloader = downloader.with_resume_manager(self.resume_manager.clone());

                // 如果有带宽调度器，设置到下载器中
                if let Some(scheduler) = &self.bandwidth_scheduler {
                    downloader = downloader.with_bandwidth_scheduler(scheduler.clone());
                }

                Ok(Box::new(downloader) as Box<dyn Downloader>)
            },
            "bt" | "magnet" => {
                // 使用BitTorrent工厂创建下载器
                if self.bt_factory.supports_url(url) {
                    let mut bt_factory = self.bt_factory.clone();

                    // 设置恢复管理器
                    bt_factory = bt_factory.with_resume_manager(self.resume_manager.clone());

                    // 如果有带宽调度器，设置到工厂中
                    if let Some(scheduler) = &self.bandwidth_scheduler {
                        bt_factory = bt_factory.with_bandwidth_scheduler(scheduler.clone());
                    }

                    // 创建BitTorrent下载器
                    let downloader = bt_factory.create_downloader(url, output_path, task_id);
                    
                    Ok(Box::new(downloader) as Box<dyn Downloader>)
                } else {
                    Err(CoreError::Unsupported(format!("BitTorrent工厂不支持URL: {}，请检查URL格式是否正确", url)))
                }
            },
            "p2p" => {
                // 使用自定义P2P协议工厂创建下载器
                if self.p2p_factory.supports_url(url) {
                    // 目前自定义P2P协议尚未实现，返回未实现错误
                    return Err(CoreError::Unsupported(format!("自定义P2P协议尚未实现，URL: {}", url)));
                    
                    // 注释掉原有的实现，因为Protocol trait没有实现Downloader trait
                    // let p2p_protocol = match self.p2p_factory.create_protocol(url, output_path, task_id).await {
                    //     Ok(protocol) => protocol,
                    //     Err(e) => return Err(CoreError::Protocol(format!("创建P2P协议失败: {}，URL: {}", e, url)))
                    // };
                    // 
                    // Ok(Box::new(p2p_protocol) as Box<dyn Downloader>)
                } else {
                    Err(CoreError::Unsupported(format!("P2P工厂不支持URL: {}，请检查URL格式是否正确", url)))
                }
            },
            "r2" => {
                // R2协议将在未来整合
                return Err(CoreError::Unsupported(format!("R2协议暂不可用，将在未来版本中整合: {}", url)));
                
                // 以下代码已注释，将在未来整合
                /*
                // 验证URL格式
                if !url.starts_with("r2://") {
                    return Err(CoreError::InvalidArgument(format!("无效的R2 URL格式: {}，R2 URL应以'r2://'开头", url)));
                }
                
                // 创建R2下载器
                let downloader = R2Downloader::new(
                    url.to_string(),
                    output_path.to_string(),
                    self.config_manager.clone(),
                    task_id,
                );
                
                Ok(Box::new(downloader) as Box<dyn Downloader>)
                */
            },
            _ => Err(CoreError::Unsupported(format!("不支持的协议类型: {}，支持的协议有: HTTP, HTTPS, BitTorrent, Magnet, P2P", protocol))),
        }
    }
    
    /// 获取支持的协议类型
    fn supported_protocols(&self) -> Vec<ProtocolType> {
        vec![
            ProtocolType::Http,
            ProtocolType::Https,
            ProtocolType::BitTorrent,
            ProtocolType::Magnet,
            ProtocolType::P2P,
            // ProtocolType::R2, // 将在未来整合
        ]
    }
}