use anyhow::Result;
use std::sync::Arc;
use tokio::sync::{<PERSON>te<PERSON>, RwLock};
use std::collections::HashMap;
use uuid::Uuid;
use tracing::{info, error};

use crate::config::{Config<PERSON>anager, Settings};
use crate::download::Downloader;
use crate::download::downloader_factory_impl::DownloaderFactoryImpl;
use crate::download::manager::models::{TaskInfo, DownloadStats};
use crate::download::manager::task_operations::TaskOperations;
use crate::download::manager::event_handling::EventHandler;
use crate::download::manager::resume_handling::ResumeHandler;
use crate::download::resume::ResumeManagerImpl;
use crate::storage::local::LocalStorage;
use crate::ws::WebSocketServer;

/// WebSocket下载管理器，通过WebSocket接口提供下载管理功能
pub struct WebSocketDownloadManager {
    tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
    downloaders: Arc<Mutex<HashMap<Uuid, Box<dyn Downloader>>>>,
    downloader_factory: Arc<DownloaderFactoryImpl>,
    task_operations: Arc<TaskOperations>,
    event_handler: Arc<EventHandler>,
    resume_handler: Arc<ResumeHandler>,
    ws_server: Arc<WebSocketServer>,
}

impl WebSocketDownloadManager {
    /// 创建一个新的WebSocket下载管理器
    pub fn new(settings: Settings, ws_server: Arc<WebSocketServer>) -> Self {
        // 创建配置管理器
        let config_manager = Arc::new(ConfigManager::new());
        
        // 创建本地存储
        let storage = Arc::new(LocalStorage::new(&settings.download_dir).unwrap());
        
        // 创建恢复管理器
        let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage.clone()));
        
        // 创建下载器工厂
        let downloader_factory = Arc::new(DownloaderFactoryImpl::new(
            config_manager.clone(),
            Arc::new(crate::analyzer::link_analyzer::DefaultLinkAnalyzer::new()),
            resume_manager.clone()
        ));
        
        // 创建任务和下载器集合
        let tasks = Arc::new(RwLock::new(HashMap::new()));
        let downloaders = Arc::new(Mutex::new(HashMap::new()));
        
        // 创建事件处理器
        let event_handler = Arc::new(EventHandler::new(ws_server.clone()));
        
        // 创建恢复处理器
        let resume_handler = Arc::new(ResumeHandler::new(
            settings.clone(),
            resume_manager.clone(),
            tasks.clone()
        ));
        
        // 创建任务操作处理器
        let task_operations = Arc::new(TaskOperations::new(
            tasks.clone(),
            downloaders.clone(),
            downloader_factory.clone(),
            event_handler.clone(),
            resume_handler.clone()
        ));
        
        // 启动全局速度监控
        Self::start_global_speed_monitor(
            tasks.clone(),
            downloaders.clone(),
            event_handler.clone()
        );
        
        Self {
            tasks,
            downloaders,
            downloader_factory,
            task_operations,
            event_handler,
            resume_handler,
            ws_server,
        }
    }
    
    /// 启动全局速度监控
    fn start_global_speed_monitor(
        tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
        downloaders: Arc<Mutex<HashMap<Uuid, Box<dyn Downloader>>>>,
        event_handler: Arc<EventHandler>
    ) {
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(1));
            
            loop {
                interval.tick().await;
                
                // 计算全局下载速度
                let mut total_speed = 0;
                let downloaders_guard = downloaders.lock().await;
                
                for (_, downloader) in downloaders_guard.iter() {
                    if let Ok(progress) = downloader.progress().await {
                        total_speed += progress.speed;
                    }
                }
                
                // 通知全局速度更新
                if let Err(e) = event_handler.notify_global_speed_updated(total_speed).await {
                    error!("Failed to notify global speed updated: {}", e);
                }
            }
        });
    }
    
    /// 获取下载统计信息
    pub async fn get_download_stats(&self) -> Result<DownloadStats> {
        self.task_operations.get_download_stats().await
    }
    
    /// 添加下载任务
    pub async fn add_task(&self, url: String, output_path: String) -> Result<Uuid> {
        // 创建任务信息
        let task_id = Uuid::new_v4();
        let task = TaskInfo::new(task_id, url, output_path);
        
        // 添加任务
        self.task_operations.add_task(task).await
    }
    
    /// 启动下载任务
    pub async fn start_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.start_task(task_id).await
    }
    
    /// 暂停下载任务
    pub async fn pause_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.pause_task(task_id).await
    }
    
    /// 恢复下载任务
    pub async fn resume_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.resume_task(task_id).await
    }
    
    /// 取消下载任务
    pub async fn cancel_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.cancel_task(task_id).await
    }
    
    /// 移除下载任务
    pub async fn remove_task(&self, task_id: Uuid) -> Result<()> {
        self.task_operations.remove_task(task_id).await
    }
    
    /// 获取任务信息
    pub async fn get_task(&self, task_id: Uuid) -> Result<TaskInfo> {
        self.task_operations.get_task(task_id).await
    }
    
    /// 获取所有任务
    pub async fn get_all_tasks(&self) -> Result<Vec<TaskInfo>> {
        self.task_operations.get_all_tasks().await
    }
}