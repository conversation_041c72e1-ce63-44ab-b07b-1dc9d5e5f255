//! 对等点质量评分系统的数据模型

use std::collections::{HashMap, VecDeque};
use std::net::SocketAddr;
use std::time::{Duration, SystemTime};

/// 对等点违规记录
#[derive(Debug, <PERSON><PERSON>)]
pub struct PeerViolation {
    /// 违规类型
    pub violation_type: String,
    /// 违规描述
    pub description: String,
    /// 违规时间
    pub timestamp: SystemTime,
    /// 严重程度 (1-10)
    pub severity: u8,
}

/// 历史评分记录
#[derive(Debug, Clone)]
pub struct HistoricalScore {
    /// 评分时间
    pub timestamp: SystemTime,
    /// 综合评分
    pub overall_score: f64,
}

/// 增强的对等点评分
#[derive(Debug, Clone)]
pub struct EnhancedPeerScore {
    // 基础性能指标
    /// 下载速度 (bytes/s)
    pub download_speed: u64,
    /// 上传速度 (bytes/s)
    pub upload_speed: u64,
    /// 成功率 (0.0-1.0)
    pub success_rate: f64,
    /// 响应时间 (ms)
    pub response_time: u64,
    
    // 高级指标
    /// 连接稳定性 (0.0-1.0)
    pub connection_stability: f64,
    /// 地理位置评分 (0.0-1.0)
    pub geo_score: f64,
    /// 协议兼容性 (0.0-1.0)
    pub protocol_compatibility: f64,
    
    // 安全指标
    /// 可疑行为计数
    pub suspicious_behavior_count: u32,
    /// 违规历史
    pub violations: Vec<PeerViolation>,
    
    // 元数据
    /// 首次发现时间
    pub first_seen: SystemTime,
    /// 最后更新时间
    pub last_update: SystemTime,
    /// 更新次数
    pub update_count: u32,
    /// 历史评分
    pub historical_scores: VecDeque<HistoricalScore>,
    /// 综合评分 (0.0-1.0)
    pub overall_score: f64,
}

impl EnhancedPeerScore {
    /// 创建新的对等点评分
    pub fn new() -> Self {
        let now = SystemTime::now();
        Self {
            download_speed: 0,
            upload_speed: 0,
            success_rate: 1.0,
            response_time: 0,
            connection_stability: 1.0,
            geo_score: 0.5, // 默认中等地理评分
            protocol_compatibility: 1.0,
            suspicious_behavior_count: 0,
            violations: Vec::new(),
            first_seen: now,
            last_update: now,
            update_count: 0,
            historical_scores: VecDeque::with_capacity(10),
            overall_score: 0.5, // 默认中等评分
        }
    }

    /// 更新评分
    pub fn update(
        &mut self,
        download_speed: Option<u64>,
        upload_speed: Option<u64>,
        success: Option<bool>,
        response_time: Option<u64>,
        connection_stability: Option<f64>,
        geo_score: Option<f64>,
        protocol_compatibility: Option<f64>,
    ) {
        let now = SystemTime::now();
        
        // 更新下载速度（使用指数移动平均）
        if let Some(speed) = download_speed {
            if self.download_speed == 0 {
                self.download_speed = speed;
            } else {
                self.download_speed = (self.download_speed * 7 + speed * 3) / 10;
            }
        }

        // 更新上传速度（使用指数移动平均）
        if let Some(speed) = upload_speed {
            if self.upload_speed == 0 {
                self.upload_speed = speed;
            } else {
                self.upload_speed = (self.upload_speed * 7 + speed * 3) / 10;
            }
        }

        // 更新成功率（使用指数移动平均）
        if let Some(success_val) = success {
            let success_value = if success_val { 1.0 } else { 0.0 };
            self.success_rate = self.success_rate * 0.7 + success_value * 0.3;
        }

        // 更新响应时间（使用指数移动平均）
        if let Some(time) = response_time {
            if self.response_time == 0 {
                self.response_time = time;
            } else {
                self.response_time = (self.response_time * 7 + time * 3) / 10;
            }
        }

        // 更新连接稳定性
        if let Some(stability) = connection_stability {
            self.connection_stability = stability;
        }

        // 更新地理位置评分
        if let Some(score) = geo_score {
            self.geo_score = score;
        }

        // 更新协议兼容性
        if let Some(compatibility) = protocol_compatibility {
            self.protocol_compatibility = compatibility;
        }

        // 更新元数据
        self.last_update = now;
        self.update_count += 1;

        // 计算综合评分
        self.calculate_overall_score();

        // 添加历史评分记录
        if self.update_count % 10 == 0 { // 每10次更新记录一次历史评分
            self.historical_scores.push_back(HistoricalScore {
                timestamp: now,
                overall_score: self.overall_score,
            });

            // 保持历史记录在合理范围内
            if self.historical_scores.len() > 10 {
                self.historical_scores.pop_front();
            }
        }
    }

    /// 计算综合评分
    pub fn calculate_overall_score(&mut self) {
        // 归一化下载速度 (0.0-1.0)，假设5MB/s是满分
        let max_download_speed = 5 * 1024 * 1024; // 5MB/s
        let download_score = (self.download_speed as f64 / max_download_speed as f64).min(1.0);

        // 归一化上传速度 (0.0-1.0)，假设1MB/s是满分
        let max_upload_speed = 1024 * 1024; // 1MB/s
        let upload_score = (self.upload_speed as f64 / max_upload_speed as f64).min(1.0);

        // 归一化响应时间 (0.0-1.0)，假设50ms以下是满分，1000ms以上是0分
        let response_score = if self.response_time < 50 {
            1.0
        } else if self.response_time > 1000 {
            0.0
        } else {
            1.0 - (self.response_time - 50) as f64 / 950.0
        };

        // 安全评分，基于违规历史和可疑行为
        let security_score = if self.violations.is_empty() {
            1.0
        } else {
            // 计算违规严重程度总和
            let total_severity: u32 = self.violations.iter()
                .map(|v| v.severity as u32)
                .sum();
            
            // 安全评分随着违规严重程度增加而降低
            (1.0 - (total_severity as f64 / 100.0)).max(0.0)
        };

        // 综合评分，权重可调整
        self.overall_score = download_score * 0.3 +
                            upload_score * 0.1 +
                            self.success_rate * 0.15 +
                            response_score * 0.1 +
                            self.connection_stability * 0.1 +
                            self.geo_score * 0.05 +
                            self.protocol_compatibility * 0.05 +
                            security_score * 0.15;
    }

    /// 添加违规记录
    pub fn add_violation(&mut self, violation_type: &str, description: &str, severity: u8) {
        let violation = PeerViolation {
            violation_type: violation_type.to_string(),
            description: description.to_string(),
            timestamp: SystemTime::now(),
            severity,
        };

        self.violations.push(violation);
        self.suspicious_behavior_count += 1;

        // 重新计算综合评分
        self.calculate_overall_score();
    }

    /// 获取最近的违规记录
    pub fn recent_violations(&self, count: usize) -> Vec<&PeerViolation> {
        let start = if self.violations.len() > count {
            self.violations.len() - count
        } else {
            0
        };

        self.violations[start..].iter().collect()
    }

    /// 检查评分是否过期
    pub fn is_expired(&self, max_age: Duration) -> bool {
        match SystemTime::now().duration_since(self.last_update) {
            Ok(duration) => duration > max_age,
            Err(_) => false, // 如果出现时间错误，假设未过期
        }
    }
}

/// 对等点质量配置
#[derive(Debug, Clone)]
pub struct PeerQualityConfig {
    /// 评分权重
    pub weights: HashMap<String, f64>,
    /// 评分参数
    pub scoring_params: HashMap<String, f64>,
    /// 历史记录参数
    pub history_params: HashMap<String, u32>,
    /// 过滤参数
    pub filter_params: HashMap<String, String>,
}

impl Default for PeerQualityConfig {
    fn default() -> Self {
        let mut weights = HashMap::new();
        weights.insert("download_speed".to_string(), 0.3);
        weights.insert("upload_speed".to_string(), 0.1);
        weights.insert("success_rate".to_string(), 0.15);
        weights.insert("response_time".to_string(), 0.1);
        weights.insert("connection_stability".to_string(), 0.1);
        weights.insert("geo_score".to_string(), 0.05);
        weights.insert("protocol_compatibility".to_string(), 0.05);
        weights.insert("security_score".to_string(), 0.15);
        
        let mut scoring_params = HashMap::new();
        scoring_params.insert("max_download_speed".to_string(), 5.0 * 1024.0 * 1024.0); // 5MB/s
        scoring_params.insert("max_upload_speed".to_string(), 1024.0 * 1024.0); // 1MB/s
        scoring_params.insert("min_response_time".to_string(), 50.0); // 50ms
        scoring_params.insert("max_response_time".to_string(), 1000.0); // 1000ms
        
        let mut history_params = HashMap::new();
        history_params.insert("max_history_size".to_string(), 10);
        history_params.insert("history_update_interval".to_string(), 10);
        
        let mut filter_params = HashMap::new();
        filter_params.insert("performance_threshold".to_string(), "0.3".to_string());
        
        Self {
            weights,
            scoring_params,
            history_params,
            filter_params,
        }
    }
}