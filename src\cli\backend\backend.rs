//! CLI后端接口定义
//!
//! 定义CLI后端接口。

use anyhow::Result;
use uuid::Uuid;

use super::models::{SpeedLimitResponse, TimeRule, Config, Status, DownloadStats};

/// CLI后端接口
#[async_trait::async_trait]
pub trait CliBackend: Send + Sync {
    /// 添加下载任务
    async fn add_download_task(&self, url: &str, output_path: &str) -> Result<Uuid>;
    
    /// 开始下载任务
    async fn start_download_task(&self, task_id: Uuid) -> Result<()>;
    
    /// 暂停下载任务
    async fn pause_download_task(&self, task_id: Uuid) -> Result<()>;
    
    /// 恢复下载任务
    async fn resume_download_task(&self, task_id: Uuid) -> Result<()>;
    
    /// 取消下载任务
    async fn cancel_download_task(&self, task_id: Uuid) -> Result<()>;
    
    /// 获取下载任务信息
    async fn get_download_task_info(&self, task_id: Uuid) -> Result<String>;
    
    /// 获取所有下载任务
    async fn get_all_download_tasks(&self) -> Result<Vec<String>>;
    
    /// 移除下载任务
    async fn remove_download_task(&self, task_id: Uuid) -> Result<()>;
    
    /// 获取下载统计信息
    async fn get_download_stats(&self) -> Result<DownloadStats>;
    
    /// 设置全局下载速度限制
    async fn set_global_download_limit(&self, limit: u64) -> Result<()>;
    
    /// 获取全局下载速度限制
    async fn get_global_download_limit(&self) -> Result<SpeedLimitResponse>;
    
    /// 设置全局上传速度限制
    async fn set_global_upload_limit(&self, limit: u64) -> Result<()>;
    
    /// 获取全局上传速度限制
    async fn get_global_upload_limit(&self) -> Result<SpeedLimitResponse>;
    
    /// 设置任务下载速度限制
    async fn set_task_download_limit(&self, task_id: Uuid, limit: u64) -> Result<()>;
    
    /// 获取任务下载速度限制
    async fn get_task_download_limit(&self, task_id: Uuid) -> Result<SpeedLimitResponse>;
    
    /// 设置任务上传速度限制
    async fn set_task_upload_limit(&self, task_id: Uuid, limit: u64) -> Result<()>;
    
    /// 获取任务上传速度限制
    async fn get_task_upload_limit(&self, task_id: Uuid) -> Result<SpeedLimitResponse>;
    
    /// 添加时间规则
    async fn add_time_rule(&self, rule: TimeRule) -> Result<Uuid>;
    
    /// 获取所有时间规则
    async fn get_time_rules(&self) -> Result<Vec<TimeRule>>;
    
    /// 删除时间规则
    async fn delete_time_rule(&self, rule_id: Uuid) -> Result<()>;
    
    /// 获取配置
    async fn get_config(&self) -> Result<Config>;
    
    /// 更新配置
    async fn update_config(&self, config: Config) -> Result<()>;
    
    /// 更新配置项
    async fn update_config_item(&self, key: &str, value: &str) -> Result<()>;
    
    /// 保存配置到默认路径
    async fn save_config(&self) -> Result<()>;
    
    /// 重置配置到默认值
    async fn reset_config(&self) -> Result<()>;
    
    /// 获取系统状态
    async fn get_system_status(&self) -> Result<Status>;
    
    /// 获取速度限制
    async fn get_speed_limit(&self) -> Result<Option<u64>>;
    
    /// 设置速度限制
    async fn set_speed_limit(&self, limit: u64) -> Result<()>;
    
    /// 移除速度限制
    async fn remove_speed_limit(&self) -> Result<()>;
}