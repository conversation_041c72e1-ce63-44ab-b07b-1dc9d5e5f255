#[cfg(test)]
mod tests {
    use std::net::{IpAddr, Ipv4Addr, SocketAddr};
    use std::sync::Arc;
    use tokio::net::UdpSocket;
    use tokio::sync::Mutex;

    use crate::protocols::bittorrent::nat::nat_detector::NATType;
    use crate::protocols::bittorrent::nat::stun::client::{StunClient, StunClientConfig};
    use crate::protocols::bittorrent::nat::stun::message::{StunMessage, StunMessageType};

    // 模拟STUN服务器
    struct MockStunServer {
        socket: UdpSocket,
        addr: SocketAddr,
        nat_type: NATType,
    }

    impl MockStunServer {
        async fn new(nat_type: NATType) -> Self {
            let socket = UdpSocket::bind("127.0.0.1:0").await.unwrap();
            let addr = socket.local_addr().unwrap();

            Self {
                socket,
                addr,
                nat_type,
            }
        }

        // 添加映射地址属性到STUN响应
        fn add_mapped_address(&self, response: &mut StunMessage, addr: SocketAddr) {
            use crate::protocols::bittorrent::nat::stun::message::StunAttributeType;

            // 创建映射地址属性
            let mut value = Vec::new();

            // 保留字段（8位）和地址族（8位）
            value.push(0); // 保留字段

            // 地址族（IPv4 = 1, IPv6 = 2）
            match addr.ip() {
                IpAddr::V4(_) => value.push(1),
                IpAddr::V6(_) => value.push(2),
            }

            // 端口（16位，网络字节序）
            let port_bytes = addr.port().to_be_bytes();
            value.extend_from_slice(&port_bytes);

            // IP地址（IPv4 = 32位，IPv6 = 128位，网络字节序）
            match addr.ip() {
                IpAddr::V4(ipv4) => {
                    value.extend_from_slice(&ipv4.octets());
                },
                IpAddr::V6(ipv6) => {
                    value.extend_from_slice(&ipv6.octets());
                },
            }

            // 添加属性
            response.attributes.push(crate::protocols::bittorrent::nat::stun::message::StunAttribute {
                attribute_type: StunAttributeType::MappedAddress,
                value,
            });
        }

        async fn run(&self) {
            let mut buf = vec![0u8; 1024];

            loop {
                match self.socket.recv_from(&mut buf).await {
                    Ok((len, src)) => {
                        if let Ok(request) = StunMessage::decode(&buf[..len]) {
                            if request.message_type == StunMessageType::BindingRequest {
                                // 创建响应
                                let mut response = StunMessage {
                                    message_type: StunMessageType::BindingResponse,
                                    transaction_id: request.transaction_id,
                                    attributes: Vec::new(),
                                };

                                // 根据NAT类型模拟不同的行为
                                match self.nat_type {
                                    NATType::Open => {
                                        // 返回真实的源地址
                                        // 在这个测试中，我们假设客户端和服务器在同一台机器上
                                        let mapped_addr = src;
                                        // 添加映射地址属性
                                        self.add_mapped_address(&mut response, mapped_addr);
                                    },
                                    NATType::FullCone => {
                                        // 返回一个固定的映射地址
                                        let mapped_addr = SocketAddr::new(
                                            IpAddr::V4(Ipv4Addr::new(1, 2, 3, 4)),
                                            12345
                                        );
                                        // 添加映射地址属性
                                        self.add_mapped_address(&mut response, mapped_addr);
                                    },
                                    NATType::RestrictedCone => {
                                        // 返回一个固定的映射地址
                                        let mapped_addr = SocketAddr::new(
                                            IpAddr::V4(Ipv4Addr::new(1, 2, 3, 4)),
                                            12345
                                        );
                                        // 添加映射地址属性
                                        self.add_mapped_address(&mut response, mapped_addr);

                                        // 对于变更请求，只响应IP变更请求
                                        for attr in &request.attributes {
                                            if attr.attribute_type == crate::protocols::bittorrent::nat::stun::message::StunAttributeType::ChangeRequest {
                                                let change_ip = (attr.value[3] & 0x04) != 0;
                                                let change_port = (attr.value[3] & 0x02) != 0;

                                                if change_ip && !change_port {
                                                    // 不响应IP变更请求
                                                    return;
                                                }
                                            }
                                        }
                                    },
                                    NATType::PortRestrictedCone => {
                                        // 返回一个固定的映射地址
                                        let mapped_addr = SocketAddr::new(
                                            IpAddr::V4(Ipv4Addr::new(1, 2, 3, 4)),
                                            12345
                                        );
                                        // 添加映射地址属性
                                        self.add_mapped_address(&mut response, mapped_addr);

                                        // 对于变更请求，不响应任何变更请求
                                        for attr in &request.attributes {
                                            if attr.attribute_type == crate::protocols::bittorrent::nat::stun::message::StunAttributeType::ChangeRequest {
                                                let change_ip = (attr.value[3] & 0x04) != 0;
                                                let change_port = (attr.value[3] & 0x02) != 0;

                                                if change_ip || change_port {
                                                    // 不响应任何变更请求
                                                    return;
                                                }
                                            }
                                        }
                                    },
                                    NATType::Symmetric => {
                                        // 返回一个基于源地址的映射地址
                                        let port = src.port();
                                        let mapped_addr = SocketAddr::new(
                                            IpAddr::V4(Ipv4Addr::new(1, 2, 3, 4)),
                                            port + 1000 // 使端口依赖于源端口
                                        );
                                        // 添加映射地址属性
                                        self.add_mapped_address(&mut response, mapped_addr);
                                    },
                                    _ => {
                                        // 未知类型，不响应
                                        return;
                                    }
                                }

                                // 发送响应
                                if let Ok(response_data) = response.encode() {
                                    let _ = self.socket.send_to(&response_data, src).await;
                                }
                            }
                        }
                    },
                    Err(_) => break,
                }
            }
        }
    }

    #[tokio::test]
    #[ignore] // 这个测试需要模拟STUN服务器，实际环境中可能无法正常运行
    async fn test_nat_type_detection() {

        // 创建模拟STUN服务器
        let server = MockStunServer::new(NATType::PortRestrictedCone).await;
        let server_addr = server.addr;

        // 启动服务器
        tokio::spawn(async move {
            server.run().await;
        });

        // 创建STUN客户端
        let config = StunClientConfig {
            servers: vec![server_addr.to_string()],
            request_timeout: 5,
            cache_ttl: 300,
        };
        let client = StunClient::new(config);

        // 检测NAT类型
        match client.detect_nat_type().await {
            Ok(nat_type) => {
                println!("Detected NAT type: {:?}", nat_type);
                // 在实际测试中，我们应该断言检测到的NAT类型
                // 但由于模拟服务器的限制，这里只打印结果
            },
            Err(e) => {
                println!("Failed to detect NAT type: {}", e);
            }
        }
    }
}
