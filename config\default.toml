# Tonitru Downloader Configuration

[server]
host = "127.0.0.1"
port = 8080

[download]
path = "./downloads"
concurrent_downloads = 5
connections_per_download = 8
chunk_size = 1048576  # 1MB
buffer_size = 8192    # 8KB
speed_limit = 0       # 0 means no limit

[database]
url = "sqlite:tonitru.db"

[mirror]
enabled = true
max_mirrors = 5
health_check_interval_secs = 300  # 5 minutes

[proxy]
enabled = false
url = ""
proxy_type = "http"  # "http", "https", "socks5"
username = ""
password = ""
no_proxy = []  # 不使用代理的域名或IP列表

[bittorrent]
enabled = true
port = 6881
max_peers = 50
max_connections = 30
connection_timeout = 30
request_timeout = 60
enable_dht = true
dht_port = 6881
dht_routing_table_size = 8
dht_node_timeout = 900
dht_query_timeout = 30
dht_auto_start = true
dht_maintenance_interval = 300
dht_routing_table_path = "data/dht_routing_table.json"
dht_routing_table_save_interval = 600
dht_load_routing_table = true

# 上传设置
upload_enabled = true

# Default BitTorrent trackers
default_trackers = [
    "udp://tracker.opentrackr.org:1337/announce",
    "udp://open.demonii.com:1337/announce",
    "udp://open.stealth.si:80/announce",
    "udp://exodus.desync.com:6969/announce",
    "udp://tracker.torrent.eu.org:451/announce",
    "udp://tracker.skyts.net:6969/announce",
    "udp://tracker.ololosh.space:6969/announce",
    "udp://explodie.org:6969/announce",
    "http://tracker.ipv6tracker.org:80/announce",
    "http://tracker.dmcomic.org:2710/announce",
    "http://tracker.bt-hash.com:80/announce",
    "http://t.jaekr.sh:6969/announce",
    "http://highteahop.top:6960/announce",
    "http://finbytes.org:80/announce.php",
    "http://bt1.xxxxbt.cc:6969/announce",
    "udp://tracker.tiny-vps.com:6969/announce",
    "udp://tracker.dump.cl:6969/announce",
    "udp://tracker.bittor.pw:1337/announce",
    "udp://tracker-udp.gbitt.info:80/announce",
    "udp://opentracker.io:6969/announce"
]

# DHT bootstrap nodes
dht_bootstrap_nodes = [
    "router.bittorrent.com:6881",
    "dht.transmissionbt.com:6881",
    "router.utorrent.com:6881"
]
