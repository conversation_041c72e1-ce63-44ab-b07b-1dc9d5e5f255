//! HTTP下载器工具模块 - 重新导出各个功能模块

// 这个文件现在作为一个重新导出模块，将功能分散到各个专门的模块中
// 保持向后兼容性，所有原有的公共接口都可以通过这个模块访问

// 导入各个功能模块的实现 - 使用 use 语句从同级模块导入
pub use crate::protocols::http::file_utils;
pub use crate::protocols::http::buffer_manager;
pub use crate::protocols::http::http_client;
pub use crate::protocols::http::resume_manager;
pub use crate::protocols::http::speed_tracker;
pub use crate::protocols::http::cancellation;
pub use crate::protocols::http::chunk_manager;
pub use crate::protocols::http::multi_thread_downloader;
pub use crate::protocols::http::metadata_manager;
pub use crate::protocols::http::performance_optimizer;

// 重新导出所有功能，保持向后兼容性
pub use file_utils::*;
pub use buffer_manager::*;
pub use http_client::*;
pub use resume_manager::*;
pub use speed_tracker::*;
pub use cancellation::*;
pub use chunk_manager::*;
pub use multi_thread_downloader::*;
pub use metadata_manager::*;
pub use performance_optimizer::*;
