use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use anyhow::{Result, anyhow};
use tokio::sync::{Mute<PERSON>, RwLock};
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tracing::{debug, info, warn, error};

use crate::protocols::bittorrent::peer::PeerInfo;
use crate::protocols::bittorrent::message::BitTorrentMessage;
use crate::protocols::bittorrent::torrent::TorrentInfo;
use crate::core::p2p::piece::PieceManager;

use super::{UploadConfig, Uploader, PieceUploader, ChokingAlgorithm, StandardChokingAlgorithm};

/// 上传任务
/// 表示一个上传任务
#[derive(Debug)]
pub struct UploadTask {
    /// 任务ID
    pub id: String,
    
    /// 对等点地址
    pub peer_addr: SocketAddr,
    
    /// 上传器
    pub uploader: Arc<dyn Uploader>,
    
    /// 任务状态
    pub state: UploadTaskState,
    
    /// 创建时间
    pub created_at: std::time::Instant,
    
    /// 最后活动时间
    pub last_activity: std::time::Instant,
}

/// 上传任务状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UploadTaskState {
    /// 初始化
    Initializing,
    
    /// 活跃
    Active,
    
    /// 暂停
    Paused,
    
    /// 完成
    Completed,
    
    /// 失败
    Failed,
}

/// 上传调度器
/// 管理多个上传任务
pub struct UploadScheduler {
    /// 上传配置
    config: UploadConfig,
    
    /// 分片管理器
    piece_manager: Arc<Mutex<dyn PieceManager>>,
    
    /// 种子信息
    torrent_info: Arc<TorrentInfo>,
    
    /// 上传任务
    tasks: Arc<RwLock<HashMap<String, UploadTask>>>,
    
    /// 阻塞算法
    choking_algorithm: Arc<dyn ChokingAlgorithm>,
    
    /// 调度器任务句柄
    scheduler_task: Option<JoinHandle<()>>,
    
    /// 是否正在运行
    running: Arc<RwLock<bool>>,
}

impl UploadScheduler {
    /// 创建新的上传调度器
    pub fn new(
        piece_manager: Arc<Mutex<dyn PieceManager>>,
        torrent_info: Arc<TorrentInfo>,
        config: UploadConfig,
    ) -> Self {
        let choking_algorithm = Arc::new(StandardChokingAlgorithm::new(config.clone()));
        
        Self {
            config,
            piece_manager,
            torrent_info,
            tasks: Arc::new(RwLock::new(HashMap::new())),
            choking_algorithm,
            scheduler_task: None,
            running: Arc::new(RwLock::new(false)),
        }
    }
    
    /// 创建带自定义阻塞算法的上传调度器
    pub fn with_choking_algorithm(
        piece_manager: Arc<Mutex<dyn PieceManager>>,
        torrent_info: Arc<TorrentInfo>,
        config: UploadConfig,
        choking_algorithm: Arc<dyn ChokingAlgorithm>,
    ) -> Self {
        Self {
            config,
            piece_manager,
            torrent_info,
            tasks: Arc::new(RwLock::new(HashMap::new())),
            choking_algorithm,
            scheduler_task: None,
            running: Arc::new(RwLock::new(false)),
        }
    }
    
    /// 启动上传调度器
    pub async fn start(&mut self) -> Result<()> {
        let mut running = self.running.write().await;
        
        if *running {
            return Ok(());
        }
        
        *running = true;
        
        // 创建调度器任务
        let config = self.config.clone();
        let tasks = self.tasks.clone();
        let choking_algorithm = self.choking_algorithm.clone();
        let running_flag = self.running.clone();
        
        let scheduler_task = tokio::spawn(async move {
            let interval = std::time::Duration::from_secs(1);
            
            while *running_flag.read().await {
                // 等待一段时间
                tokio::time::sleep(interval).await;
                
                // 获取所有上传任务
                let tasks_read = tasks.read().await;
                
                // 创建对等点信息映射
                let mut peers = HashMap::new();
                
                for task in tasks_read.values() {
                    if task.state == UploadTaskState::Active {
                        // 创建对等点信息
                        let peer_info = PeerInfo {
                            addr: task.peer_addr,
                            connected: true,
                            am_interested: false,
                            am_choked: false,
                            peer_interested: match task.uploader.is_peer_interested().await {
                                Ok(interested) => interested,
                                Err(_) => false,
                            },
                            peer_choking: match task.uploader.is_choking().await {
                                Ok(choking) => choking,
                                Err(_) => true,
                            },
                            last_seen: task.last_activity,
                            download_rate: 0, // 需要从统计信息中获取
                            upload_rate: 0, // 需要从统计信息中获取
                        };
                        
                        peers.insert(task.peer_addr, peer_info);
                    }
                }
                
                // 运行阻塞算法
                if let Ok(messages) = choking_algorithm.run(&peers).await {
                    // 发送消息给对等点
                    for (addr, peer_messages) in messages {
                        // 查找对应的上传任务
                        for task in tasks_read.values() {
                            if task.peer_addr == addr && task.state == UploadTaskState::Active {
                                // 发送消息
                                for message in peer_messages {
                                    match message {
                                        BitTorrentMessage::Choke => {
                                            if let Err(e) = task.uploader.set_choking(true).await {
                                                error!("Failed to set choking state: {}", e);
                                            }
                                        },
                                        BitTorrentMessage::Unchoke => {
                                            if let Err(e) = task.uploader.set_choking(false).await {
                                                error!("Failed to set choking state: {}", e);
                                            }
                                        },
                                        _ => {
                                            // 忽略其他消息类型
                                        }
                                    }
                                }
                                
                                break;
                            }
                        }
                    }
                }
            }
        });
        
        self.scheduler_task = Some(scheduler_task);
        
        Ok(())
    }
    
    /// 停止上传调度器
    pub async fn stop(&mut self) -> Result<()> {
        let mut running = self.running.write().await;
        
        if !*running {
            return Ok(());
        }
        
        *running = false;
        
        // 等待调度器任务完成
        if let Some(task) = self.scheduler_task.take() {
            if !task.is_finished() {
                task.abort();
            }
        }
        
        Ok(())
    }
    
    /// 创建上传任务
    pub async fn create_task(&self, peer_addr: SocketAddr, supports_fast: bool) -> Result<String> {
        // 创建上传器
        let uploader = Arc::new(PieceUploader::new(
            self.piece_manager.clone(),
            self.torrent_info.clone(),
            self.config.clone(),
            supports_fast,
        ));
        
        // 创建任务ID
        let task_id = format!("upload-{}-{}", peer_addr, uuid::Uuid::new_v4());
        
        // 创建上传任务
        let task = UploadTask {
            id: task_id.clone(),
            peer_addr,
            uploader,
            state: UploadTaskState::Initializing,
            created_at: std::time::Instant::now(),
            last_activity: std::time::Instant::now(),
        };
        
        // 添加任务
        let mut tasks = self.tasks.write().await;
        tasks.insert(task_id.clone(), task);
        
        Ok(task_id)
    }
    
    /// 获取上传任务
    pub async fn get_task(&self, task_id: &str) -> Result<Arc<dyn Uploader>> {
        let tasks = self.tasks.read().await;
        
        if let Some(task) = tasks.get(task_id) {
            Ok(task.uploader.clone())
        } else {
            Err(anyhow!("Upload task not found: {}", task_id))
        }
    }
    
    /// 更新任务状态
    pub async fn update_task_state(&self, task_id: &str, state: UploadTaskState) -> Result<()> {
        let mut tasks = self.tasks.write().await;
        
        if let Some(task) = tasks.get_mut(task_id) {
            task.state = state;
            task.last_activity = std::time::Instant::now();
            Ok(())
        } else {
            Err(anyhow!("Upload task not found: {}", task_id))
        }
    }
    
    /// 移除上传任务
    pub async fn remove_task(&self, task_id: &str) -> Result<()> {
        let mut tasks = self.tasks.write().await;
        
        if tasks.remove(task_id).is_some() {
            Ok(())
        } else {
            Err(anyhow!("Upload task not found: {}", task_id))
        }
    }
    
    /// 获取所有上传任务
    pub async fn get_all_tasks(&self) -> Result<Vec<String>> {
        let tasks = self.tasks.read().await;
        Ok(tasks.keys().cloned().collect())
    }
    
    /// 获取活跃上传任务数量
    pub async fn get_active_task_count(&self) -> Result<usize> {
        let tasks = self.tasks.read().await;
        Ok(tasks.values().filter(|task| task.state == UploadTaskState::Active).count())
    }
}
