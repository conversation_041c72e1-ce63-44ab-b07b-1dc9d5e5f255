<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAppStore } from '../../stores/app'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'close'])

const appStore = useAppStore()

// 应用版本
const appVersion = computed(() => appStore.appVersion)

// 引擎版本
const engineVersion = computed(() => appStore.engineInfo.version || '未知')

// 引擎特性
const engineFeatures = computed(() => appStore.engineInfo.enabledFeatures || [])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 关闭面板
const handleClose = () => {
  emit('close')
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="关于"
    width="400px"
    @close="handleClose"
  >
    <div class="about-content">
      <div class="app-logo">
        <el-icon :size="48"><Download /></el-icon>
      </div>

      <h2 class="app-name">{{ appStore.appName }}</h2>

      <div class="app-version">
        <p>版本: v{{ appVersion }}</p>
        <p>引擎版本: {{ engineVersion }}</p>
      </div>

      <div class="engine-features" v-if="engineFeatures.length > 0">
        <h4>支持的功能:</h4>
        <ul>
          <li v-for="(feature, index) in engineFeatures" :key="index">
            {{ feature }}
          </li>
        </ul>
      </div>

      <div class="copyright">
        <p>© 2025 Lumen 团队</p>
        <p>
          <a href="https://github.com/yourusername/lumen" target="_blank">
            GitHub
          </a>
        </p>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped>
.about-content {
  text-align: center;
  padding: 20px 0;
}

.app-logo {
  margin-bottom: 20px;
}

.app-name {
  font-size: 24px;
  margin-bottom: 15px;
}

.app-version {
  margin-bottom: 20px;
}

.app-version p {
  margin: 5px 0;
  color: #606266;
}

.engine-features {
  text-align: left;
  margin: 0 auto;
  max-width: 300px;
  margin-bottom: 20px;
}

.engine-features h4 {
  margin-bottom: 10px;
}

.engine-features ul {
  padding-left: 20px;
}

.engine-features li {
  margin-bottom: 5px;
}

.copyright {
  font-size: 12px;
  color: #909399;
}

.copyright a {
  color: #409EFF;
  text-decoration: none;
}

.copyright a:hover {
  text-decoration: underline;
}
</style>
