# Tonitru 下载器插件系统开发指南

## 1. 插件系统概述

### 1.1 设计目标

Tonitru 下载器的插件系统旨在提供一个灵活、可扩展的框架，允许第三方开发者为下载器添加新功能，而无需修改核心代码。插件系统的主要设计目标包括：

- **可扩展性**：允许轻松添加新功能
- **隔离性**：插件错误不应影响核心系统
- **版本兼容**：提供稳定的 API，确保插件在版本更新后仍能正常工作
- **热插拔**：支持在运行时加载和卸载插件
- **资源控制**：限制插件的资源使用
- **安全性**：防止恶意插件损害系统

### 1.2 插件类型

Tonitru 下载器支持以下类型的插件：

1. **协议插件**：实现新的下载协议
2. **处理器插件**：处理下载的文件（如解压、病毒扫描等）
3. **通知插件**：提供事件通知（如邮件、消息推送等）
4. **界面插件**：扩展用户界面
5. **调度插件**：自定义下载调度策略
6. **存储插件**：提供额外的存储后端
7. **集成插件**：与第三方服务集成

### 1.3 技术架构

插件系统基于以下技术实现：

- **动态库加载**：使用 `libloading` 加载动态库
- **插件注册表**：管理已安装的插件
- **事件系统**：通过事件触发插件功能
- **API 抽象**：提供稳定的插件 API
- **沙箱执行**：限制插件的系统访问权限

## 2. 插件 API

### 2.1 核心接口

所有插件必须实现 `Plugin` 特性（trait）：

```rust
#[plugin_trait]
pub trait Plugin: Send + Sync + 'static {
    /// 获取插件信息
    fn info(&self) -> PluginInfo;
    
    /// 初始化插件
    fn init(&mut self, context: &PluginContext) -> Result<(), PluginError>;
    
    /// 启动插件
    fn start(&mut self) -> Result<(), PluginError>;
    
    /// 停止插件
    fn stop(&mut self) -> Result<(), PluginError>;
    
    /// 卸载插件
    fn unload(&mut self) -> Result<(), PluginError>;
    
    /// 处理事件
    fn handle_event(&mut self, event: &Event) -> Result<(), PluginError>;
    
    /// 获取插件配置架构
    fn config_schema(&self) -> Option<ConfigSchema> {
        None
    }
    
    /// 更新插件配置
    fn update_config(&mut self, config: &Config) -> Result<(), PluginError> {
        Ok(())
    }
}
```

### 2.2 插件信息

每个插件必须提供基本信息：

```rust
pub struct PluginInfo {
    /// 插件 ID，格式为 "作者.插件名"
    pub id: String,
    /// 插件名称
    pub name: String,
    /// 插件版本
    pub version: Version,
    /// 插件作者
    pub author: String,
    /// 插件描述
    pub description: String,
    /// 插件类型
    pub plugin_type: PluginType,
    /// 插件兼容的 API 版本
    pub api_version: Version,
    /// 插件依赖
    pub dependencies: Vec<PluginDependency>,
    /// 插件许可证
    pub license: String,
    /// 插件主页
    pub homepage: Option<String>,
    /// 插件仓库
    pub repository: Option<String>,
}
```

### 2.3 插件上下文

插件上下文提供插件访问核心系统的接口：

```rust
pub struct PluginContext {
    /// 下载管理器
    pub download_manager: Arc<dyn DownloadManager>,
    /// 配置管理器
    pub config_manager: Arc<dyn ConfigManager>,
    /// 事件管理器
    pub event_manager: Arc<dyn EventManager>,
    /// 存储管理器
    pub storage_manager: Arc<dyn StorageManager>,
    /// 日志记录器
    pub logger: Logger,
    /// 插件 API
    pub plugin_api: Arc<dyn PluginApi>,
}
```

### 2.4 事件系统

插件可以订阅和发布事件：

```rust
pub enum Event {
    /// 下载事件
    Download(DownloadEvent),
    /// 任务事件
    Task(TaskEvent),
    /// 系统事件
    System(SystemEvent),
    /// 插件事件
    Plugin(PluginEvent),
    /// 自定义事件
    Custom(String, Value),
}

pub enum DownloadEvent {
    /// 下载开始
    Started { task_id: String },
    /// 下载进度更新
    ProgressUpdated { task_id: String, progress: f64, speed: u64 },
    /// 下载暂停
    Paused { task_id: String },
    /// 下载恢复
    Resumed { task_id: String },
    /// 下载完成
    Completed { task_id: String },
    /// 下载失败
    Failed { task_id: String, error: String },
}
```

## 3. 插件开发流程

### 3.1 创建插件项目

#### 3.1.1 项目结构

```
my_plugin/
├── Cargo.toml
├── src/
│   ├── lib.rs
│   ├── plugin.rs
│   └── config.rs
└── README.md
```

#### 3.1.2 配置 Cargo.toml

```toml
[package]
name = "tonitru-my-plugin"
version = "0.1.0"
authors = ["Your Name <<EMAIL>>"]
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
tonitru-plugin-api = "0.1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"
log = "0.4"
```

### 3.2 实现插件接口

#### 3.2.1 基本插件结构

```rust
// src/lib.rs
use tonitru_plugin_api::prelude::*;

mod plugin;
mod config;

// 插件入口点
#[no_mangle]
pub extern "C" fn create_plugin() -> *mut dyn Plugin {
    let plugin = plugin::MyPlugin::new();
    Box::into_raw(Box::new(plugin))
}

// src/plugin.rs
use tonitru_plugin_api::prelude::*;
use crate::config::PluginConfig;

pub struct MyPlugin {
    info: PluginInfo,
    config: PluginConfig,
    context: Option<PluginContext>,
}

impl MyPlugin {
    pub fn new() -> Self {
        Self {
            info: PluginInfo {
                id: "yourname.myplugin".to_string(),
                name: "My Plugin".to_string(),
                version: Version::new(0, 1, 0),
                author: "Your Name".to_string(),
                description: "My awesome plugin".to_string(),
                plugin_type: PluginType::Processor,
                api_version: Version::new(0, 1, 0),
                dependencies: vec![],
                license: "MIT".to_string(),
                homepage: Some("https://github.com/yourname/tonitru-my-plugin".to_string()),
                repository: Some("https://github.com/yourname/tonitru-my-plugin".to_string()),
            },
            config: PluginConfig::default(),
            context: None,
        }
    }
}

impl Plugin for MyPlugin {
    fn info(&self) -> PluginInfo {
        self.info.clone()
    }
    
    fn init(&mut self, context: &PluginContext) -> Result<(), PluginError> {
        self.context = Some(context.clone());
        Ok(())
    }
    
    fn start(&mut self) -> Result<(), PluginError> {
        let context = self.context.as_ref().ok_or(PluginError::NotInitialized)?;
        context.logger.info("My plugin started");
        Ok(())
    }
    
    fn stop(&mut self) -> Result<(), PluginError> {
        let context = self.context.as_ref().ok_or(PluginError::NotInitialized)?;
        context.logger.info("My plugin stopped");
        Ok(())
    }
    
    fn unload(&mut self) -> Result<(), PluginError> {
        Ok(())
    }
    
    fn handle_event(&mut self, event: &Event) -> Result<(), PluginError> {
        match event {
            Event::Download(DownloadEvent::Completed { task_id }) => {
                let context = self.context.as_ref().ok_or(PluginError::NotInitialized)?;
                context.logger.info(&format!("Download completed: {}", task_id));
                // 处理下载完成事件
            }
            _ => {}
        }
        Ok(())
    }
    
    fn config_schema(&self) -> Option<ConfigSchema> {
        Some(ConfigSchema {
            properties: vec![
                ConfigProperty {
                    name: "enable_feature".to_string(),
                    property_type: ConfigPropertyType::Boolean,
                    default_value: Some(json!(false)),
                    description: Some("Enable special feature".to_string()),
                    required: false,
                },
                ConfigProperty {
                    name: "process_delay".to_string(),
                    property_type: ConfigPropertyType::Integer,
                    default_value: Some(json!(0)),
                    description: Some("Processing delay in seconds".to_string()),
                    required: false,
                },
            ],
        })
    }
    
    fn update_config(&mut self, config: &Config) -> Result<(), PluginError> {
        self.config = serde_json::from_value(config.clone())?;
        Ok(())
    }
}

// src/config.rs
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginConfig {
    #[serde(default = "default_enable_feature")]
    pub enable_feature: bool,
    
    #[serde(default = "default_process_delay")]
    pub process_delay: u32,
}

fn default_enable_feature() -> bool {
    false
}

fn default_process_delay() -> u32 {
    0
}

impl Default for PluginConfig {
    fn default() -> Self {
        Self {
            enable_feature: default_enable_feature(),
            process_delay: default_process_delay(),
        }
    }
}
```

### 3.3 构建和测试插件

#### 3.3.1 构建插件

```bash
cargo build --release
```

构建后的插件位于 `target/release/libtonitru_my_plugin.so`（Linux）、`target/release/libtonitru_my_plugin.dylib`（macOS）或 `target/release/tonitru_my_plugin.dll`（Windows）。

#### 3.3.2 测试插件

```rust
// tests/integration_test.rs
use tonitru_plugin_api::prelude::*;
use tonitru_plugin_api::testing::*;

#[test]
fn test_plugin() {
    // 创建测试上下文
    let context = TestPluginContext::new();
    
    // 加载插件
    let plugin_path = "../target/release/libtonitru_my_plugin.so";
    let mut plugin_loader = PluginLoader::new();
    let plugin = plugin_loader.load(plugin_path).unwrap();
    
    // 初始化插件
    plugin.init(&context).unwrap();
    
    // 启动插件
    plugin.start().unwrap();
    
    // 测试事件处理
    let event = Event::Download(DownloadEvent::Completed {
        task_id: "test-task".to_string(),
    });
    plugin.handle_event(&event).unwrap();
    
    // 停止插件
    plugin.stop().unwrap();
    
    // 卸载插件
    plugin.unload().unwrap();
}
```

## 4. 插件类型详解

### 4.1 协议插件

协议插件实现新的下载协议，需要实现 `ProtocolPlugin` 特性：

```rust
#[plugin_trait]
pub trait ProtocolPlugin: Plugin {
    /// 获取协议名称
    fn protocol_name(&self) -> &str;
    
    /// 检查 URL 是否由此协议处理
    fn can_handle_url(&self, url: &str) -> bool;
    
    /// 创建下载器
    fn create_downloader(&self, task: &DownloadTask) -> Result<Box<dyn Downloader>, PluginError>;
}

#[plugin_trait]
pub trait Downloader: Send + Sync + 'static {
    /// 开始下载
    fn start(&mut self) -> Result<(), DownloadError>;
    
    /// 暂停下载
    fn pause(&mut self) -> Result<(), DownloadError>;
    
    /// 恢复下载
    fn resume(&mut self) -> Result<(), DownloadError>;
    
    /// 取消下载
    fn cancel(&mut self) -> Result<(), DownloadError>;
    
    /// 获取下载进度
    fn progress(&self) -> Result<DownloadProgress, DownloadError>;
    
    /// 设置速度限制
    fn set_speed_limit(&mut self, limit: Option<u64>) -> Result<(), DownloadError>;
}
```

### 4.2 处理器插件

处理器插件处理下载的文件，需要实现 `ProcessorPlugin` 特性：

```rust
#[plugin_trait]
pub trait ProcessorPlugin: Plugin {
    /// 获取处理器名称
    fn processor_name(&self) -> &str;
    
    /// 检查文件是否由此处理器处理
    fn can_process_file(&self, file_info: &FileInfo) -> bool;
    
    /// 处理文件
    fn process_file(&self, file_path: &Path) -> Result<ProcessResult, PluginError>;
}

pub struct ProcessResult {
    /// 处理是否成功
    pub success: bool,
    /// 处理结果信息
    pub message: Option<String>,
    /// 处理后的文件路径（如果有变化）
    pub output_path: Option<PathBuf>,
    /// 处理后的元数据
    pub metadata: HashMap<String, Value>,
}
```

### 4.3 通知插件

通知插件提供事件通知，需要实现 `NotificationPlugin` 特性：

```rust
#[plugin_trait]
pub trait NotificationPlugin: Plugin {
    /// 获取通知器名称
    fn notifier_name(&self) -> &str;
    
    /// 发送通知
    fn send_notification(&self, notification: &Notification) -> Result<(), PluginError>;
    
    /// 获取支持的通知类型
    fn supported_notification_types(&self) -> Vec<NotificationType>;
}

pub struct Notification {
    /// 通知类型
    pub notification_type: NotificationType,
    /// 通知标题
    pub title: String,
    /// 通知内容
    pub content: String,
    /// 通知级别
    pub level: NotificationLevel,
    /// 相关任务 ID
    pub task_id: Option<String>,
    /// 附加数据
    pub data: HashMap<String, Value>,
}
```

### 4.4 界面插件

界面插件扩展用户界面，需要实现 `UiPlugin` 特性：

```rust
#[plugin_trait]
pub trait UiPlugin: Plugin {
    /// 获取 UI 组件名称
    fn component_name(&self) -> &str;
    
    /// 获取 UI 组件类型
    fn component_type(&self) -> UiComponentType;
    
    /// 获取 UI 组件位置
    fn component_location(&self) -> UiComponentLocation;
    
    /// 获取 UI 组件内容
    fn get_component_content(&self) -> Result<UiComponentContent, PluginError>;
    
    /// 处理 UI 事件
    fn handle_ui_event(&mut self, event: &UiEvent) -> Result<UiEventResponse, PluginError>;
}

pub enum UiComponentType {
    /// HTML 组件
    Html,
    /// Vue 组件
    Vue,
    /// React 组件
    React,
    /// 自定义组件
    Custom(String),
}

pub enum UiComponentLocation {
    /// 侧边栏
    Sidebar,
    /// 任务详情页
    TaskDetail,
    /// 设置页面
    Settings,
    /// 仪表盘
    Dashboard,
    /// 自定义位置
    Custom(String),
}
```

## 5. 插件管理

### 5.1 插件安装

插件可以通过以下方式安装：

1. **手动安装**：将插件文件复制到插件目录
2. **插件市场**：通过内置的插件市场安装
3. **命令行安装**：使用 CLI 工具安装

插件目录结构：

```
plugins/
├── protocol/
│   ├── my-protocol-plugin.dll
│   └── ...
├── processor/
│   ├── my-processor-plugin.dll
│   └── ...
├── notification/
│   ├── my-notification-plugin.dll
│   └── ...
└── ui/
    ├── my-ui-plugin.dll
    └── ...
```

### 5.2 插件配置

插件配置存储在配置文件中：

```json
{
  "plugins": {
    "enabled": ["yourname.myplugin", "otherauthor.otherplugin"],
    "disabled": ["someauthor.someplugin"],
    "config": {
      "yourname.myplugin": {
        "enable_feature": true,
        "process_delay": 5
      },
      "otherauthor.otherplugin": {
        "option1": "value1",
        "option2": 42
      }
    }
  }
}
```

### 5.3 插件生命周期

插件的生命周期包括以下阶段：

1. **发现**：系统扫描插件目录，发现可用插件
2. **加载**：加载插件动态库
3. **初始化**：调用插件的 `init` 方法
4. **启动**：调用插件的 `start` 方法
5. **运行**：插件正常运行，处理事件
6. **停止**：调用插件的 `stop` 方法
7. **卸载**：调用插件的 `unload` 方法，卸载动态库

## 6. 最佳实践

### 6.1 插件设计原则

- **单一职责**：每个插件应专注于一个功能
- **最小权限**：只请求必要的权限
- **优雅降级**：在出现错误时能够优雅处理
- **资源管理**：合理使用资源，避免泄漏
- **配置验证**：验证用户输入的配置
- **错误处理**：提供有意义的错误信息

### 6.2 性能优化

- **异步处理**：使用异步处理避免阻塞主线程
- **资源池化**：复用连接和其他资源
- **批处理**：合并小操作减少开销
- **缓存**：缓存频繁访问的数据
- **延迟加载**：按需加载资源

### 6.3 安全注意事项

- **输入验证**：验证所有外部输入
- **安全通信**：使用加密通信
- **敏感数据保护**：加密存储敏感信息
- **权限检查**：检查操作权限
- **资源限制**：限制资源使用

## 7. 示例插件

### 7.1 通知邮件插件

```rust
// email_notifier.rs
use tonitru_plugin_api::prelude::*;
use lettre::{Message, SmtpTransport, Transport};
use lettre::transport::smtp::authentication::Credentials;

pub struct EmailNotifierPlugin {
    info: PluginInfo,
    config: EmailConfig,
    context: Option<PluginContext>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailConfig {
    pub smtp_server: String,
    pub smtp_port: u16,
    pub username: String,
    pub password: String,
    pub from_address: String,
    pub to_address: String,
    pub use_tls: bool,
}

impl Default for EmailConfig {
    fn default() -> Self {
        Self {
            smtp_server: "smtp.example.com".to_string(),
            smtp_port: 587,
            username: "".to_string(),
            password: "".to_string(),
            from_address: "<EMAIL>".to_string(),
            to_address: "<EMAIL>".to_string(),
            use_tls: true,
        }
    }
}

impl Plugin for EmailNotifierPlugin {
    // 实现 Plugin 特性的方法...
}

impl NotificationPlugin for EmailNotifierPlugin {
    fn notifier_name(&self) -> &str {
        "email"
    }
    
    fn send_notification(&self, notification: &Notification) -> Result<(), PluginError> {
        let config = &self.config;
        
        // 创建邮件
        let email = Message::builder()
            .from(config.from_address.parse()?)
            .to(config.to_address.parse()?)
            .subject(&notification.title)
            .body(notification.content.clone())?;
        
        // 创建 SMTP 传输
        let creds = Credentials::new(config.username.clone(), config.password.clone());
        
        let mailer = if config.use_tls {
            SmtpTransport::relay(&config.smtp_server)?
                .credentials(creds)
                .build()
        } else {
            SmtpTransport::builder_dangerous(&config.smtp_server)
                .credentials(creds)
                .port(config.smtp_port)
                .build()
        };
        
        // 发送邮件
        mailer.send(&email)?;
        
        Ok(())
    }
    
    fn supported_notification_types(&self) -> Vec<NotificationType> {
        vec![
            NotificationType::DownloadCompleted,
            NotificationType::DownloadFailed,
            NotificationType::SystemAlert,
        ]
    }
}
```

### 7.2 解压处理器插件

```rust
// unzip_processor.rs
use tonitru_plugin_api::prelude::*;
use std::fs::File;
use std::path::{Path, PathBuf};
use zip::ZipArchive;

pub struct UnzipProcessorPlugin {
    info: PluginInfo,
    config: UnzipConfig,
    context: Option<PluginContext>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnzipConfig {
    pub auto_extract: bool,
    pub delete_archive: bool,
    pub extract_path: Option<String>,
}

impl Default for UnzipConfig {
    fn default() -> Self {
        Self {
            auto_extract: true,
            delete_archive: false,
            extract_path: None,
        }
    }
}

impl Plugin for UnzipProcessorPlugin {
    // 实现 Plugin 特性的方法...
}

impl ProcessorPlugin for UnzipProcessorPlugin {
    fn processor_name(&self) -> &str {
        "unzip"
    }
    
    fn can_process_file(&self, file_info: &FileInfo) -> bool {
        if !self.config.auto_extract {
            return false;
        }
        
        match file_info.mime_type.as_deref() {
            Some("application/zip") => true,
            _ => {
                let path = Path::new(&file_info.path);
                path.extension().map_or(false, |ext| ext == "zip")
            }
        }
    }
    
    fn process_file(&self, file_path: &Path) -> Result<ProcessResult, PluginError> {
        let context = self.context.as_ref().ok_or(PluginError::NotInitialized)?;
        
        // 打开 ZIP 文件
        let file = File::open(file_path)?;
        let mut archive = ZipArchive::new(file)?;
        
        // 确定解压路径
        let extract_path = match &self.config.extract_path {
            Some(path) => PathBuf::from(path),
            None => {
                let parent = file_path.parent().unwrap_or(Path::new(""));
                let stem = file_path.file_stem().unwrap_or_default();
                parent.join(stem)
            }
        };
        
        // 创建解压目录
        std::fs::create_dir_all(&extract_path)?;
        
        // 解压文件
        for i in 0..archive.len() {
            let mut file = archive.by_index(i)?;
            let outpath = extract_path.join(file.name());
            
            if file.name().ends_with('/') {
                std::fs::create_dir_all(&outpath)?;
            } else {
                if let Some(p) = outpath.parent() {
                    if !p.exists() {
                        std::fs::create_dir_all(p)?;
                    }
                }
                let mut outfile = File::create(&outpath)?;
                std::io::copy(&mut file, &mut outfile)?;
            }
        }
        
        // 如果配置了删除归档，则删除原文件
        if self.config.delete_archive {
            std::fs::remove_file(file_path)?;
        }
        
        // 返回处理结果
        Ok(ProcessResult {
            success: true,
            message: Some(format!("Successfully extracted {} files to {}", archive.len(), extract_path.display())),
            output_path: Some(extract_path),
            metadata: HashMap::new(),
        })
    }
}
```

## 8. 故障排除

### 8.1 常见问题

#### 8.1.1 插件加载失败

**问题**：插件无法加载

**可能原因**：
- 插件与 API 版本不兼容
- 插件依赖缺失
- 插件文件损坏

**解决方案**：
1. 检查插件 API 版本兼容性
2. 安装缺失的依赖
3. 重新下载或编译插件

#### 8.1.2 插件崩溃

**问题**：插件在运行时崩溃

**可能原因**：
- 内存访问错误
- 未处理的异常
- 资源耗尽

**解决方案**：
1. 使用 `catch_unwind` 捕获 panic
2. 添加更多错误处理
3. 限制资源使用

### 8.2 调试技巧

#### 8.2.1 日志记录

```rust
fn handle_event(&mut self, event: &Event) -> Result<(), PluginError> {
    let context = self.context.as_ref().ok_or(PluginError::NotInitialized)?;
    
    // 记录调试信息
    context.logger.debug(&format!("Handling event: {:?}", event));
    
    // 处理事件...
    
    // 记录结果
    context.logger.info("Event handled successfully");
    
    Ok(())
}
```

#### 8.2.2 调试构建

```bash
# 构建调试版本
cargo build

# 启用调试日志
RUST_LOG=debug ./tonitru_downloader
```

#### 8.2.3 使用调试器

```bash
# 使用 GDB 调试
gdb --args ./tonitru_downloader --plugin-path ./my_plugin.so

# 在 Windows 上使用 Visual Studio 调试器
devenv /debugexe tonitru_downloader.exe --plugin-path ./my_plugin.dll
```

## 9. 参考资料

### 9.1 API 文档

- [Tonitru 插件 API 文档](https://docs.tonitru.dev/plugin-api/)
- [插件系统架构](https://docs.tonitru.dev/architecture/plugin-system/)
- [插件开发教程](https://docs.tonitru.dev/tutorials/plugin-development/)

### 9.2 示例插件

- [示例插件仓库](https://github.com/tonitru/plugin-examples)
- [插件模板](https://github.com/tonitru/plugin-template)

### 9.3 相关资源

- [Rust 插件系统设计](https://www.reddit.com/r/rust/comments/b0i625/plugin_architectures_in_rust/)
- [动态加载库教程](https://michael-f-bryan.github.io/rust-ffi-guide/dynamic_loading.html)
- [FFI 最佳实践](https://jake-shadle.github.io/rust-ffi-guide/)