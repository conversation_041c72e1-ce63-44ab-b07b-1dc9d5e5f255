//! CLI 运行模块
//!
//! 提供CLI的运行逻辑，包括参数解析、日志设置、后端初始化和命令处理。

use std::time::Instant;
use std::collections::HashMap;
use anyhow::{Result, Context};
use clap::Parser;
use log::{debug, info, trace, warn};
use colored::Color;

use crate::cli::args::{Cli, Commands};
use crate::cli::backend::backend_factory::BackendFactory;
use crate::cli::core::initialize_core_components;
use crate::cli::logging::{setup_logging, get_log_level_name};
use crate::cli::utils::formatter::OutputFormat;
use crate::cli::utils::debug::{DebugTimer, print_debug_object, print_debug_info, DebugLevel, print_colored_debug, print_resource_usage, print_debug_json};
use crate::cli::utils::profiler::{Profiler, ProfileLevel, print_performance_report, PerformanceContext, set_profiling_enabled, is_profiling_enabled};
use crate::cli::utils::diagnostics::{print_diagnostics, print_compatibility_check};

/// 运行 CLI
/// 
/// 此函数是CLI的主入口点，负责解析命令行参数、设置日志级别、初始化核心组件、
/// 创建后端并根据子命令调用相应的处理函数。
pub async fn run() -> Result<()> {
    // 解析命令行参数
    let start_parse = Instant::now();
    let cli = Cli::parse();
    let parse_time = start_parse.elapsed();
    
    // 设置性能分析开关
    set_profiling_enabled(cli.debug && cli.profile);
    
    // 创建总体性能分析器
    let mut total_profiler = Profiler::new("CLI执行总体性能", ProfileLevel::Basic);
    let mut total_timer = DebugTimer::new("CLI执行总计时");
    
    // 如果启用了性能分析，打印提示信息
    if cli.debug && cli.profile {
        debug!("性能分析已启用");
    }
    total_profiler.checkpoint("命令行参数解析");
    total_timer.checkpoint("命令行参数解析");
    
    // 设置日志级别
    let log_level = setup_logging(cli.verbose, cli.quiet, cli.debug);
    total_profiler.checkpoint("日志设置");
    total_timer.checkpoint("日志设置");
    
    // 打印启动信息
    info!("Tonitru 下载器 CLI 启动");
    debug!("日志级别: {}", get_log_level_name(log_level));
    debug!("命令行参数解析耗时: {:?}", parse_time);
    
    // 打印详细的调试信息
    if cli.debug {
        // 打印系统兼容性检查
        print_compatibility_check();
        
        // 打印诊断信息（不包含环境变量）
        print_diagnostics(false);
        
        print_debug_object("CLI 参数详情", &cli, DebugLevel::Full);
        print_colored_debug("调试模式已启用", Color::BrightYellow);
        
        if let Some(config_path) = &cli.config {
            debug!("使用配置文件: {}", config_path.display());
        } else {
            debug!("使用默认配置文件");
        }
        
        debug!("API 服务器: {}", cli.api);
        debug!("运行模式: {:?}", cli.mode);
        
        if let Some(format) = &cli.format {
            debug!("输出格式: {:?}", format);
        }
    }
    total_profiler.checkpoint("调试信息输出");
    total_timer.checkpoint("调试信息输出");
    
    // 创建核心组件计时器
    let mut core_timer = DebugTimer::new("核心组件初始化");
    let core_context = PerformanceContext::new("核心组件初始化");
    
    // 创建后端
    trace!("开始初始化核心组件...");
    let core_components = initialize_core_components(&cli.config)
        .await
        .context("初始化核心组件失败")?;
    let (download_manager, config_manager, storage) = core_components;
    
    core_timer.checkpoint("核心组件创建完成");
    total_profiler.checkpoint("核心组件创建");
    total_timer.checkpoint("核心组件创建");
    
    // 如果是调试模式，打印配置信息
    if cli.debug {
        let settings = config_manager.get_settings().await;
        print_debug_object("当前配置", &settings, DebugLevel::Verbose);
        
        // 打印存储信息
        print_debug_object("存储类型", &storage.storage_type(), DebugLevel::Basic);
        print_debug_object("存储根路径", &storage.root_path(), DebugLevel::Basic);
    }
    
    trace!("开始创建后端...");
    let backend = BackendFactory::create_backend(download_manager, config_manager, storage);
    debug!("后端创建成功");
    
    // 如果是调试模式，打印后端信息
    if cli.debug {
        print_debug_info("后端类型: Direct", DebugLevel::Basic);
    }
    
    core_timer.stop();
    core_context.complete();
    total_profiler.checkpoint("后端创建");
    total_timer.checkpoint("后端创建");
    
    // 命令执行计时器
    let mut cmd_timer = DebugTimer::new("命令执行");
    let cmd_context = PerformanceContext::new("命令执行");
    
    // 如果是调试模式且启用了性能分析，打印命令执行前的系统资源使用情况
    if cli.debug && cli.profile {
        debug!("命令执行前系统资源使用情况:");
        print_resource_usage();
    }
    
    // 处理命令
    trace!("开始处理命令: {:?}", cli.command);
    let result = match &cli.command {
        Commands::Download(cmd) => {
            print_debug_info("执行下载命令", DebugLevel::Basic);
            let cmd_result = crate::cli::commands::download::handle_command(&backend, cmd, cli.format.unwrap_or(OutputFormat::Text)).await;
            total_profiler.checkpoint("下载命令执行");
            cmd_result
        },
        Commands::Speed(cmd) => {
            print_debug_info("执行速度限制命令", DebugLevel::Basic);
            let cmd_result = crate::cli::commands::speed::handle_command(cmd, &backend, cli.format).await;
            total_profiler.checkpoint("速度限制命令执行");
            cmd_result
        },
        Commands::Config(cmd) => {
            print_debug_info("执行配置命令", DebugLevel::Basic);
            let cmd_result = crate::cli::commands::config::handle_command(cmd, &backend, cli.format).await;
            total_profiler.checkpoint("配置命令执行");
            cmd_result
        },
        Commands::Status(cmd) => {
            print_debug_info("执行状态命令", DebugLevel::Basic);
            let cmd_result = crate::cli::commands::status::handle_command(cmd, &backend, cli.format).await;
            total_profiler.checkpoint("状态命令执行");
            cmd_result
        },
    };
    
    // 检查命令执行结果
    if let Err(ref e) = result {
        warn!("命令执行失败: {}", e);
        if cli.debug {
            print_colored_debug(&format!("错误详情: {:?}", e), Color::BrightRed);
        }
    } else {
        debug!("命令执行成功");
    }
    
    // 如果是调试模式且启用了性能分析，打印命令执行后的系统资源使用情况
    if cli.debug && cli.profile {
        debug!("命令执行后系统资源使用情况:");
        print_resource_usage();
        
        // 打印命令执行的详细时间信息
        let cmd_elapsed = cmd_timer.stop();
        print_colored_debug(&format!("命令执行耗时: {:?}", cmd_elapsed), Color::BrightCyan);
    } else {
        cmd_timer.stop();
    }
    
    cmd_context.complete();
    total_profiler.checkpoint("命令结果处理");
    total_timer.checkpoint("命令结果处理");
    
    // 总计时结束
    let total_time = total_timer.stop();
    // 只在性能分析模式下输出总耗时
    if cli.debug && cli.profile {
        debug!("CLI 执行总耗时: {:?}", total_time);
    }
    
    // 如果是调试模式且启用了性能分析，打印性能报告
    if cli.debug && cli.profile {
        // 创建性能报告数据
        let mut performance_report = HashMap::new();
        
        // 先获取所有需要的检查点数据，因为stop()会消耗掉total_profiler的所有权
        
        // 添加关键操作的耗时百分比
        let key_operations = [
            "命令行参数解析",
            "日志设置",
            "调试信息输出",
            "核心组件创建",
            "后端创建",
            "命令结果处理"
        ];
        
        // 存储所有检查点数据，以便在调用stop()后仍能使用
        let mut checkpoint_durations = HashMap::new();
        
        for &operation in &key_operations {
            if let Some(time) = total_profiler.checkpoint_elapsed(operation) {
                checkpoint_durations.insert(operation.to_string(), time);
            }
        }
        
        // 添加命令执行相关的检查点
        let cmd_checkpoints = [
            "命令执行",
            "下载命令执行",
            "速度限制命令执行",
            "配置命令执行",
            "状态命令执行"
        ];
        
        for &cmd in &cmd_checkpoints {
            if let Some(duration) = total_profiler.checkpoint_elapsed(cmd) {
                checkpoint_durations.insert(cmd.to_string(), duration);
            }
        }
        
        // 获取性能分析器的总耗时（这会消耗掉total_profiler的所有权）
        let elapsed = total_profiler.stop();
        debug!("CLI总耗时: {:?}", elapsed);
        
        // 添加总执行时间到性能报告
        performance_report.insert("总执行时间".to_string(), elapsed);
        
        // 将之前保存的检查点数据添加到性能报告
        for (key, value) in &checkpoint_durations {
            performance_report.insert(key.clone(), *value);
        }
        
        // 打印完整的性能报告
        print_performance_report("CLI性能分析报告", &performance_report);
        
        // 如果是详细调试模式，还可以打印更多信息
        if cli.verbose && cli.debug {
            debug!("详细性能检查点信息:");
            // 使用已保存的检查点数据
            for (operation, duration) in &checkpoint_durations {
                let percentage = duration.as_secs_f64() / elapsed.as_secs_f64() * 100.0;
                debug!("  {} - {:?} ({:.2}%)", operation, duration, percentage);
            }
            
            // 打印内存使用情况
            #[cfg(target_os = "windows")]
            {
                use std::process::Command;
                if let Ok(output) = Command::new("powershell")
                    .args(["-Command", "Get-Process -Id $PID | Select-Object WorkingSet, VirtualMemorySize | ConvertTo-Json"])
                    .output() 
                {
                    if let Ok(json_str) = String::from_utf8(output.stdout) {
                        debug!("内存使用情况: {}", json_str.trim());
                    }
                }
            }
            
            // 打印系统资源使用情况
            debug!("系统资源使用情况:");
            let diagnostic_report = crate::cli::utils::diagnostics::generate_diagnostic_report(false);
            print_debug_json("诊断报告", &diagnostic_report, DebugLevel::Full);
            
            // 打印命令执行的详细信息
            debug!("命令执行详细信息:");
            print_debug_object("执行的命令", &cli.command, DebugLevel::Full);
        }
    }
    
    // 返回结果
    result
}