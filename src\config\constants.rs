// Application constants

// Default values
pub const DEFAULT_PORT: u16 = 8080;
pub const DEFAULT_HOST: &str = "127.0.0.1";
pub const DEFAULT_DOWNLOAD_PATH: &str = "./downloads";
pub const DEFAULT_CONCURRENT_DOWNLOADS: i64 = 5;
pub const DEFAULT_CONNECTIONS_PER_DOWNLOAD: i64 = 8;
pub const DEFAULT_CHUNK_SIZE: i64 = 1024 * 1024; // 1MB
pub const DEFAULT_BUFFER_SIZE: i64 = 8192; // 8KB
pub const DEFAULT_SPEED_LIMIT: Option<u64> = None; // No limit by default

// Timeouts (in seconds)
pub const CONNECTION_TIMEOUT: u64 = 30;
pub const READ_TIMEOUT: u64 = 60;
pub const WRITE_TIMEOUT: u64 = 60;

// Retry settings
pub const MAX_RETRIES: usize = 3;
pub const RETRY_DELAY_MS: u64 = 1000; // 1 second

// Database settings
pub const DATABASE_URL: &str = "sqlite:tonitru.db";

// API endpoints
pub const API_PREFIX: &str = "/api/v1";

// Protocol identifiers
pub const PROTOCOL_HTTP: &str = "http";
pub const PROTOCOL_HTTPS: &str = "https";
pub const PROTOCOL_BT: &str = "bt";
pub const PROTOCOL_MAGNET: &str = "magnet";
pub const PROTOCOL_P2P: &str = "p2p";
pub const PROTOCOL_R2: &str = "r2";

// File extensions
pub const TORRENT_EXTENSION: &str = ".torrent";

// Mirror settings
pub const MAX_MIRRORS: i64 = 5;
pub const MIRROR_HEALTH_CHECK_INTERVAL_SECS: u64 = 300; // 5 minutes

// Proxy settings
pub const DEFAULT_PROXY_ENABLED: bool = false;
pub const DEFAULT_PROXY_URL: &str = "";
pub const DEFAULT_PROXY_TYPE: &str = "http";

// BitTorrent settings
pub const DEFAULT_BT_ENABLED: bool = true;
pub const DEFAULT_BT_PORT: u16 = 6881;
pub const DEFAULT_BT_MAX_PEERS: i64 = 50;
pub const DEFAULT_BT_MAX_CONNECTIONS: i64 = 30;
pub const DEFAULT_BT_CONNECTION_TIMEOUT: u64 = 30;
pub const DEFAULT_BT_REQUEST_TIMEOUT: u64 = 60;
pub const DEFAULT_PEER_CONNECT_TIMEOUT: u64 = 10;
pub const DEFAULT_TRACKER_INTERVAL: u64 = 1800;
pub const DEFAULT_PEER_HANDSHAKE_TIMEOUT_SECS: u64 = 10;

// BitTorrent block manager settings
pub const DEFAULT_BLOCK_SIZE: i64 = 16384; // 16KB
pub const DEFAULT_BLOCK_TIMEOUT: u64 = 30; // 30 seconds

// BitTorrent stats manager settings
pub const DEFAULT_STATS_UPDATE_INTERVAL: u64 = 1000; // 1 second

// Default BitTorrent trackers
pub const DEFAULT_BT_TRACKERS: [&str; 20] = [
    "udp://tracker.opentrackr.org:1337/announce",
    "udp://open.demonii.com:1337/announce",
    "udp://open.stealth.si:80/announce",
    "udp://exodus.desync.com:6969/announce",
    "udp://tracker.torrent.eu.org:451/announce",
    "udp://tracker.skyts.net:6969/announce",
    "udp://tracker.ololosh.space:6969/announce",
    "udp://explodie.org:6969/announce",
    "http://tracker.ipv6tracker.org:80/announce",
    "http://tracker.dmcomic.org:2710/announce",
    "http://tracker.bt-hash.com:80/announce",
    "http://t.jaekr.sh:6969/announce",
    "http://highteahop.top:6960/announce",
    "http://finbytes.org:80/announce.php",
    "http://bt1.xxxxbt.cc:6969/announce",
    "udp://tracker.tiny-vps.com:6969/announce",
    "udp://tracker.dump.cl:6969/announce",
    "udp://tracker.bittor.pw:1337/announce",
    "udp://tracker-udp.gbitt.info:80/announce",
    "udp://opentracker.io:6969/announce",
];
