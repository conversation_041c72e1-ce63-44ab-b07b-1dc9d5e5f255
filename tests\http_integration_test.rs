#![cfg(feature = "integration_tests")]

mod common;

use std::path::PathBuf;
use std::sync::Arc;
use anyhow::Result;
use tokio::fs;
use uuid::Uuid;

use tonitru_downloader::config::{<PERSON><PERSON><PERSON>, ConfigManager};
use tonitru_downloader::protocols::http::HttpDownloader;

use tonitru_downloader::download::resume::{ResumeManager, ResumeManagerImpl};
use tonitru_downloader::storage::storage_impl::LocalStorage;
use tonitru_downloader::download::bandwidth_scheduler::{BandwidthScheduler, BandwidthSchedulerImpl};

#[tokio::test]
async fn test_http_download_complete() -> Result<()> {
    // Start a test HTTP server
    let (server_url, shutdown_tx) = common::start_test_http_server().await?;

    // Create a temporary directory for the test
    let temp_dir = common::create_temp_dir().await?;
    let output_path = temp_dir.join("downloaded_file.txt");

    // Create a settings object
    let settings = common::create_test_settings();
    let config_manager = Arc::new(ConfigManager::with_settings(settings.clone()));

    // Create a task ID
    let task_id = Uuid::new_v4();

    // Create a resume manager
    let storage = Arc::new(LocalStorage::new(&settings.download.path));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage.clone()));

    // Create a bandwidth scheduler
    let bandwidth_scheduler = Arc::new(BandwidthSchedulerImpl::new());

    // Create an HttpDownloader
    let mut downloader = HttpDownloader::new(
        format!("{}/files/test_file.txt", server_url),
        output_path.to_string_lossy().to_string(),
        config_manager,
        task_id
    )
    .with_resume_manager(resume_manager.clone())
    .with_bandwidth_scheduler(bandwidth_scheduler.clone());

    // Initialize the downloader
    downloader.init().await?;

    // Start the download
    downloader.download().await?;

    // Verify that the file was downloaded correctly
    let downloaded_content = fs::read_to_string(&output_path).await?;
    let original_content = fs::read_to_string("tests/files/test_file.txt").await?;

    assert_eq!(downloaded_content, original_content);

    // Clean up
    common::cleanup_temp_dir(&temp_dir).await?;
    shutdown_tx.send(()).unwrap();

    Ok(())
}

#[tokio::test]
async fn test_http_download_pause_resume() -> Result<()> {
    // Start a test HTTP server
    let (server_url, shutdown_tx) = common::start_test_http_server().await?;

    // Create a temporary directory for the test
    let temp_dir = common::create_temp_dir().await?;
    let output_path = temp_dir.join("downloaded_file.txt");

    // Create a settings object
    let settings = common::create_test_settings();
    let config_manager = Arc::new(ConfigManager::with_settings(settings.clone()));

    // Create a task ID
    let task_id = Uuid::new_v4();

    // Create a resume manager
    let storage = Arc::new(LocalStorage::new(&settings.download.path));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage.clone()));

    // Create a bandwidth scheduler
    let bandwidth_scheduler = Arc::new(BandwidthSchedulerImpl::new());

    // Create an HttpDownloader
    let mut downloader = HttpDownloader::new(
        format!("{}/files/test_file.txt", server_url),
        output_path.to_string_lossy().to_string(),
        config_manager.clone(),
        task_id
    )
    .with_resume_manager(resume_manager.clone())
    .with_bandwidth_scheduler(bandwidth_scheduler.clone());

    // Initialize the downloader
    downloader.init().await?;

    // Pause the downloader before starting the download
    downloader.pause().await?;

    // This should return an error because the download was paused
    let download_result = downloader.download().await;
    assert!(download_result.is_err());
    assert!(download_result.unwrap_err().to_string().contains("Download is paused"));

    // Create a new downloader with the same task ID to simulate restarting the application
    let mut new_downloader = HttpDownloader::new(
        format!("{}/files/test_file.txt", server_url),
        output_path.to_string_lossy().to_string(),
        config_manager,
        task_id
    )
    .with_resume_manager(resume_manager)
    .with_bandwidth_scheduler(bandwidth_scheduler);

    // Initialize the new downloader
    new_downloader.init().await?;

    // Resume the download
    new_downloader.resume().await?;

    // Verify that the file was downloaded correctly
    let downloaded_content = fs::read_to_string(&output_path).await?;
    let original_content = fs::read_to_string("tests/files/test_file.txt").await?;

    assert_eq!(downloaded_content, original_content);

    // Clean up
    common::cleanup_temp_dir(&temp_dir).await?;
    shutdown_tx.send(()).unwrap();

    Ok(())
}

#[tokio::test]
async fn test_http_download_speed_limit() -> Result<()> {
    // Start a test HTTP server
    let (server_url, shutdown_tx) = common::start_test_http_server().await?;

    // Create a temporary directory for the test
    let temp_dir = common::create_temp_dir().await?;
    let output_path = temp_dir.join("downloaded_file.txt");

    // Create a settings object
    let settings = common::create_test_settings();
    let config_manager = Arc::new(ConfigManager::with_settings(settings.clone()));

    // Create a task ID
    let task_id = Uuid::new_v4();

    // Create a resume manager
    let storage = Arc::new(LocalStorage::new(&settings.download.path));
    let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage.clone()));

    // Create a bandwidth scheduler with a very low speed limit
    let bandwidth_scheduler = Arc::new(BandwidthSchedulerImpl::new());
    bandwidth_scheduler.set_global_download_limit(Some(1024)).await?; // 1 KB/s

    // Create an HttpDownloader
    let mut downloader = HttpDownloader::new(
        format!("{}/files/test_file.txt", server_url),
        output_path.to_string_lossy().to_string(),
        config_manager,
        task_id
    )
    .with_resume_manager(resume_manager)
    .with_bandwidth_scheduler(bandwidth_scheduler.clone());

    // Initialize the downloader
    downloader.init().await?;

    // Record the start time
    let start_time = std::time::Instant::now();

    // Start the download
    downloader.download().await?;

    // Record the end time
    let download_time = start_time.elapsed();

    // Verify that the file was downloaded correctly
    let downloaded_content = fs::read_to_string(&output_path).await?;
    let original_content = fs::read_to_string("tests/files/test_file.txt").await?;

    assert_eq!(downloaded_content, original_content);

    // Get the download speed
    let speed = downloader.speed().await?;

    // The speed should be close to the limit we set
    assert!(speed <= 1024 * 2); // Allow some margin for error

    // Clean up
    common::cleanup_temp_dir(&temp_dir).await?;
    shutdown_tx.send(()).unwrap();

    Ok(())
}

// Add more integration tests as needed
