use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// Download task status
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum TaskStatus {
    Pending,
    Initializing,
    Downloading,
    Paused,
    Completed,
    Failed,
    Cancelled,
    Error,
}

impl std::fmt::Display for TaskStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TaskStatus::Pending => write!(f, "Pending"),
            TaskStatus::Initializing => write!(f, "Initializing"),
            TaskStatus::Downloading => write!(f, "Downloading"),
            TaskStatus::Paused => write!(f, "Paused"),
            TaskStatus::Completed => write!(f, "Completed"),
            TaskStatus::Failed => write!(f, "Failed"),
            TaskStatus::Cancelled => write!(f, "Cancelled"),
            TaskStatus::Error => write!(f, "Error"),
        }
    }
}

/// Download task information
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TaskInfo {
    pub id: Uuid,
    pub url: String,
    pub output_path: String,
    pub status: TaskStatus,
    pub progress: f64,
    pub speed: u64,
    pub downloaded_size: u64,
    pub total_size: Option<u64>,
    pub uploaded_bytes: u64,  // 新增字段：已上传字节数
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub error_message: Option<String>,
}

/// Download statistics
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct DownloadStats {
    pub total_downloads: usize,
    pub active_downloads: usize,
    pub completed_downloads: usize,
    pub failed_downloads: usize,
    pub total_downloaded_bytes: u64,
    pub total_uploaded_bytes: u64,
}