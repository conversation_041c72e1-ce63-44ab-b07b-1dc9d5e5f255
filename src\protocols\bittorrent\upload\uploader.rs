use std::sync::Arc;
use anyhow::{Result, anyhow};
use async_trait::async_trait;
use bytes::Bytes;
use tokio::sync::Mutex;
use tracing::{debug, warn, info};

use crate::core::p2p::piece::PieceManager;
use crate::protocols::bittorrent::message::BitTorrentMessage;
use crate::protocols::bittorrent::torrent::TorrentInfo;

use super::{UploadError, UploadConfig, RequestQueue, UploadRequest, UploadStatistics};

/// 上传器接口
/// 负责处理上传请求和分片数据传输
#[async_trait]
pub trait Uploader: Send + Sync {
    /// 处理请求消息
    async fn handle_request(&self, index: u32, begin: u32, length: u32) -> Result<Option<BitTorrentMessage>>;
    
    /// 处理上传请求队列
    async fn process_uploads(&self) -> Result<Vec<BitTorrentMessage>>;
    
    /// 设置阻塞状态
    async fn set_choking(&self, choking: bool) -> Result<()>;
    
    /// 设置对方兴趣状态
    async fn set_peer_interested(&self, interested: bool) -> Result<()>;
    
    /// 添加允许快速请求的分片
    async fn add_allowed_fast_piece(&self, index: u32) -> Result<()>;
    
    /// 获取上传统计信息
    async fn get_statistics(&self) -> Result<Arc<dyn UploadStatistics>>;
    
    /// 设置上传速率限制
    async fn set_upload_rate_limit(&self, limit: Option<u64>) -> Result<()>;
    
    /// 获取上传速率限制
    async fn get_upload_rate_limit(&self) -> Result<Option<u64>>;
    
    /// 是否正在阻塞对方
    async fn is_choking(&self) -> Result<bool>;
    
    /// 对方是否对我方感兴趣
    async fn is_peer_interested(&self) -> Result<bool>;
}

/// 分片上传器
/// 实现上传器接口，处理分片上传请求
pub struct PieceUploader {
    /// 分片管理器
    piece_manager: Arc<Mutex<dyn PieceManager>>,
    
    /// 种子信息
    torrent_info: Arc<TorrentInfo>,
    
    /// 上传请求队列
    request_queue: Arc<Mutex<RequestQueue>>,
    
    /// 上传统计信息
    statistics: Arc<Mutex<UploadStatistics>>,
    
    /// 上传配置
    config: UploadConfig,
    
    /// 我方是否阻塞对方
    am_choking: Arc<Mutex<bool>>,
    
    /// 对方是否对我方感兴趣
    peer_interested: Arc<Mutex<bool>>,
    
    /// 我方允许对方快速请求的分片列表
    allowed_fast_pieces: Arc<Mutex<Vec<u32>>>,
    
    /// 是否支持Fast Extension
    supports_fast: bool,
}

impl PieceUploader {
    /// 创建新的分片上传器
    pub fn new(
        piece_manager: Arc<Mutex<dyn PieceManager>>,
        torrent_info: Arc<TorrentInfo>,
        config: UploadConfig,
        supports_fast: bool,
    ) -> Self {
        Self {
            piece_manager,
            torrent_info,
            request_queue: Arc::new(Mutex::new(RequestQueue::new(config.max_queue_length))),
            statistics: Arc::new(Mutex::new(UploadStatistics::new())),
            config,
            am_choking: Arc::new(Mutex::new(true)), // 默认阻塞
            peer_interested: Arc::new(Mutex::new(false)), // 默认不感兴趣
            allowed_fast_pieces: Arc::new(Mutex::new(Vec::new())),
            supports_fast,
        }
    }
}

#[async_trait]
impl Uploader for PieceUploader {
    async fn handle_request(&self, index: u32, begin: u32, length: u32) -> Result<Option<BitTorrentMessage>> {
        // 如果上传被禁用，拒绝所有请求
        if !self.config.enabled {
            return Ok(None);
        }
        
        // 如果我们阻塞了对方，并且这个分片不在允许快速请求的列表中，拒绝请求
        let am_choking = *self.am_choking.lock().await;
        let allowed_fast_pieces = self.allowed_fast_pieces.lock().await;
        
        if am_choking && !allowed_fast_pieces.contains(&index) {
            debug!("Rejecting request for piece {} because we are choking the peer", index);
            
            // 如果支持Fast Extension，发送拒绝消息
            if self.supports_fast {
                return Ok(Some(BitTorrentMessage::Reject(index, begin, length)));
            }
            
            return Ok(None);
        }
        
        // 将请求添加到上传队列
        let mut request_queue = self.request_queue.lock().await;
        let request = UploadRequest::new(index, begin, length);
        
        if let Err(e) = request_queue.add_request(request) {
            warn!("Failed to add request to queue: {}", e);
            
            // 如果支持Fast Extension，发送拒绝消息
            if self.supports_fast {
                return Ok(Some(BitTorrentMessage::Reject(index, begin, length)));
            }
            
            return Ok(None);
        }
        
        debug!("Added request for piece {} to upload queue", index);
        
        Ok(None)
    }
    
    async fn process_uploads(&self) -> Result<Vec<BitTorrentMessage>> {
        let mut messages = Vec::new();
        
        // 如果上传被禁用，返回空消息列表
        if !self.config.enabled {
            return Ok(messages);
        }
        
        // 如果我们阻塞了对方，并且没有允许快速请求的分片，返回空消息列表
        let am_choking = *self.am_choking.lock().await;
        let peer_interested = *self.peer_interested.lock().await;
        
        if am_choking && !peer_interested {
            return Ok(messages);
        }
        
        // 获取请求队列
        let mut request_queue = self.request_queue.lock().await;
        
        // 处理请求队列中的请求
        let max_requests = self.config.max_concurrent_uploads;
        let requests = request_queue.get_requests(max_requests);
        
        for request in requests {
            // 获取分片数据
            let piece_manager = self.piece_manager.lock().await;
            let piece_info_opt = piece_manager.get_piece_info(request.index).await?;
            
            // 如果没有找到分片信息，拒绝请求
            let piece_info = match piece_info_opt {
                Some(info) => info,
                None => {
                    debug!("Piece info not found for piece {}, rejecting request", request.index);
                    
                    // 如果支持Fast Extension，发送拒绝消息
                    if self.supports_fast {
                        messages.push(BitTorrentMessage::Reject(request.index, request.begin, request.length));
                    }
                    
                    continue;
                }
            };
            
            // 读取块数据
            debug!("Attempting to read block: index={}, begin={}, length={}", 
                   request.index, request.begin, request.length);
            
            let block_data = match piece_manager.read_block(request.index, request.begin, request.length).await {
                Ok(data) => data,
                Err(e) => {
                    warn!("Failed to read block: {}", e);
                    
                    // 如果支持Fast Extension，发送拒绝消息
                    if self.supports_fast {
                        messages.push(BitTorrentMessage::Reject(request.index, request.begin, request.length));
                    }
                    
                    continue;
                }
            };
            
            debug!("Successfully read block data of length: {}", block_data.len());
            
            // 释放锁，以便其他线程可以访问PieceManager
            drop(piece_manager);
            
            // 应用上传速率限制（如果有）
            if let Some(rate_limit) = self.config.max_upload_rate {
                let bytes_to_upload = block_data.len();
                let upload_time = bytes_to_upload as f64 / rate_limit as f64;
                let upload_delay = std::time::Duration::from_secs_f64(upload_time);
                
                // 等待一段时间以限制上传速率
                tokio::time::sleep(upload_delay).await;
            }
            
            // 更新上传统计信息
            let mut statistics = self.statistics.lock().await;
            statistics.add_uploaded(block_data.len() as u64);
            
            // 发送分片数据
            messages.push(BitTorrentMessage::Piece(request.index, request.begin, block_data));
        }
        
        Ok(messages)
    }
    
    async fn set_choking(&self, choking: bool) -> Result<()> {
        let mut am_choking = self.am_choking.lock().await;
        *am_choking = choking;
        Ok(())
    }
    
    async fn set_peer_interested(&self, interested: bool) -> Result<()> {
        let mut peer_interested = self.peer_interested.lock().await;
        *peer_interested = interested;
        Ok(())
    }
    
    async fn add_allowed_fast_piece(&self, index: u32) -> Result<()> {
        let mut allowed_fast_pieces = self.allowed_fast_pieces.lock().await;
        if !allowed_fast_pieces.contains(&index) {
            allowed_fast_pieces.push(index);
        }
        Ok(())
    }
    
    async fn get_statistics(&self) -> Result<Arc<dyn UploadStatistics>> {
        Ok(self.statistics.clone() as Arc<dyn UploadStatistics>)
    }
    
    async fn set_upload_rate_limit(&self, limit: Option<u64>) -> Result<()> {
        let mut config = self.config.clone();
        config.max_upload_rate = limit;
        Ok(())
    }
    
    async fn get_upload_rate_limit(&self) -> Result<Option<u64>> {
        Ok(self.config.max_upload_rate)
    }
    
    async fn is_choking(&self) -> Result<bool> {
        Ok(*self.am_choking.lock().await)
    }
    
    async fn is_peer_interested(&self) -> Result<bool> {
        Ok(*self.peer_interested.lock().await)
    }
}
