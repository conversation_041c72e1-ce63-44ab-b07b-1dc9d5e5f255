import { ref } from 'vue';
import type { Ref } from 'vue';
import type { Task } from '../stores/task';

/**
 * 任务选择相关的composable
 */
export function useTaskSelection() {
  // 选中的任务ID列表
  const selectedTasks: Ref<string[]> = ref([]);
  
  // 上一次选中的任务ID列表（用于刷新后恢复选择）
  const previousSelectedTasks: Ref<string[]> = ref([]);

  /**
   * 处理表格选择变化
   * @param selection 选中的行数据
   */
  const handleSelectionChange = (selection: Task[]) => {
    selectedTasks.value = selection.map(item => item.id);
  };

  /**
   * 保存当前选择状态
   */
  const saveSelection = () => {
    previousSelectedTasks.value = [...selectedTasks.value];
  };

  /**
   * 恢复之前的选择状态
   * @param taskList 当前任务列表
   */
  const restoreSelection = (taskList: Task[]) => {
    if (previousSelectedTasks.value.length > 0) {
      // 过滤出仍然存在于当前任务列表中的任务ID
      selectedTasks.value = previousSelectedTasks.value.filter(id => 
        taskList.some(task => task.id === id)
      );
    }
  };

  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedTasks.value = [];
    previousSelectedTasks.value = [];
  };

  /**
   * 获取可暂停的任务列表
   * @param taskList 当前任务列表
   * @returns 可暂停的任务列表
   */
  const getPausableTasks = (taskList: Task[]): Task[] => {
    return taskList.filter(task => 
      selectedTasks.value.includes(task.id) && 
      (task.status === 'active' || task.status === 'waiting')
    );
  };

  /**
   * 获取可恢复的任务列表
   * @param taskList 当前任务列表
   * @returns 可恢复的任务列表
   */
  const getResumableTasks = (taskList: Task[]): Task[] => {
    return taskList.filter(task => 
      selectedTasks.value.includes(task.id) && 
      task.status === 'paused'
    );
  };

  return {
    selectedTasks,
    previousSelectedTasks,
    handleSelectionChange,
    saveSelection,
    restoreSelection,
    clearSelection,
    getPausableTasks,
    getResumableTasks
  };
}