use std::collections::HashMap;
use std::time::Instant;

/// 对等点统计管理器，负责收集和管理对等点的统计信息
#[derive(Clone)]
pub struct PeerStatistics {
    /// 对等点响应时间记录
    peer_response_times: HashMap<String, Vec<(Instant, u64)>>,
    /// 对等点下载速度记录
    peer_download_speeds: HashMap<String, Vec<(Instant, u64)>>,
    /// 对等点成功率记录
    peer_success_rates: HashMap<String, Vec<(Instant, bool)>>,
    /// 活跃对等点数量
    active_peers_count: usize,
    /// 总对等点数量
    total_peers_count: usize,
    /// 上传字节数
    uploaded_bytes: u64,
}

impl PeerStatistics {
    /// 创建一个新的对等点统计管理器
    pub fn new() -> Self {
        Self {
            peer_response_times: HashMap::new(),
            peer_download_speeds: HashMap::new(),
            peer_success_rates: HashMap::new(),
            active_peers_count: 0,
            total_peers_count: 0,
            uploaded_bytes: 0,
        }
    }

    /// 记录对等点响应时间
    pub fn record_response_time(&mut self, addr: &str, response_time: u64) {
        let now = Instant::now();
        let times = self.peer_response_times.entry(addr.to_string()).or_insert_with(Vec::new);
        times.push((now, response_time));

        // 保留最近的100条记录
        if times.len() > 100 {
            times.remove(0);
        }
    }

    /// 记录对等点下载速度
    pub fn record_download_speed(&mut self, addr: &str, speed: u64) {
        let now = Instant::now();
        let speeds = self.peer_download_speeds.entry(addr.to_string()).or_insert_with(Vec::new);
        speeds.push((now, speed));

        // 保留最近的100条记录
        if speeds.len() > 100 {
            speeds.remove(0);
        }
    }

    /// 记录对等点成功率
    pub fn record_success_rate(&mut self, addr: &str, success: bool) {
        let now = Instant::now();
        let rates = self.peer_success_rates.entry(addr.to_string()).or_insert_with(Vec::new);
        rates.push((now, success));

        // 保留最近的100条记录
        if rates.len() > 100 {
            rates.remove(0);
        }
    }

    /// 获取对等点平均响应时间
    pub fn get_average_response_time(&self, addr: &str) -> Option<u64> {
        if let Some(times) = self.peer_response_times.get(addr) {
            if times.is_empty() {
                return None;
            }

            let sum: u64 = times.iter().map(|(_, time)| *time).sum();
            return Some(sum / times.len() as u64);
        }
        None
    }

    /// 获取对等点平均下载速度
    pub fn get_average_download_speed(&self, addr: &str) -> Option<u64> {
        if let Some(speeds) = self.peer_download_speeds.get(addr) {
            if speeds.is_empty() {
                return None;
            }

            let sum: u64 = speeds.iter().map(|(_, speed)| *speed).sum();
            return Some(sum / speeds.len() as u64);
        }
        None
    }

    /// 获取对等点成功率
    pub fn get_success_rate(&self, addr: &str) -> Option<f64> {
        if let Some(rates) = self.peer_success_rates.get(addr) {
            if rates.is_empty() {
                return None;
            }

            let success_count = rates.iter().filter(|(_, success)| *success).count();
            return Some(success_count as f64 / rates.len() as f64);
        }
        None
    }

    /// 获取所有对等点的统计信息
    pub fn get_all_stats(&self) -> HashMap<String, PeerStats> {
        let mut stats = HashMap::new();

        // 合并所有地址
        let mut all_addrs = self.peer_response_times.keys().collect::<Vec<_>>();
        all_addrs.extend(self.peer_download_speeds.keys());
        all_addrs.extend(self.peer_success_rates.keys());

        // 去重
        let mut unique_addrs = Vec::new();
        for addr in all_addrs {
            if !unique_addrs.contains(&addr) {
                unique_addrs.push(addr);
            }
        }

        // 收集每个地址的统计信息
        for addr in unique_addrs {
            let response_time = self.get_average_response_time(addr);
            let download_speed = self.get_average_download_speed(addr);
            let success_rate = self.get_success_rate(addr);

            stats.insert(addr.to_string(), PeerStats {
                response_time,
                download_speed,
                success_rate,
            });
        }

        stats
    }

    /// 清理过期的统计信息
    pub fn cleanup_old_stats(&mut self) {
        let now = Instant::now();
        let max_age = std::time::Duration::from_secs(3600); // 1小时

        // 清理响应时间记录
        for times in self.peer_response_times.values_mut() {
            times.retain(|(time, _)| now.duration_since(*time) < max_age);
        }

        // 清理下载速度记录
        for speeds in self.peer_download_speeds.values_mut() {
            speeds.retain(|(time, _)| now.duration_since(*time) < max_age);
        }

        // 清理成功率记录
        for rates in self.peer_success_rates.values_mut() {
            rates.retain(|(time, _)| now.duration_since(*time) < max_age);
        }

        // 移除空记录
        self.peer_response_times.retain(|_, times| !times.is_empty());
        self.peer_download_speeds.retain(|_, speeds| !speeds.is_empty());
        self.peer_success_rates.retain(|_, rates| !rates.is_empty());
    }
    
    /// 更新活跃对等点数量
    pub fn update_active_peers(&mut self, count: usize) {
        self.active_peers_count = count;
    }
    
    /// 更新总对等点数量
    pub fn update_total_peers(&mut self, count: usize) {
        self.total_peers_count = count;
    }
    
    /// 获取活跃对等点数量
    pub fn active_peers(&self) -> usize {
        self.active_peers_count
    }
    
    /// 获取总对等点数量
    pub fn total_peers(&self) -> usize {
        self.total_peers_count
    }
    
    /// 更新上传字节数
    pub fn update_uploaded_bytes(&mut self, bytes: u64) {
        self.uploaded_bytes += bytes;
    }
    
    /// 获取上传字节数
    pub fn uploaded_bytes(&self) -> u64 {
        self.uploaded_bytes
    }
}

/// 对等点统计信息
pub struct PeerStats {
    /// 平均响应时间（毫秒）
    pub response_time: Option<u64>,
    /// 平均下载速度（字节/秒）
    pub download_speed: Option<u64>,
    /// 成功率（0.0-1.0）
    pub success_rate: Option<f64>,
}
