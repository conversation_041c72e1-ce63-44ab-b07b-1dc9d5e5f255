# Tonitru 下载器 CLI 完整指南

## 1. CLI 概述

### 1.1 设计目标

Tonitru 下载器 CLI 是一个命令行工具，允许用户通过终端管理和控制 Tonitru 下载器。它的设计目标包括：

- 提供完整的下载任务管理功能
- 支持控制下载速度和带宽限制
- 允许查询和修改配置设置
- 提供下载统计信息和状态查询
- 支持批处理和脚本集成
- 提供 API 调用和直接集成两种运行模式

### 1.2 技术选择

#### 1.2.1 依赖库

- **clap**: 用于命令行参数解析，提供丰富的命令行界面功能
- **reqwest**: 用于与 Tonitru API 进行 HTTP 通信
- **tokio**: 异步运行时
- **serde_json**: 用于 JSON 序列化和反序列化
- **serde_yaml**: 用于 YAML 序列化和反序列化
- **colored**: 用于彩色输出
- **indicatif**: 用于进度条和加载动画
- **dialoguer**: 用于交互式提示
- **prettytable-rs**: 用于表格输出
- **tabled**: 用于结构化表格输出

#### 1.2.2 架构设计

- 采用模块化设计，将不同功能分离为独立模块
- 使用命令模式，每个 CLI 命令对应一个处理函数
- 实现 API 客户端层，负责与 Tonitru API 通信
- 提供格式化输出层，统一处理命令输出格式
- 使用后端抽象层，支持 API 调用和直接集成两种模式

### 1.3 运行模式

Tonitru CLI 支持两种运行模式：

#### 1.3.1 API 调用模式

在 API 调用模式下，CLI 通过 HTTP API 与运行中的 Tonitru 下载器服务通信。这是默认模式，适合以下场景：

- 远程管理下载任务
- 与现有 Tonitru 服务集成
- 轻量级使用（无需额外资源）
- 脚本和自动化场景

#### 1.3.2 直接集成模式

在直接集成模式下，CLI 直接使用 Tonitru 下载器的核心组件，无需启动独立的服务。这种模式适合以下场景：

- 单机使用
- 离线环境
- 需要更高执行效率
- 便携式部署

## 2. 用户指南

### 2.1 安装方法

#### 2.1.1 从源码编译

```bash
# 克隆仓库
git clone https://github.com/your-username/tonitru_downloader_rust.git
cd tonitru_downloader_rust

# 编译
cargo build --release

# 安装（可选）
cargo install --path .
```

#### 2.1.2 使用预编译二进制文件

1. 从 [Releases](https://github.com/your-username/tonitru_downloader_rust/releases) 页面下载适合您系统的二进制文件
2. 解压缩下载的文件
3. 将可执行文件添加到您的系统 PATH 中（可选）

### 2.2 命令结构

#### 2.2.1 主命令

```
tonitru_cli [OPTIONS] <SUBCOMMAND>
```

全局选项：
- `--config <FILE>`: 指定配置文件路径
- `--api <URL>`: 指定 API 服务器 URL
- `--mode <MODE>`: 运行模式(api, direct)
- `--format <FORMAT>`: 输出格式(text, json, yaml, table)
- `--verbose`: 详细输出模式
- `--quiet`: 安静模式，只输出错误信息
- `--debug, -D`: 调试模式，提供更多详细信息（开发者使用）
- `--profile, -P`: 启用性能分析（仅在调试模式下有效）

#### 2.2.2 子命令

##### 下载管理命令

```
tonitru_cli download [OPTIONS] <URL>
```

选项：
- `--output <PATH>`: 指定输出路径
- `--start`: 立即开始下载
- `--limit-rate <RATE>`: 设置下载速度限制

##### 任务管理命令

```
tonitru_cli task [SUBCOMMAND]
```

子命令：
- `list`: 列出所有任务
- `info <ID>`: 显示任务详情
- `pause <ID>`: 暂停任务
- `resume <ID>`: 恢复任务
- `cancel <ID>`: 取消任务
- `remove <ID>`: 删除任务

##### 配置管理命令

```
tonitru_cli config [SUBCOMMAND]
```

子命令：
- `get [KEY]`: 获取配置
- `set <KEY> <VALUE>`: 设置配置
- `reset [KEY]`: 重置配置
- `save`: 保存配置到默认路径
- `export`: 导出配置到指定文件
- `import`: 从指定文件导入配置

##### 速度控制命令

```
tonitru_cli speed [SUBCOMMAND]
```

子命令：
- `limit <TYPE> <RATE>`: 设置全局速度限制
- `task-limit <ID> <TYPE> <RATE>`: 设置任务速度限制
- `time-rule [SUBCOMMAND]`: 管理时间规则

### 2.3 运行模式

#### 2.3.1 普通模式与调试模式

Tonitru 下载器 CLI 提供两种主要的运行模式：

1. **普通模式（默认）**：
   - 面向普通用户的默认模式
   - 提供简洁的输出信息，只显示必要的操作结果和错误
   - 适合日常使用，输出简洁明了

2. **调试模式**：
   - 通过 `--debug` 或 `-D` 参数启用
   - 面向开发者，提供详细的运行信息
   - 显示内部操作流程、网络请求详情、错误堆栈等信息
   - 可以帮助开发者诊断问题和理解程序运行过程

调试模式下还可以启用性能分析功能：
- 使用 `--profile` 或 `-P` 参数（必须与 `--debug` 一起使用）
- 记录并显示各个操作的执行时间和资源消耗
- 生成性能报告，帮助识别性能瓶颈

示例：

```bash
# 普通模式运行（默认）
tonitru-cli download add https://example.com/file.zip

# 调试模式运行
tonitru-cli --debug download add https://example.com/file.zip

# 调试模式 + 性能分析
tonitru-cli --debug --profile download add https://example.com/file.zip
```

### 2.4 使用示例

#### 2.4.1 添加下载任务

```bash
# 添加下载任务并立即开始下载
tonitru_cli download https://example.com/file.zip --start

# 指定输出路径
tonitru_cli download https://example.com/file.zip --output /path/to/save/

# 设置下载速度限制
tonitru_cli download https://example.com/file.zip --limit-rate 1M
```

#### 2.4.2 管理下载任务

```bash
# 列出所有任务
tonitru_cli task list

# 列出特定状态的任务
tonitru_cli task list --status downloading

# 显示任务详情
tonitru_cli task info 550e8400-e29b-41d4-a716-446655440000

# 暂停任务
tonitru_cli task pause 550e8400-e29b-41d4-a716-446655440000

# 恢复任务
tonitru_cli task resume 550e8400-e29b-41d4-a716-446655440000

# 取消任务
tonitru_cli task cancel 550e8400-e29b-41d4-a716-446655440000

# 删除任务
tonitru_cli task remove 550e8400-e29b-41d4-a716-446655440000

# 删除任务并删除文件
tonitru_cli task remove 550e8400-e29b-41d4-a716-446655440000 --delete-file
```

#### 2.4.3 配置管理

```bash
# 获取所有配置
tonitru config get

# 获取特定配置
tonitru config get --key download.default_path

# 更新配置
tonitru config update --key download.default_path --value /new/path/to/downloads

# 重置特定配置
tonitru config reset --key download.default_path

# 重置所有配置
tonitru config reset

# 保存配置到默认路径
tonitru config save

# 导出配置到指定文件
tonitru config export --path /path/to/config.json

# 从指定文件导入配置
tonitru config import --path /path/to/config.json
```

#### 2.4.4 速度控制

```bash
# 设置全局下载速度限制
tonitru_cli speed limit download 5M

# 设置全局上传速度限制
tonitru_cli speed limit upload 1M

# 设置任务下载速度限制
tonitru_cli speed task-limit 550e8400-e29b-41d4-a716-446655440000 download 2M

# 添加时间规则
tonitru_cli speed time-rule add --name "夜间限速" --start 22:00 --end 08:00 --days 1-7 --download-limit 1M --upload-limit 500K

# 列出所有时间规则
tonitru_cli speed time-rule list

# 删除时间规则
tonitru_cli speed time-rule remove 550e8400-e29b-41d4-a716-446655440000
```
#### 2.4.5 使用直接集成模式
```bash
# 使用直接集成模式添加下载任务
tonitru_cli --direct download https://example.com/file.zip --start

# 使用直接集成模式列出任务
tonitru_cli --direct task list
```

## 3. 开发文档

### 3.1 实现计划

#### 3.1.1 第一阶段：基础功能

- 实现命令行参数解析
- 实现 API 客户端
- 实现基本下载命令
- 实现任务管理命令
- 实现配置管理命令
- 实现格式化输出

#### 3.1.2 第二阶段：高级功能

- 实现直接集成模式
- 实现速度控制命令
- 实现批量操作命令
- 实现交互式模式
- 添加进度显示和状态更新
- 实现自动补全脚本生成

#### 3.1.3 第三阶段：优化和扩展

- 性能优化
- 添加插件支持
- 实现配置文件编辑器
- 添加更多输出格式
- 国际化支持
- 实现 Shell 集成

### 3.2 架构设计

#### 3.2.1 模块结构

```
src/cli/
├── api/                # API 客户端
│   ├── client.rs       # API 客户端实现
│   ├── models.rs       # API 数据模型
│   └── mod.rs          # 模块导出
├── commands/           # 命令实现
│   ├── download.rs     # 下载命令
│   ├── task.rs         # 任务管理命令
│   ├── config.rs       # 配置管理命令
│   ├── speed.rs        # 速度控制命令
│   └── mod.rs          # 模块导出
├── utils/              # 工具函数
│   ├── formatter.rs    # 输出格式化
│   ├── progress.rs     # 进度显示
│   ├── config.rs       # 配置文件处理
│   └── mod.rs          # 模块导出
└── mod.rs              # CLI 模块入口
```

#### 3.2.2 核心组件

##### CLI 应用

```rust
pub struct CliApp {
    /// 命令行参数
    args: CliArgs,
    /// 后端接口
    backend: Box<dyn CliBackend>,
    /// 输出格式化器
    formatter: Box<dyn OutputFormatter>,
}

impl CliApp {
    /// 创建新的 CLI 应用
    pub fn new() -> Result<Self> {
        // 解析命令行参数
        let args = CliArgs::parse();
        
        // 创建后端接口
        let backend: Box<dyn CliBackend> = if args.direct {
            Box::new(DirectBackend::new(&args)?)
        } else {
            Box::new(ApiBackend::new(&args)?)
        };
        
        // 创建输出格式化器
        let formatter: Box<dyn OutputFormatter> = match args.format.as_deref() {
            Some("json") => Box::new(JsonFormatter::new()),
            Some("yaml") => Box::new(YamlFormatter::new()),
            Some("table") => Box::new(TableFormatter::new()),
            _ => Box::new(TextFormatter::new()),
        };
        
        Ok(Self {
            args,
            backend,
            formatter,
        })
    }
    
    /// 运行 CLI 应用
    pub async fn run(&self) -> Result<()> {
        match &self.args.subcommand {
            SubCommand::Download(args) => {
                self.run_download_command(args).await
            },
            SubCommand::Task(args) => {
                self.run_task_command(args).await
            },
            SubCommand::Config(args) => {
                self.run_config_command(args).await
            },
            SubCommand::Speed(args) => {
                self.run_speed_command(args).await
            },
            // ... 其他子命令 ...
        }
    }
    
    // ... 子命令处理函数 ...
}
```

##### 后端接口

```rust
#[async_trait]
pub trait CliBackend: Send + Sync {
    /// 添加下载任务
    async fn add_download(&self, url: &str, output_path: Option<&str>, auto_start: bool) -> Result<String>;
    
    /// 获取任务列表
    async fn get_tasks(&self, status: Option<&str>) -> Result<Vec<TaskInfo>>;
    
    /// 获取任务详情
    async fn get_task_info(&self, task_id: &str) -> Result<TaskDetail>;
    
    /// 暂停任务
    async fn pause_task(&self, task_id: &str) -> Result<()>;
    
    /// 恢复任务
    async fn resume_task(&self, task_id: &str) -> Result<()>;
    
    /// 取消任务
    async fn cancel_task(&self, task_id: &str) -> Result<()>;
    
    /// 删除任务
    async fn remove_task(&self, task_id: &str, delete_file: bool) -> Result<()>;
    
    /// 获取配置
    async fn get_config(&self, key: Option<&str>) -> Result<Value>;
    
    /// 设置配置
    async fn set_config(&self, key: &str, value: Value) -> Result<()>;
    
    /// 重置配置
    async fn reset_config(&self, key: Option<&str>) -> Result<()>;
    
    /// 设置全局速度限制
    async fn set_global_speed_limit(&self, limit_type: SpeedLimitType, limit: u64) -> Result<()>;
    
    /// 设置任务速度限制
    async fn set_task_speed_limit(&self, task_id: &str, limit_type: SpeedLimitType, limit: u64) -> Result<()>;
    
    // ... 其他后端接口 ...
}
```

##### API 后端实现

```rust
pub struct ApiBackend {
    /// API 客户端
    client: ApiClient,
}

#[async_trait]
impl CliBackend for ApiBackend {
    async fn add_download(&self, url: &str, output_path: Option<&str>, auto_start: bool) -> Result<String> {
        let request = AddDownloadRequest {
            url: url.to_string(),
            output_path: output_path.map(|s| s.to_string()),
            auto_start,
        };
        
        let response = self.client.add_download(request).await?;
        Ok(response.id)
    }
    
    // ... 其他接口实现 ...
}
```

##### 直接集成后端实现

```rust
pub struct DirectBackend {
    /// 下载管理器
    download_manager: Arc<DownloadManager>,
    /// 配置管理器
    config_manager: Arc<ConfigManager>,
    /// 任务管理器
    task_manager: Arc<TaskManager>,
}

#[async_trait]
impl CliBackend for DirectBackend {
    async fn add_download(&self, url: &str, output_path: Option<&str>, auto_start: bool) -> Result<String> {
        // 创建下载任务
        let task_id = self.download_manager.create_task(url, output_path).await?;
        
        // 如果需要，立即开始下载
        if auto_start {
            self.download_manager.start_task(&task_id).await?;
        }
        
        Ok(task_id)
    }
    
    // ... 其他接口实现 ...
}
```

### 3.3 直接集成模式

#### 3.3.1 集成架构

直接集成模式允许 CLI 工具直接使用 Tonitru 下载器的核心组件，而不需要通过 HTTP API 通信。这种模式的架构如下：

```
┌─────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  CLI 命令   │────▶│  后端抽象层    │────▶│  核心组件      │
└─────────────┘     └─────────────────┘     └─────────────────┘
                            │                       ▲
                            │                       │
                            ▼                       │
                     ┌─────────────────┐            │
                     │  依赖注入容器  │────────────┘
                     └─────────────────┘
```

#### 3.3.2 依赖注入

```rust
pub struct DirectBackendFactory {
    /// 依赖注入容器
    container: Arc<Container>,
}

impl DirectBackendFactory {
    /// 创建新的工厂
    pub fn new() -> Result<Self> {
        // 创建依赖注入容器
        let container = create_di_container()?;
        
        Ok(Self {
            container: Arc::new(container),
        })
    }
    
    /// 创建直接集成后端
    pub fn create_backend(&self) -> Result<DirectBackend> {
        // 从容器中获取所需组件
        let download_manager = self.container.resolve::<dyn DownloadManager>()?;
        let config_manager = self.container.resolve::<dyn ConfigManager>()?;
        let task_manager = self.container.resolve::<dyn TaskManager>()?;
        
        Ok(DirectBackend {
            download_manager,
            config_manager,
            task_manager,
        })
    }
}

/// 创建依赖注入容器
fn create_di_container() -> Result<Container> {
    let mut builder = ContainerBuilder::new();
    
    // 注册配置管理器
    builder.register_factory::<dyn ConfigManager, _>(|_| {
        let config_manager = ConfigManagerImpl::new("config.toml")?;
        Ok(Arc::new(config_manager) as Arc<dyn ConfigManager>)
    });
    
    // 注册存储管理器
    builder.register_factory_with_dependency::<dyn StorageManager, _, _>(|container| {
        let config_manager = container.resolve::<dyn ConfigManager>()?;
        let storage_manager = StorageManagerImpl::new(config_manager)?;
        Ok(Arc::new(storage_manager) as Arc<dyn StorageManager>)
    });
    
    // 注册下载管理器
    builder.register_factory_with_dependency::<dyn DownloadManager, _, _>(|container| {
        let config_manager = container.resolve::<dyn ConfigManager>()?;
        let storage_manager = container.resolve::<dyn StorageManager>()?;
        let download_manager = DownloadManagerImpl::new(config_manager, storage_manager)?;
        Ok(Arc::new(download_manager) as Arc<dyn DownloadManager>)
    });
    
    // 注册任务管理器
    builder.register_factory_with_dependency::<dyn TaskManager, _, _>(|container| {
        let download_manager = container.resolve::<dyn DownloadManager>()?;
        let task_manager = TaskManagerImpl::new(download_manager)?;
        Ok(Arc::new(task_manager) as Arc<dyn TaskManager>)
    });
    
    // 构建容器
    builder.build()
}
```

#### 3.3.3 性能优化

直接集成模式相比 API 调用模式有以下性能优势：

1. **减少通信开销**：无需 HTTP 请求/响应，直接调用函数
2. **共享内存**：直接访问内存中的数据结构，无需序列化/反序列化
3. **减少资源消耗**：不需要运行独立的 HTTP 服务器
4. **更低的延迟**：直接函数调用比网络请求快得多
5. **批量操作优化**：可以在一次操作中处理多个任务，而不需要多次 API 调用