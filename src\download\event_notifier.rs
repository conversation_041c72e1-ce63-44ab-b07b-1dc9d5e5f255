use anyhow::Result;
use async_trait::async_trait;
use std::collections::HashMap;
use uuid::Uuid;

use crate::api::events::EventManager;
use crate::core::interfaces::downloader::{DownloadProgress, DownloadStatus as CoreDownloadStatus, ProtocolType as CoreProtocolType, DownloadOptions};
use crate::core::interfaces::task::TaskInfo as CoreTaskInfo;
use crate::download::manager::{TaskInfo, TaskStatus};

/// 事件通知器接口
#[async_trait]
pub trait EventNotifier: Send + Sync {
    /// 通知任务添加
    async fn notify_task_added(&self, task: &TaskInfo) -> Result<()>;
    
    /// 通知任务开始
    async fn notify_task_started(&self, task: &TaskInfo) -> Result<()>;
    
    /// 通知任务暂停
    async fn notify_task_paused(&self, task: &TaskInfo) -> Result<()>;
    
    /// 通知任务恢复
    async fn notify_task_resumed(&self, task: &TaskInfo) -> Result<()>;
    
    /// 通知任务完成
    async fn notify_task_completed(&self, task: &TaskInfo) -> Result<()>;
    
    /// 通知任务失败
    async fn notify_task_failed(&self, task: &TaskInfo) -> Result<()>;
    
    /// 通知任务取消
    async fn notify_task_cancelled(&self, task: &TaskInfo) -> Result<()>;
    
    /// 通知任务删除
    async fn notify_task_removed(&self, task: &TaskInfo) -> Result<()>;
    
    /// 通知任务进度
    async fn notify_task_progress(&self, task: &TaskInfo) -> Result<()>;
    
    /// 通知任务速度
    async fn notify_task_speed(&self, task_id: Uuid, download_speed: u64) -> Result<()>;
    
    /// 通知任务状态变更
    async fn notify_task_status_changed(&self, task: &TaskInfo, old_status: TaskStatus) -> Result<()>;
    
    /// 通知全局状态
    async fn notify_global_status(&self, active_count: usize, waiting_count: usize, stopped_count: usize) -> Result<()>;
    
    /// 通知全局速度
    async fn notify_global_speed(&self, download_speed: u64, upload_speed: u64) -> Result<()>;
}

/// 事件通知器实现
#[derive(Clone)]
pub struct EventNotifierImpl {
    event_manager: EventManager,
}

impl EventNotifierImpl {
    /// 创建新的事件通知器
    pub fn new(event_manager: EventManager) -> Self {
        Self {
            event_manager,
        }
    }
    
    /// 将内部 TaskInfo 转换为 Core TaskInfo
    fn to_core_task_info(&self, task: &TaskInfo) -> CoreTaskInfo {
        // 确定协议类型
        // 由于无法同步调用异步方法，这里根据URL前缀简单判断协议类型
        let protocol_type = if task.url.starts_with("http://") {
            CoreProtocolType::Http
        } else if task.url.starts_with("https://") {
            CoreProtocolType::Https
        } else if task.url.starts_with("ftp://") {
            CoreProtocolType::Ftp
        } else if task.url.starts_with("magnet:") {
            CoreProtocolType::Magnet
        } else if task.url.ends_with(".torrent") {
            CoreProtocolType::BitTorrent
        } else if task.url.starts_with("p2p://") {
            CoreProtocolType::P2P
        } else if task.url.starts_with("r2://") {
            CoreProtocolType::R2
        } else {
            CoreProtocolType::Custom(0)
        };

        // 转换状态
        let status = match task.status {
            TaskStatus::Pending => CoreDownloadStatus::Pending,
            TaskStatus::Initializing => CoreDownloadStatus::Initializing,
            TaskStatus::Downloading => CoreDownloadStatus::Downloading,
            TaskStatus::Paused => CoreDownloadStatus::Paused,
            TaskStatus::Completed => CoreDownloadStatus::Completed,
            TaskStatus::Failed => CoreDownloadStatus::Failed,
            TaskStatus::Cancelled => CoreDownloadStatus::Cancelled,
            TaskStatus::Error => CoreDownloadStatus::Failed, // 将Error映射到Failed状态
            
        };

        // 创建进度信息
        let progress = DownloadProgress {
            total_size: task.total_size,
            downloaded_size: task.downloaded_size,
            progress_percentage: task.progress,
            speed: task.speed,
            eta: None, // 暂不计算ETA
        };

        // 创建默认下载选项
        let options = DownloadOptions {
            connections: Some(6), // 默认连接数
            speed_limit: None,
            headers: None,
            timeout: None,
            retry_count: None,
            use_mirrors: None,
            verify_checksum: None,
            checksum: None,
            checksum_algorithm: None,
            custom_options: None,
        };

        // 创建并返回CoreTaskInfo
        CoreTaskInfo {
            id: task.id,
            url: task.url.clone(),
            output_path: task.output_path.clone(),
            protocol: protocol_type,
            status,
            progress,
            created_at: task.created_at,
            updated_at: task.updated_at,
            completed_at: if task.status == TaskStatus::Completed { Some(task.updated_at) } else { None },
            error_message: task.error_message.clone(),
            options,
            metadata: HashMap::new(),
        }
    }
    
    /// 将 TaskStatus 转换为 CoreDownloadStatus
    fn to_core_status(&self, status: TaskStatus) -> CoreDownloadStatus {
        match status {
            TaskStatus::Pending => CoreDownloadStatus::Pending,
            TaskStatus::Initializing => CoreDownloadStatus::Initializing,
            TaskStatus::Downloading => CoreDownloadStatus::Downloading,
            TaskStatus::Paused => CoreDownloadStatus::Paused,
            TaskStatus::Completed => CoreDownloadStatus::Completed,
            TaskStatus::Failed => CoreDownloadStatus::Failed,
            TaskStatus::Cancelled => CoreDownloadStatus::Cancelled,
            TaskStatus::Error => CoreDownloadStatus::Failed, // 将Error映射到Failed状态
            TaskStatus::Error => CoreDownloadStatus::Failed, // 将Error映射到Failed状态
        }
    }
}

#[async_trait]
impl EventNotifier for EventNotifierImpl {
    async fn notify_task_added(&self, task: &TaskInfo) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        self.event_manager.task_added(&core_task_info);
        Ok(())
    }
    
    async fn notify_task_started(&self, task: &TaskInfo) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        self.event_manager.task_started(&core_task_info);
        Ok(())
    }
    
    async fn notify_task_paused(&self, task: &TaskInfo) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        self.event_manager.task_paused(&core_task_info);
        Ok(())
    }
    
    async fn notify_task_resumed(&self, task: &TaskInfo) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        self.event_manager.task_resumed(&core_task_info);
        Ok(())
    }
    
    async fn notify_task_completed(&self, task: &TaskInfo) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        self.event_manager.task_completed(&core_task_info);
        Ok(())
    }
    
    async fn notify_task_failed(&self, task: &TaskInfo) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        self.event_manager.task_error(&core_task_info);
        Ok(())
    }
    
    async fn notify_task_cancelled(&self, task: &TaskInfo) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        self.event_manager.task_cancelled(&core_task_info);
        Ok(())
    }
    
    async fn notify_task_removed(&self, task: &TaskInfo) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        self.event_manager.task_removed(&core_task_info);
        Ok(())
    }
    
    async fn notify_task_progress(&self, task: &TaskInfo) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        self.event_manager.task_progress(&core_task_info);
        Ok(())
    }
    
    async fn notify_task_speed(&self, task_id: Uuid, download_speed: u64) -> Result<()> {
        self.event_manager.task_speed(task_id, download_speed, 0);
        Ok(())
    }
    
    async fn notify_task_status_changed(&self, task: &TaskInfo, old_status: TaskStatus) -> Result<()> {
        let core_task_info = self.to_core_task_info(task);
        let old_core_status = self.to_core_status(old_status);
        self.event_manager.task_status_changed(&core_task_info, old_core_status);
        Ok(())
    }
    
    async fn notify_global_status(&self, active_count: usize, waiting_count: usize, stopped_count: usize) -> Result<()> {
        self.event_manager.global_status(active_count, waiting_count, stopped_count);
        Ok(())
    }
    
    async fn notify_global_speed(&self, download_speed: u64, upload_speed: u64) -> Result<()> {
        self.event_manager.global_speed(download_speed, upload_speed);
        Ok(())
    }
}

/// 空的事件通知器实现，用于在没有提供EventManager时使用
pub struct NoopEventNotifier {}

#[async_trait]
impl EventNotifier for NoopEventNotifier {
    async fn notify_task_added(&self, _task: &TaskInfo) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_started(&self, _task: &TaskInfo) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_paused(&self, _task: &TaskInfo) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_resumed(&self, _task: &TaskInfo) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_completed(&self, _task: &TaskInfo) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_failed(&self, _task: &TaskInfo) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_cancelled(&self, _task: &TaskInfo) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_removed(&self, _task: &TaskInfo) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_progress(&self, _task: &TaskInfo) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_speed(&self, _task_id: Uuid, _download_speed: u64) -> Result<()> {
        Ok(())
    }
    
    async fn notify_task_status_changed(&self, _task: &TaskInfo, _old_status: TaskStatus) -> Result<()> {
        Ok(())
    }
    
    async fn notify_global_status(&self, _active_count: usize, _waiting_count: usize, _stopped_count: usize) -> Result<()> {
        Ok(())
    }
    
    async fn notify_global_speed(&self, _download_speed: u64, _upload_speed: u64) -> Result<()> {
        Ok(())
    }
}
