use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use crate::core::error::CoreResult;

/// Plugin metadata
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PluginMetadata {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub website: Option<String>,
    pub dependencies: Vec<String>,
    pub capabilities: Vec<PluginCapability>,
}

/// Plugin capability
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum PluginCapability {
    Downloader,
    Storage,
    UI,
    Analyzer,
    Converter,
    Notification,
    Authentication,
    Custom(String),
}

/// Plugin status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PluginStatus {
    Enabled,
    Disabled,
    Error,
}

/// Plugin interface
#[async_trait]
pub trait Plugin: Send + Sync {
    /// Get plugin metadata
    fn metadata(&self) -> &PluginMetadata;
    
    /// Initialize the plugin
    async fn init(&mut self, config: HashMap<String, String>) -> CoreResult<()>;
    
    /// Shutdown the plugin
    async fn shutdown(&mut self) -> CoreResult<()>;
    
    /// Get plugin status
    fn status(&self) -> PluginStatus;
    
    /// Get plugin configuration schema
    fn config_schema(&self) -> serde_json::Value;
    
    /// Get plugin instance
    fn instance(&self) -> Box<dyn std::any::Any>;
}

/// Plugin manager interface
#[async_trait]
pub trait PluginManager: Send + Sync {
    /// Load a plugin from a path
    async fn load_plugin(&self, path: &str) -> CoreResult<String>;
    
    /// Unload a plugin
    async fn unload_plugin(&self, id: &str) -> CoreResult<()>;
    
    /// Enable a plugin
    async fn enable_plugin(&self, id: &str) -> CoreResult<()>;
    
    /// Disable a plugin
    async fn disable_plugin(&self, id: &str) -> CoreResult<()>;
    
    /// Get plugin metadata
    async fn get_plugin_metadata(&self, id: &str) -> CoreResult<PluginMetadata>;
    
    /// Get all plugins
    async fn get_all_plugins(&self) -> CoreResult<Vec<PluginMetadata>>;
    
    /// Get plugin by capability
    async fn get_plugins_by_capability(&self, capability: PluginCapability) -> CoreResult<Vec<PluginMetadata>>;
    
    /// Get plugin instance
    async fn get_plugin_instance(&self, id: &str) -> CoreResult<Box<dyn Plugin>>;
    
    /// Configure plugin
    async fn configure_plugin(&self, id: &str, config: HashMap<String, String>) -> CoreResult<()>;
}
