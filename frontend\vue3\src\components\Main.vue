<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAppStore } from '../stores/app'
import Aside from './Aside.vue'
import Speedometer from './Speedometer.vue'
import AddTask from './Task/AddTask.vue'
import AboutPanel from './About/AboutPanel.vue'
import WebSocketStatus from './WebSocketStatus.vue'

const appStore = useAppStore()

// 从 store 获取状态
const addTaskVisible = computed(() => appStore.addTaskVisible)
const addTaskType = computed(() => appStore.addTaskType)
const aboutPanelVisible = computed(() => appStore.aboutPanelVisible)
</script>

<template>
  <el-container id="container">
    <Aside />
    <router-view />
    <Speedometer />
    <AddTask
      v-model="appStore.addTaskVisible"
      :type="addTaskType"
      @close="appStore.hideAddTaskDialog"
    />
    <AboutPanel
      v-model="appStore.aboutPanelVisible"
      @close="appStore.hideAboutPanel"
    />
    <!-- WebSocket状态组件放在底部，不显眼的位置 -->
    <WebSocketStatus class="ws-status-minimal" />
  </el-container>
</template>

<style scoped>
#container {
  height: 100%;
  position: relative;
}

.ws-status-minimal {
  position: absolute;
  bottom: 5px;
  right: 5px;
  z-index: 100;
  opacity: 0.6;
  transition: opacity 0.3s;
  transform: scale(0.8);
}

.ws-status-minimal:hover {
  opacity: 1;
  transform: scale(1);
}
</style>
