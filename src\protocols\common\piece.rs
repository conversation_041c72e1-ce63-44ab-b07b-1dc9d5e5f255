use anyhow::{Result, anyhow};

use crate::core::p2p::piece::PieceState;

/// 块状态
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum BlockState {
    /// 未请求
    Missing,
    /// 已请求
    Requested,
    /// 已下载
    Downloaded,
}

/// 块信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct BlockInfo {
    /// 块索引
    pub index: u32,
    /// 块在分片中的偏移量
    pub offset: u32,
    /// 块大小
    pub size: u32,
    /// 块状态
    pub state: BlockState,
    /// 请求时间
    pub request_time: Option<std::time::Instant>,
}

/// 通用分片信息
#[derive(Debug)]
pub struct CommonPieceInfo {
    /// 分片索引
    pub index: u32,
    /// 分片大小
    pub size: u64,
    /// 分片哈希
    pub hash: Option<Vec<u8>>,
    /// 分片状态
    pub state: PieceState,
    /// 分片优先级
    pub priority: u8,
    /// 分片块
    pub blocks: Vec<BlockInfo>,
    /// 已下载块数
    pub downloaded_blocks: u32,
    /// 总块数
    pub total_blocks: u32,
    /// 分片数据
    pub data: Option<Vec<u8>>,
}

impl CommonPieceInfo {
    /// 创建新的分片信息
    pub fn new(index: u32, size: u64, hash: Option<Vec<u8>>, block_size: u32) -> Self {
        // 计算块数
        let total_blocks = ((size + block_size as u64 - 1) / block_size as u64) as u32;
        let mut blocks = Vec::with_capacity(total_blocks as usize);

        // 创建块
        let mut remaining_size = size;
        let mut offset = 0;

        for i in 0..total_blocks {
            let block_size = if remaining_size >= block_size as u64 {
                block_size
            } else {
                remaining_size as u32
            };

            blocks.push(BlockInfo {
                index: i,
                offset,
                size: block_size,
                state: BlockState::Missing,
                request_time: None,
            });

            offset += block_size;
            remaining_size -= block_size as u64;
        }

        Self {
            index,
            size,
            hash,
            state: PieceState::Missing,
            priority: 1,
            blocks,
            downloaded_blocks: 0,
            total_blocks,
            data: None,
        }
    }

    /// 获取下一个要请求的块
    pub fn next_block(&mut self) -> Option<&mut BlockInfo> {
        // 如果分片已完成，返回None
        if self.state == PieceState::Downloaded || self.state == PieceState::Verified {
            return None;
        }

        // 查找第一个未请求的块的索引
        let missing_block_index = self.blocks.iter()
            .position(|block| block.state == BlockState::Missing);

        if let Some(index) = missing_block_index {
            // 获取并更新块
            let block = &mut self.blocks[index];
            block.state = BlockState::Requested;
            block.request_time = Some(std::time::Instant::now());
            self.state = PieceState::Requested;
            return Some(block);
        }

        // 如果所有块都已请求，检查是否有需要重新请求的块
        let now = std::time::Instant::now();
        let timeout = std::time::Duration::from_secs(30); // 30秒超时

        // 查找超时的块的索引
        let timeout_block_index = self.blocks.iter()
            .position(|block| {
                if block.state == BlockState::Requested {
                    if let Some(request_time) = block.request_time {
                        return now.duration_since(request_time) > timeout;
                    }
                }
                false
            });

        if let Some(index) = timeout_block_index {
            // 获取并更新块
            let block = &mut self.blocks[index];
            block.request_time = Some(now);
            return Some(block);
        }

        None
    }

    /// 添加块数据
    pub fn add_block_data(&mut self, offset: u32, data: Vec<u8>) -> Result<()> {
        // 查找块
        for block in &mut self.blocks {
            if block.offset == offset {
                // 检查数据大小
                if data.len() as u32 != block.size {
                    return Err(anyhow!("Block size mismatch: expected {}, got {}", block.size, data.len()));
                }

                // 更新块状态
                block.state = BlockState::Downloaded;
                self.downloaded_blocks += 1;

                // 如果是第一个下载的块，初始化数据缓冲区
                if self.data.is_none() {
                    self.data = Some(vec![0; self.size as usize]);
                }

                // 将数据复制到缓冲区
                if let Some(ref mut piece_data) = self.data {
                    let start = offset as usize;
                    let end = start + data.len();
                    piece_data[start..end].copy_from_slice(&data);
                }

                // 更新分片状态
                if self.downloaded_blocks == self.total_blocks {
                    self.state = PieceState::Downloaded;
                } else {
                    self.state = PieceState::Downloading;
                }

                return Ok(());
            }
        }

        Err(anyhow!("Block not found: piece={}, offset={}", self.index, offset))
    }

    /// 获取分片进度
    pub fn progress(&self) -> f64 {
        if self.total_blocks == 0 {
            return 0.0;
        }

        self.downloaded_blocks as f64 / self.total_blocks as f64
    }

    /// 重置分片
    pub fn reset(&mut self) {
        self.state = PieceState::Missing;
        self.downloaded_blocks = 0;
        self.data = None;

        for block in &mut self.blocks {
            block.state = BlockState::Missing;
            block.request_time = None;
        }
    }
}

/// 通用分片管理器
pub struct CommonPieceManager {
    /// 分片列表
    pub pieces: Vec<CommonPieceInfo>,
    /// 输出文件
    pub output_files: Vec<(String, u64)>,
    /// 输出目录
    pub output_dir: String,
    /// 块大小
    pub block_size: u32,
    /// 已下载分片数
    pub downloaded_pieces: u32,
    /// 已验证分片数
    pub verified_pieces: u32,
    /// 总分片数
    pub total_pieces: u32,
    /// 总大小
    pub total_size: u64,
    /// 已下载大小
    pub downloaded_size: u64,
}
