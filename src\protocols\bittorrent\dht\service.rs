use anyhow::Result;
use async_trait::async_trait;
use std::net::SocketAddr;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;
use tracing::{debug, info, warn};

use crate::config::Settings;
use crate::core::p2p::dht::{DHT, DHTConfig, DHTEvent, DHTEventListener, DHTStatus};

use super::client::DHTClient;
use super::config::DHTClientConfig;

/// DHT服务配置
#[derive(Debug, Clone)]
pub struct DHTServiceConfig {
    /// DHT客户端配置
    pub client_config: DHTClientConfig,
    /// 自动启动
    pub auto_start: bool,
    /// 定期维护间隔（秒）
    pub maintenance_interval: u64,
    /// 路由表持久化文件路径
    pub routing_table_path: Option<PathBuf>,
    /// 路由表持久化间隔（秒）
    pub routing_table_save_interval: u64,
    /// 启动时加载路由表
    pub load_routing_table: bool,
}

impl DHTServiceConfig {
    /// 从应用程序设置创建DHT服务配置
    pub fn from_settings(settings: &Settings) -> Self {
        // 从设置中提取DHT配置
        let dht_config = DHTConfig {
            port: settings.dht_port.unwrap_or(6881),
            bootstrap_nodes: settings.dht_bootstrap_nodes.clone().unwrap_or_else(|| {
                vec![
                    "router.bittorrent.com:6881".to_string(),
                    "dht.transmissionbt.com:6881".to_string(),
                    "router.utorrent.com:6881".to_string(),
                ]
            }),
            routing_table_size: settings.dht_routing_table_size.unwrap_or(8),
            node_timeout: settings.dht_node_timeout.unwrap_or(900),
            query_timeout: settings.dht_query_timeout.unwrap_or(30),
            enabled: settings.enable_dht.unwrap_or(true),
            enable_ipv6: settings.enable_ipv6.unwrap_or(true),
        };

        // 获取路由表路径
        let routing_table_path = settings.dht_routing_table_path.clone().map(|path| {
            if Path::new(&path).is_absolute() {
                PathBuf::from(path)
            } else {
                // 相对于应用程序数据目录
                let data_dir = settings.data_dir.clone().unwrap_or_else(|| ".".to_string());
                PathBuf::from(data_dir).join(path)
            }
        });

        Self {
            client_config: DHTClientConfig::from_dht_config(dht_config),
            auto_start: settings.dht_auto_start.unwrap_or(true),
            maintenance_interval: settings.dht_maintenance_interval.unwrap_or(300),
            routing_table_path,
            routing_table_save_interval: settings.dht_routing_table_save_interval.unwrap_or(600),
            load_routing_table: settings.dht_load_routing_table.unwrap_or(true),
        }
    }
}

/// BitTorrent协议的DHT事件监听器
pub struct BitTorrentDHTListener {
    /// 对等点回调
    peers_callback: Arc<Mutex<Box<dyn Fn([u8; 20], Vec<SocketAddr>) -> Result<()> + Send + Sync>>>,
}

impl BitTorrentDHTListener {
    /// 创建新的BitTorrent DHT事件监听器
    pub fn new<F>(callback: F) -> Self
    where
        F: Fn([u8; 20], Vec<SocketAddr>) -> Result<()> + Send + Sync + 'static,
    {
        Self {
            peers_callback: Arc::new(Mutex::new(Box::new(callback))),
        }
    }
}

#[async_trait]
impl DHTEventListener for BitTorrentDHTListener {
    async fn on_event(&self, event: DHTEvent) -> Result<()> {
        match event {
            DHTEvent::PeersFound { info_hash, peers } => {
                if !peers.is_empty() {
                    let callback = self.peers_callback.lock().await;
                    (*callback)(info_hash, peers)?;
                }
            },
            DHTEvent::NodeStateChanged { node_count, bootstrapped } => {
                debug!("DHT node state changed: nodes={}, bootstrapped={}", node_count, bootstrapped);
            },
            DHTEvent::Error { message } => {
                warn!("DHT error: {}", message);
            },
        }

        Ok(())
    }
}

/// DHT服务
pub struct DHTService {
    /// DHT客户端
    client: Arc<Mutex<DHTClient>>,
    /// 配置
    config: DHTServiceConfig,
    /// 是否已初始化
    initialized: bool,
    /// 维护任务句柄
    maintenance_task: Option<tokio::task::JoinHandle<()>>,
}

impl DHTService {
    /// 创建新的DHT服务
    pub async fn new(config: DHTServiceConfig) -> Result<Self> {
        let client = DHTClient::new(config.client_config.clone()).await?;

        Ok(Self {
            client: Arc::new(Mutex::new(client)),
            config,
            initialized: false,
            maintenance_task: None,
        })
    }

    /// 从应用程序设置创建DHT服务
    pub async fn from_settings(settings: &Settings) -> Result<Self> {
        let config = DHTServiceConfig::from_settings(settings);
        Self::new(config).await
    }

    /// 初始化DHT服务
    pub async fn init(&mut self) -> Result<()> {
        if self.initialized {
            return Ok(());
        }

        // 初始化DHT客户端
        {
            let mut client = self.client.lock().await;
            client.init().await?;

            // 如果配置了路由表路径，尝试加载路由表
            if self.config.load_routing_table {
                if let Some(path) = &self.config.routing_table_path {
                    if path.exists() {
                        info!("Loading DHT routing table from {}", path.display());
                        if let Err(e) = client.load_routing_table(path).await {
                            warn!("Failed to load DHT routing table: {}", e);
                        }
                    } else {
                        debug!("DHT routing table file does not exist: {}", path.display());
                    }
                }
            }

            // 如果配置为自动启动，则启动DHT
            if self.config.auto_start {
                client.start().await?;
            }

            // 如果配置了路由表路径，启动定期保存
            if let Some(path) = &self.config.routing_table_path {
                info!("Starting periodic DHT routing table save to {}", path.display());
                if let Err(e) = client.start_periodic_save(path.clone(), self.config.routing_table_save_interval).await {
                    warn!("Failed to start periodic DHT routing table save: {}", e);
                }
            }
        }

        // 启动维护任务
        self.start_maintenance_task();

        self.initialized = true;
        Ok(())
    }

    /// 启动维护任务
    fn start_maintenance_task(&mut self) {
        let client = self.client.clone();
        let interval = self.config.maintenance_interval;

        self.maintenance_task = Some(tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(Duration::from_secs(interval));

            loop {
                interval_timer.tick().await;

                // 执行维护任务
                if let Err(e) = Self::perform_maintenance(&client).await {
                    warn!("Error performing DHT maintenance: {}", e);
                }
            }
        }));
    }

    /// 执行维护任务
    async fn perform_maintenance(client: &Arc<Mutex<DHTClient>>) -> Result<()> {
        let mut client = client.lock().await;

        // 获取DHT状态
        let status = client.get_status().await?;

        // 如果DHT未运行，尝试启动它
        if !status.running && status.initialized {
            client.start().await?;
        }
        
        // FIXME: 这里可扩展更多维护任务，如刷新路由表等。
        // 刷新路由表

        Ok(())
    }

    /// 启动DHT服务
    pub async fn start(&mut self) -> Result<()> {
        if !self.initialized {
            return Err(anyhow::anyhow!("DHT service not initialized"));
        }

        let mut client = self.client.lock().await;
        client.start().await
    }

    /// 停止DHT服务
    pub async fn stop(&mut self) -> Result<()> {
        if !self.initialized {
            return Ok(());
        }

        let mut client = self.client.lock().await;
        client.stop().await
    }

    /// 添加BitTorrent协议的DHT事件监听器
    pub async fn add_bittorrent_listener<F>(&self, callback: F) -> Result<()>
    where
        F: Fn([u8; 20], Vec<SocketAddr>) -> Result<()> + Send + Sync + 'static,
    {
        let listener = Arc::new(BitTorrentDHTListener::new(callback));
        let mut client = self.client.lock().await;
        client.add_event_listener(listener).await?;
        Ok(())
    }

    /// 查找拥有特定info_hash的对等点
    pub async fn get_peers(&self, info_hash: [u8; 20]) -> Result<Vec<SocketAddr>> {
        let client = self.client.lock().await;
        client.get_peers(info_hash).await
    }

    /// 宣布自己拥有特定info_hash
    pub async fn announce_peer(&self, info_hash: [u8; 20], port: u16) -> Result<()> {
        let client = self.client.lock().await;
        client.announce_peer(info_hash, port).await
    }

    /// 添加事件监听器
    pub async fn add_event_listener(&mut self, listener: Arc<dyn DHTEventListener>) -> Result<()> {
        let mut client = self.client.lock().await;
        client.add_event_listener(listener).await
    }

    /// 移除事件监听器
    pub async fn remove_event_listener(&mut self, listener_id: usize) -> Result<()> {
        let mut client = self.client.lock().await;
        client.remove_event_listener(listener_id).await
    }

    /// 获取DHT状态
    pub async fn get_status(&self) -> Result<DHTStatus> {
        let client = self.client.lock().await;
        client.get_status().await
    }
    
    /// 添加引导节点到DHT客户端配置
    pub async fn add_bootstrap_node(&mut self, addr: SocketAddr) -> Result<()> {
        // 获取DHT客户端
        let mut client = self.client.lock().await;
        
        // 获取当前配置
        let mut config = client.get_config();
        
        // 添加引导节点
        config.add_bootstrap_node(addr);
        
        // 更新配置
        client.update_config(config);
        
        Ok(())
    }
}

impl Drop for DHTService {
    fn drop(&mut self) {
        // 取消维护任务
        if let Some(task) = self.maintenance_task.take() {
            task.abort();
        }
    }
}
