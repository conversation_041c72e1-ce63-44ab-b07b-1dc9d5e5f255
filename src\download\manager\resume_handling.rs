use anyhow::{Result, anyhow};
use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, error};
use uuid::Uuid;

use crate::config::Settings;
use crate::download::resume::{ResumeManager, ResumePoint};
use super::models::TaskInfo;

/// 恢复点处理器，负责管理下载任务的恢复点
pub struct ResumeHandler {
    settings: Settings,
    resume_manager: Arc<dyn ResumeManager>,
    tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
}

impl ResumeHandler {
    /// 创建一个新的恢复点处理器
    pub fn new(
        settings: Settings,
        resume_manager: Arc<dyn ResumeManager>,
        tasks: Arc<RwLock<HashMap<Uuid, TaskInfo>>>,
    ) -> Self {
        Self {
            settings,
            resume_manager,
            tasks,
        }
    }

    /// 初始化恢复管理器并加载所有恢复点
    pub async fn initialize(&self) -> Result<()> {
        // 初始化恢复管理器
        if let Err(e) = self.resume_manager.get_all_resume_points().await {
            error!("Failed to initialize resume manager: {}", e);
            error!("Download tasks may not be saved or restored properly");
            
            // 尝试创建恢复目录，确保至少目录存在
            let resume_dir = Path::new(&self.settings.download.path).join(".resume");
            if let Err(dir_err) = std::fs::create_dir_all(&resume_dir) {
                error!("Failed to create resume directory: {}", dir_err);
            } else {
                info!("Created resume directory: {:?}", resume_dir);
            }
            
            return Err(anyhow!("Failed to initialize resume manager: {}", e));
        }
        
        info!("Resume manager initialized successfully");
        
        // 加载所有恢复点
        self.load_all_resume_points().await?;
        
        Ok(())
    }

    /// 加载所有恢复点并转换为任务
    async fn load_all_resume_points(&self) -> Result<()> {
        // 从恢复管理器获取所有恢复点
        let resume_points = match self.resume_manager.get_all_resume_points().await {
            Ok(points) => points,
            Err(e) => {
                error!("Failed to load resume points: {}", e);
                return Err(anyhow!("Failed to load resume points: {}", e));
            }
        };
        
        // 将恢复点转换为任务并添加到任务列表
        let mut tasks_map = HashMap::new();
        for point in resume_points {
            let task = self.convert_resume_point_to_task(&point);
            tasks_map.insert(task.id, task);
        }
        
        // 更新任务列表
        let mut tasks_lock = self.tasks.write().await;
        *tasks_lock = tasks_map;
        info!("Loaded {} tasks from resume points", tasks_lock.len());
        
        Ok(())
    }

    /// 将恢复点转换为任务
    fn convert_resume_point_to_task(&self, point: &ResumePoint) -> TaskInfo {
        use super::models::TaskStatus;
        use chrono::Utc;
        
        TaskInfo {
            id: point.task_id,
            url: point.url.clone(),
            output_path: point.output_path.clone(),
            status: TaskStatus::Pending, // 假设任务是暂停的
            progress: 0.0,
            speed: 0,
            downloaded_size: point.downloaded_size,
            total_size: point.total_size,
            uploaded_bytes: point.metadata.get("uploaded_bytes")
                .and_then(|v| v.parse::<u64>().ok())
                .unwrap_or(0),
            created_at: point.created_at,
            updated_at: point.updated_at,
            error_message: None,
        }
    }

    /// 保存任务到恢复点
    pub async fn save_task(&self, task: &TaskInfo) -> Result<()> {
        if let Err(e) = self.resume_manager.save_resume_point(task).await {
            error!("Failed to save task to resume manager: {}", e);
            // 尝试再次保存，确保任务信息被持久化
            if let Err(retry_err) = self.resume_manager.save_resume_point(task).await {
                error!("Second attempt to save task failed: {}", retry_err);
                return Err(anyhow!("Failed to save task to resume manager: {}", retry_err));
            } else {
                info!("Task saved successfully on second attempt");
            }
        }
        
        Ok(())
    }

    /// 从恢复点加载任务
    pub async fn load_task(&self, task_id: Uuid) -> Result<Option<TaskInfo>> {
        match self.resume_manager.load_resume_point(task_id).await {
            Ok(Some(resume_point)) => {
                let task = self.convert_resume_point_to_task(&resume_point);
                Ok(Some(task))
            },
            Ok(None) => Ok(None),
            Err(e) => {
                error!("Failed to load task from resume manager: {}", e);
                Err(anyhow!("Failed to load task from resume manager: {}", e))
            }
        }
    }

    /// 删除任务的恢复点
    pub async fn delete_task(&self, task_id: Uuid) -> Result<()> {
        if let Err(e) = self.resume_manager.delete_resume_point(task_id).await {
            error!("Failed to delete task from resume manager: {}", e);
            return Err(anyhow!("Failed to delete task from resume manager: {}", e));
        }
        
        Ok(())
    }
}