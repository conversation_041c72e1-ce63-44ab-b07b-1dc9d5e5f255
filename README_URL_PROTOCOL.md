# Lumen 下载管理器 URL 协议处理功能

## 概述

URL 协议处理功能允许用户通过点击网页上的链接或在浏览器地址栏输入特定格式的 URL，将下载任务直接发送到 Lumen 下载管理器。这种功能大大提升了用户体验，简化了下载流程。

## 功能特点

- **一键下载**：用户可以通过点击网页上的链接直接将下载任务发送到 Lumen 下载管理器
- **浏览器集成**：通过浏览器扩展，用户可以右键点击链接，选择「使用 Lumen 下载」选项
- **多平台支持**：支持 Windows、macOS 和 Linux 平台
- **灵活配置**：支持通过 URL 参数或自定义协议两种方式传递下载链接

## 实现方式

### 前端实现

前端已经实现了通过 URL 参数接收下载链接的功能。在 `App.vue` 中，我们添加了 `parseUrlParams` 函数，该函数在应用初始化时执行，检查 URL 参数中是否包含下载链接，如果有则自动填充到添加任务对话框中。

### 自定义协议注册

为了支持通过自定义协议（如 `lumen://`）启动应用，我们提供了不同平台的注册方法：

- **Windows**：使用注册表配置（见 `tools/register_protocol_windows.reg`）
- **macOS**：通过修改应用的 `Info.plist` 文件
- **Linux**：使用 `.desktop` 文件和 `xdg-mime` 命令（见 `tools/lumen.desktop` 和 `tools/register_protocol_linux.sh`）

### 示例代码

我们提供了多种实现示例：

- **Electron 应用**：`examples/electron/` 目录包含了 Electron 应用中处理自定义协议的示例代码
- **Neutralino 应用**：`examples/neutralino/` 目录包含了 Neutralino 应用中处理自定义协议的示例代码
- **浏览器扩展**：`examples/browser_extension/` 目录包含了浏览器扩展的示例代码，用于在浏览器中添加右键菜单

## 使用方法

### 通过 URL 参数

用户可以通过在浏览器中访问带有 `url` 参数的应用 URL 来添加下载任务：

```
http://localhost:9080/?url=https://example.com/file.zip
```

### 通过自定义协议

用户可以通过点击网页上的自定义协议链接或在浏览器地址栏输入自定义协议 URL 来添加下载任务：

```
lumen://download?url=https://example.com/file.zip
```

或者简化形式：

```
lumen://https://example.com/file.zip
```

### 通过浏览器扩展

1. 安装 Lumen 下载助手浏览器扩展
2. 右键点击网页上的链接
3. 选择「使用 Lumen 下载」选项

## 安装配置

### Windows 平台

1. 双击 `tools/register_protocol_windows.reg` 文件，将配置添加到注册表中
2. 在弹出的确认对话框中点击「是」

### macOS 平台

在应用打包时，确保在 `Info.plist` 文件中添加以下内容：

```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLName</key>
    <string>com.lumen.downloader</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>lumen</string>
    </array>
  </dict>
</array>
```

### Linux 平台

1. 确保 `tools/lumen.desktop` 文件中的路径正确指向应用可执行文件
2. 以 root 权限运行 `tools/register_protocol_linux.sh` 脚本：

```bash
sudo bash tools/register_protocol_linux.sh
```

## 浏览器扩展安装

### Chrome / Edge

1. 打开扩展管理页面（chrome://extensions 或 edge://extensions）
2. 启用「开发者模式」
3. 点击「加载已解压的扩展程序」
4. 选择 `examples/browser_extension` 目录

### Firefox

1. 打开 `about:debugging` 页面
2. 点击「此 Firefox」
3. 点击「临时载入附加组件」
4. 选择 `examples/browser_extension/manifest.json` 文件

## 注意事项

1. 自定义协议处理需要在应用打包和安装过程中配置，不同平台的配置方式有所不同
2. 在使用自定义协议时，需要确保 URL 中的特殊字符被正确编码
3. 为了安全考虑，应该验证传入的 URL 是否合法，避免潜在的安全风险

## 未来改进

1. 支持批量下载链接
2. 支持更多参数，如下载目录、文件名等
3. 提供更完善的浏览器扩展，支持更多功能
4. 添加更多平台的安装脚本和文档