use anyhow::Result;
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::Mutex;
use tracing::{debug, warn};

use crate::core::p2p::piece::PieceManager;
use crate::protocols::bittorrent::message::BitTorrentMessage;
use crate::protocols::bittorrent::BitTorrentPeer;
use crate::protocols::bittorrent::piece_selector::PieceSelector;

/// 分片请求管理器，负责处理分片请求
#[derive(Clone)]
pub struct PieceRequestManager {
    /// 分片选择器
    piece_selector: Arc<Mutex<PieceSelector>>,
}

impl PieceRequestManager {
    /// 创建一个新的分片请求管理器
    pub fn new(piece_selector: Arc<Mutex<PieceSelector>>) -> Self {
        Self {
            piece_selector,
        }
    }
    
    /// 设置分片选择器
    pub fn set_piece_selector(&mut self, piece_selector: Arc<Mutex<PieceSelector>>) {
        self.piece_selector = piece_selector;
    }

    /// 请求分片
    pub async fn request_pieces(
        &self,
        peer: &mut BitTorrentPeer,
        addr: &str,
        piece_manager: &Arc<Mutex<dyn PieceManager>>,
        peers_to_remove: &mut Vec<String>
    ) -> Result<()> {
        // 记录请求开始时间
        let request_start = Instant::now();

        // 获取对等点地址
        let peer_addr = peer.connection.common.peer_info.addr;

        // 获取分片选择器
        let piece_selector = self.piece_selector.lock().await;

        // 获取分片管理器
        let mut piece_manager = piece_manager.lock().await;

        // 获取对等点拥有的分片（克隆以避免借用冲突）
        let peer_pieces_clone = peer.state.pieces_have.clone();

        // 获取分片状态和稀有度
        let piece_states = match piece_manager.all_piece_states().await {
            Ok(states) => states,
            Err(e) => {
                warn!("Failed to get piece states: {}", e);
                return Ok(());
            }
        };

        let piece_rarities = match piece_manager.get_piece_rarities().await {
            Ok(rarities) => rarities,
            Err(e) => {
                warn!("Failed to get piece rarities: {}", e);
                return Ok(());
            }
        };

        // 获取已请求的分片
        let requested_pieces = match piece_manager.get_requested_pieces().await {
            Ok(pieces) => pieces,
            Err(e) => {
                warn!("Failed to get requested pieces: {}", e);
                return Ok(());
            }
        };

        // 如果对等点没有分片或者被阻塞，则不请求
        if peer_pieces_clone.is_empty() || peer.state.peer_choking {
            return Ok(());
        }

        // 计算可以请求的最大块数
        let max_blocks = if peer.state.handshake_handler.supports_fast {
            // 如果支持Fast扩展，可以请求更多块
            50
        } else {
            // 否则限制请求数量
            5
        };

        // 计算当前已请求但未收到的块数
        let pending_blocks = peer.state.requested_blocks.len();

        // 如果已经有足够的请求，则不再请求
        if pending_blocks >= max_blocks {
            return Ok(());
        }

        // 计算需要请求的块数
        let blocks_to_request = max_blocks - pending_blocks;

        // 选择要请求的分片
        for _ in 0..blocks_to_request {
            // 选择下一个要请求的分片
            let next_piece = piece_selector.select_next_piece_for_peer(
                &peer_addr,
                &peer_pieces_clone,
                &piece_states,
                &requested_pieces
            );

            if let Some(piece_index) = next_piece {
                // 标记分片为已请求
                if let Err(e) = piece_manager.mark_piece_requested(piece_index).await {
                    warn!("Failed to mark piece as requested: {}", e);
                    continue;
                }

                // 获取分片信息
                let piece_info = match piece_manager.get_piece_info(piece_index).await {
                    Ok(Some(info)) => info,
                    Ok(None) => {
                        warn!("Piece info not found for index={}", piece_index);
                        continue;
                    },
                    Err(e) => {
                        warn!("Failed to get piece info: {}", e);
                        continue;
                    }
                };

                // 计算块大小和数量
                let block_size = 16384; // 16 KB
                let piece_size = piece_info.size as usize;
                let num_blocks = (piece_size + block_size - 1) / block_size;

                // 请求分片的所有块
                for i in 0..num_blocks {
                    let offset = i * block_size;
                    let size = if i == num_blocks - 1 && piece_size % block_size != 0 {
                        piece_size % block_size
                    } else {
                        block_size
                    };

                    // 创建请求消息
                    let request = BitTorrentMessage::Request(
                        piece_index,
                        offset as u32,
                        size as u32
                    );

                    // 发送请求
                    match peer.send_bt_message(request).await {
                        Ok(_) => {
                            // 记录请求的块
                            peer.state.requested_blocks.insert((piece_index, offset as u32));
                            debug!("Requested piece {} block {} from {}", piece_index, i, addr);
                        },
                        Err(e) => {
                            warn!("Failed to send request to {}: {}", addr, e);
                            peers_to_remove.push(addr.to_string());
                            break;
                        }
                    }
                }
            } else {
                // 没有更多可请求的分片
                break;
            }
        }

        // 计算请求耗时
        let request_time = request_start.elapsed().as_millis() as u64;
        debug!("Piece request took {}ms", request_time);

        Ok(())
    }
}