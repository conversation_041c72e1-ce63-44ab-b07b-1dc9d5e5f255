// BitTorrent 扩展协议模块
// 实现 BEP 10 扩展协议框架和各种扩展

pub mod extension_protocol;
pub mod message;
pub mod metadata;
pub mod pex;
pub mod extension_message_handler;
pub mod magnet;

// 重新导出主要类型
pub use extension_protocol::{ExtensionProtocol, ExtensionHandler};
pub use message::{ExtensionMessage, ExtensionMessageType, ExtensionHandshake};
pub use pex::{PEXExtension, PEXMessage};
pub use metadata::{MetadataExtension, MetadataMessage, MetadataMessageType};
pub use magnet::{MagnetLinkParser, MagnetLinkInfo};
