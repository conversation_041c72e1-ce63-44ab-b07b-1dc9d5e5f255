use std::net::{Ip<PERSON>ddr, SocketAddr};
use anyhow::{Result, anyhow};
use rand::Rng;

/// STUN消息类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum StunMessageType {
    /// 绑定请求
    BindingRequest,
    /// 绑定响应
    BindingResponse,
    /// 绑定错误响应
    BindingErrorResponse,
    /// 未知类型
    Unknown,
}

/// STUN属性类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum StunAttributeType {
    /// 映射地址
    MappedAddress = 0x0001,
    /// 响应地址
    ResponseAddress = 0x0002,
    /// 变更请求
    ChangeRequest = 0x0003,
    /// 源地址
    SourceAddress = 0x0004,
    /// 变更地址
    ChangedAddress = 0x0005,
    /// 用户名
    Username = 0x0006,
    /// 密码
    Password = 0x0007,
    /// 消息完整性
    MessageIntegrity = 0x0008,
    /// 错误码
    ErrorCode = 0x0009,
    /// 未知属性
    UnknownAttributes = 0x000A,
    /// 反射源地址
    ReflectedFrom = 0x000B,
    /// XOR映射地址
    XorMappedAddress = 0x0020,
    /// 软件
    Software = 0x8022,
    /// 备用服务器
    AlternateServer = 0x8023,
    /// 指纹
    Fingerprint = 0x8028,
}

/// STUN属性
#[derive(Debug, Clone)]
pub struct StunAttribute {
    /// 属性类型
    pub attribute_type: StunAttributeType,
    /// 属性值
    pub value: Vec<u8>,
}

/// STUN消息
#[derive(Debug, Clone)]
pub struct StunMessage {
    /// 消息类型
    pub message_type: StunMessageType,
    /// 事务ID
    pub transaction_id: [u8; 12],
    /// 属性列表
    pub attributes: Vec<StunAttribute>,
}

impl StunMessage {
    /// 创建新的绑定请求
    pub fn new_binding_request() -> Self {
        let mut transaction_id = [0u8; 12];
        rand::thread_rng().fill(&mut transaction_id);

        Self {
            message_type: StunMessageType::BindingRequest,
            transaction_id,
            attributes: Vec::new(),
        }
    }

    /// 创建带有变更请求的绑定请求
    pub fn new_binding_request_with_change(change_ip: bool, change_port: bool) -> Self {
        let mut request = Self::new_binding_request();

        // 创建变更请求属性
        let mut value = vec![0, 0, 0, 0];

        // 设置变更标志
        if change_ip {
            value[3] |= 0x04; // 设置变更IP标志
        }
        if change_port {
            value[3] |= 0x02; // 设置变更端口标志
        }

        // 添加变更请求属性
        request.attributes.push(StunAttribute {
            attribute_type: StunAttributeType::ChangeRequest,
            value,
        });

        request
    }

    /// 编码STUN消息
    pub fn encode(&self) -> Result<Vec<u8>> {
        let mut buf = Vec::with_capacity(20 + self.attributes.len() * 8);

        // 消息类型
        let type_val = match self.message_type {
            StunMessageType::BindingRequest => 0x0001,
            StunMessageType::BindingResponse => 0x0101,
            StunMessageType::BindingErrorResponse => 0x0111,
            StunMessageType::Unknown => return Err(anyhow!("Cannot encode unknown message type")),
        };
        buf.extend_from_slice(&type_val.to_be_bytes()[2..]);

        // 消息长度（占位）
        buf.extend_from_slice(&[0, 0]);

        // 魔术字符串
        buf.extend_from_slice(&[0x21, 0x12, 0xA4, 0x42]);

        // 事务ID
        buf.extend_from_slice(&self.transaction_id);

        // 属性
        for attr in &self.attributes {
            // 属性类型
            buf.extend_from_slice(&(attr.attribute_type as u16).to_be_bytes());

            // 属性长度
            buf.extend_from_slice(&(attr.value.len() as u16).to_be_bytes());

            // 属性值
            buf.extend_from_slice(&attr.value);

            // 填充到4字节边界
            let padding = (4 - (attr.value.len() % 4)) % 4;
            buf.extend_from_slice(&vec![0; padding]);
        }

        // 更新消息长度
        let msg_len = (buf.len() - 20) as u16;
        buf[2..4].copy_from_slice(&msg_len.to_be_bytes());

        Ok(buf)
    }

    /// 解码STUN消息
    pub fn decode(data: &[u8]) -> Result<Self> {
        if data.len() < 20 {
            return Err(anyhow!("STUN message too short"));
        }

        // 检查魔术字符串
        if data[4..8] != [0x21, 0x12, 0xA4, 0x42] {
            return Err(anyhow!("Invalid STUN magic cookie"));
        }

        // 解析消息类型
        let type_val = u16::from_be_bytes([data[0], data[1]]);
        let message_type = match type_val {
            0x0001 => StunMessageType::BindingRequest,
            0x0101 => StunMessageType::BindingResponse,
            0x0111 => StunMessageType::BindingErrorResponse,
            _ => StunMessageType::Unknown,
        };

        // 解析事务ID
        let mut transaction_id = [0u8; 12];
        transaction_id.copy_from_slice(&data[8..20]);

        // 解析消息长度
        let msg_len = u16::from_be_bytes([data[2], data[3]]) as usize;

        // 解析属性
        let mut attributes = Vec::new();
        let mut pos = 20;

        while pos + 4 <= data.len() && pos - 20 < msg_len {
            if pos + 4 > data.len() {
                break;
            }

            // 属性类型
            let attr_type = u16::from_be_bytes([data[pos], data[pos + 1]]);
            let attribute_type = match attr_type {
                0x0001 => StunAttributeType::MappedAddress,
                0x0002 => StunAttributeType::ResponseAddress,
                0x0003 => StunAttributeType::ChangeRequest,
                0x0004 => StunAttributeType::SourceAddress,
                0x0005 => StunAttributeType::ChangedAddress,
                0x0006 => StunAttributeType::Username,
                0x0007 => StunAttributeType::Password,
                0x0008 => StunAttributeType::MessageIntegrity,
                0x0009 => StunAttributeType::ErrorCode,
                0x000A => StunAttributeType::UnknownAttributes,
                0x000B => StunAttributeType::ReflectedFrom,
                0x0020 => StunAttributeType::XorMappedAddress,
                0x8022 => StunAttributeType::Software,
                0x8023 => StunAttributeType::AlternateServer,
                0x8028 => StunAttributeType::Fingerprint,
                _ => continue, // 跳过未知属性
            };

            // 属性长度
            let attr_len = u16::from_be_bytes([data[pos + 2], data[pos + 3]]) as usize;

            if pos + 4 + attr_len > data.len() {
                break;
            }

            // 属性值
            let value = data[pos + 4..pos + 4 + attr_len].to_vec();

            attributes.push(StunAttribute {
                attribute_type,
                value,
            });

            // 移动到下一个属性（考虑4字节对齐）
            pos += 4 + ((attr_len + 3) & !3);
        }

        Ok(Self {
            message_type,
            transaction_id,
            attributes,
        })
    }

    /// 获取映射地址
    pub fn get_mapped_address(&self) -> Result<SocketAddr> {
        // 首先尝试XOR映射地址
        for attr in &self.attributes {
            if attr.attribute_type == StunAttributeType::XorMappedAddress {
                return Self::parse_xor_mapped_address(&attr.value, &self.transaction_id);
            }
        }

        // 然后尝试普通映射地址
        for attr in &self.attributes {
            if attr.attribute_type == StunAttributeType::MappedAddress {
                return Self::parse_mapped_address(&attr.value);
            }
        }

        Err(anyhow!("No mapped address found in STUN response"))
    }

    /// 解析映射地址
    fn parse_mapped_address(data: &[u8]) -> Result<SocketAddr> {
        if data.len() < 8 {
            return Err(anyhow!("Invalid mapped address attribute"));
        }

        let family = data[1];
        let port = u16::from_be_bytes([data[2], data[3]]);

        match family {
            1 => { // IPv4
                if data.len() < 8 {
                    return Err(anyhow!("Invalid IPv4 mapped address"));
                }
                let ip = IpAddr::from([data[4], data[5], data[6], data[7]]);
                Ok(SocketAddr::new(ip, port))
            },
            2 => { // IPv6
                if data.len() < 20 {
                    return Err(anyhow!("Invalid IPv6 mapped address"));
                }
                let mut ip_bytes = [0u8; 16];
                ip_bytes.copy_from_slice(&data[4..20]);
                let ip = IpAddr::from(ip_bytes);
                Ok(SocketAddr::new(ip, port))
            },
            _ => Err(anyhow!("Unsupported address family")),
        }
    }

    /// 解析XOR映射地址
    fn parse_xor_mapped_address(data: &[u8], transaction_id: &[u8; 12]) -> Result<SocketAddr> {
        if data.len() < 8 {
            return Err(anyhow!("Invalid XOR mapped address attribute"));
        }

        let family = data[1];
        let xor_port = u16::from_be_bytes([data[2], data[3]]);
        let port = xor_port ^ 0x2112; // XOR with first 2 bytes of magic cookie

        match family {
            1 => { // IPv4
                if data.len() < 8 {
                    return Err(anyhow!("Invalid IPv4 XOR mapped address"));
                }
                let magic_cookie = [0x21, 0x12, 0xA4, 0x42];
                let mut ip_bytes = [0u8; 4];
                for i in 0..4 {
                    ip_bytes[i] = data[4 + i] ^ magic_cookie[i];
                }
                let ip = IpAddr::from(ip_bytes);
                Ok(SocketAddr::new(ip, port))
            },
            2 => { // IPv6
                if data.len() < 20 {
                    return Err(anyhow!("Invalid IPv6 XOR mapped address"));
                }
                let magic_cookie = [0x21, 0x12, 0xA4, 0x42];
                let mut ip_bytes = [0u8; 16];
                for i in 0..4 {
                    ip_bytes[i] = data[4 + i] ^ magic_cookie[i];
                }
                for i in 0..12 {
                    ip_bytes[4 + i] = data[8 + i] ^ transaction_id[i];
                }
                let ip = IpAddr::from(ip_bytes);
                Ok(SocketAddr::new(ip, port))
            },
            _ => Err(anyhow!("Unsupported address family")),
        }
    }
}
