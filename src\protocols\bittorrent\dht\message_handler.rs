use std::net::SocketAddr;
use std::sync::Arc;
use anyhow::Result;
use tokio::net::UdpSocket;
use tokio::sync::{RwLock, Mutex};
use tracing::debug;

use super::node::{DHTNode, NodeId};
use super::routing::RoutingTable;
use super::message::{DHTMessage, DHTMessageType, MessageCodec, TokenManager, DHTResponse};
use super::query::{QueryManager};
use super::event::{DHTEvent, DHTEventDispatcher};

/// DHT消息处理器
pub struct DHTMessageHandler {
    /// 节点ID
    node_id: NodeId,
    /// UDP套接字
    socket: Arc<UdpSocket>,
    /// 路由表
    routing_table: Arc<RwLock<RoutingTable>>,
    /// 查询管理器
    query_manager: Arc<RwLock<QueryManager>>,
    /// 令牌管理器
    token_manager: Arc<Mutex<TokenManager>>,
    /// 事件分发器
    event_dispatcher: Arc<DHTEventDispatcher>,
    /// 发现的对等点计数
    discovered_peers_count: Arc<RwLock<usize>>,
}

impl DHTMessageHandler {
    /// 创建新的DHT消息处理器
    pub fn new(
        node_id: NodeId,
        socket: Arc<UdpSocket>,
        routing_table: Arc<RwLock<RoutingTable>>,
        query_manager: Arc<RwLock<QueryManager>>,
        token_manager: Arc<Mutex<TokenManager>>,
        event_dispatcher: Arc<DHTEventDispatcher>,
        discovered_peers_count: Arc<RwLock<usize>>,
    ) -> Self {
        Self {
            node_id,
            socket,
            routing_table,
            query_manager,
            token_manager,
            event_dispatcher,
            discovered_peers_count,
        }
    }

    /// 处理接收到的DHT消息
    pub async fn handle_message(&self, data: &[u8], addr: SocketAddr) -> Result<()> {
        // 尝试解码消息
        let message = match MessageCodec::decode(data) {
            Ok(msg) => msg,
            Err(e) => {
                debug!("Failed to decode DHT message: {}", e);
                return Ok(());
            }
        };

        // 更新路由表
        let sender_node = DHTNode::new(message.sender_id, addr.ip(), addr.port());
        {
            let mut table = self.routing_table.write().await;
            table.add_node(sender_node.clone());
        }

        // 处理不同类型的消息
        match message.message_type {
            DHTMessageType::Ping => {
                // 响应Ping请求
                let response = DHTMessage {
                    message_type: DHTMessageType::Response,
                    transaction_id: message.transaction_id,
                    sender_id: self.node_id,
                    target_id: None,
                    nodes: None,
                    peers: None,
                    token: None,
                    port: None,
                    error_code: None,
                    error_message: None,
                };

                let encoded = MessageCodec::encode(&response)?;
                self.socket.send_to(&encoded, addr).await?;
            },
            DHTMessageType::FindNode => {
                // 处理查找节点请求
                if let Some(target_id) = message.target_id {
                    // 查找最接近目标ID的节点
                    let closest_nodes = {
                        let table = self.routing_table.read().await;
                        table.get_closest_nodes(&target_id, 8)
                    };

                    // 响应查找节点请求
                    let response = DHTMessage {
                        message_type: DHTMessageType::Response,
                        transaction_id: message.transaction_id,
                        sender_id: self.node_id,
                        target_id: None,
                        nodes: Some(closest_nodes),
                        peers: None,
                        token: None,
                        port: None,
                        error_code: None,
                        error_message: None,
                    };

                    let encoded = MessageCodec::encode(&response)?;
                    self.socket.send_to(&encoded, addr).await?;
                }
            },
            DHTMessageType::GetPeers => {
                // 处理获取对等点请求
                if let Some(info_hash) = message.target_id {
                    // 生成令牌
                    let token = {
                        let token_manager = self.token_manager.lock().await;
                        token_manager.generate_token(&addr)
                    };

                    // 查找最接近info_hash的节点
                    let closest_nodes = {
                        let table = self.routing_table.read().await;
                        table.get_closest_nodes(&info_hash, 8)
                    };

                    // 响应获取对等点请求
                    let response = DHTMessage {
                        message_type: DHTMessageType::Response,
                        transaction_id: message.transaction_id,
                        sender_id: self.node_id,
                        target_id: None,
                        nodes: Some(closest_nodes),
                        peers: None, // 我们没有对等点信息
                        token: Some(token),
                        port: None,
                        error_code: None,
                        error_message: None,
                    };

                    let encoded = MessageCodec::encode(&response)?;
                    self.socket.send_to(&encoded, addr).await?;
                }
            },
            DHTMessageType::AnnouncePeer => {
                // 处理宣布对等点请求
                if let (Some(info_hash), Some(token), Some(port)) = (message.target_id, message.token, message.port) {
                    // 验证令牌
                    let is_valid = {
                        let token_manager = self.token_manager.lock().await;
                        token_manager.verify_token(&token, &addr)
                    };

                    if is_valid {
                        // 令牌有效，记录对等点
                        let peer_addr = SocketAddr::new(addr.ip(), port);

                        // 发送对等点发现事件
                        let event = DHTEvent::PeersFound {
                            info_hash: info_hash.as_bytes().to_vec().try_into().unwrap(),
                            peers: vec![peer_addr],
                        };

                        // 更新发现的对等点计数
                        {
                            let mut count = self.discovered_peers_count.write().await;
                            *count += 1;
                        }

                        // 发送事件
                        self.event_dispatcher.dispatch(event).await?;

                        // 响应宣布对等点请求
                        let response = DHTMessage {
                            message_type: DHTMessageType::Response,
                            transaction_id: message.transaction_id,
                            sender_id: self.node_id,
                            target_id: None,
                            nodes: None,
                            peers: None,
                            token: None,
                            port: None,
                            error_code: None,
                            error_message: None,
                        };

                        let encoded = MessageCodec::encode(&response)?;
                        self.socket.send_to(&encoded, addr).await?;
                    }
                }
            },
            DHTMessageType::Response => {
                // 处理响应消息
                let mut query_manager = self.query_manager.write().await;
                if let Some(query) = query_manager.get_query_mut(&message.transaction_id) {
                    // 更新查询状态
                    query.add_responded_node(message.sender_id);

                    // 处理节点列表
                    if let Some(nodes) = &message.nodes {
                        for node in nodes {
                            query.add_found_node(node.clone());

                            // 更新路由表
                            let mut table = self.routing_table.write().await;
                            table.add_node(node.clone());
                        }
                    }

                    // 处理对等点列表
                    if let Some(peers) = &message.peers {
                        for peer in peers {
                            query.add_found_peer(*peer);
                        }

                        // 如果是获取对等点查询，发送对等点发现事件
                        if query.query_type == DHTMessageType::GetPeers {
                            let event = DHTEvent::PeersFound {
                                info_hash: query.target_id.as_bytes().to_vec().try_into().unwrap(),
                                peers: peers.clone(),
                            };

                            // 更新发现的对等点计数
                            {
                                let mut count = self.discovered_peers_count.write().await;
                                *count += peers.len();
                            }

                            // 发送事件
                            self.event_dispatcher.dispatch(event).await?;
                        }
                    }

                    // 创建响应对象
                    if let Ok(response) = DHTResponse::from_message(&message) {
                        // 发送响应到通道
                        let _ = query_manager.send_response(&message.transaction_id, Ok(response));
                    }
                }
            },
            DHTMessageType::Error => {
                // 处理错误消息
                debug!("Received DHT error: code={:?}, message={:?}", message.error_code, message.error_message);

                // 尝试查找对应的查询
                let mut query_manager = self.query_manager.write().await;
                if let Some(response) = DHTResponse::from_message(&message).ok() {
                    // 发送错误响应到通道
                    let _ = query_manager.send_response(&message.transaction_id, Ok(response));
                }
            },
        }

        Ok(())
    }
}
