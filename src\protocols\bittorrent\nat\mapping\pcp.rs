use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr};
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow, Context};
use tokio::net::UdpSocket;
use tokio::sync::{Mutex, RwLock};
use tokio::time::timeout;
use tracing::{debug, info, warn, error};
use rand::Rng;

use super::{Protocol, MappingInfo, PortMapper};

/// PCP常量
const PCP_PORT: u16 = 5351;
const PCP_VERSION: u8 = 2;
const PCP_OP_ANNOUNCE: u8 = 0;
const PCP_OP_MAP: u8 = 1;
const PCP_OP_PEER: u8 = 2;
const PCP_RESULT_SUCCESS: u8 = 0;
const PCP_INITIAL_TIMEOUT: u64 = 250; // 毫秒
const PCP_MAX_RETRIES: u32 = 9;       // 增加重试次数以提高成功率
const PCP_MAX_LIFETIME: u32 = 7200;   // 最大映射生存时间（秒）
const PCP_RECOMMENDED_LIFETIME: u32 = 3600; // 推荐映射生存时间（秒）
const PCP_MIN_LIFETIME: u32 = 600;    // 最小映射生存时间（秒）

/// PCP错误码
const PCP_ERR_UNSUPP_VERSION: u8 = 1;
const PCP_ERR_NOT_AUTHORIZED: u8 = 2;
const PCP_ERR_MALFORMED_REQUEST: u8 = 3;
const PCP_ERR_UNSUPP_OPCODE: u8 = 4;
const PCP_ERR_UNSUPP_OPTION: u8 = 5;
const PCP_ERR_MALFORMED_OPTION: u8 = 6;
const PCP_ERR_NETWORK_FAILURE: u8 = 7;
const PCP_ERR_NO_RESOURCES: u8 = 8;
const PCP_ERR_UNSUPP_PROTOCOL: u8 = 9;
const PCP_ERR_USER_EX_QUOTA: u8 = 10;
const PCP_ERR_CANNOT_PROVIDE_EXTERNAL: u8 = 11;
const PCP_ERR_ADDRESS_MISMATCH: u8 = 12;
const PCP_ERR_EXCESSIVE_REMOTE_PEERS: u8 = 13;

/// 将PCP错误码转换为可读的错误消息
fn pcp_error_message(code: u8) -> &'static str {
    match code {
        PCP_RESULT_SUCCESS => "Success",
        PCP_ERR_UNSUPP_VERSION => "Unsupported protocol version",
        PCP_ERR_NOT_AUTHORIZED => "Not authorized to create mapping",
        PCP_ERR_MALFORMED_REQUEST => "Malformed request",
        PCP_ERR_UNSUPP_OPCODE => "Unsupported operation code",
        PCP_ERR_UNSUPP_OPTION => "Unsupported option",
        PCP_ERR_MALFORMED_OPTION => "Malformed option",
        PCP_ERR_NETWORK_FAILURE => "Network failure",
        PCP_ERR_NO_RESOURCES => "Out of resources, cannot create more mappings",
        PCP_ERR_UNSUPP_PROTOCOL => "Unsupported protocol",
        PCP_ERR_USER_EX_QUOTA => "User exceeded quota",
        PCP_ERR_CANNOT_PROVIDE_EXTERNAL => "Cannot provide external address",
        PCP_ERR_ADDRESS_MISMATCH => "Address mismatch",
        PCP_ERR_EXCESSIVE_REMOTE_PEERS => "Excessive remote peers",
        _ => "Unknown error code",
    }
}

/// PCP映射器
pub struct PCPMapper {
    /// 网关地址
    gateway: Mutex<Option<IpAddr>>,
    /// 外部IP地址
    external_ip: Mutex<Option<IpAddr>>,
    /// 上次发现时间
    last_discovery: Mutex<Option<Instant>>,
    /// 发现超时（秒）
    discovery_timeout: u64,
    /// 客户端标识符
    client_id: [u8; 12],
    /// 活跃映射
    active_mappings: RwLock<HashMap<String, MappingInfo>>,
    /// 上次更新外部IP时间
    last_external_ip_update: Mutex<Option<Instant>>,
    /// 外部IP更新间隔（秒）
    external_ip_update_interval: u64,
    /// 映射更新间隔（秒）
    mapping_refresh_interval: u64,
    /// 是否启用自动刷新
    auto_refresh_enabled: bool,
    /// 是否支持IPv6
    ipv6_supported: bool,
}

impl PCPMapper {
    /// 创建新的PCP映射器
    pub async fn new() -> Result<Self> {
        // 生成随机客户端标识符
        let mut client_id = [0u8; 12];
        rand::thread_rng().fill(&mut client_id);

        let mapper = Self {
            gateway: Mutex::new(None),
            external_ip: Mutex::new(None),
            last_discovery: Mutex::new(None),
            discovery_timeout: 600, // 10分钟
            client_id,
            active_mappings: RwLock::new(HashMap::new()),
            last_external_ip_update: Mutex::new(None),
            external_ip_update_interval: 3600, // 1小时
            mapping_refresh_interval: 1800, // 30分钟
            auto_refresh_enabled: true,
            ipv6_supported: true,
        };

        // 初始化发现网关
        mapper.discover_gateway().await?;

        // 启动自动刷新任务
        mapper.start_auto_refresh().await?;

        Ok(mapper)
    }

    /// 创建新的PCP映射器，带配置
    pub async fn new_with_config(
        discovery_timeout: u64,
        external_ip_update_interval: u64,
        mapping_refresh_interval: u64,
        auto_refresh_enabled: bool,
        ipv6_supported: bool
    ) -> Result<Self> {
        // 生成随机客户端标识符
        let mut client_id = [0u8; 12];
        rand::thread_rng().fill(&mut client_id);

        let mapper = Self {
            gateway: Mutex::new(None),
            external_ip: Mutex::new(None),
            last_discovery: Mutex::new(None),
            discovery_timeout,
            client_id,
            active_mappings: RwLock::new(HashMap::new()),
            last_external_ip_update: Mutex::new(None),
            external_ip_update_interval,
            mapping_refresh_interval,
            auto_refresh_enabled,
            ipv6_supported,
        };

        // 初始化发现网关
        mapper.discover_gateway().await?;

        // 如果启用了自动刷新，启动自动刷新任务
        if auto_refresh_enabled {
            mapper.start_auto_refresh().await?;
        }

        Ok(mapper)
    }

    /// 发现网关
    async fn discover_gateway(&self) -> Result<IpAddr> {
        // 检查缓存
        {
            let gateway = self.gateway.lock().await;
            if let Some(addr) = *gateway {
                return Ok(addr);
            }
        }

        // 检查是否需要重新发现
        {
            let last_discovery = self.last_discovery.lock().await;
            if let Some(time) = *last_discovery {
                if time.elapsed().as_secs() < self.discovery_timeout {
                    let gateway = self.gateway.lock().await;
                    if let Some(addr) = *gateway {
                        return Ok(addr);
                    }
                }
            }
        }

        // 获取默认网关
        debug!("Discovering PCP gateway...");

        // 尝试多种方法查找网关
        let gateway = self.find_gateway().await
            .context("Failed to find PCP gateway")?;

        // 验证网关是否支持PCP
        self.verify_gateway_support(&gateway).await
            .context("Gateway does not support PCP")?;

        // 更新缓存
        {
            let mut gateway_guard = self.gateway.lock().await;
            *gateway_guard = Some(gateway);
        }

        // 更新发现时间
        {
            let mut last_discovery = self.last_discovery.lock().await;
            *last_discovery = Some(Instant::now());
        }

        info!("Discovered PCP gateway: {}", gateway);

        Ok(gateway)
    }

    /// 查找网关
    async fn find_gateway(&self) -> Result<IpAddr> {
        // 方法1: 使用本地IP地址推断网关
        let local_ip = self.detect_local_ip().await?;

        let gateway = match local_ip {
            IpAddr::V4(ipv4) => {
                // 假设网关是本地网络的第一个地址
                let octets = ipv4.octets();
                let gateway = IpAddr::V4(Ipv4Addr::new(octets[0], octets[1], octets[2], 1));

                // 验证网关是否可达
                if self.ping_gateway(&gateway).await {
                    debug!("Found gateway at {} based on local IP {}", gateway, local_ip);
                    return Ok(gateway);
                }

                // 尝试常见的IPv4网关地址
                self.find_ipv4_gateway().await?
            },
            IpAddr::V6(ipv6) => {
                if !self.ipv6_supported {
                    // 如果不支持IPv6，尝试使用IPv4
                    return self.find_ipv4_gateway().await;
                }

                // 对于IPv6，使用链路本地地址作为网关
                if ipv6.is_unicast_link_local() {
                    // 使用fe80::1作为默认网关
                    let gateway = IpAddr::V6(Ipv6Addr::new(0xfe80, 0, 0, 0, 0, 0, 0, 1));

                    // 验证网关是否可达
                    if self.ping_gateway(&gateway).await {
                        debug!("Found IPv6 link-local gateway at {}", gateway);
                        return Ok(gateway);
                    }
                }

                // 尝试其他常见的IPv6网关地址
                let common_ipv6_gateways = [
                    Ipv6Addr::new(0xfe80, 0, 0, 0, 0, 0, 0, 1),
                    Ipv6Addr::new(0xfe80, 0, 0, 0, 0, 0, 0, 2),
                ];

                for &ip in &common_ipv6_gateways {
                    let gateway = IpAddr::V6(ip);
                    if self.ping_gateway(&gateway).await {
                        debug!("Found responsive IPv6 gateway at {}", gateway);
                        return Ok(gateway);
                    }
                }

                // 如果IPv6网关不可用，尝试IPv4
                self.find_ipv4_gateway().await?
            },
        };

        Ok(gateway)
    }

    /// 查找IPv4网关
    async fn find_ipv4_gateway(&self) -> Result<IpAddr> {
        // 尝试常见的网关地址
        let common_gateways = [
            Ipv4Addr::new(192, 168, 0, 1),
            Ipv4Addr::new(192, 168, 1, 1),
            Ipv4Addr::new(10, 0, 0, 1),
            Ipv4Addr::new(10, 0, 1, 1),
            Ipv4Addr::new(10, 1, 1, 1),
            Ipv4Addr::new(172, 16, 0, 1),
        ];

        for &ip in &common_gateways {
            let gateway = IpAddr::V4(ip);
            if self.ping_gateway(&gateway).await {
                debug!("Found responsive IPv4 gateway at {}", gateway);
                return Ok(gateway);
            }
        }

        // 如果所有方法都失败，返回错误
        Err(anyhow!("Could not find PCP gateway"))
    }

    /// 检查网关是否可达
    async fn ping_gateway(&self, gateway: &IpAddr) -> bool {
        // 尝试连接网关的PCP端口
        if let Ok(socket) = UdpSocket::bind("0.0.0.0:0").await {
            let gateway_addr = SocketAddr::new(*gateway, PCP_PORT);
            if socket.connect(gateway_addr).await.is_ok() {
                // 发送一个简单的PCP请求作为ping
                let request = self.build_announce_request();
                if let Ok(_) = socket.send(&request).await {
                    // 尝试接收响应，但不关心内容
                    let mut buf = [0u8; 24];
                    if let Ok(Ok(_)) = timeout(Duration::from_millis(500), socket.recv(&mut buf)).await {
                        return true;
                    }
                }
            }
        }
        false
    }

    /// 验证网关是否支持PCP
    async fn verify_gateway_support(&self, gateway: &IpAddr) -> Result<()> {
        // 尝试发送一个简单的PCP请求，如果成功则表示网关支持PCP
        let socket = UdpSocket::bind("0.0.0.0:0").await
            .context("Failed to bind UDP socket")?;

        let gateway_addr = SocketAddr::new(*gateway, PCP_PORT);
        socket.connect(gateway_addr).await
            .context("Failed to connect to gateway")?;

        // 构造PCP ANNOUNCE请求
        let request = self.build_announce_request();

        // 发送请求
        socket.send(&request).await
            .context("Failed to send PCP ANNOUNCE request")?;

        // 接收响应
        let mut buf = [0u8; 24];
        match timeout(Duration::from_millis(500), socket.recv(&mut buf)).await {
            Ok(Ok(size)) if size >= 24 => {
                // 检查响应格式
                if buf[0] != PCP_VERSION || buf[1] != PCP_OP_ANNOUNCE {
                    return Err(anyhow!("Invalid PCP response"));
                }

                // 检查结果码
                let result_code = buf[3];
                if result_code != PCP_RESULT_SUCCESS {
                    return Err(anyhow!("PCP error: {}", pcp_error_message(result_code)));
                }

                // 成功验证
                Ok(())
            },
            _ => Err(anyhow!("Gateway did not respond to PCP request"))
        }
    }

    /// 构建PCP ANNOUNCE请求
    fn build_announce_request(&self) -> Vec<u8> {
        let mut request = Vec::with_capacity(24);

        // PCP头部
        request.push(PCP_VERSION); // 版本
        request.push(PCP_OP_ANNOUNCE); // 操作码
        request.push(0); // 保留字段
        request.push(0); // 保留字段

        // 请求生命周期（对于ANNOUNCE请求，这个字段被忽略）
        request.extend_from_slice(&[0, 0, 0, 0]);

        // 客户端IP地址（全0表示使用源地址）
        request.extend_from_slice(&[0; 16]);

        request
    }

    /// 检测本地IP地址
    async fn detect_local_ip(&self) -> Result<IpAddr> {
        // 创建UDP套接字连接到公共DNS服务器
        let socket = UdpSocket::bind("0.0.0.0:0").await?;
        socket.connect("*******:53").await?;

        // 获取本地地址
        let local_addr = socket.local_addr()?;
        let local_ip = local_addr.ip();

        Ok(local_ip)
    }

    /// 检测本地IPv4地址
    async fn detect_local_ip_v4(&self) -> Result<Ipv4Addr> {
        // 创建UDP套接字连接到公共DNS服务器
        let socket = UdpSocket::bind("0.0.0.0:0").await?;
        socket.connect("*******:53").await?;

        // 获取本地地址
        let local_addr = socket.local_addr()?;

        match local_addr.ip() {
            IpAddr::V4(ipv4) => Ok(ipv4),
            IpAddr::V6(_) => Err(anyhow!("No IPv4 address available")),
        }
    }

    /// 跟踪映射
    async fn track_mapping(&self, mapping: &MappingInfo) {
        // 创建映射键
        let key = format!("{}:{}:{}",
            mapping.external_addr.ip(),
            mapping.external_addr.port(),
            mapping.protocol);

        // 添加到活跃映射列表
        let mut mappings = self.active_mappings.write().await;
        mappings.insert(key, mapping.clone());

        debug!("Tracking PCP mapping: {}:{} ({}) for {} seconds",
            mapping.external_addr.ip(), mapping.external_addr.port(),
            mapping.protocol, mapping.lifetime);
    }

    /// 获取所有活跃映射
    pub async fn get_active_mappings(&self) -> Vec<MappingInfo> {
        let mappings = self.active_mappings.read().await;
        mappings.values().cloned().collect()
    }

    /// 清理过期映射
    pub async fn cleanup_expired_mappings(&self) -> Result<usize> {
        let mut expired_count = 0;
        let mut to_remove = Vec::new();

        // 查找过期映射
        {
            let mappings = self.active_mappings.read().await;
            for (key, mapping) in mappings.iter() {
                if mapping.created_at.elapsed().as_secs() >= mapping.lifetime {
                    to_remove.push((key.clone(), mapping.clone()));
                }
            }
        }

        // 删除过期映射
        for (key, mapping) in to_remove {
            if let Err(e) = self.delete_mapping(&mapping).await {
                warn!("Failed to delete expired mapping {}: {}", key, e);
            } else {
                expired_count += 1;
            }
        }

        if expired_count > 0 {
            info!("Cleaned up {} expired PCP mappings", expired_count);
        }

        Ok(expired_count)
    }

    /// 启动自动刷新任务
    pub async fn start_auto_refresh(&self) -> Result<()> {
        if !self.auto_refresh_enabled {
            return Ok(());
        }

        // 创建一个弱引用，避免循环引用
        let self_weak = Arc::downgrade(&Arc::new(self.clone()));

        // 启动后台任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60)); // 每分钟检查一次

            loop {
                interval.tick().await;

                // 尝试升级弱引用
                if let Some(mapper) = self_weak.upgrade() {
                    // 清理过期映射
                    if let Err(e) = mapper.cleanup_expired_mappings().await {
                        warn!("Failed to cleanup expired mappings: {}", e);
                    }

                    // 刷新需要更新的映射
                    let mappings = mapper.get_active_mappings().await;
                    for mut mapping in mappings {
                        // 如果映射已经过期，跳过
                        if mapping.created_at.elapsed().as_secs() >= mapping.lifetime {
                            continue;
                        }

                        // 如果映射还有超过一半的生命周期，跳过
                        if mapping.created_at.elapsed().as_secs() < mapping.lifetime / 2 {
                            continue;
                        }

                        // 更新映射
                        let lifetime = Duration::from_secs(mapping.lifetime);
                        if let Err(e) = mapper.renew_mapping(&mut mapping, lifetime).await {
                            warn!("Failed to renew mapping: {}", e);
                        }
                    }
                } else {
                    // 如果弱引用无法升级，说明映射器已被释放，退出循环
                    break;
                }
            }
        });

        Ok(())
    }

    /// 构建PCP MAP请求
    fn build_map_request(&self, protocol: Protocol, internal_port: u16, external_port: u16, lifetime: Duration) -> Vec<u8> {
        let mut request = Vec::with_capacity(60);

        // PCP头部
        request.push(PCP_VERSION); // 版本
        request.push(PCP_OP_MAP); // 操作码
        request.push(0); // 保留字段
        request.push(0); // 保留字段

        // 请求生命周期
        request.extend_from_slice(&(lifetime.as_secs() as u32).to_be_bytes());

        // 客户端IP地址（全0表示使用源地址）
        request.extend_from_slice(&[0; 16]);

        // Mapping Nonce
        request.extend_from_slice(&self.client_id);

        // 协议
        request.push(match protocol {
            Protocol::TCP => 6, // TCP
            Protocol::UDP => 17, // UDP
        });

        // 保留字段
        request.extend_from_slice(&[0, 0, 0]);

        // 内部端口
        request.extend_from_slice(&internal_port.to_be_bytes());

        // 外部端口
        request.extend_from_slice(&external_port.to_be_bytes());

        // 外部IP地址（全0表示由NAT分配）
        request.extend_from_slice(&[0; 16]);

        request
    }

    /// 解析PCP MAP响应
    fn parse_map_response(&self, data: &[u8]) -> Result<(u16, IpAddr, u32)> {
        if data.len() < 60 {
            return Err(anyhow!("Invalid PCP response length"));
        }

        // 检查版本和操作码
        if data[0] != PCP_VERSION {
            return Err(anyhow!("Invalid PCP version"));
        }

        if data[1] != PCP_OP_MAP {
            return Err(anyhow!("Invalid PCP operation"));
        }

        // 检查结果码
        let result = data[3];
        if result != PCP_RESULT_SUCCESS {
            return Err(anyhow!("PCP error: {}", result));
        }

        // 提取生命周期
        let lifetime = u32::from_be_bytes([data[4], data[5], data[6], data[7]]);

        // 提取外部端口
        let external_port = u16::from_be_bytes([data[42], data[43]]);

        // 提取外部IP地址
        let mut ip_bytes = [0u8; 16];
        ip_bytes.copy_from_slice(&data[44..60]);

        // 检查是否是IPv4映射地址
        let external_ip = if ip_bytes[0..10] == [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] && ip_bytes[10..12] == [0xff, 0xff] {
            // IPv4映射地址
            IpAddr::V4(Ipv4Addr::new(ip_bytes[12], ip_bytes[13], ip_bytes[14], ip_bytes[15]))
        } else {
            // IPv6地址
            IpAddr::V6(Ipv6Addr::from(ip_bytes))
        };

        Ok((external_port, external_ip, lifetime))
    }
}

#[async_trait]
impl PortMapper for PCPMapper {
    async fn create_mapping(&self, protocol: Protocol, internal_port: u16, external_port: u16, lifetime: Duration) -> Result<MappingInfo> {
        // 获取网关
        let gateway = self.discover_gateway().await?;

        // 创建UDP套接字
        let socket = UdpSocket::bind("0.0.0.0:0").await?;

        // 构建PCP请求
        // 确保生命周期不超过最大值
        let lifetime_secs = std::cmp::min(lifetime.as_secs() as u32, PCP_MAX_LIFETIME);
        let request = self.build_map_request(protocol, internal_port, external_port, Duration::from_secs(lifetime_secs as u64));

        // 发送请求，带重试
        let gateway_addr = SocketAddr::new(gateway, PCP_PORT);
        socket.connect(gateway_addr).await
            .context("Failed to connect to gateway for mapping request")?;

        // 实现指数退避重试
        let mut timeout_ms = PCP_INITIAL_TIMEOUT;

        for retry in 0..PCP_MAX_RETRIES {
            // 发送请求
            if let Err(e) = socket.send(&request).await {
                debug!("Failed to send mapping request (retry {}/{}): {}",
                       retry + 1, PCP_MAX_RETRIES, e);
                continue;
            }

            // 接收响应
            let mut buf = [0u8; 128];
            match timeout(Duration::from_millis(timeout_ms), socket.recv(&mut buf)).await {
                Ok(Ok(len)) => {
                    // 解析响应
                    match self.parse_map_response(&buf[..len]) {
                        Ok((external_port, external_ip, lifetime)) => {
                            // 获取本地IP地址
                            let local_ip = self.detect_local_ip().await?;

                            // 创建映射信息
                            let mapping = MappingInfo {
                                internal_addr: SocketAddr::new(local_ip, internal_port),
                                external_addr: SocketAddr::new(external_ip, external_port),
                                protocol,
                                lifetime: lifetime as u64,
                                created_at: Instant::now(),
                            };

                            // 更新外部IP地址缓存
                            {
                                let mut external_ip_guard = self.external_ip.lock().await;
                                *external_ip_guard = Some(external_ip);
                            }

                            // 更新最后更新时间
                            {
                                let mut last_update = self.last_external_ip_update.lock().await;
                                *last_update = Some(Instant::now());
                            }

                            // 添加到活跃映射列表
                            self.track_mapping(&mapping).await;

                            info!("Created PCP mapping: {}:{} -> {}:{} ({}) for {} seconds",
                                local_ip, internal_port, external_ip, external_port, protocol, lifetime);

                            return Ok(mapping);
                        },
                        Err(e) => {
                            debug!("Failed to parse PCP response (retry {}/{}): {}",
                                   retry + 1, PCP_MAX_RETRIES, e);
                            timeout_ms *= 2;
                            continue;
                        }
                    }
                },
                Ok(Err(e)) => {
                    debug!("Failed to receive PCP response (retry {}/{}): {}",
                           retry + 1, PCP_MAX_RETRIES, e);
                },
                Err(_) => {
                    debug!("PCP request timed out after {}ms (retry {}/{})",
                           timeout_ms, retry + 1, PCP_MAX_RETRIES);
                }
            }

            // 指数退避，但添加一些随机性以避免同步
            timeout_ms = std::cmp::min(timeout_ms * 2, 10000); // 最大10秒
            timeout_ms += rand::thread_rng().gen_range(0..100); // 添加随机抖动
        }

        Err(anyhow!("Failed to create mapping after {} retries", PCP_MAX_RETRIES))
    }

    async fn delete_mapping(&self, mapping: &MappingInfo) -> Result<()> {
        // 在PCP中，删除映射是通过将生命周期设置为0来实现的
        let _new_mapping = self.create_mapping(
            mapping.protocol,
            mapping.internal_addr.port(),
            mapping.external_addr.port(),
            Duration::from_secs(0)
        ).await?;

        // 从活跃映射列表中移除
        let key = format!("{}:{}:{}",
            mapping.external_addr.ip(),
            mapping.external_addr.port(),
            mapping.protocol);

        let mut mappings = self.active_mappings.write().await;
        mappings.remove(&key);

        info!("Deleted PCP mapping: {}:{} ({})",
            mapping.external_addr.ip(), mapping.external_addr.port(), mapping.protocol);

        Ok(())
    }

    async fn renew_mapping(&self, mapping: &mut MappingInfo, lifetime: Duration) -> Result<()> {
        // 从活跃映射列表中移除旧映射
        let key = format!("{}:{}:{}",
            mapping.external_addr.ip(),
            mapping.external_addr.port(),
            mapping.protocol);

        {
            let mut mappings = self.active_mappings.write().await;
            mappings.remove(&key);
        }

        // 创建新的映射
        let new_mapping = self.create_mapping(
            mapping.protocol,
            mapping.internal_addr.port(),
            mapping.external_addr.port(),
            lifetime
        ).await?;

        // 更新映射信息
        mapping.lifetime = new_mapping.lifetime;
        mapping.created_at = new_mapping.created_at;

        // 重新添加到活跃映射列表
        self.track_mapping(mapping).await;

        info!("Renewed PCP mapping: {}:{} ({}) for {} seconds",
            mapping.external_addr.ip(), mapping.external_addr.port(),
            mapping.protocol, mapping.lifetime);

        Ok(())
    }

    async fn get_external_address(&self) -> Result<IpAddr> {
        // 检查是否需要更新
        {
            let last_update = self.last_external_ip_update.lock().await;
            if let Some(time) = *last_update {
                if time.elapsed().as_secs() < self.external_ip_update_interval {
                    // 如果已经有外部IP地址，且更新时间未到，则直接返回
                    let external_ip = self.external_ip.lock().await;
                    if let Some(ip) = *external_ip {
                        return Ok(ip);
                    }
                }
            }
        }

        // 创建临时映射来获取外部IP地址
        debug!("Requesting external IP address via PCP...");

        // 使用随机端口，避免冲突
        let random_port = rand::thread_rng().gen_range(10000..60000);

        let mapping = self.create_mapping(
            Protocol::UDP,
            random_port, // 随机临时端口
            0, // 让NAT分配端口
            Duration::from_secs(10) // 短生命周期
        ).await?;

        // 获取外部IP地址
        let external_ip = mapping.external_addr.ip();

        // 更新缓存
        {
            let mut external_ip_guard = self.external_ip.lock().await;
            *external_ip_guard = Some(external_ip);
        }

        // 更新最后更新时间
        {
            let mut last_update = self.last_external_ip_update.lock().await;
            *last_update = Some(Instant::now());
        }

        // 删除临时映射
        if let Err(e) = self.delete_mapping(&mapping).await {
            warn!("Failed to delete temporary mapping: {}", e);
            // 继续执行，因为这不是致命错误
        }

        info!("Got external IP address: {}", external_ip);

        Ok(external_ip)
    }

    /// 实现Clone特性
    fn clone(&self) -> Self {
        // 创建新的实例
        Self {
            gateway: Mutex::new(self.gateway.try_lock().ok().and_then(|g| *g)),
            external_ip: Mutex::new(self.external_ip.try_lock().ok().and_then(|ip| *ip)),
            last_discovery: Mutex::new(self.last_discovery.try_lock().ok().and_then(|ld| *ld)),
            discovery_timeout: self.discovery_timeout,
            client_id: self.client_id,
            active_mappings: RwLock::new(HashMap::new()), // 创建新的空映射表
            last_external_ip_update: Mutex::new(self.last_external_ip_update.try_lock().ok().and_then(|lu| *lu)),
            external_ip_update_interval: self.external_ip_update_interval,
            mapping_refresh_interval: self.mapping_refresh_interval,
            auto_refresh_enabled: self.auto_refresh_enabled,
            ipv6_supported: self.ipv6_supported,
        }
    }
}
