use async_trait::async_trait;
use bytes::Bytes;
use std::path::{Path, PathBuf};
use tokio::fs::{self, File, OpenOptions};
use tokio::io::{AsyncReadExt, AsyncSeekExt, AsyncWriteExt, SeekFrom};
use tracing::debug;

use crate::core::error::{CoreError, CoreResult};
use crate::core::interfaces::storage::{Storage, StorageType};

/// Local storage implementation
pub struct LocalStorage {
    root_path: PathBuf,
}

impl LocalStorage {
    /// Create a new local storage
    pub fn new(root_path: impl Into<PathBuf>) -> Self {
        Self {
            root_path: root_path.into(),
        }
    }
    
    /// Get the full path for a file
    fn get_full_path(&self, path: &str) -> PathBuf {
        self.root_path.join(path)
    }
}

#[async_trait]
impl Storage for LocalStorage {
    async fn write(&self, path: &str, data: &[u8]) -> CoreResult<()> {
        let full_path = self.get_full_path(path);
        
        // Create the parent directory if it doesn't exist
        if let Some(parent) = full_path.parent() {
            fs::create_dir_all(parent).await
                .map_err(|e| CoreError::Io(e))?;
        }
        
        // Write the data
        fs::write(&full_path, data).await
            .map_err(|e| CoreError::Io(e))?;
        
        debug!("Wrote {} bytes to {}", data.len(), full_path.display());
        
        Ok(())
    }
    
    async fn write_at(&self, path: &str, data: &[u8], offset: u64) -> CoreResult<()> {
        let full_path = self.get_full_path(path);
        
        // Create the parent directory if it doesn't exist
        if let Some(parent) = full_path.parent() {
            fs::create_dir_all(parent).await
                .map_err(|e| CoreError::Io(e))?;
        }
        
        // Open the file
        let mut file = OpenOptions::new()
            .write(true)
            .create(true)
            .open(&full_path).await
            .map_err(|e| CoreError::Io(e))?;
        
        // Seek to the offset
        file.seek(SeekFrom::Start(offset)).await
            .map_err(|e| CoreError::Io(e))?;
        
        // Write the data
        file.write_all(data).await
            .map_err(|e| CoreError::Io(e))?;
        
        debug!("Wrote {} bytes at offset {} to {}", data.len(), offset, full_path.display());
        
        Ok(())
    }
    
    async fn read(&self, path: &str) -> CoreResult<Bytes> {
        let full_path = self.get_full_path(path);
        
        // Read the file
        let data = fs::read(&full_path).await
            .map_err(|e| CoreError::Io(e))?;
        
        debug!("Read {} bytes from {}", data.len(), full_path.display());
        
        Ok(Bytes::from(data))
    }
    
    async fn read_at(&self, path: &str, offset: u64, length: usize) -> CoreResult<Bytes> {
        let full_path = self.get_full_path(path);
        
        // Open the file
        let mut file = File::open(&full_path).await
            .map_err(|e| CoreError::Io(e))?;
        
        // Get the file size
        let file_size = file.metadata().await
            .map_err(|e| CoreError::Io(e))?.len();
        
        // Check if the offset is valid
        if offset >= file_size {
            return Err(CoreError::InvalidArgument(
                format!("Offset {} is beyond the end of the file (size: {})", offset, file_size)
            ));
        }
        
        // Calculate the actual length to read
        let actual_length = length.min((file_size - offset) as usize);
        
        // Seek to the offset
        file.seek(SeekFrom::Start(offset)).await
            .map_err(|e| CoreError::Io(e))?;
        
        // Read the data
        let mut buffer = vec![0; actual_length];
        file.read_exact(&mut buffer).await
            .map_err(|e| CoreError::Io(e))?;
        
        debug!("Read {} bytes at offset {} from {}", actual_length, offset, full_path.display());
        
        Ok(Bytes::from(buffer))
    }
    
    async fn delete(&self, path: &str) -> CoreResult<()> {
        let full_path = self.get_full_path(path);
        
        // Delete the file
        fs::remove_file(&full_path).await
            .map_err(|e| CoreError::Io(e))?;
        
        debug!("Deleted {}", full_path.display());
        
        Ok(())
    }
    
    async fn exists(&self, path: &str) -> CoreResult<bool> {
        let full_path = self.get_full_path(path);
        
        // Check if the file exists
        let exists = fs::metadata(&full_path).await.is_ok();
        
        Ok(exists)
    }
    
    async fn size(&self, path: &str) -> CoreResult<u64> {
        let full_path = self.get_full_path(path);
        
        // Get the file size
        let metadata = fs::metadata(&full_path).await
            .map_err(|e| CoreError::Io(e))?;
        
        Ok(metadata.len())
    }
    
    async fn create_dir(&self, path: &str) -> CoreResult<()> {
        let full_path = self.get_full_path(path);
        
        // Create the directory
        fs::create_dir_all(&full_path).await
            .map_err(|e| CoreError::Io(e))?;
        
        debug!("Created directory {}", full_path.display());
        
        Ok(())
    }
    
    async fn list_dir(&self, path: &str) -> CoreResult<Vec<String>> {
        let full_path = self.get_full_path(path);
        
        // Read the directory
        let mut entries = fs::read_dir(&full_path).await
            .map_err(|e| CoreError::Io(e))?;
        
        let mut result = Vec::new();
        
        // Collect the entries
        while let Some(entry) = entries.next_entry().await
            .map_err(|e| CoreError::Io(e))? {
            
            let file_name = entry.file_name();
            let file_name_str = file_name.to_string_lossy().to_string();
            result.push(file_name_str);
        }
        
        debug!("Listed {} entries in {}", result.len(), full_path.display());
        
        Ok(result)
    }
    
    async fn move_file(&self, from: &str, to: &str) -> CoreResult<()> {
        let from_path = self.get_full_path(from);
        let to_path = self.get_full_path(to);
        
        // Create the parent directory if it doesn't exist
        if let Some(parent) = to_path.parent() {
            fs::create_dir_all(parent).await
                .map_err(|e| CoreError::Io(e))?;
        }
        
        // Move the file
        fs::rename(&from_path, &to_path).await
            .map_err(|e| CoreError::Io(e))?;
        
        debug!("Moved {} to {}", from_path.display(), to_path.display());
        
        Ok(())
    }
    
    async fn copy_file(&self, from: &str, to: &str) -> CoreResult<()> {
        let from_path = self.get_full_path(from);
        let to_path = self.get_full_path(to);
        
        // Create the parent directory if it doesn't exist
        if let Some(parent) = to_path.parent() {
            fs::create_dir_all(parent).await
                .map_err(|e| CoreError::Io(e))?;
        }
        
        // Copy the file
        fs::copy(&from_path, &to_path).await
            .map_err(|e| CoreError::Io(e))?;
        
        debug!("Copied {} to {}", from_path.display(), to_path.display());
        
        Ok(())
    }
    
    fn storage_type(&self) -> StorageType {
        StorageType::Local
    }
    
    fn root_path(&self) -> &Path {
        &self.root_path
    }
}

/// Storage factory implementation
pub struct LocalStorageFactory;

#[async_trait]
impl crate::core::interfaces::storage::StorageFactory for LocalStorageFactory {
    async fn create_storage(&self, storage_type: StorageType, config: &str) -> CoreResult<Box<dyn Storage>> {
        match storage_type {
            StorageType::Local => {
                Ok(Box::new(LocalStorage::new(config)))
            },
            _ => Err(CoreError::Unsupported(format!("Unsupported storage type: {:?}", storage_type))),
        }
    }
    
    fn supported_storage_types(&self) -> Vec<StorageType> {
        vec![StorageType::Local]
    }
}
