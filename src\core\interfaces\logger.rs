use async_trait::async_trait;
use std::collections::HashMap;

use crate::core::error::CoreResult;

/// Log level
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
}

/// Log record
#[derive(Debug, Clone)]
pub struct LogRecord {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub level: LogLevel,
    pub target: String,
    pub message: String,
    pub module_path: Option<String>,
    pub file: Option<String>,
    pub line: Option<u32>,
    pub thread_id: Option<String>,
    pub thread_name: Option<String>,
    pub fields: HashMap<String, String>,
}

/// Logger interface
#[async_trait]
pub trait Logger: Send + Sync {
    /// Log a message
    async fn log(&self, record: LogRecord) -> CoreResult<()>;
    
    /// Set the log level
    async fn set_level(&self, level: LogLevel) -> CoreResult<()>;
    
    /// Get the current log level
    async fn get_level(&self) -> CoreResult<LogLevel>;
    
    /// Enable logging to a file
    async fn enable_file_logging(&self, path: &str) -> CoreResult<()>;
    
    /// Disable logging to a file
    async fn disable_file_logging(&self) -> CoreResult<()>;
    
    /// Enable console logging
    async fn enable_console_logging(&self) -> CoreResult<()>;
    
    /// Disable console logging
    async fn disable_console_logging(&self) -> CoreResult<()>;
    
    /// Flush logs
    async fn flush(&self) -> CoreResult<()>;
}

/// Logger factory interface
#[async_trait]
pub trait LoggerFactory: Send + Sync {
    /// Create a logger instance
    async fn create_logger(&self, name: &str) -> CoreResult<Box<dyn Logger>>;
}
