use async_trait::async_trait;
use bytes::Bytes;
use std::path::Path;
use anyhow::anyhow;

use crate::core::error::CoreResult;

/// Storage interface
/// This is the main interface for all storage backends
#[async_trait]
pub trait Storage: Send + Sync {
    /// Write data to a file
    async fn write(&self, path: &str, data: &[u8]) -> CoreResult<()>;
    
    /// Write data to a file at a specific offset
    async fn write_at(&self, path: &str, data: &[u8], offset: u64) -> CoreResult<()>;
    
    /// Read data from a file
    async fn read(&self, path: &str) -> CoreResult<Bytes>;
    
    /// Read data from a file at a specific offset
    async fn read_at(&self, path: &str, offset: u64, length: usize) -> CoreResult<Bytes>;
    
    /// Delete a file
    async fn delete(&self, path: &str) -> CoreResult<()>;
    
    /// Check if a file exists
    async fn exists(&self, path: &str) -> CoreResult<bool>;
    
    /// Get the size of a file
    async fn size(&self, path: &str) -> CoreResult<u64>;
    
    /// Create a directory
    async fn create_dir(&self, path: &str) -> CoreResult<()>;
    
    /// List files in a directory
    async fn list_dir(&self, path: &str) -> CoreResult<Vec<String>>;
    
    /// Move a file
    async fn move_file(&self, from: &str, to: &str) -> CoreResult<()>;
    
    /// Copy a file
    async fn copy_file(&self, from: &str, to: &str) -> CoreResult<()>;
    
    /// Get the storage type
    fn storage_type(&self) -> StorageType;
    
    /// Get the root path
    fn root_path(&self) -> &Path;
    
    /// 获取字符串数据
    /// 
    /// 从指定路径读取数据并转换为字符串
    async fn get(&self, path: &str) -> CoreResult<String> {
        let bytes = self.read(path).await?;
        String::from_utf8(bytes.to_vec())
            .map_err(|e| anyhow!("Failed to convert bytes to string: {}", e).into())
    }
    
    /// 设置字符串数据
    /// 
    /// 将字符串数据写入指定路径
    async fn set(&self, path: &str, data: &str) -> CoreResult<()> {
        self.write(path, data.as_bytes()).await
    }
}

/// Storage type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum StorageType {
    Local,
    Cloud,
    Memory,
    Custom(u8),
}

/// Storage factory interface
/// This is used to create storage backends
#[async_trait]
pub trait StorageFactory: Send + Sync {
    /// Create a storage backend
    async fn create_storage(&self, storage_type: StorageType, config: &str) -> CoreResult<Box<dyn Storage>>;
    
    /// Get the supported storage types
    fn supported_storage_types(&self) -> Vec<StorageType>;
}
