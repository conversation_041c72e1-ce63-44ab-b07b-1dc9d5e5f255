/**
 * 增强版 WebSocket 连接管理器
 * 用于处理与后端的实时通信，支持心跳检测、消息确认和重连机制
 */

// 事件监听器类型
type EventListener = (payload: any) => void;

// WebSocket 连接状态
enum ConnectionState {
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
  RECONNECTING = 4,
}

// WebSocket 配置
interface WebSocketConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  debug: boolean;
}

// 默认配置
const defaultConfig: WebSocketConfig = {
  url: 'ws://localhost:8080/api/v1/ws',
  reconnectInterval: 3000,
  maxReconnectAttempts: 5,
  debug: false,
};

// 事件映射表（将后端事件名映射到前端事件名）
const eventMap: Record<string, string> = {
  'aria2.onDownloadStart': 'task.start',
  'aria2.onDownloadPause': 'task.pause',
  'aria2.onDownloadStop': 'task.cancel',
  'aria2.onDownloadComplete': 'task.complete',
  'aria2.onDownloadError': 'task.error',
  'aria2.onBtDownloadComplete': 'task.bt.complete',
  'task.add': 'task.add' // 添加task.add事件映射
};

class EnhancedWebSocket {
  private ws: WebSocket | null = null;
  private eventListeners: Record<string, EventListener[]> = {};
  private reconnectAttempts = 0;
  private reconnectTimer: number | null = null;
  private heartbeatTimer: number | null = null;
  private config: WebSocketConfig;
  private state: ConnectionState = ConnectionState.CLOSED;

  /**
   * 构造函数
   * @param config WebSocket配置
   */
  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  /**
   * 初始化 WebSocket 连接
   */
  public connect(): void {
    if (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING)) {
      this.log('WebSocket已连接或正在连接中');
      return;
    }

    this.state = ConnectionState.CONNECTING;
    this.log('正在建立WebSocket连接...');

    try {
      this.ws = new WebSocket(this.config.url);
      this.setupEventHandlers();
    } catch (error) {
      this.log('WebSocket连接失败', error);
      this.reconnect();
    }
  }

  /**
   * 设置WebSocket事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      this.state = ConnectionState.OPEN;
      this.reconnectAttempts = 0;
      this.log('WebSocket连接已建立');
      this.startHeartbeat();
      this.triggerEvent('connection', { status: 'connected' });
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        const { event: eventName, payload, version } = data;

        this.log(`收到消息: ${eventName}`, payload, `版本: ${version || '无'}`);

        // 处理特殊事件
        if (eventName === 'ack') {
          this.log('收到确认消息', payload);
          return;
        }

        if (eventName === 'connection') {
          this.triggerEvent('connection', payload);
          return;
        }

        // 触发事件
        this.triggerEvent(eventName, payload);
      } catch (err) {
        this.log('解析WebSocket消息失败', err);
      }
    };

    this.ws.onclose = (event) => {
      if (this.state !== ConnectionState.RECONNECTING) {
        this.state = ConnectionState.CLOSED;
        this.log(`WebSocket连接已关闭: ${event.code} ${event.reason}`);
        this.triggerEvent('connection', { status: 'disconnected', code: event.code, reason: event.reason });
        this.stopHeartbeat();
        this.reconnect();
      }
    };

    this.ws.onerror = (error) => {
      this.log('WebSocket错误', error);
      this.triggerEvent('error', { error });
    };
  }

  /**
   * 重新连接
   */
  private reconnect(): void {
    if (
      this.state === ConnectionState.RECONNECTING ||
      this.reconnectAttempts >= this.config.maxReconnectAttempts
    ) {
      return;
    }

    this.state = ConnectionState.RECONNECTING;
    this.reconnectAttempts++;

    this.log(`尝试重新连接... (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);
    this.triggerEvent('connection', { status: 'reconnecting', attempt: this.reconnectAttempts });

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectTimer = window.setTimeout(() => {
      this.connect();
    }, this.config.reconnectInterval);
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatTimer = window.setInterval(() => {
      this.sendPing();
    }, 30000); // 每30秒发送一次ping
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 发送ping消息
   */
  private sendPing(): void {
    this.send('ping', { time: Date.now() });
  }

  /**
   * 触发事件
   * @param event 事件名
   * @param payload 事件数据
   */
  private triggerEvent(event: string, payload: any): void {
    // 查找前端事件名
    const frontendEvent = eventMap[event] || event;

    const listeners = this.eventListeners[frontendEvent] || [];
    listeners.forEach(callback => {
      try {
        callback(payload);
      } catch (err) {
        this.log(`事件监听器错误: ${event}`, err);
      }
    });
  }

  /**
   * 添加事件监听器
   * @param event 事件名
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: EventListener): void {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }

    this.eventListeners[event].push(callback);
    this.log(`添加事件监听器: ${event}`);
  }

  /**
   * 移除事件监听器
   * @param event 事件名
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: EventListener): void {
    if (!this.eventListeners[event]) {
      return;
    }

    this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
    this.log(`移除事件监听器: ${event}`);
  }

  /**
   * 发送消息
   * @param event 事件名
   * @param payload 事件数据
   */
  public send(event: string, payload: any): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this.log('WebSocket未连接，无法发送消息');
      return;
    }

    const message = {
      event,
      payload,
      version: '1.0',
    };

    this.ws.send(JSON.stringify(message));
    this.log(`发送消息: ${event}`, payload);
  }

  /**
   * 关闭连接
   */
  public close(): void {
    this.stopHeartbeat();

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.ws) {
      this.state = ConnectionState.CLOSING;
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * 获取连接状态
   */
  public getState(): ConnectionState {
    return this.state;
  }

  /**
   * 日志输出
   */
  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[WebSocket]', ...args);
    }
  }
}

// 创建单例实例
const wsInstance = new EnhancedWebSocket({ debug: true });

// 导出方法
export function initWebSocket() {
  wsInstance.connect();
}

export function addEventListener(event: string, callback: EventListener) {
  wsInstance.addEventListener(event, callback);
}

export function removeEventListener(event: string, callback: EventListener) {
  wsInstance.removeEventListener(event, callback);
}

export function sendMessage(event: string, payload: any) {
  wsInstance.send(event, payload);
}

export function closeWebSocket() {
  wsInstance.close();
}

export function getWebSocketState() {
  return wsInstance.getState();
}

// 自动初始化WebSocket连接
initWebSocket();

export default wsInstance;
