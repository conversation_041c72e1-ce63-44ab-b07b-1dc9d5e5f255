/* 全局样式 */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: "Monospaced Number", "Chinese Quote", -apple-system,
    BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-variant: tabular-nums;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f7fa;
}

#app {
  height: 100%;
  width: 100%;
}

/* 链接样式 */
a {
  text-decoration: none;
  color: #5b5bfa;
  transition: color 0.2s;
}

a:hover {
  color: #7979ff;
}

/* Element Plus 样式覆盖 */
.el-button--primary {
  --el-button-bg-color: #5b5bfa;
  --el-button-border-color: #5b5bfa;
  --el-button-hover-bg-color: #7979ff;
  --el-button-hover-border-color: #7979ff;
}

.el-menu {
  border-right: none !important;
}

.el-menu-item.is-active {
  color: #5b5bfa !important;
}

.el-progress-bar__inner {
  background-color: #5b5bfa !important;
}

/* 暗色主题适配 */
.theme-dark {
  color: #eee;
  background-color: #252525;
}

.theme-dark .el-card {
  --el-card-bg-color: #333;
  color: #eee;
}

.theme-dark .el-input__inner {
  background-color: #333;
  color: #eee;
}

.theme-dark .el-table {
  --el-table-bg-color: #333;
  --el-table-tr-bg-color: #333;
  --el-table-border-color: #444;
  --el-table-header-bg-color: #252525;
  --el-table-header-text-color: #eee;
  --el-table-text-color: #eee;
}

/* 响应式布局 */
@media only screen and (max-width: 767px) {
  .hidden-xs-only {
    display: none !important;
  }
}

@media only screen and (min-width: 768px) {
  .hidden-sm-and-up {
    display: none !important;
  }
}
