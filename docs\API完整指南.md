# Tonitru 下载工具 API 完整指南

## 1. 概述

Tonitru 下载工具提供了一套完整的 API 接口，允许外部应用程序与下载器进行交互。本文档详细描述了这些 API 接口的设计原则、使用方法和开发计划。

### 1.1 API 访问方式

API 接口有两种访问方式：

1. **HTTP API 服务**：通过 HTTP 请求访问运行中的 Tonitru 下载器服务
2. **CLI 直接集成**：通过 CLI 工具直接调用核心组件，无需启动独立的服务

### 1.2 设计原则

- **RESTful 风格**：API 遵循 REST 架构风格，使用标准 HTTP 方法
- **JSON 数据交换**：请求和响应均使用 JSON 格式
- **版本控制**：API 路径包含版本号，便于未来升级
- **一致性**：保持请求和响应格式的一致性
- **错误处理**：统一的错误码和错误消息格式

### 1.3 基础 URL

```
http://<host>:<port>/api/v1
```

### 1.4 认证方式

目前 API 不要求认证。未来版本可能添加基于 API 密钥或 OAuth 的认证机制。

### 1.5 通用请求格式

所有 POST 请求使用 JSON 格式：

```
Content-Type: application/json
```

### 1.6 通用响应格式

所有响应均为 JSON 格式：

```json
{
  "code": 0,       // 状态码，0 表示成功，非 0 表示错误
  "message": "",  // 状态消息，成功时为空或成功消息，错误时为错误消息
  "data": {}      // 响应数据，根据不同接口返回不同的数据结构
}
```

### 1.7 跨域支持

API 服务器支持跨域资源共享 (CORS)，允许来自任何源的请求。

### 1.8 错误码定义

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 1000 | 通用错误 |
| 1001 | 参数错误 |
| 1002 | 资源不存在 |
| 1003 | 资源已存在 |
| 1004 | 操作失败 |
| 1005 | 内部错误 |
| 2000 | 下载错误 |
| 2001 | 下载任务不存在 |
| 2002 | 下载任务状态错误 |
| 3000 | 配置错误 |
| 3001 | 配置项不存在 |
| 3002 | 配置值无效 |

## 2. API 模块详解

### 2.1 下载管理 API

#### 2.1.1 添加下载任务

- **URL**: `/downloads`
- **方法**: `POST`
- **描述**: 添加一个新的下载任务

**请求体**:

```json
{
  "url": "https://example.com/file.zip",
  "output_path": "/path/to/save",  // 可选，默认为配置中的下载路径
  "auto_start": true               // 可选，是否自动开始下载，默认为 false
}
```

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

#### 2.1.2 开始下载任务

- **URL**: `/downloads/{id}/start`
- **方法**: `POST`
- **描述**: 开始一个下载任务

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

#### 2.1.3 暂停下载任务

- **URL**: `/downloads/{id}/pause`
- **方法**: `POST`
- **描述**: 暂停一个下载任务

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

#### 2.1.4 恢复下载任务

- **URL**: `/downloads/{id}/resume`
- **方法**: `POST`
- **描述**: 恢复一个暂停的下载任务

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

#### 2.1.5 取消下载任务

- **URL**: `/downloads/{id}/cancel`
- **方法**: `POST`
- **描述**: 取消一个下载任务

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

### 2.2 任务管理 API

#### 2.2.1 获取所有任务

- **URL**: `/tasks`
- **方法**: `GET`
- **描述**: 获取所有下载任务

**查询参数**:

- `status` (可选): 按状态过滤任务 (waiting, downloading, paused, completed, error)
- `page` (可选): 页码，默认为 1
- `page_size` (可选): 每页数量，默认为 20
- `sort_by` (可选): 排序字段 (created_at, name, size, progress)
- `sort_order` (可选): 排序方向 (asc, desc)

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "tasks": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "url": "https://example.com/file.zip",
        "file_name": "file.zip",
        "status": "downloading",
        "progress": 45.6,
        "size": 10485760,
        "downloaded": 4781466,
        "speed": 1048576,
        "created_at": "2023-01-01T12:00:00Z",
        "updated_at": "2023-01-01T12:05:00Z"
      },
      // 更多任务...
    ],
    "pagination": {
      "total": 42,
      "page": 1,
      "page_size": 20,
      "total_pages": 3
    }
  }
}
```

#### 2.2.2 获取任务详情

- **URL**: `/tasks/{id}`
- **方法**: `GET`
- **描述**: 获取特定任务的详细信息

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "url": "https://example.com/file.zip",
    "file_name": "file.zip",
    "output_path": "/path/to/save/file.zip",
    "status": "downloading",
    "progress": 45.6,
    "size": 10485760,
    "downloaded": 4781466,
    "speed": 1048576,
    "average_speed": 524288,
    "time_left": 5.5,
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-01T12:05:00Z",
    "error": null,
    "protocol": "http",
    "metadata": {
      // 协议特定的元数据
    }
  }
}
```

#### 2.2.3 删除任务

- **URL**: `/tasks/{id}`
- **方法**: `DELETE`
- **描述**: 删除一个任务及其相关资源

**查询参数**:

- `delete_file` (可选): 是否同时删除已下载的文件，默认为 false

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

### 2.3 配置管理 API

#### 2.3.1 获取系统配置

- **URL**: `/config`
- **方法**: `GET`
- **描述**: 获取系统配置

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "download": {
      "default_path": "/path/to/downloads",
      "max_concurrent_tasks": 5,
      "max_connections_per_server": 16
    },
    "speed": {
      "global_download_limit": 10485760,
      "global_upload_limit": 1048576
    },
    "bittorrent": {
      "enable_dht": true,
      "enable_pex": true,
      "enable_lsd": true,
      "max_peers": 200
    },
    "proxy": {
      "enabled": false,
      "type": "http",
      "host": "",
      "port": 0,
      "username": "",
      "password": ""
    }
  }
}
```

#### 2.3.2 更新系统配置

- **URL**: `/config`
- **方法**: `POST`
- **描述**: 更新系统配置

**请求体**:

```json
{
  "download": {
    "default_path": "/new/path/to/downloads",
    "max_concurrent_tasks": 10
  },
  "speed": {
    "global_download_limit": 5242880
  }
}
```

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

#### 2.3.3 获取特定配置项

- **URL**: `/config/{key}`
- **方法**: `GET`
- **描述**: 获取特定配置项

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "value": 5242880
  }
}
```

#### 2.3.4 更新特定配置项

- **URL**: `/config/{key}`
- **方法**: `POST`
- **描述**: 更新特定配置项

**请求体**:

```json
{
  "value": 10485760
}
```

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

### 2.4 速度控制 API

#### 2.4.1 设置全局下载速度限制

- **URL**: `/speed/download`
- **方法**: `POST`
- **描述**: 设置全局下载速度限制

**请求体**:

```json
{
  "limit": 5242880  // 字节/秒，0 表示不限制
}
```

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

#### 2.4.2 设置全局上传速度限制

- **URL**: `/speed/upload`
- **方法**: `POST`
- **描述**: 设置全局上传速度限制

**请求体**:

```json
{
  "limit": 1048576  // 字节/秒，0 表示不限制
}
```

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

#### 2.4.3 设置任务下载速度限制

- **URL**: `/tasks/{id}/speed/download`
- **方法**: `POST`
- **描述**: 设置特定任务的下载速度限制

**请求体**:

```json
{
  "limit": 1048576  // 字节/秒，0 表示不限制
}
```

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

#### 2.4.4 设置任务上传速度限制

- **URL**: `/tasks/{id}/speed/upload`
- **方法**: `POST`
- **描述**: 设置特定任务的上传速度限制

**请求体**:

```json
{
  "limit": 524288  // 字节/秒，0 表示不限制
}
```

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

#### 2.4.5 添加时间规则

- **URL**: `/speed/time-rules`
- **方法**: `POST`
- **描述**: 添加基于时间的速度限制规则

**请求体**:

```json
{
  "name": "夜间限速",
  "start_time": "22:00",
  "end_time": "08:00",
  "days": [1, 2, 3, 4, 5, 6, 7],  // 1-7 表示周一至周日
  "download_limit": 1048576,      // 字节/秒
  "upload_limit": 524288         // 字节/秒
}
```

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

#### 2.2.4 更新任务

- **URL**: `/tasks/{id}`
- **方法**: `PUT`
- **描述**: 更新任务的信息

**请求体**:

```json
{
  "file_name": "new_file_name.zip",
  "output_path": "/new/path/to/save"
  // 其他可更新的任务属性
}
```

**响应**:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "url": "https://example.com/file.zip",
    "file_name": "new_file_name.zip",
    "output_path": "/new/path/to/save",
    "status": "paused",
    "progress": 45.6,
    "size": 10485760,
    "downloaded": 4781466,
    "speed": 0,
    "average_speed": 524288,
    "time_left": 0,
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-01T12:10:00Z",
    "error": null,
    "protocol": "http",
    "metadata": {}
  }
}
```

## 3. 前端集成指南

### 3.1 HTTP 请求配置

前端使用 Axios 发送 HTTP 请求，基础配置如下：

```typescript
// 配置 axios
axios.defaults.baseURL = 'http://localhost:8080/api/v1';
```

### 3.2 WebSocket 连接配置

前端使用原生 WebSocket API 建立 WebSocket 连接，基础配置如下：

```typescript
// WebSocket 配置
const defaultConfig = {
  url: 'ws://localhost:8080/api/v1/ws',
  reconnectInterval: 3000,
  maxReconnectAttempts: 5,
  debug: false,
};
```

### 3.3 API 路径常量

为了避免硬编码 API 路径和保持一致性，前端提供了一个 API 路径常量文件 `apiConstants.ts`。所有组件都应该使用这个文件中定义的常量，而不是直接硬编码 API 路径。

```typescript
// 下载任务相关API
export const DownloadAPI = {
  // 基础路径
  BASE: '/downloads',
  // 添加下载任务
  ADD: '/downloads',
  // 暂停下载任务
  PAUSE: (taskId: string) => `/downloads/${taskId}/pause`,
  // 恢复下载任务
  RESUME: (taskId: string) => `/downloads/${taskId}/resume`,
  // 取消下载任务
  CANCEL: (taskId: string) => `/downloads/${taskId}/cancel`,
} as const;

// 任务管理相关API
export const TaskAPI = {
  // 基础路径
  BASE: '/tasks',
  // 获取所有任务
  GET_ALL: '/tasks',
  // 获取特定状态的任务
  GET_BY_STATUS: (status: string) => `/tasks?status=${status}`,
  // 获取特定任务
  GET: (taskId: string) => `/tasks/${taskId}`,
  // 更新任务
  UPDATE: (taskId: string) => `/tasks/${taskId}`,
  // 删除任务
  DELETE: (taskId: string) => `/tasks/${taskId}`,
  // 设置任务下载速度限制
  SET_DOWNLOAD_SPEED: (taskId: string) => `/tasks/${taskId}/speed/download`,
} as const;
```

### 3.4 使用示例

#### 3.4.1 添加下载任务

```typescript
import { DownloadAPI } from '@/utils/apiConstants';

async function addDownloadTask(url, outputPath) {
  try {
    const response = await axios.post(DownloadAPI.ADD, {
      url: url,
      output_path: outputPath
    });
    
    if (response.data && response.data.code === 0) {
      return response.data.data.id;
    }
    throw new Error(response.data.message || '添加任务失败');
  } catch (error) {
    console.error('添加任务失败:', error);
    throw error;
  }
}
```

#### 3.4.2 获取任务列表

```typescript
import { TaskAPI } from '@/utils/apiConstants';

async function getTaskList(status = null, page = 1, pageSize = 20) {
  try {
    let url = TaskAPI.GET_ALL;
    if (status) {
      url = TaskAPI.GET_BY_STATUS(status);
    }
    
    url += `${url.includes('?') ? '&' : '?'}page=${page}&page_size=${pageSize}`;
    
    const response = await axios.get(url);
    
    if (response.data && response.data.code === 0) {
      return response.data.data;
    }
    throw new Error(response.data.message || '获取任务列表失败');
  } catch (error) {
    console.error('获取任务列表失败:', error);
    throw error;
  }
}
```

#### 3.4.3 WebSocket 实时通知

```typescript
class WebSocketManager {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectInterval: number;
  private maxReconnectAttempts: number;
  private reconnectAttempts = 0;
  private debug: boolean;
  private eventHandlers: Map<string, Function[]> = new Map();
  
  constructor(config: {
    url: string;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    debug?: boolean;
  }) {
    this.url = config.url;
    this.reconnectInterval = config.reconnectInterval || 3000;
    this.maxReconnectAttempts = config.maxReconnectAttempts || 5;
    this.debug = config.debug || false;
  }
  
  connect() {
    if (this.ws) {
      this.ws.close();
    }
    
    this.ws = new WebSocket(this.url);
    
    this.ws.onopen = () => {
      this.log('WebSocket 连接已建立');
      this.reconnectAttempts = 0;
      this.trigger('open');
    };
    
    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.log('收到消息:', data);
        
        if (data.event) {
          this.trigger(data.event, data.data);
        }
      } catch (error) {
        this.log('解析消息失败:', error);
      }
    };
    
    this.ws.onclose = () => {
      this.log('WebSocket 连接已关闭');
      this.trigger('close');
      
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        this.log(`尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        setTimeout(() => this.connect(), this.reconnectInterval);
      }
    };
    
    this.ws.onerror = (error) => {
      this.log('WebSocket 错误:', error);
      this.trigger('error', error);
    };
  }
  
  on(event: string, callback: Function) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event)!.push(callback);
  }
  
  off(event: string, callback?: Function) {
    if (!this.eventHandlers.has(event)) {
      return;
    }
    
    if (!callback) {
      this.eventHandlers.delete(event);
      return;
    }
    
    const handlers = this.eventHandlers.get(event)!;
    const index = handlers.indexOf(callback);
    
    if (index !== -1) {
      handlers.splice(index, 1);
    }
    
    if (handlers.length === 0) {
      this.eventHandlers.delete(event);
    }
  }
  
  private trigger(event: string, data?: any) {
    if (!this.eventHandlers.has(event)) {
      return;
    }
    
    for (const callback of this.eventHandlers.get(event)!) {
      try {
        callback(data);
      } catch (error) {
        this.log('事件处理器错误:', error);
      }
    }
  }
  
  private log(...args: any[]) {
    if (this.debug) {
      console.log('[WebSocketManager]', ...args);
    }
  }
}

// 使用示例
const wsManager = new WebSocketManager({
  url: 'ws://localhost:8080/api/v1/ws',
  debug: true,
});

wsManager.on('task_progress', (data) => {
  console.log('任务进度更新:', data);
  // 更新 UI
});

wsManager.on('task_status_changed', (data) => {
  console.log('任务状态变更:', data);
  // 更新 UI
});

wsManager.connect();
```

## 4. 后端开发计划

### 4.1 开发阶段概览

| 阶段 | 优先级 | 预计时间 | 主要内容 |
|------|--------|----------|----------|
| 第一阶段 | 高 | 4-6周 | 核心功能完善 |
| 第二阶段 | 中 | 6-8周 | 功能扩展 |
| 第三阶段 | 低 | 8-10周 | 高级功能 |

### 4.2 第一阶段：核心功能完善

#### 4.2.1 时间规则 API 接口实现 ✅
- **描述**：实现已在路由中定义的时间规则相关 API
- **接口**：
  - `POST /speed/time-rules` - 添加时间规则
  - `GET /speed/time-rules` - 获取所有时间规则
  - `DELETE /speed/time-rules/:id` - 删除时间规则
- **工作量**：中等
- **依赖**：带宽调度器
- **状态**：已完成

#### 4.2.2 任务上传速度限制 API ✅
- **描述**：完善任务级别的上传速度限制 API
- **接口**：
  - `POST /tasks/{task_id}/speed/upload` - 设置任务上传速度限制
  - `GET /tasks/{task_id}/speed/upload` - 获取任务上传速度限制
- **工作量**：小
- **依赖**：带宽调度器
- **状态**：已完成

#### 4.2.3 任务过滤和分页 ✅
- **描述**：增强任务列表 API，支持更多过滤条件和分页
- **接口**：
  - 增强 `GET /tasks` - 添加分页参数、排序参数和更多过滤条件
- **工作量**：中等
- **依赖**：任务管理器
- **状态**：已完成

#### 4.2.4 配置管理 API ✅
- **描述**：添加系统配置管理 API
- **接口**：
  - `GET /config` - 获取系统配置
  - `POST /config` - 更新系统配置
  - `GET /config/:key` - 获取特定配置项
  - `POST /config/:key` - 更新特定配置项
- **工作量**：中等
- **依赖**：配置管理器
- **状态**：已完成

### 4.3 第二阶段：功能扩展

#### 4.3.1 批量操作 API
- **描述**：添加批量任务操作 API
- **接口**：
  - `POST /batch/downloads/pause` - 批量暂停任务
  - `POST /batch/downloads/resume` - 批量恢复任务
  - `POST /batch/downloads/cancel` - 批量取消任务
  - `DELETE /batch/tasks` - 批量删除任务
- **工作量**：中等
- **依赖**：下载管理器、任务管理器

#### 4.3.2 BitTorrent 特定 API
- **描述**：添加 BitTorrent 协议特定的 API
- **接口**：
  - `GET /torrents/{info_hash}/info` - 获取种子信息
  - `GET /torrents/{info_hash}/peers` - 获取对等点列表
  - `GET /torrents/{info_hash}/files` - 获取文件列表
  - `POST /torrents/{info_hash}/files` - 设置文件优先级
- **工作量**：大
- **依赖**：BitTorrent 协议实现

#### 4.3.3 任务优先级和标签 API
- **描述**：添加任务优先级和标签管理 API
- **接口**：
  - `POST /tasks/{task_id}/priority` - 设置任务优先级
  - `GET /tasks/{task_id}/priority` - 获取任务优先级
  - `POST /tasks/{task_id}/tags` - 添加任务标签
  - `GET /tasks/{task_id}/tags` - 获取任务标签
  - `DELETE /tasks/{task_id}/tags/{tag_id}` - 删除任务标签
  - `GET /tags` - 获取所有标签
- **工作量**：中等
- **依赖**：任务管理器

### 4.4 第三阶段：高级功能

#### 4.4.1 插件 API
- **描述**：添加插件系统 API
- **接口**：
  - `GET /plugins` - 获取所有插件
  - `GET /plugins/{id}` - 获取插件详情
  - `POST /plugins/{id}/enable` - 启用插件
  - `POST /plugins/{id}/disable` - 禁用插件
  - `POST /plugins/{id}/config` - 更新插件配置
- **工作量**：大
- **依赖**：插件系统

#### 4.4.2 事件订阅 API
- **描述**：添加事件订阅 API
- **接口**：
  - `POST /events/subscribe` - 订阅事件
  - `POST /events/unsubscribe` - 取消订阅事件
  - `GET /events/subscriptions` - 获取所有订阅
- **工作量**：中等
- **依赖**：事件通知系统

#### 4.4.3 统计和监控 API
- **描述**：添加系统统计和监控 API
- **接口**：
  - `GET /stats/system` - 获取系统资源使用情况
  - `GET /stats/network` - 获取网络统计信息
  - `GET /stats/tasks` - 获取任务统计信息
- **工作量**：中等
- **依赖**：统计模块

#### 3.4.4 更新任务信息

```typescript
import { TaskAPI } from '@/utils/apiConstants';

async function updateTask(taskId, updateData) {
  try {
    const response = await axios.put(TaskAPI.UPDATE(taskId), updateData);
    
    if (response.data && response.data.code === 0) {
      return response.data.data;
    }
    throw new Error(response.data.message || '更新任务失败');
  } catch (error) {
    console.error('更新任务失败:', error);
    throw error;
  }
}

// 使用示例
const taskId = '550e8400-e29b-41d4-a716-446655440000';
const updateData = {
  file_name: 'new_file_name.zip',
  output_path: '/new/path/to/save'
};

updateTask(taskId, updateData)
  .then(updatedTask => {
    console.log('任务已更新:', updatedTask);
  })
  .catch(error => {
    console.error('更新任务失败:', error);
  });
```