//! HTTP下载器网络请求处理模块

use anyhow::{Result, anyhow};
use bytes::Bytes;
use futures::Stream;
use futures::StreamExt;
use tracing::{debug, error};

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// Download a chunk of the file
    pub(crate) async fn download_chunk(&self, start: u64, end: Option<u64>) -> Result<impl Stream<Item = Result<Bytes, reqwest::Error>>> {
        // 构建请求头
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(reqwest::header::USER_AGENT, "Tonitru-Downloader/1.0".parse().unwrap());
        headers.insert(reqwest::header::ACCEPT, "*/*".parse().unwrap());
        
        // 使用chunk_size来确定下载块的大小
        let end = match end {
            Some(end) => Some(end),
            None => {
                if self.supports_range {
                    // 如果支持范围请求，使用chunk_size来限制请求大小
                    Some(start + self.chunk_size - 1)
                } else {
                    None
                }
            }
        };
        
        if self.supports_range {
            let range = match end {
                Some(end) => format!("bytes={}-{}", start, end),
                None => format!("bytes={}-", start),
            };
            headers.insert(reqwest::header::RANGE, range.parse().unwrap());
            debug!("Setting range header: {}", range);
        }
    
        // 打印请求信息
        debug!("Sending request to {}", self.url);
        
        // 创建取消通道的接收端（用于支持下载中断/取消）
        // 使用 broadcast 通道支持多个接收者，外部可以安全地广播取消信号
        let mut cancel_rx = match &self.cancel_sender {
            Some(sender) => {
                // 订阅取消信号
                sender.subscribe()
            },
            None => {
                // 兜底：没有 sender，返回一个不会收到信号的 receiver
                let (_, dummy_rx) = tokio::sync::broadcast::channel::<()>(1);
                dummy_rx
            }
        };
    
        // 使用HttpClientTrait的get_with_headers方法，而不是inner().get()
        let response = match self.client.get_with_headers(&self.url, headers).await {
            Ok(resp) => resp,
            Err(e) => {
                error!("Failed to send HTTP request: {}", e);
                return Err(anyhow!("Failed to send HTTP request: {}", e));
            }
        };
    
        // 打印响应信息
        let status = response.status();
        debug!("Received response: status={}", status);
    
        if !status.is_success() {
            // 尝试获取响应体内容以提供更详细的错误信息
            let error_body = match response.text().await {
                Ok(body) => {
                    if body.len() > 1000 {
                        format!("{}..... (truncated)", &body[..1000])
                    } else {
                        body
                    }
                },
                Err(_) => "<unable to read response body>".to_string(),
            };
            
            error!("HTTP request failed with status {}: {}", status, error_body);
            return Err(anyhow!("Failed to download chunk: HTTP {} - {}", status, error_body));
        }
        
        // 获取响应流
        let stream = response.bytes_stream();
        
        // 创建一个可以被取消的流
        let stream = futures::stream::unfold(
            (stream, cancel_rx),
            |(mut stream, mut cancel_rx)| Box::pin(async move {
                tokio::select! {
                    // 尝试从流中获取下一个数据块
                    chunk = stream.next() => {
                        if let Some(chunk) = chunk {
                            Some((chunk, (stream, cancel_rx)))
                        } else {
                            None
                        }
                    },
                    // 如果收到取消信号，则中断流
                    _ = cancel_rx.recv() => {
                        debug!("Download cancelled via cancel channel");
                        None
                    },
                }
            }),
        );
    
        Ok(stream)
    }

    /// 验证服务器是否支持断点续传
    pub(crate) async fn verify_range_support(&mut self) -> Result<bool> {
        debug!("Verifying range support for URL: {}", self.url);

        // 发送HEAD请求检查服务器支持
        let head_response = match self.client.head(&self.url).await {
            Ok(resp) => resp,
            Err(e) => {
                error!("Failed to send HEAD request for range support check: {}", e);
                return Err(anyhow!("Failed to send HEAD request: {}", e));
            }
        };

        // 检查Accept-Ranges头
        let supports_range = head_response.headers()
            .get(reqwest::header::ACCEPT_RANGES)
            .map(|v| v.to_str().unwrap_or("").contains("bytes"))
            .unwrap_or(false);

        // 获取文件大小
        let content_length = head_response.headers()
            .get(reqwest::header::CONTENT_LENGTH)
            .and_then(|v| v.to_str().ok())
            .and_then(|v| v.parse::<u64>().ok());

        // 更新下载器状态
        self.supports_range = supports_range;
        self.total_size = content_length;

        debug!("Range support: {}, Content-Length: {:?}", supports_range, content_length);

        // 如果服务器声称支持范围请求，进行实际测试
        if supports_range && content_length.is_some() {
            let total_size = content_length.unwrap();
            if total_size > 1 {
                // 尝试请求最后一个字节来验证范围支持
                match self.test_range_request(total_size - 1, total_size - 1).await {
                    Ok(actual_supports) => {
                        if !actual_supports {
                            debug!("Server claims to support ranges but test failed");
                            self.supports_range = false;
                        }
                        Ok(self.supports_range)
                    },
                    Err(e) => {
                        debug!("Range support test failed: {}, assuming no range support", e);
                        self.supports_range = false;
                        Ok(false)
                    }
                }
            } else {
                Ok(supports_range)
            }
        } else {
            Ok(supports_range)
        }
    }

    /// 测试范围请求是否真正有效
    async fn test_range_request(&self, start: u64, end: u64) -> Result<bool> {
        debug!("Testing range request: bytes={}-{}", start, end);

        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(reqwest::header::USER_AGENT, "Tonitru-Downloader/1.0".parse().unwrap());
        headers.insert(reqwest::header::RANGE, format!("bytes={}-{}", start, end).parse().unwrap());

        match self.client.get_with_headers(&self.url, headers).await {
            Ok(response) => {
                let status = response.status();
                debug!("Range test response status: {}", status);

                // 206 Partial Content 表示范围请求成功
                if status == reqwest::StatusCode::PARTIAL_CONTENT {
                    // 验证Content-Range头
                    if let Some(content_range) = response.headers().get(reqwest::header::CONTENT_RANGE) {
                        if let Ok(range_str) = content_range.to_str() {
                            debug!("Content-Range: {}", range_str);
                            return Ok(range_str.contains(&format!("{}-{}", start, end)));
                        }
                    }
                    Ok(true)
                } else if status == reqwest::StatusCode::OK {
                    // 服务器返回完整内容，不支持范围请求
                    debug!("Server returned 200 OK instead of 206, range not supported");
                    Ok(false)
                } else {
                    debug!("Range request failed with status: {}", status);
                    Ok(false)
                }
            },
            Err(e) => {
                debug!("Range test request failed: {}", e);
                Ok(false)
            }
        }
    }
}
