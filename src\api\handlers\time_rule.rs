use axum::{
    extract::{Path, State},
    Json,
};
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use tracing::warn;

use crate::api::state::AppState;

use crate::api::response::{ApiResponse, ApiError};
use crate::download::bandwidth_scheduler::TimeBasedSpeedRule;

/// 时间段限速规则请求
#[derive(Debug, Deserialize)]
pub struct TimeBasedRuleRequest {
    /// 开始小时 (0-23)
    pub start_hour: u8,
    /// 开始分钟 (0-59)
    pub start_minute: u8,
    /// 结束小时 (0-23)
    pub end_hour: u8,
    /// 结束分钟 (0-59)
    pub end_minute: u8,
    /// 星期几 (0 = 周日, 1 = 周一, ..., 6 = 周六)
    pub days: Vec<u8>,
    /// 下载速度限制（字节/秒），None表示不限速
    pub download_limit: Option<u64>,
    /// 上传速度限制（字节/秒），None表示不限速
    pub upload_limit: Option<u64>,
}

/// 时间段限速规则响应
#[derive(Debug, Serialize)]
pub struct TimeBasedRuleResponse {
    /// 规则ID
    pub id: Uuid,
    /// 开始小时 (0-23)
    pub start_hour: u8,
    /// 开始分钟 (0-59)
    pub start_minute: u8,
    /// 结束小时 (0-23)
    pub end_hour: u8,
    /// 结束分钟 (0-59)
    pub end_minute: u8,
    /// 星期几 (0 = 周日, 1 = 周一, ..., 6 = 周六)
    pub days: Vec<u8>,
    /// 下载速度限制（字节/秒），None表示不限速
    pub download_limit: Option<u64>,
    /// 上传速度限制（字节/秒），None表示不限速
    pub upload_limit: Option<u64>,
}

impl From<TimeBasedSpeedRule> for TimeBasedRuleResponse {
    fn from(rule: TimeBasedSpeedRule) -> Self {
        Self {
            id: rule.id,
            start_hour: rule.start_hour,
            start_minute: rule.start_minute,
            end_hour: rule.end_hour,
            end_minute: rule.end_minute,
            days: rule.days_of_week,
            download_limit: rule.download_limit,
            upload_limit: rule.upload_limit,
        }
    }
}

/// 添加时间段限速规则
pub async fn add_time_rule(
    State(app_state): State<AppState>,
    Json(request): Json<TimeBasedRuleRequest>,
) -> Result<Json<ApiResponse<TimeBasedRuleResponse>>, ApiError> {
    // 获取带宽调度器
    let bandwidth_scheduler = app_state.bandwidth_scheduler
        .ok_or_else(|| ApiError::internal_error("Bandwidth scheduler not available".to_string()))?;

    // 验证请求参数
    if request.start_hour > 23 || request.end_hour > 23 {
        return Err(ApiError::bad_request("Hour must be between 0 and 23"));
    }

    if request.start_minute > 59 || request.end_minute > 59 {
        return Err(ApiError::bad_request("Minute must be between 0 and 59"));
    }

    if request.days.is_empty() {
        return Err(ApiError::bad_request("At least one day must be specified"));
    }

    for day in &request.days {
        if *day > 6 {
            return Err(ApiError::bad_request("Day must be between 0 and 6"));
        }
    }

    // 验证至少有一个速度限制
    if request.download_limit.is_none() && request.upload_limit.is_none() {
        return Err(ApiError::bad_request("At least one of download_limit or upload_limit must be specified"));
    }

    // 验证速度限制值的合理性
    if let Some(limit) = request.download_limit {
        if limit == 0 {
            return Err(ApiError::bad_request("Download limit cannot be zero, use null for no limit"));
        }
    }

    if let Some(limit) = request.upload_limit {
        if limit == 0 {
            return Err(ApiError::bad_request("Upload limit cannot be zero, use null for no limit"));
        }
    }

    // 创建规则
    let rule = TimeBasedSpeedRule {
        id: Uuid::new_v4(),
        name: format!("Rule {}", Uuid::new_v4().to_string().split('-').next().unwrap_or("New")),
        start_hour: request.start_hour,
        start_minute: request.start_minute,
        end_hour: request.end_hour,
        end_minute: request.end_minute,
        days_of_week: request.days,
        download_limit: request.download_limit,
        upload_limit: request.upload_limit,
    };

    // 添加规则
    let _rule_id = bandwidth_scheduler.add_time_based_rule(rule.clone()).await
        .map_err(|e| ApiError::internal_error(format!("Failed to add time-based rule: {}", e)))?;

    // 确保更新时间规则
    if let Err(e) = bandwidth_scheduler.update_time_based_limits().await {
        warn!("Failed to update time-based limits after adding rule: {}", e);
    }

    // 返回响应
    Ok(Json(ApiResponse::success(TimeBasedRuleResponse::from(rule))))
}

/// 获取所有时间段限速规则
pub async fn get_all_time_rules(
    State(app_state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<TimeBasedRuleResponse>>>, ApiError> {
    // 获取带宽调度器
    let bandwidth_scheduler = app_state.bandwidth_scheduler
        .ok_or_else(|| ApiError::internal_error("Bandwidth scheduler not available".to_string()))?;

    // 获取所有规则
    let rules = bandwidth_scheduler.get_time_based_rules().await
        .map_err(|e| ApiError::internal_error(format!("Failed to get time-based rules: {}", e)))?;

    // 转换为响应格式
    let responses = rules.into_iter()
        .map(TimeBasedRuleResponse::from)
        .collect();

    // 返回响应
    Ok(Json(ApiResponse::success(responses)))
}

/// 删除时间段限速规则
pub async fn remove_time_rule(
    State(app_state): State<AppState>,
    Path(rule_id): Path<Uuid>,
) -> Result<Json<ApiResponse<()>>, ApiError> {
    // 获取带宽调度器
    let bandwidth_scheduler = app_state.bandwidth_scheduler
        .ok_or_else(|| ApiError::internal_error("Bandwidth scheduler not available".to_string()))?;

    // 检查规则是否存在
    let rules = bandwidth_scheduler.get_time_based_rules().await
        .map_err(|e| ApiError::internal_error(format!("Failed to get time-based rules: {}", e)))?;

    let rule_exists = rules.iter().any(|rule| rule.id == rule_id);
    if !rule_exists {
        return Err(ApiError::not_found(format!("Time-based rule not found: {}", rule_id)));
    }

    // 删除规则
    bandwidth_scheduler.remove_time_based_rule(rule_id).await
        .map_err(|e| ApiError::internal_error(format!("Failed to remove time-based rule: {}", e)))?;

    // 确保更新时间规则
    if let Err(e) = bandwidth_scheduler.update_time_based_limits().await {
        warn!("Failed to update time-based limits after removing rule: {}", e);
    }

    // 返回响应
    Ok(Json(ApiResponse::success(())))
}
