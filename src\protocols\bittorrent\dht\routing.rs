use std::fs::File;
use std::io::{Read, Write};
use std::path::Path;
use std::time::Duration;
use anyhow::{Result, anyhow};
use serde::{Serialize, Deserialize};
use tracing::warn;

use super::node::{DHTNode, NodeId, SerializableDHTNode};

/// 路由表配置
#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct RoutingTableConfig {
    /// K值（每个桶的最大节点数）
    pub k: usize,
    /// 节点超时时间（秒）
    pub node_timeout: u64,
    /// 最大失败次数
    pub max_failures: u32,
    /// 是否替换可疑节点
    pub replace_questionable: bool,
}

impl Default for RoutingTableConfig {
    fn default() -> Self {
        Self {
            k: 8,
            node_timeout: 900, // 15分钟
            max_failures: 3,
            replace_questionable: true,
        }
    }
}

/// K桶（Kademlia路由表的一部分）
#[derive(Debug)]
pub struct KBucket {
    /// 桶中的节点
    pub nodes: Vec<DHTNode>,
    /// 桶的最大容量
    pub capacity: usize,
    /// 桶的前缀长度
    pub prefix_len: usize,
    /// 最大失败次数
    pub max_failures: u32,
    /// 是否替换可疑节点
    pub replace_questionable: bool,
}

impl KBucket {
    /// 创建新的K桶
    pub fn new(capacity: usize, prefix_len: usize, max_failures: u32, replace_questionable: bool) -> Self {
        Self {
            nodes: Vec::with_capacity(capacity),
            capacity,
            prefix_len,
            max_failures,
            replace_questionable,
        }
    }

    /// 添加节点到桶中
    pub fn add_node(&mut self, node: DHTNode) -> bool {
        // 如果节点已存在，更新它
        for existing_node in &mut self.nodes {
            if existing_node.id == node.id {
                existing_node.ip = node.ip;
                existing_node.port = node.port;
                existing_node.update_last_seen();
                return true;
            }
        }

        // 如果桶未满，直接添加
        if self.nodes.len() < self.capacity {
            self.nodes.push(node);
            return true;
        }

        // 桶已满，尝试移除过期或可疑节点
        let timeout = Duration::from_secs(900); // 15分钟

        // 首先尝试移除过期节点
        let mut expired_index = None;
        for (i, existing_node) in self.nodes.iter().enumerate() {
            if existing_node.is_expired(timeout) {
                expired_index = Some(i);
                break;
            }
        }

        if let Some(index) = expired_index {
            self.nodes.remove(index);
            self.nodes.push(node);
            return true;
        }

        // 如果配置允许，尝试移除可疑节点
        if self.replace_questionable {
            let mut questionable_index = None;
            for (i, existing_node) in self.nodes.iter().enumerate() {
                if existing_node.is_questionable(self.max_failures) {
                    questionable_index = Some(i);
                    break;
                }
            }

            if let Some(index) = questionable_index {
                self.nodes.remove(index);
                self.nodes.push(node);
                return true;
            }
        }

        // 无法添加节点
        false
    }

    /// 获取桶中最接近给定ID的节点
    pub fn get_closest_nodes(&self, target_id: &NodeId, count: usize) -> Vec<DHTNode> {
        let mut nodes = self.nodes.clone();
        nodes.sort_by(|a, b| {
            let dist_a = a.id.distance(target_id);
            let dist_b = b.id.distance(target_id);
            dist_a.as_bytes().cmp(dist_b.as_bytes())
        });

        nodes.into_iter().take(count).collect()
    }

    /// 移除过期节点
    pub fn remove_expired_nodes(&mut self, timeout_duration: Duration) {
        self.nodes.retain(|node| !node.is_expired(timeout_duration));
    }

    /// 获取节点数量
    pub fn len(&self) -> usize {
        self.nodes.len()
    }

    /// 检查桶是否为空
    pub fn is_empty(&self) -> bool {
        self.nodes.is_empty()
    }
}

/// 路由表（Kademlia DHT的核心数据结构）
#[derive(Debug)]
pub struct RoutingTable {
    /// K桶数组
    pub buckets: Vec<KBucket>,
    /// 本地节点ID
    pub node_id: NodeId,
    /// 配置
    pub config: RoutingTableConfig,
    /// 节点总数
    pub node_count: usize,
}

/// 可序列化的路由表
#[derive(Debug, Serialize, Deserialize)]
pub struct SerializableRoutingTable {
    /// 节点列表
    pub nodes: Vec<SerializableDHTNode>,
    /// 本地节点ID
    pub node_id: NodeId,
    /// 配置
    pub config: RoutingTableConfig,
}

impl RoutingTable {
    /// 创建新的路由表
    pub fn new(node_id: NodeId, config: RoutingTableConfig) -> Self {
        let mut buckets = Vec::with_capacity(160); // 160位ID空间
        for i in 0..160 {
            buckets.push(KBucket::new(
                config.k,
                i,
                config.max_failures,
                config.replace_questionable
            ));
        }

        Self {
            buckets,
            node_id,
            config,
            node_count: 0,
        }
    }

    /// 添加节点到路由表
    pub fn add_node(&mut self, node: DHTNode) -> bool {
        // 不添加自己
        if node.id == self.node_id {
            return false;
        }

        // 计算桶索引
        let bucket_index = self.get_bucket_index(&node.id);
        let result = self.buckets[bucket_index].add_node(node);

        // 更新节点计数
        if result {
            self.update_node_count();
        }

        result
    }

    /// 获取最接近给定ID的节点
    pub fn get_closest_nodes(&self, target_id: &NodeId, count: usize) -> Vec<DHTNode> {
        let bucket_index = self.get_bucket_index(target_id);

        // 首先从目标ID所在的桶中获取节点
        let mut result = self.buckets[bucket_index].get_closest_nodes(target_id, count);

        // 如果节点不足，从相邻的桶中获取更多节点
        let mut i = 1;
        while result.len() < count && (bucket_index >= i || bucket_index + i < self.buckets.len()) {
            // 交替检查左右桶
            if bucket_index >= i {
                let left_bucket = &self.buckets[bucket_index - i];
                result.extend(left_bucket.get_closest_nodes(target_id, count - result.len()));
            }

            if result.len() < count && bucket_index + i < self.buckets.len() {
                let right_bucket = &self.buckets[bucket_index + i];
                result.extend(right_bucket.get_closest_nodes(target_id, count - result.len()));
            }

            i += 1;
        }

        // 按距离排序并限制数量
        result.sort_by(|a, b| {
            let dist_a = a.id.distance(target_id);
            let dist_b = b.id.distance(target_id);
            dist_a.as_bytes().cmp(dist_b.as_bytes())
        });

        result.truncate(count);
        result
    }

    /// 获取节点所属的桶索引
    pub fn get_bucket_index(&self, node_id: &NodeId) -> usize {
        // 计算与本地节点ID的共同前缀长度
        let prefix_len = self.node_id.common_prefix_len(node_id);

        // 桶索引是前缀长度，最大为159
        std::cmp::min(prefix_len, 159)
    }

    /// 移除所有桶中的过期节点
    pub fn remove_expired_nodes(&mut self) {
        let timeout = Duration::from_secs(self.config.node_timeout);
        for bucket in &mut self.buckets {
            bucket.remove_expired_nodes(timeout);
        }

        self.update_node_count();
    }

    /// 更新节点计数
    fn update_node_count(&mut self) {
        self.node_count = self.buckets.iter().map(|bucket| bucket.len()).sum();
    }

    /// 获取节点总数
    pub fn node_count(&self) -> usize {
        self.node_count
    }

    /// 计算IPv6节点数量
    pub fn count_ipv6_nodes(&self) -> usize {
        let mut count = 0;

        for bucket in &self.buckets {
            for node in &bucket.nodes {
                if let std::net::IpAddr::V6(_) = node.ip {
                    count += 1;
                }
            }
        }

        count
    }

    /// 获取节点
    pub fn get_node(&self, node_id: &NodeId) -> Option<DHTNode> {
        let bucket_index = self.get_bucket_index(node_id);

        for node in &self.buckets[bucket_index].nodes {
            if node.id == *node_id {
                return Some(node.clone());
            }
        }

        None
    }

    /// 更新节点状态
    pub fn update_node(&mut self, node_id: &NodeId, success: bool) -> Result<()> {
        let bucket_index = self.get_bucket_index(node_id);

        for node in &mut self.buckets[bucket_index].nodes {
            if node.id == *node_id {
                if success {
                    node.update_last_seen();
                } else {
                    node.increment_failures();
                }
                return Ok(());
            }
        }

        Err(anyhow!("Node not found in routing table"))
    }

    /// 转换为可序列化的路由表
    pub fn to_serializable(&self) -> SerializableRoutingTable {
        // 收集所有节点
        let mut nodes = Vec::new();

        for bucket in &self.buckets {
            for node in &bucket.nodes {
                nodes.push(node.to_serializable());
            }
        }

        SerializableRoutingTable {
            nodes,
            node_id: self.node_id,
            config: self.config.clone(),
        }
    }

    /// 从可序列化的路由表创建
    pub fn from_serializable(serializable: SerializableRoutingTable) -> Result<Self> {
        // 创建新的路由表
        let mut table = Self::new(serializable.node_id, serializable.config);

        // 添加所有节点
        for serializable_node in serializable.nodes {
            match DHTNode::from_serializable(serializable_node) {
                Ok(node) => {
                    table.add_node(node);
                },
                Err(e) => {
                    warn!("Failed to deserialize node: {}", e);
                }
            }
        }

        Ok(table)
    }

    /// 保存路由表到文件
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        // 转换为可序列化的路由表
        let serializable = self.to_serializable();

        // 序列化为JSON
        let json = serde_json::to_string_pretty(&serializable)?;

        // 写入文件
        let mut file = File::create(path)?;
        file.write_all(json.as_bytes())?;

        Ok(())
    }

    /// 从文件加载路由表
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        // 读取文件
        let mut file = File::open(path)?;
        let mut contents = String::new();
        file.read_to_string(&mut contents)?;

        // 反序列化
        let serializable: SerializableRoutingTable = serde_json::from_str(&contents)?;

        // 创建路由表
        Self::from_serializable(serializable)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};

    #[test]
    fn test_kbucket_new() {
        let capacity = 8;
        let prefix_len = 5;
        let max_failures = 3;
        let replace_questionable = true;

        let bucket = KBucket::new(capacity, prefix_len, max_failures, replace_questionable);

        assert_eq!(bucket.capacity, capacity);
        assert_eq!(bucket.prefix_len, prefix_len);
        assert_eq!(bucket.max_failures, max_failures);
        assert_eq!(bucket.replace_questionable, replace_questionable);
        assert!(bucket.nodes.is_empty());
    }

    #[test]
    fn test_kbucket_add_node() {
        let mut bucket = KBucket::new(2, 0, 3, true);
        let node_id1 = NodeId::new_random();
        let node_id2 = NodeId::new_random();
        let node_id3 = NodeId::new_random();

        let node1 = DHTNode::new(node_id1, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
        let node2 = DHTNode::new(node_id2, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2)), 6882);
        let node3 = DHTNode::new(node_id3, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 3)), 6883);

        // 添加第一个节点
        assert!(bucket.add_node(node1.clone()));
        assert_eq!(bucket.len(), 1);

        // 添加第二个节点
        assert!(bucket.add_node(node2.clone()));
        assert_eq!(bucket.len(), 2);

        // 桶已满，但实现可能允许替换，所以不测试返回值
        bucket.add_node(node3.clone());
        // 确保桶中仍然只有2个节点
        assert_eq!(bucket.len(), 2);
    }

    #[test]
    fn test_routing_table_new() {
        let node_id = NodeId::new_random();
        let config = RoutingTableConfig::default();

        let table = RoutingTable::new(node_id, config.clone());

        assert_eq!(table.node_id, node_id);
        assert_eq!(table.config.k, config.k);
        assert_eq!(table.buckets.len(), 160);
        assert_eq!(table.node_count, 0);
    }

    #[test]
    fn test_routing_table_add_node() {
        let local_id = NodeId::new_random();
        let config = RoutingTableConfig::default();

        let mut table = RoutingTable::new(local_id, config);

        // 创建一个节点
        let remote_id = NodeId::new_random();
        let node = DHTNode::new(remote_id, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        // 添加节点
        assert!(table.add_node(node.clone()));
        assert_eq!(table.node_count, 1);

        // 不应该添加自己
        assert!(!table.add_node(DHTNode::new(local_id, IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881)));
        assert_eq!(table.node_count, 1);
    }
}