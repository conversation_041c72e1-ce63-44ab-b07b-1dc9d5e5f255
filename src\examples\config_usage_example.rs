//! 配置管理器使用示例
//!
//! 本示例展示了如何使用ConfigManager的泛型get和set方法，
//! 以及它们与原有get_value和set_value方法的对比。

use anyhow::Result;
use crate::config::config_manager::ConfigManager;

/// 演示ConfigManager的使用方法
pub async fn demonstrate_config_usage() -> Result<()> {
    // 创建ConfigManager实例
    let config_manager = ConfigManager::new().await?;
    
    // 使用原有的基于字符串的方法
    {
        // 设置配置值（需要手动转换为字符串）
        config_manager.set_value("server.port", "8080".to_string()).await?;
        config_manager.set_value("download.concurrent_downloads", "5".to_string()).await?;
        config_manager.set_value("mirror.enabled", "true".to_string()).await?;
        
        // 获取配置值（需要手动解析字符串）
        let port_str = config_manager.get_value("server.port").await?;
        let port = port_str.parse::<u16>().unwrap_or(8080);
        
        let concurrent_downloads_str = config_manager.get_value("download.concurrent_downloads").await?;
        let concurrent_downloads = concurrent_downloads_str.parse::<u32>().unwrap_or(3);
        
        let mirror_enabled_str = config_manager.get_value("mirror.enabled").await?;
        let mirror_enabled = mirror_enabled_str.parse::<bool>().unwrap_or(false);
        
        println!("使用原有方法获取的配置：");
        println!("  端口：{}", port);
        println!("  并发下载数：{}", concurrent_downloads);
        println!("  镜像启用：{}", mirror_enabled);
    }
    
    // 使用新的泛型方法
    {
        // 设置配置值（自动序列化）
        config_manager.set("server.port", 9090).await?;
        config_manager.set("download.concurrent_downloads", 10).await?;
        config_manager.set("mirror.enabled", false).await?;
        
        // 获取配置值（自动反序列化）
        let port: u16 = config_manager.get("server.port").await?;
        let concurrent_downloads: u32 = config_manager.get("download.concurrent_downloads").await?;
        let mirror_enabled: bool = config_manager.get("mirror.enabled").await?;
        
        println!("\n使用泛型方法获取的配置：");
        println!("  端口：{}", port);
        println!("  并发下载数：{}", concurrent_downloads);
        println!("  镜像启用：{}", mirror_enabled);
    }
    
    // 使用泛型方法处理复杂类型
    {
        // 定义一个复杂类型
        #[derive(serde::Serialize, serde::Deserialize, Debug)]
        struct ProxyConfig {
            enabled: bool,
            url: String,
            proxy_type: String,
        }
        
        // 设置复杂类型配置
        let proxy_config = ProxyConfig {
            enabled: true,
            url: "http://proxy.example.com:8080".to_string(),
            proxy_type: "http".to_string(),
        };
        
        // 使用泛型方法设置和获取复杂类型
        config_manager.set("proxy.config", &proxy_config).await?;
        let retrieved_config: ProxyConfig = config_manager.get("proxy.config").await?;
        
        println!("\n使用泛型方法处理复杂类型：");
        println!("  代理启用：{}", retrieved_config.enabled);
        println!("  代理URL：{}", retrieved_config.url);
        println!("  代理类型：{}", retrieved_config.proxy_type);
    }
    
    Ok(())
}