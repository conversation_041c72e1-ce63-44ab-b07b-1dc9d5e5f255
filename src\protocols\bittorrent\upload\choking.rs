use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use anyhow::Result;
use async_trait::async_trait;
use tokio::sync::Mutex;
use tracing::{debug, info, warn};

use crate::protocols::bittorrent::peer::PeerInfo;
use crate::protocols::bittorrent::message::BitTorrentMessage;

use super::{UploadConfig, UploadStrategy, StandardUploadStrategy};

/// 阻塞算法接口
/// 定义阻塞算法的行为
#[async_trait]
pub trait ChokingAlgorithm: Send + Sync {
    /// 运行阻塞算法
    async fn run(&self, peers: &HashMap<SocketAddr, PeerInfo>) -> Result<HashMap<SocketAddr, Vec<BitTorrentMessage>>>;
    
    /// 更新对等点统计信息
    async fn update_peer_stats(&self, addr: SocketAddr, uploaded: u64, download_rate: u64) -> Result<()>;
    
    /// 重置统计信息
    async fn reset_stats(&self) -> Result<()>;
    
    /// 获取当前解除阻塞的对等点
    async fn get_unchoked_peers(&self) -> Result<Vec<SocketAddr>>;
    
    /// 获取当前乐观解除阻塞的对等点
    async fn get_optimistic_unchoked_peer(&self) -> Result<Option<SocketAddr>>;
}

/// 标准阻塞算法
/// 实现基于上传速率的阻塞算法
pub struct StandardChokingAlgorithm {
    /// 上传配置
    config: UploadConfig,
    
    /// 上传策略
    strategy: Arc<dyn UploadStrategy>,
    
    /// 上次运行时间
    last_run: Arc<Mutex<std::time::Instant>>,
    
    /// 当前解除阻塞的对等点
    unchoked_peers: Arc<Mutex<Vec<SocketAddr>>>,
    
    /// 当前乐观解除阻塞的对等点
    optimistic_unchoked_peer: Arc<Mutex<Option<SocketAddr>>>,
}

impl StandardChokingAlgorithm {
    /// 创建新的标准阻塞算法
    pub fn new(config: UploadConfig) -> Self {
        let strategy = Arc::new(StandardUploadStrategy::new(config.clone()));
        
        Self {
            config,
            strategy,
            last_run: Arc::new(Mutex::new(std::time::Instant::now())),
            unchoked_peers: Arc::new(Mutex::new(Vec::new())),
            optimistic_unchoked_peer: Arc::new(Mutex::new(None)),
        }
    }
    
    /// 创建带自定义上传策略的标准阻塞算法
    pub fn with_strategy(config: UploadConfig, strategy: Arc<dyn UploadStrategy>) -> Self {
        Self {
            config,
            strategy,
            last_run: Arc::new(Mutex::new(std::time::Instant::now())),
            unchoked_peers: Arc::new(Mutex::new(Vec::new())),
            optimistic_unchoked_peer: Arc::new(Mutex::new(None)),
        }
    }
    
    /// 检查是否需要运行阻塞算法
    async fn need_run(&self) -> bool {
        let last_run = *self.last_run.lock().await;
        let interval = std::time::Duration::from_secs(self.config.choking_interval);
        
        last_run.elapsed() >= interval
    }
    
    /// 更新运行时间
    async fn update_run_time(&self) {
        let mut last_run = self.last_run.lock().await;
        *last_run = std::time::Instant::now();
    }
}

#[async_trait]
impl ChokingAlgorithm for StandardChokingAlgorithm {
    async fn run(&self, peers: &HashMap<SocketAddr, PeerInfo>) -> Result<HashMap<SocketAddr, Vec<BitTorrentMessage>>> {
        let mut messages = HashMap::new();
        
        // 检查是否需要运行阻塞算法
        if !self.need_run().await {
            return Ok(messages);
        }
        
        // 获取当前解除阻塞的对等点
        let old_unchoked_peers = self.unchoked_peers.lock().await.clone();
        
        // 选择要解除阻塞的对等点
        let new_unchoked_peers = self.strategy.select_unchoked_peers(peers).await?;
        
        // 选择要乐观解除阻塞的对等点
        let new_optimistic_unchoked_peer = self.strategy.select_optimistic_unchoked_peer(peers).await?;
        
        // 更新当前解除阻塞的对等点
        *self.unchoked_peers.lock().await = new_unchoked_peers.clone();
        *self.optimistic_unchoked_peer.lock().await = new_optimistic_unchoked_peer;
        
        // 合并所有解除阻塞的对等点
        let mut all_unchoked_peers = new_unchoked_peers.clone();
        if let Some(peer) = new_optimistic_unchoked_peer {
            if !all_unchoked_peers.contains(&peer) {
                all_unchoked_peers.push(peer);
            }
        }
        
        // 处理新解除阻塞的对等点
        for addr in &all_unchoked_peers {
            if !old_unchoked_peers.contains(addr) {
                // 新解除阻塞的对等点，发送解除阻塞消息
                let peer_messages = messages.entry(*addr).or_insert_with(Vec::new);
                peer_messages.push(BitTorrentMessage::Unchoke);
                
                debug!("Unchoking peer: {}", addr);
            }
        }
        
        // 处理新阻塞的对等点
        for addr in &old_unchoked_peers {
            if !all_unchoked_peers.contains(addr) {
                // 新阻塞的对等点，发送阻塞消息
                let peer_messages = messages.entry(*addr).or_insert_with(Vec::new);
                peer_messages.push(BitTorrentMessage::Choke);
                
                debug!("Choking peer: {}", addr);
            }
        }
        
        // 更新运行时间
        self.update_run_time().await;
        
        Ok(messages)
    }
    
    async fn update_peer_stats(&self, addr: SocketAddr, uploaded: u64, download_rate: u64) -> Result<()> {
        self.strategy.update_peer_stats(addr, uploaded, download_rate).await
    }
    
    async fn reset_stats(&self) -> Result<()> {
        self.strategy.reset_stats().await
    }
    
    async fn get_unchoked_peers(&self) -> Result<Vec<SocketAddr>> {
        Ok(self.unchoked_peers.lock().await.clone())
    }
    
    async fn get_optimistic_unchoked_peer(&self) -> Result<Option<SocketAddr>> {
        Ok(*self.optimistic_unchoked_peer.lock().await)
    }
}
