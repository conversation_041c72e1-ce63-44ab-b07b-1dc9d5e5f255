//! HTTP下载器速度统计模块

use std::time::{Duration, Instant};

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// 更新下载速度
    pub(crate) fn update_speed(&mut self) {
        if let Some(last_update) = self.last_speed_update {
            let now = Instant::now();
            let elapsed = now.duration_since(last_update);

            // 每秒更新一次速度
            if elapsed >= Duration::from_secs(1) {
                let bytes_per_second = (self.bytes_since_speed_update as f64 / elapsed.as_secs_f64()) as u64;
                self.current_speed = bytes_per_second;
                self.last_speed_update = Some(now);
                self.bytes_since_speed_update = 0;
            }
        }
    }
}
