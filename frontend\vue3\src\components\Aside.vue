<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '../stores/app'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()

// 当前活动的菜单项
const activeMenu = computed(() => {
  const path = route.path
  if (path.startsWith('/task')) {
    return 'task'
  } else if (path.startsWith('/preference')) {
    return 'preference'
  }
  return 'task'
})

// 菜单项点击处理
const handleMenuClick = (menu: string) => {
  switch (menu) {
    case 'task':
      router.push('/task')
      break
    case 'add':
      appStore.showAddTaskDialog('uri')
      break
    case 'preference':
      router.push('/preference')
      break
    default:
      break
  }
}
</script>

<template>
  <aside class="aside hidden-xs-only">
    <div class="aside-inner">
      <div class="logo">
        <router-link to="/">
          <el-icon><Download /></el-icon>
        </router-link>
      </div>
      <ul class="menu">
        <li 
          class="menu-item" 
          :class="{ active: activeMenu === 'task' }"
          @click="handleMenuClick('task')"
        >
          <el-icon><List /></el-icon>
          <span class="menu-item-title">任务</span>
        </li>
        <li 
          class="menu-item" 
          @click="handleMenuClick('add')"
        >
          <el-icon><Plus /></el-icon>
          <span class="menu-item-title">新建</span>
        </li>
        <li 
          class="menu-item" 
          :class="{ active: activeMenu === 'preference' }"
          @click="handleMenuClick('preference')"
        >
          <el-icon><Setting /></el-icon>
          <span class="menu-item-title">设置</span>
        </li>
      </ul>
      <div class="app-info">
        <div class="app-version">
          <span>v{{ appStore.appVersion }}</span>
        </div>
      </div>
    </div>
  </aside>
</template>

<style scoped>
.aside {
  width: 78px;
  height: 100%;
  background-color: var(--aside-background);
  color: var(--aside-text-color);
  overflow: hidden;
}

.aside-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px 0;
}

.logo {
  text-align: center;
  margin-bottom: 36px;
}

.logo a {
  display: inline-block;
  color: var(--aside-text-color);
  font-size: 24px;
}

.menu {
  flex: 1;
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 68px;
  cursor: pointer;
  transition: all 0.2s;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.menu-item.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.menu-item .el-icon {
  font-size: 22px;
  margin-bottom: 8px;
}

.menu-item-title {
  font-size: 12px;
}

.app-info {
  text-align: center;
  font-size: 12px;
  opacity: 0.5;
}
</style>
