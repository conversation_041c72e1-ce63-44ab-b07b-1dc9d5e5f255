use anyhow::Result;
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{debug, warn};

use crate::core::p2p::peer::Peer;
use crate::core::p2p::piece::{PieceManager, PieceState};
use crate::protocols::bittorrent::block_manager::BlockManager;
use crate::protocols::bittorrent::BitTorrentPeer;
use crate::protocols::bittorrent::piece_selector::PieceSelector;
use crate::protocols::bittorrent::torrent::TorrentInfo;

/// 消息处理器，负责处理BitTorrent消息
#[derive(Clone)]
pub struct MessageProcessor {
    /// 分片选择器
    piece_selector: Arc<Mutex<PieceSelector>>,
}

impl MessageProcessor {
    /// 创建一个新的消息处理器
    pub fn new(piece_selector: Arc<Mutex<PieceSelector>>) -> Self {
        Self {
            piece_selector,
        }
    }
    
    /// 设置分片选择器
    pub fn set_piece_selector(&mut self, piece_selector: Arc<Mutex<PieceSelector>>) {
        self.piece_selector = piece_selector;
    }

    /// 处理位图消息
    pub async fn process_bitfield(
        &self,
        peer: &mut BitTorrentPeer,
        bitfield: &[u8],
        torrent_info: Option<&TorrentInfo>,
        piece_manager: Option<&Arc<Mutex<dyn PieceManager>>>
    ) -> Result<()> {
        debug!("Received bitfield of length {}", bitfield.len());

        // 清空之前的位图信息
        peer.state.pieces_have.clear();

        // 获取总分片数
        let num_pieces = if let Some(info) = torrent_info {
            (info.total_size + info.piece_length - 1) / info.piece_length
        } else {
            return Err(anyhow::anyhow!("Missing torrent info"));
        };

        // 解析位图
        for i in 0..num_pieces {
            let byte_index = (i / 8) as usize;
            if byte_index >= bitfield.len() {
                break;
            }
            let bit_index = 7 - (i % 8);
            if (bitfield[byte_index] & (1 << bit_index)) != 0 {
                peer.state.pieces_have.insert(i as u32);
            }
        }

        // 更新分片选择器中的对等点评分
        let mut piece_selector = self.piece_selector.lock().await;
        piece_selector.update_peer_score(
            peer.connection.common.peer_info.addr,
            peer.connection.common.download_speed(), // 使用下载速度（字节/秒）作为性能指标
            true,
            0 // 没有响应时间数据，设为0
        );

        // 更新分片状态（如果有分片管理器）
        if let Some(piece_manager) = piece_manager {
            let mut pm = piece_manager.lock().await;
            // 遍历peer拥有的所有分片，更新它们的状态
            for piece_index in &peer.state.pieces_have {
                // 使用update_piece_state方法更新分片状态
                // 这里我们不改变分片状态，只是通知分片管理器这个peer拥有这个分片
                if let Err(e) = pm.update_piece_state(*piece_index, PieceState::Missing).await {
                    debug!("Failed to update piece state for index={}: {}", piece_index, e);
                }
            }
        }

        debug!("Peer has {} pieces", peer.state.pieces_have.len());

        Ok(())
    }

    /// 处理分片数据
    pub async fn process_piece_data(
        &self,
        peer: &mut BitTorrentPeer,
        index: u32,
        begin: u32,
        data: Vec<u8>,
        piece_manager: &Arc<Mutex<dyn PieceManager>>,
        block_manager: &mut BlockManager,
        torrent_info: Option<&TorrentInfo>,
        downloaded: &mut u64
    ) -> Result<()> {
        debug!("Received piece {} (offset {}, size {})",
               index, begin, data.len());

        // 更新下载统计
        *downloaded += data.len() as u64;

        // 获取对等点地址
        let peer_addr = peer.connection.common.peer_info.addr;

        // 获取分片选择器
        let mut piece_selector = self.piece_selector.lock().await;

        // 添加块数据到块管理器
        let start_time = std::time::Instant::now();
        let result = block_manager.add_block_data(index, begin, data, torrent_info).await;
        let response_time = start_time.elapsed().as_millis() as u64;

        match result {
            Ok(true) => {
                // 块已完成，更新对等点评分
                piece_selector.update_peer_score(
                    peer_addr,
                    peer.connection.common.download_speed(), // 使用下载速度（字节/秒）作为性能指标
                    true,
                    response_time
                );

                // 检查分片是否已完成
                if let Some(piece_data) = block_manager.get_completed_piece(index).await? {
                    debug!("Piece {} completed, size: {} bytes", index, piece_data.len());

                    // 添加分片数据到分片管理器
                    let mut piece_manager = piece_manager.lock().await;
                    match piece_manager.add_piece_data(index, piece_data).await {
                        Ok(true) => {
                            debug!("Piece {} verified and saved", index);
                            // 分片验证成功，更新对等点评分
                            piece_selector.update_peer_score(
                                peer_addr,
                                peer.connection.common.uploaded(),
                                true,
                                response_time
                            );
                        },
                        Ok(false) => {
                            warn!("Piece {} verification failed", index);
                            // 分片验证失败，更新对等点评分
                            piece_selector.update_peer_score(
                                peer_addr,
                                peer.connection.common.download_speed(), // 使用下载速度（字节/秒）作为性能指标
                                false,
                                response_time
                            );
                        },
                        Err(e) => {
                            warn!("Failed to add piece data: {}", e);
                            // 添加分片数据失败，更新对等点评分
                            piece_selector.update_peer_score(
                                peer_addr,
                                peer.connection.common.download_speed(), // 使用下载速度（字节/秒）作为性能指标
                                false,
                                response_time
                            );
                        }
                    }
                }
            },
            Ok(false) => {
                // 块添加成功但分片未完成，更新对等点评分
                piece_selector.update_peer_score(
                    peer_addr,
                    peer.connection.common.download_speed(),
                    true,
                    response_time
                );
            },
            Err(e) => {
                warn!("Failed to add block: {}", e);
                // 块添加失败，更新对等点评分
                piece_selector.update_peer_score(
                    peer_addr,
                    peer.connection.common.download_speed(),
                    false,
                    response_time
                );
            }
        }

        Ok(())
    }
}