<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { RouterView } from 'vue-router'
import { useThemeStore } from './stores/theme'
import { useAppStore } from './stores/app'
import TitleBar from './components/TitleBar.vue'

// 状态管理
const themeStore = useThemeStore()
const appStore = useAppStore()

// 计算属性
const themeClass = computed(() => {
  return `theme-${themeStore.currentTheme}`
})

// 监听主题变化
watch(() => themeStore.currentTheme, () => {
  updateRootClassName()
})

// 更新根元素的类名
const updateRootClassName = () => {
  document.documentElement.className = themeClass.value
}

// 解析URL参数
const parseUrlParams = () => {
  // 获取URL参数
  const urlParams = new URLSearchParams(window.location.search)
  
  // 检查是否有下载链接参数
  const downloadUrl = urlParams.get('url')
  if (downloadUrl) {
    // 更新应用状态中的下载链接
    appStore.updateAddTaskUrl(downloadUrl)
    // 显示添加任务对话框
    appStore.showAddTaskDialog('uri')
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化主题
  themeStore.initTheme()
  // 更新根元素类名
  updateRootClassName()
  // 获取应用信息
  appStore.fetchAppInfo()
  // 解析URL参数
  parseUrlParams()
})
</script>

<template>
  <div id="app">
    <TitleBar />
    <el-container id="container">
      <RouterView />
    </el-container>
  </div>
</template>

<style>
html,
body {
  height: 100%;
  padding: 0;
  margin: 0;
  font-family: "Monospaced Number", "Chinese Quote", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-variant: tabular-nums;
  font-size: 14px;
}

#app,
#container {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.4);
}

::-webkit-scrollbar-thumb:window-inactive {
  background-color: rgba(0, 0, 0, 0.25);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

::-webkit-resizer {
  display: none;
}

/* 主题相关 */
.theme-light {
  --app-background: transparent;
  --aside-background: rgba(0, 0, 0, 0.8);
  --aside-text-color: #fff;
  --subnav-background: #f4f5f7;
  --subnav-text-color: #4c5161;
  --main-background: #fff;
}

.theme-dark {
  --app-background: transparent;
  --aside-background: rgba(0, 0, 0, 0.9);
  --aside-text-color: #fff;
  --subnav-background: #2D2D2D;
  --subnav-text-color: #ddd;
  --main-background: #343434;
}

/* 布局相关 */
.aside {
  background-color: var(--aside-background);
  color: var(--aside-text-color);
}

.subnav {
  background-color: var(--subnav-background);
  color: var(--subnav-text-color);
}

.main {
  background-color: var(--main-background);
}

/* 响应式布局 */
@media only screen and (max-width: 767px) {
  .hidden-xs-only {
    display: none !important;
  }
}

@media only screen and (min-width: 768px) {
  .hidden-sm-and-up {
    display: none !important;
  }
}
</style>
