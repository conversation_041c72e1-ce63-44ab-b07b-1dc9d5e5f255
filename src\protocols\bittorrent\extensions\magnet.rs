use std::collections::HashMap;
use anyhow::{Result, anyhow};
use url::Url;
use hex;

/// 磁力链接信息
#[derive(Debug, Clone)]
pub struct MagnetLinkInfo {
    /// 信息哈希 (20字节)
    pub info_hash: [u8; 20],

    /// 信息哈希 (十六进制字符串)
    pub info_hash_hex: String,

    /// 显示名称 (可选)
    pub display_name: Option<String>,

    /// Tracker URL (可选)
    pub tracker_urls: Vec<String>,

    /// WebSeed URL (可选)
    pub webseed_urls: Vec<String>,

    /// 其他参数
    pub params: HashMap<String, String>,
}

/// 磁力链接解析器
pub struct MagnetLinkParser;

impl MagnetLinkParser {
    /// 解析磁力链接
    pub fn parse(magnet_link: &str) -> Result<MagnetLinkInfo> {
        // 验证链接格式
        if !magnet_link.starts_with("magnet:?") {
            return Err(anyhow!("Invalid magnet link format"));
        }

        // 解析URL
        let url = Url::parse(magnet_link)?;

        // 提取参数
        let mut params = HashMap::new();
        for (key, value) in url.query_pairs() {
            params.insert(key.to_string(), value.to_string());
        }

        // 提取信息哈希 (xt参数)
        let info_hash = Self::extract_info_hash(&params)?;
        let info_hash_hex = hex::encode(&info_hash);

        // 提取显示名称 (dn参数)
        let display_name = params.get("dn").cloned();

        // 提取Tracker URL (tr参数，可能有多个)
        let mut tracker_urls = Vec::new();
        for (key, value) in params.iter() {
            if key == "tr" {
                tracker_urls.push(value.clone());
            }
        }

        // 提取WebSeed URL (ws参数，可能有多个)
        let mut webseed_urls = Vec::new();
        for (key, value) in params.iter() {
            if key == "ws" {
                webseed_urls.push(value.clone());
            }
        }

        Ok(MagnetLinkInfo {
            info_hash,
            info_hash_hex,
            display_name,
            tracker_urls,
            webseed_urls,
            params,
        })
    }

    /// 提取信息哈希
    fn extract_info_hash(params: &HashMap<String, String>) -> Result<[u8; 20]> {
        // 查找xt参数
        let xt = params.get("xt").ok_or_else(|| anyhow!("Missing xt parameter"))?;

        // 验证xt参数格式
        if !xt.starts_with("urn:btih:") {
            return Err(anyhow!("Invalid xt parameter format"));
        }

        // 提取哈希部分
        let hash_str = &xt[9..];

        // 解码哈希
        let hash_bytes = if hash_str.len() == 40 {
            // 十六进制格式
            hex::decode(hash_str)?
        } else if hash_str.len() == 32 {
            // Base32格式
            base32::decode(base32::Alphabet::Rfc4648 { padding: false }, hash_str)
                .ok_or_else(|| anyhow!("Invalid base32 encoding"))?
        } else {
            return Err(anyhow!("Invalid hash length"));
        };

        // 验证哈希长度
        if hash_bytes.len() != 20 {
            return Err(anyhow!("Invalid hash length after decoding"));
        }

        // 转换为固定长度数组
        let mut info_hash = [0u8; 20];
        info_hash.copy_from_slice(&hash_bytes);

        Ok(info_hash)
    }

    /// 创建磁力链接
    pub fn create_magnet_link(info_hash: &[u8], name: Option<&str>, trackers: &[&str]) -> String {
        let mut link = format!("magnet:?xt=urn:btih:{}", hex::encode(info_hash));

        // 添加显示名称
        if let Some(name) = name {
            link.push_str(&format!("&dn={}", urlencoding::encode(name)));
        }

        // 添加Tracker
        for tracker in trackers {
            link.push_str(&format!("&tr={}", urlencoding::encode(tracker)));
        }

        link
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_magnet_link() {
        let magnet = "magnet:?xt=urn:btih:c12fe1c06bba254a9dc9f519b335aa7c1367a88a&dn=Test+File&tr=http%3A%2F%2Ftracker.example.com%2Fannounce";

        let info = MagnetLinkParser::parse(magnet).unwrap();

        assert_eq!(info.info_hash_hex, "c12fe1c06bba254a9dc9f519b335aa7c1367a88a");
        assert_eq!(info.display_name, Some("Test+File".to_string()));
        assert_eq!(info.tracker_urls.len(), 1);
        assert_eq!(info.tracker_urls[0], "http%3A%2F%2Ftracker.example.com%2Fannounce");
    }

    #[test]
    fn test_create_magnet_link() {
        let info_hash = hex::decode("c12fe1c06bba254a9dc9f519b335aa7c1367a88a").unwrap();
        let name = "Test File";
        let trackers = vec!["http://tracker.example.com/announce"];

        let magnet = MagnetLinkParser::create_magnet_link(&info_hash, Some(name), &trackers.iter().map(|s| *s).collect::<Vec<_>>());

        assert!(magnet.contains("urn:btih:c12fe1c06bba254a9dc9f519b335aa7c1367a88a"));
        assert!(magnet.contains("dn=Test%20File"));
        assert!(magnet.contains("tr=http%3A%2F%2Ftracker.example.com%2Fannounce"));
    }
}
