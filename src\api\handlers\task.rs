use axum::{
    extract::{Path, State, Query},
    Json,
};
use tracing::{debug, info, error};
use uuid::Uuid;
use serde::Deserialize;

use crate::api::response::{ApiResponse, error_response};
use crate::download::manager::{TaskInfo, TaskStatus};

/// Get information about a download task
use crate::api::state::AppState;

pub async fn get_task(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
) -> ApiResponse<TaskInfo> {
    let download_manager = app_state.download_manager;

    info!("Getting task: task_id={}", task_id);

    match download_manager.get_task(task_id).await {
        Ok(task_info) => ApiResponse::success(task_info),
        Err(e) => {
            error!("Failed to get task info: task_id={}, error={}", task_id, e);
            error_response(e)
        },
    }
}

/// 任务查询参数
#[derive(Debug, Deserialize)]
pub struct TaskQueryParams {
    /// 任务状态过滤
    pub status: Option<String>,
    /// 排序字段
    pub sort_by: Option<String>,
    /// 排序方向 (asc 或 desc)
    pub sort_order: Option<String>,
    /// 分页: 页码 (从1开始)
    pub page: Option<u32>,
    /// 分页: 每页数量
    pub per_page: Option<u32>,
    /// URL过滤 (包含指定字符串的URL)
    pub url_contains: Option<String>,
    /// 创建时间过滤 (ISO 8601格式, 如 2023-01-01T00:00:00Z)
    pub created_after: Option<String>,
    /// 创建时间过滤 (ISO 8601格式, 如 2023-01-01T00:00:00Z)
    pub created_before: Option<String>,
}

/// Get information about all download tasks
pub async fn get_all_tasks(
    State(app_state): State<AppState>,
    Query(params): Query<TaskQueryParams>,
) -> Json<ApiResponse<Vec<TaskInfo>>> {
    let download_manager = app_state.download_manager;

    debug!("Getting tasks with filters: {:?}", params);

    // 获取所有任务
    let mut tasks = match download_manager.get_all_tasks().await {
        Ok(t) => t,
        Err(e) => {
            error!("Failed to get all tasks: error={}", e);
            return Json(error_response(e));
        }
    };

    // 应用状态过滤
    if let Some(status_str) = params.status {
        let status = match status_str.to_lowercase().as_str() {
            "pending" => Some(TaskStatus::Pending),
            "initializing" => Some(TaskStatus::Initializing),
            "downloading" => Some(TaskStatus::Downloading),
            "active" => Some(TaskStatus::Downloading), // 添加对active状态的支持，映射到downloading状态
            "paused" => Some(TaskStatus::Paused),
            "completed" => Some(TaskStatus::Completed),
            "failed" => Some(TaskStatus::Failed),
            "cancelled" => Some(TaskStatus::Cancelled),
            _ => None,
        };

        if let Some(status_filter) = status {
            tasks.retain(|task| task.status == status_filter);
        }
    }

    // 应用URL过滤
    if let Some(url_filter) = params.url_contains {
        if !url_filter.is_empty() {
            tasks.retain(|task| task.url.contains(&url_filter));
        }
    }

    // 应用创建时间过滤
    if let Some(created_after) = params.created_after {
        if let Ok(date) = chrono::DateTime::parse_from_rfc3339(&created_after) {
            let utc_date = date.with_timezone(&chrono::Utc);
            tasks.retain(|task| task.created_at >= utc_date);
        }
    }

    if let Some(created_before) = params.created_before {
        if let Ok(date) = chrono::DateTime::parse_from_rfc3339(&created_before) {
            let utc_date = date.with_timezone(&chrono::Utc);
            tasks.retain(|task| task.created_at <= utc_date);
        }
    }

    // 应用排序
    if let Some(sort_by) = params.sort_by {
        let is_ascending = params.sort_order.as_deref() != Some("desc");

        match sort_by.as_str() {
            "created_at" => {
                if is_ascending {
                    tasks.sort_by(|a, b| a.created_at.cmp(&b.created_at));
                } else {
                    tasks.sort_by(|a, b| b.created_at.cmp(&a.created_at));
                }
            },
            "updated_at" => {
                if is_ascending {
                    tasks.sort_by(|a, b| a.updated_at.cmp(&b.updated_at));
                } else {
                    tasks.sort_by(|a, b| b.updated_at.cmp(&a.updated_at));
                }
            },
            "progress" => {
                if is_ascending {
                    tasks.sort_by(|a, b| a.progress.partial_cmp(&b.progress).unwrap_or(std::cmp::Ordering::Equal));
                } else {
                    tasks.sort_by(|a, b| b.progress.partial_cmp(&a.progress).unwrap_or(std::cmp::Ordering::Equal));
                }
            },
            "speed" => {
                if is_ascending {
                    tasks.sort_by(|a, b| a.speed.cmp(&b.speed));
                } else {
                    tasks.sort_by(|a, b| b.speed.cmp(&a.speed));
                }
            },
            _ => {
                // 默认按创建时间降序排序
                tasks.sort_by(|a, b| b.created_at.cmp(&a.created_at));
            }
        }
    } else {
        // 默认按创建时间降序排序
        tasks.sort_by(|a, b| b.created_at.cmp(&a.created_at));
    }

    // 应用分页
    let page = params.page.unwrap_or(1).max(1);
    let per_page = params.per_page.unwrap_or(10).min(100);

    let start = ((page - 1) * per_page) as usize;
    let end = start + per_page as usize;

    let paginated_tasks = if start < tasks.len() {
        tasks[start..tasks.len().min(end)].to_vec()
    } else {
        Vec::new()
    };

    // 返回结果
    Json(ApiResponse::success(paginated_tasks))
}

/// Remove a download task
pub async fn remove_task(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
) -> ApiResponse<()> {
    let download_manager = app_state.download_manager;

    info!("Removing task: task_id={}", task_id);

    match download_manager.remove_task(task_id).await {
        Ok(_) => {
            info!("Task removed: task_id={}", task_id);
            ApiResponse::<()>::success_no_data()
        },
        Err(e) => {
            error!("Failed to remove task: task_id={}, error={}", task_id, e);
            error_response(e)
        },
    }
}

/// Update a download task
pub async fn update_task(
    State(app_state): State<AppState>,
    Path(task_id): Path<Uuid>,
    Json(task): Json<TaskInfo>,
) -> ApiResponse<TaskInfo> {
    let download_manager = app_state.download_manager;

    info!("Updating task: task_id={}", task_id);

    // 确保任务ID一致
    let mut updated_task = task;
    updated_task.id = task_id;

    match download_manager.update_task(task_id, updated_task.clone()).await {
        Ok(_) => {
            info!("Task updated: task_id={}", task_id);
            // 获取更新后的任务信息
            match download_manager.get_task(task_id).await {
                Ok(task_info) => ApiResponse::success(task_info),
                Err(e) => {
                    error!("Failed to get updated task info: task_id={}, error={}", task_id, e);
                    ApiResponse::success(updated_task) // 返回客户端提供的任务信息
                }
            }
        },
        Err(e) => {
            error!("Failed to update task: task_id={}, error={}", task_id, e);
            error_response(e)
        },
    }
}
