//! CLI 日志模块
//!
//! 提供CLI的日志记录功能，包括日志级别设置、彩色输出和格式化。

use std::io::Write;
use log::{LevelFilter, Level};
use env_logger::fmt::Color;
use env_logger::Builder;
use chrono::Local;

/// 根据CLI参数设置日志级别
pub fn setup_logging(verbose: bool, quiet: bool, debug: bool) -> LevelFilter {
    let log_level = if quiet {
        LevelFilter::Error
    } else if debug {
        LevelFilter::Trace
    } else if verbose {
        LevelFilter::Debug
    } else {
        LevelFilter::Info
    };

    // 初始化日志，添加彩色输出和时间戳
    let mut builder = Builder::new();
    builder.filter_level(log_level);
    
    // 设置自定义格式，包括时间戳、日志级别和彩色输出
    builder.format(|buf, record| {
        let mut level_style = buf.style();
        let color = match record.level() {
            Level::Error => Color::Red,
            Level::Warn => Color::Yellow,
            Level::Info => Color::Green,
            Level::Debug => Color::Blue,
            Level::Trace => Color::Magenta,
        };
        
        level_style.set_color(color).set_bold(true);
        
        let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S%.3f");
        
        writeln!(
            buf,
            "[{}] {} [{}:{}] {}",
            timestamp,
            level_style.value(record.level()),
            record.file().unwrap_or("unknown"),
            record.line().unwrap_or(0),
            record.args()
        )
    });
    
    // 如果是调试模式，添加更多的环境变量过滤器
    if debug {
        // 设置一些常见库的日志级别为更高级别，以减少噪音
        builder.filter(Some("reqwest"), LevelFilter::Warn);
        builder.filter(Some("hyper"), LevelFilter::Warn);
        builder.filter(Some("tokio"), LevelFilter::Warn);
    }
    
    builder.init();
    
    log_level
}

/// 获取日志级别的名称
pub fn get_log_level_name(level: LevelFilter) -> &'static str {
    match level {
        LevelFilter::Off => "OFF",
        LevelFilter::Error => "ERROR",
        LevelFilter::Warn => "WARN",
        LevelFilter::Info => "INFO",
        LevelFilter::Debug => "DEBUG",
        LevelFilter::Trace => "TRACE",
    }
}