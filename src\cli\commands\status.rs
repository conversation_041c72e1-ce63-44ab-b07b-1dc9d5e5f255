//! 状态命令模块
//!
//! 实现与系统状态相关的命令。

use anyhow::Result;
use clap::{Args, Subcommand};
use std::sync::Arc;
use std::time::Duration;
use tokio::time;
use log::{debug, trace, info, warn};
use serde_json::json;

use crate::cli::backend::backend::CliBackend;
use crate::cli::utils::formatter::OutputFormat;
use crate::cli::utils::formatter::format_output;
use crate::cli::utils::debug::{print_debug_info, print_debug_object, print_debug_json, DebugLevel, DebugTimer};

/// 状态命令
#[derive(Subcommand, Debug)]
pub enum StatusCommands {
    /// 获取系统状态
    System,
    /// 获取下载统计信息
    Stats,
    /// 监控系统状态
    Monitor(MonitorArgs),
}

/// 监控参数
#[derive(Args, Debug)]
pub struct MonitorArgs {
    /// 刷新间隔（秒）
    #[arg(short, long, default_value = "1")]
    pub interval: u64,
    /// 监控次数（0表示无限）
    #[arg(short, long, default_value = "0")]
    pub count: u64,
}

/// 处理状态命令
pub async fn handle_command(
    command: &StatusCommands,
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("处理状态命令: {:?}", command);
    
    let result = match command {
        StatusCommands::System => handle_system_status(backend, format).await,
        StatusCommands::Stats => handle_download_stats(backend, format).await,
        StatusCommands::Monitor(args) => handle_monitor(args, backend, format).await,
    };
    
    if let Err(ref e) = result {
        warn!("状态命令执行失败: {}", e);
    } else {
        debug!("状态命令执行成功");
    }
    
    result
}

/// 处理系统状态命令
async fn handle_system_status(
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行获取系统状态");
    
    let mut timer = DebugTimer::new("获取系统状态");
    
    let status = backend.get_system_status().await?;
    timer.checkpoint("获取状态数据");
    
    debug!("获取到系统状态数据");
    
    let json_value = serde_json::json!(status);
    print_debug_json("系统状态JSON", &json_value, DebugLevel::Full);
    format_output(json_value, format.unwrap_or(OutputFormat::Text));
    
    let elapsed = timer.stop();
    debug!("获取系统状态总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理下载统计命令
async fn handle_download_stats(
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行获取下载统计");
    
    let mut timer = DebugTimer::new("获取下载统计");
    
    let stats = backend.get_download_stats().await?;
    timer.checkpoint("获取统计数据");
    
    debug!("获取到下载统计数据: 活跃={}, 暂停={}, 完成={}, 失败={}", 
           stats.active_count, stats.paused_count, stats.completed_count, stats.failed_count);
    
    let json_value = serde_json::json!(stats);
    print_debug_json("下载统计JSON", &json_value, DebugLevel::Verbose);
    format_output(json_value, format.unwrap_or(OutputFormat::Text));
    
    let elapsed = timer.stop();
    debug!("获取下载统计总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理监控命令
async fn handle_monitor(
    args: &MonitorArgs,
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行系统监控: interval={}秒, count={}", args.interval, args.count);
    print_debug_object("监控参数", args, DebugLevel::Verbose);
    
    let interval = Duration::from_secs(args.interval);
    let mut counter = 0;
    
    let mut timer = DebugTimer::new("系统监控");
    
    loop {
        // 清屏
        print!("\x1B[2J\x1B[1;1H");
        
        trace!("监控迭代 #{}", counter + 1);
        
        // 获取系统状态
        let status = backend.get_system_status().await?;
        timer.checkpoint("获取系统状态");
        
        println!("系统状态监控 (刷新间隔: {}秒)\n", args.interval);
        format_output(serde_json::json!(status), format.unwrap_or(OutputFormat::Text));
        
        // 获取下载统计
        let stats = backend.get_download_stats().await?;
        timer.checkpoint("获取下载统计");
        
        println!("\n下载统计:\n");
        format_output(serde_json::json!(stats), format.unwrap_or(OutputFormat::Text));
        
        // 检查是否达到监控次数
        counter += 1;
        if args.count > 0 && counter >= args.count {
            debug!("已达到指定监控次数 {}, 停止监控", args.count);
            break;
        }
        
        debug!("完成监控迭代 #{}, 等待 {}秒", counter, args.interval);
        
        // 等待下一次刷新
        time::sleep(interval).await;
    }
    
    let elapsed = timer.stop();
    debug!("系统监控总耗时: {:?}, 总迭代次数: {}", elapsed, counter);
    
    Ok(())
}