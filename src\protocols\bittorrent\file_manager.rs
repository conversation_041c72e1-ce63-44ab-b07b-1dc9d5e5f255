use anyhow::{Result, anyhow};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use tokio::fs::{self, File, OpenOptions};
use tokio::io::{AsyncSeekExt, AsyncWriteExt, AsyncReadExt};

use super::torrent::TorrentInfo;

/// 文件管理器，负责管理文件操作
pub struct FileManager {
    /// 输出路径
    output_path: PathBuf,
    /// 种子信息
    torrent_info: TorrentInfo,
    /// 文件句柄缓存
    file_handles: HashMap<String, File>,
}

impl FileManager {
    /// 创建新的文件管理器
    pub async fn new(torrent_info: TorrentInfo, output_path: impl AsRef<Path>) -> Result<Self> {
        let output_path = output_path.as_ref().to_path_buf();

        // 创建文件管理器
        let file_manager = Self {
            output_path,
            torrent_info,
            file_handles: HashMap::new(),
        };

        // 初始化输出目录
        let file_manager = file_manager;
        file_manager.init_output_directory().await?;

        Ok(file_manager)
    }

    /// 初始化输出目录
    async fn init_output_directory(&self) -> Result<()> {
        // 创建输出目录
        if !self.output_path.exists() {
            fs::create_dir_all(&self.output_path).await?;
        }

        // 如果是多文件种子，创建子目录
        if let Some(files) = &self.torrent_info.files {
            if files.len() > 1 {
                let base_dir = self.output_path.join(&self.torrent_info.name);
                if !base_dir.exists() {
                    fs::create_dir_all(&base_dir).await?;
                }

                // 创建所有子目录
                for file in files {
                    let file_path = base_dir.join(&file.path);
                    if let Some(parent) = file_path.parent() {
                        if !parent.exists() {
                            fs::create_dir_all(parent).await?;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// 获取文件句柄
    async fn get_file_handle(&mut self, file_path: &str) -> Result<&mut File> {
        if !self.file_handles.contains_key(file_path) {
            // 构建完整路径
            let full_path = if let Some(files) = &self.torrent_info.files {
                if files.len() > 1 {
                    self.output_path.join(&self.torrent_info.name).join(file_path)
                } else {
                    self.output_path.join(file_path)
                }
            } else {
                self.output_path.join(file_path)
            };

            // 确保父目录存在
            if let Some(parent) = full_path.parent() {
                if !parent.exists() {
                    fs::create_dir_all(parent).await?;
                }
            }

            // 打开或创建文件
            let file = OpenOptions::new()
                .read(true)
                .write(true)
                .create(true)
                .open(&full_path)
                .await?;

            self.file_handles.insert(file_path.to_string(), file);
        }

        Ok(self.file_handles.get_mut(file_path).unwrap())
    }

    /// 写入分片数据到文件
    pub async fn write_piece_to_files(&mut self, piece_index: u32, piece_data: &[u8]) -> Result<()> {
        // 计算分片在文件中的偏移量
        let piece_offset = piece_index as u64 * self.torrent_info.piece_length;

        // 写入文件
        if let Some(files) = &self.torrent_info.files {
            if files.len() == 1 {
                // 单文件种子
                let file_path = files[0].path.clone();
                let file = self.get_file_handle(&file_path).await?;
                file.seek(std::io::SeekFrom::Start(piece_offset)).await?;
                file.write_all(piece_data).await?;
            } else {
                // 多文件种子
                let remaining_data = piece_data;
                let mut current_offset = piece_offset;

                // 收集需要处理的文件信息
                let mut file_operations = Vec::new();

                // 计算每个文件的起始偏移量
                let mut file_offset_start = 0u64;
                for file in files {
                    let file_start = file_offset_start;
                    let file_end = file_start + file.length;

                    // 如果分片与此文件有重叠
                    if current_offset < file_end && current_offset + remaining_data.len() as u64 > file_start {
                        // 计算在此文件中的偏移量
                        let file_offset = if current_offset > file_start {
                            current_offset - file_start
                        } else {
                            0
                        };

                        // 计算要写入此文件的数据长度
                        let data_offset = if current_offset < file_start {
                            (file_start - current_offset) as usize
                        } else {
                            0
                        };

                        let data_length = std::cmp::min(
                            remaining_data.len() - data_offset,
                            (file_end - std::cmp::max(current_offset, file_start)) as usize
                        );

                        // 收集操作信息
                        if data_length > 0 {
                            file_operations.push((
                                file.path.clone(),
                                file_offset,
                                data_offset,
                                data_length
                            ));

                            // 更新剩余数据跟踪
                            current_offset += data_offset as u64 + data_length as u64;

                            // 如果没有剩余数据，退出循环
                            if data_offset + data_length >= remaining_data.len() {
                                break;
                            }
                        }
                    }

                    // 更新下一个文件的起始偏移量
                    file_offset_start += file.length;
                }

                // 执行文件写入操作
                for op in file_operations {
                    let file_path = op.0;
                    let file_offset = op.1;
                    let data_offset = op.2;
                    let data_length = op.3;

                    let file_handle = self.get_file_handle(&file_path).await?;
                    file_handle.seek(std::io::SeekFrom::Start(file_offset)).await?;
                    file_handle.write_all(&remaining_data[data_offset..data_offset + data_length]).await?;
                }
            }
        } else {
            // 单文件种子（没有文件信息的情况）
            let file_path = self.torrent_info.name.clone();
            let file = self.get_file_handle(&file_path).await?;
            file.seek(std::io::SeekFrom::Start(piece_offset)).await?;
            file.write_all(piece_data).await?;
        }

        Ok(())
    }

    /// 关闭所有文件句柄
    pub async fn close_all_files(&mut self) -> Result<()> {
        self.file_handles.clear();
        Ok(())
    }

    /// 获取种子信息
    pub fn get_torrent_info(&self) -> &TorrentInfo {
        &self.torrent_info
    }

    /// 获取输出路径
    pub fn get_output_path(&self) -> &PathBuf {
        &self.output_path
    }

    /// 从文件中读取分片数据
    pub async fn read_piece_data(&self, piece_index: u32, begin: u32, length: u32) -> Result<Vec<u8>> {
        // 计算分片在文件中的偏移量
        let piece_offset = piece_index as u64 * self.torrent_info.piece_length;
        let absolute_offset = piece_offset + begin as u64;
        let mut result = vec![0u8; length as usize];

        // 从文件中读取数据
        if let Some(files) = &self.torrent_info.files {
            if files.len() == 1 {
                // 单文件种子
                let file_path = files[0].path.clone();
                let full_path = self.output_path.join(&file_path);

                let mut file = File::open(&full_path).await?;
                file.seek(std::io::SeekFrom::Start(absolute_offset)).await?;
                file.read_exact(&mut result).await?;
            } else {
                // 多文件种子
                let mut current_offset = absolute_offset;
                let mut bytes_read = 0usize;

                // 计算每个文件的起始偏移量
                let mut file_offset_start = 0u64;
                for file in files {
                    let file_start = file_offset_start;
                    let file_end = file_start + file.length;

                    // 如果请求的数据与此文件有重叠
                    if current_offset < file_end && current_offset + (length as u64 - bytes_read as u64) > file_start {
                        // 计算在此文件中的偏移量
                        let file_offset = if current_offset > file_start {
                            current_offset - file_start
                        } else {
                            0
                        };

                        // 计算要从此文件读取的数据长度
                        let _data_offset = if current_offset < file_start {
                            (file_start - current_offset) as usize
                        } else {
                            0
                        };

                        let data_length = std::cmp::min(
                            length as usize - bytes_read,
                            (file_end - std::cmp::max(current_offset, file_start)) as usize
                        );

                        // 读取数据
                        if data_length > 0 {
                            // 构建完整路径
                            let full_path = self.output_path.join(&self.torrent_info.name).join(&file.path);

                            // 打开文件并读取数据
                            let mut file_handle = File::open(&full_path).await?;
                            file_handle.seek(std::io::SeekFrom::Start(file_offset)).await?;

                            let mut buffer = vec![0u8; data_length];
                            file_handle.read_exact(&mut buffer).await?;

                            // 复制到结果缓冲区
                            result[bytes_read..bytes_read + data_length].copy_from_slice(&buffer);

                            // 更新读取位置
                            bytes_read += data_length;
                            current_offset += data_length as u64;

                            // 如果已读取所有请求的数据，退出循环
                            if bytes_read >= length as usize {
                                break;
                            }
                        }
                    }

                    // 更新下一个文件的起始偏移量
                    file_offset_start += file.length;
                }

                // 检查是否读取了所有请求的数据
                if bytes_read < length as usize {
                    return Err(anyhow!("Could not read all requested data: read {} of {} bytes",
                                      bytes_read, length));
                }
            }
        } else {
            // 单文件种子（没有文件信息的情况）
            let file_path = self.output_path.join(&self.torrent_info.name);

            let mut file = File::open(&file_path).await?;
            file.seek(std::io::SeekFrom::Start(absolute_offset)).await?;
            file.read_exact(&mut result).await?;
        }

        Ok(result)
    }
}
