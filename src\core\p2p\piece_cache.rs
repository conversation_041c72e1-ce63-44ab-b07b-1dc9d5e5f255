use anyhow::Result;
use bytes::Bytes;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tracing::debug;

/// 分片缓存项
#[derive(Clone)]
struct CacheItem {
    /// 缓存数据
    data: Bytes,
    /// 缓存时间
    timestamp: Instant,
    /// 访问次数
    access_count: u32,
    /// 最后访问时间
    last_accessed: Instant,
}

/// 分片缓存配置
#[derive(Clone, Debug)]
pub struct PieceCacheConfig {
    /// 是否启用缓存
    pub enabled: bool,
    /// 最大缓存项数量
    pub max_items: usize,
    /// 缓存项过期时间（秒）
    pub ttl: u64,
    /// 缓存清理间隔（秒）
    pub cleanup_interval: u64,
    /// 最大缓存大小（字节）
    pub max_size: usize,
}

impl Default for PieceCacheConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_items: 1000,
            ttl: 3600, // 1小时
            cleanup_interval: 300, // 5分钟
            max_size: 100 * 1024 * 1024, // 100MB
        }
    }
}

/// 分片缓存
/// 用于在内存中缓存分片数据，提高读取性能
#[derive(Clone)]
pub struct PieceCache {
    /// 缓存项
    cache: Arc<Mutex<HashMap<String, CacheItem>>>,
    /// 缓存配置
    config: PieceCacheConfig,
    /// 最后清理时间
    last_cleanup: Arc<Mutex<Instant>>,
    /// 缓存命中次数
    hits: Arc<Mutex<u64>>,
    /// 缓存未命中次数
    misses: Arc<Mutex<u64>>,
    /// 当前缓存大小（字节）
    current_size: Arc<Mutex<usize>>,
}

impl PieceCache {
    /// 创建新的分片缓存
    pub fn new(config: PieceCacheConfig) -> Self {
        Self {
            cache: Arc::new(Mutex::new(HashMap::new())),
            config,
            last_cleanup: Arc::new(Mutex::new(Instant::now())),
            hits: Arc::new(Mutex::new(0)),
            misses: Arc::new(Mutex::new(0)),
            current_size: Arc::new(Mutex::new(0)),
        }
    }

    /// 获取缓存项
    pub async fn get(&self, key: &str) -> Option<Bytes> {
        if !self.config.enabled {
            return None;
        }

        // 检查是否需要清理
        self.check_cleanup().await;

        let mut cache = self.cache.lock().await;

        if let Some(item) = cache.get_mut(key) {
            // 检查是否过期
            if item.timestamp.elapsed().as_secs() > self.config.ttl {
                // 移除过期项
                let item_size = item.data.len();
                cache.remove(key);

                // 更新缓存大小
                let mut current_size = self.current_size.lock().await;
                *current_size = current_size.saturating_sub(item_size);

                // 更新未命中计数
                let mut misses = self.misses.lock().await;
                *misses += 1;

                return None;
            }

            // 更新访问信息
            item.access_count += 1;
            item.last_accessed = Instant::now();

            // 更新命中计数
            let mut hits = self.hits.lock().await;
            *hits += 1;

            // 返回缓存数据
            Some(item.data.clone())
        } else {
            // 更新未命中计数
            let mut misses = self.misses.lock().await;
            *misses += 1;

            None
        }
    }

    /// 设置缓存项
    pub async fn set(&self, key: String, data: Bytes) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        // 检查是否需要清理
        self.check_cleanup().await;

        let mut cache = self.cache.lock().await;
        let data_size = data.len();

        // 检查数据大小是否超过最大缓存大小
        if data_size > self.config.max_size {
            debug!("Data size ({} bytes) exceeds max cache size ({} bytes)", data_size, self.config.max_size);
            return Ok(());
        }

        // 检查是否需要腾出空间
        let mut current_size = self.current_size.lock().await;
        while *current_size + data_size > self.config.max_size && !cache.is_empty() {
            // 移除最旧的项
            self.evict_oldest(&mut cache, &mut *current_size).await;
        }

        // 检查缓存大小
        if cache.len() >= self.config.max_items && !cache.contains_key(&key) {
            // 移除最旧的项
            self.evict_oldest(&mut cache, &mut *current_size).await;
        }

        // 如果已存在，先移除旧项并更新大小
        if let Some(old_item) = cache.get(&key) {
            *current_size = current_size.saturating_sub(old_item.data.len());
        }

        // 添加新项
        let item = CacheItem {
            data: data.clone(),
            timestamp: Instant::now(),
            access_count: 1,
            last_accessed: Instant::now(),
        };

        cache.insert(key, item);
        *current_size += data_size;

        Ok(())
    }

    /// 移除最旧的缓存项
    async fn evict_oldest(&self, cache: &mut HashMap<String, CacheItem>, current_size: &mut usize) {
        if cache.is_empty() {
            return;
        }

        // 查找最旧的项
        let oldest_key = cache.iter()
            .min_by_key(|(_, item)| item.last_accessed)
            .map(|(key, _)| key.clone());

        if let Some(key) = oldest_key {
            if let Some(item) = cache.remove(&key) {
                *current_size = current_size.saturating_sub(item.data.len());
                debug!("Evicted oldest cache item: {}, size: {} bytes", key, item.data.len());
            }
        }
    }

    /// 检查是否需要清理
    async fn check_cleanup(&self) {
        let mut last_cleanup = self.last_cleanup.lock().await;

        if last_cleanup.elapsed().as_secs() > self.config.cleanup_interval {
            // 更新最后清理时间
            *last_cleanup = Instant::now();

            // 清理过期项
            self.cleanup().await;
        }
    }

    /// 清理过期项
    async fn cleanup(&self) {
        let mut cache = self.cache.lock().await;
        let mut current_size = self.current_size.lock().await;

        let now = Instant::now();
        let ttl = Duration::from_secs(self.config.ttl);

        // 查找过期项
        let expired_keys: Vec<String> = cache.iter()
            .filter(|(_, item)| now.duration_since(item.timestamp) > ttl)
            .map(|(key, _)| key.clone())
            .collect();

        // 记录过期项数量
        let expired_count = expired_keys.len();

        // 移除过期项
        for key in &expired_keys {
            if let Some(item) = cache.remove(key) {
                *current_size = current_size.saturating_sub(item.data.len());
            }
        }

        if expired_count > 0 {
            debug!("Cleaned up {} expired cache items, current cache size: {} bytes", expired_count, *current_size);
        }
    }

    /// 获取缓存统计信息
    pub async fn stats(&self) -> (usize, u64, u64, usize) {
        let cache = self.cache.lock().await;
        let hits = *self.hits.lock().await;
        let misses = *self.misses.lock().await;
        let size = *self.current_size.lock().await;

        (cache.len(), hits, misses, size)
    }

    /// 获取元数据
    pub fn get_metadata(&self, key: &str) -> Option<String> {
        // 简单实现，使用 get 方法获取数据并转换为字符串
        // 在实际应用中，可能需要更复杂的实现
        tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                self.get(key).await.map(|bytes| String::from_utf8_lossy(&bytes).to_string())
            })
        })
    }

    /// 设置元数据
    pub fn set_metadata(&self, key: &str, value: &str) {
        // 简单实现，使用 set 方法设置数据
        // 在实际应用中，可能需要更复杂的实现
        let bytes = Bytes::from(value.to_string());
        tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                let _ = self.set(key.to_string(), bytes).await;
            })
        })
    }
}
