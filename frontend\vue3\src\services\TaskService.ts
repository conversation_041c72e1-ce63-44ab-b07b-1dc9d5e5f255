import axios from 'axios';
import { TaskAPI, DownloadAPI } from '../utils/apiConstants';
import type{ Task, TaskStatus } from '../stores/task';

/**
 * 任务服务类，处理与任务相关的API调用
 */
export class TaskService {
  /**
   * 获取任务列表
   * @param status 任务状态过滤器
   * @returns 任务列表
   */
  static async getTaskList(status: TaskStatus = 'all'): Promise<Task[]> {
    const response = await axios.get(TaskAPI.GET_BY_STATUS(status));
    return response.data.tasks || [];
  }

  /**
   * 暂停任务
   * @param taskId 任务ID
   * @returns API响应
   */
  static async pauseTask(taskId: string) {
    return await axios.post(DownloadAPI.PAUSE(taskId));
  }

  /**
   * 恢复任务
   * @param taskId 任务ID
   * @returns API响应
   */
  static async resumeTask(taskId: string) {
    return await axios.post(DownloadAPI.RESUME(taskId));
  }

  /**
   * 删除任务
   * @param taskId 任务ID
   * @returns API响应
   */
  static async removeTask(taskId: string) {
    return await axios.delete(TaskAPI.DELETE(taskId));
  }

  /**
   * 批量暂停任务
   * @param taskIds 任务ID列表
   * @returns 成功和失败的结果
   */
  static async batchPauseTasks(taskIds: string[]) {
    const results = {
      successCount: 0,
      errorMessages: [] as string[]
    };

    for (const taskId of taskIds) {
      try {
        const response = await this.pauseTask(taskId);
        if (response.data && response.data.success) {
          results.successCount++;
        } else {
          // 收集错误信息
          const errorMsg = response.data?.error?.message || `任务 ${taskId} 暂停失败`;
          results.errorMessages.push(errorMsg);
        }
      } catch (err: any) {
        // 收集HTTP错误信息
        const errorMsg = err.response?.data?.error?.message || `任务 ${taskId} 暂停失败`;
        results.errorMessages.push(errorMsg);
        console.error(`暂停任务失败 (${taskId}):`, err);
      }
    }

    return results;
  }

  /**
   * 批量恢复任务
   * @param taskIds 任务ID列表
   * @returns 成功和失败的结果
   */
  static async batchResumeTasks(taskIds: string[]) {
    const results = {
      successCount: 0,
      errorMessages: [] as string[]
    };

    for (const taskId of taskIds) {
      try {
        const response = await this.resumeTask(taskId);
        if (response.data && response.data.success) {
          results.successCount++;
        } else {
          // 收集错误信息
          const errorMsg = response.data?.error?.message || `任务 ${taskId} 恢复失败`;
          results.errorMessages.push(errorMsg);
        }
      } catch (err: any) {
        // 收集HTTP错误信息
        const errorMsg = err.response?.data?.error?.message || `任务 ${taskId} 恢复失败`;
        results.errorMessages.push(errorMsg);
        console.error(`恢复任务失败 (${taskId}):`, err);
      }
    }

    return results;
  }

  /**
   * 批量删除任务
   * @param taskIds 任务ID列表
   * @returns 成功和失败的结果
   */
  static async batchRemoveTasks(taskIds: string[]) {
    const results = {
      successCount: 0,
      failedTasks: [] as string[]
    };

    for (const taskId of taskIds) {
      try {
        console.log(`删除任务 ${taskId}, API端点:`, TaskAPI.DELETE(taskId));
        const deleteResponse = await this.removeTask(taskId);
        console.log(`任务 ${taskId} 删除响应:`, deleteResponse);
        
        if (deleteResponse.data && deleteResponse.data.success) {
          results.successCount++;
        } else {
          results.failedTasks.push(taskId);
          console.error(`任务 ${taskId} 删除失败 - 响应:`, deleteResponse.data);
        }
      } catch (err: any) {
        results.failedTasks.push(taskId);
        console.error(`删除任务失败 (${taskId}):`, err);
      }
    }

    return results;
  }

  /**
   * 更新任务信息
   * @param taskId 任务ID
   * @param taskData 任务数据
   * @returns API响应
   */
  static async updateTask(taskId: string, taskData: Partial<Task>) {
    return await axios.put(TaskAPI.UPDATE(taskId), taskData);
  }
}