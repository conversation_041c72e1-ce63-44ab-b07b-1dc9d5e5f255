#[cfg(test)]
mod node_tests {
    use std::net::{IpAddr, Ipv4Addr, SocketAddr};
    use crate::protocols::bittorrent::dht::node::{NodeId, DHTNode};

    #[test]
    fn test_node_id_new_random() {
        let node_id1 = NodeId::new_random();
        let node_id2 = NodeId::new_random();

        // 两个随机ID应该不同
        assert_ne!(node_id1, node_id2);
    }

    #[test]
    fn test_node_id_from_bytes() {
        let bytes = [1u8; 20];
        let node_id = NodeId::from_bytes(bytes);

        assert_eq!(node_id.as_bytes(), &bytes);
    }

    #[test]
    fn test_node_id_distance() {
        // 创建两个ID，只有第一个字节不同
        let mut bytes1 = [0u8; 20];
        let mut bytes2 = [0u8; 20];
        bytes1[0] = 1;
        bytes2[0] = 2;

        let id1 = NodeId::from_bytes(bytes1);
        let id2 = NodeId::from_bytes(bytes2);

        let distance = id1.distance(&id2);

        // XOR距离应该是 1 XOR 2 = 3 在第一个字节，其余为0
        assert_eq!(distance.as_bytes()[0], 3);
        for i in 1..20 {
            assert_eq!(distance.as_bytes()[i], 0);
        }
    }

    #[test]
    fn test_node_id_bit_at() {
        let mut bytes = [0u8; 20];
        bytes[0] = 0b10000000; // 第0位为1
        bytes[1] = 0b00000001; // 第15位为1

        let node_id = NodeId::from_bytes(bytes);

        assert!(node_id.bit_at(0));
        assert!(!node_id.bit_at(1));
        assert!(node_id.bit_at(15));
        assert!(!node_id.bit_at(16));
        assert!(!node_id.bit_at(160)); // 超出范围
    }

    #[test]
    fn test_node_id_common_prefix_len() {
        let mut bytes1 = [0u8; 20];
        let mut bytes2 = [0u8; 20];

        // 前5位相同，第6位不同
        bytes1[0] = 0b11111000;
        bytes2[0] = 0b11111111;

        let id1 = NodeId::from_bytes(bytes1);
        let id2 = NodeId::from_bytes(bytes2);

        assert_eq!(id1.common_prefix_len(&id2), 5);

        // 完全相同的ID
        let id3 = NodeId::from_bytes(bytes1);
        assert_eq!(id1.common_prefix_len(&id3), 160);
    }

    #[test]
    fn test_dht_node_new() {
        let node_id = NodeId::new_random();
        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let port = 6881;

        let node = DHTNode::new(node_id, ip, port);

        assert_eq!(node.id, node_id);
        assert_eq!(node.ip, ip);
        assert_eq!(node.port, port);
        assert_eq!(node.failures, 0);
        assert!(!node.is_good);
        assert!(node.last_seen.is_none());
    }

    #[test]
    fn test_dht_node_from_addr() {
        let node_id = NodeId::new_random();
        let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);

        let node = DHTNode::from_addr(node_id, addr);

        assert_eq!(node.id, node_id);
        assert_eq!(node.ip, addr.ip());
        assert_eq!(node.port, addr.port());
        assert_eq!(node.addr(), addr);
    }

    #[test]
    fn test_dht_node_update_last_seen() {
        let node_id = NodeId::new_random();
        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let port = 6881;

        let mut node = DHTNode::new(node_id, ip, port);
        assert!(node.last_seen.is_none());

        node.update_last_seen();
        assert!(node.last_seen.is_some());
        assert_eq!(node.failures, 0);
    }

    #[test]
    fn test_dht_node_mark_as_good() {
        let node_id = NodeId::new_random();
        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let port = 6881;

        let mut node = DHTNode::new(node_id, ip, port);
        assert!(!node.is_good);

        node.mark_as_good();
        assert!(node.is_good);
    }

    #[test]
    fn test_dht_node_increment_failures() {
        let node_id = NodeId::new_random();
        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let port = 6881;

        let mut node = DHTNode::new(node_id, ip, port);
        assert_eq!(node.failures, 0);

        node.increment_failures();
        assert_eq!(node.failures, 1);

        node.increment_failures();
        assert_eq!(node.failures, 2);
    }

    #[test]
    fn test_dht_node_is_expired() {
        let node_id = NodeId::new_random();
        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let port = 6881;

        let mut node = DHTNode::new(node_id, ip, port);

        // 没有last_seen时应该过期
        assert!(node.is_expired(std::time::Duration::from_secs(1)));

        node.update_last_seen();

        // 刚更新过不应该过期
        assert!(!node.is_expired(std::time::Duration::from_secs(1)));

        // 使用非常短的超时时间测试过期
        std::thread::sleep(std::time::Duration::from_millis(10));
        assert!(node.is_expired(std::time::Duration::from_nanos(1)));
    }

    #[test]
    fn test_dht_node_is_questionable() {
        let node_id = NodeId::new_random();
        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let port = 6881;

        let mut node = DHTNode::new(node_id, ip, port);

        // 初始不应该是可疑的
        assert!(!node.is_questionable(3));

        node.increment_failures();
        assert!(!node.is_questionable(3));

        node.increment_failures();
        assert!(!node.is_questionable(3));

        node.increment_failures();
        assert!(node.is_questionable(3));
    }

    #[test]
    fn test_dht_node_encode_decode_compact() {
        let node_id = NodeId::from_bytes([1u8; 20]);
        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let port = 6881;

        let node = DHTNode::new(node_id, ip, port);

        let encoded = node.encode_compact();
        assert_eq!(encoded.len(), 26); // 20字节ID + 4字节IP + 2字节端口

        let decoded = DHTNode::decode_compact(&encoded).unwrap();
        assert_eq!(decoded.id, node.id);
        assert_eq!(decoded.ip, node.ip);
        assert_eq!(decoded.port, node.port);
    }
}
