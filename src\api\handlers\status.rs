use serde::Serialize;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

use crate::api::response::ApiResponse;

/// Status response
#[derive(Debug, Serialize)]
pub struct StatusResponse {
    pub status: String,
    pub version: String,
    pub uptime: u64,
    pub timestamp: u64,
}

/// Get the server status


pub async fn get_status() -> ApiResponse<StatusResponse> {

    // Get the current timestamp
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or(Duration::from_secs(0))
        .as_secs();

    // Create the response
    let response = StatusResponse {
        status: "ok".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        uptime: 0, // This would be calculated from the server start time
        timestamp,
    };

    ApiResponse::success(response)
}
