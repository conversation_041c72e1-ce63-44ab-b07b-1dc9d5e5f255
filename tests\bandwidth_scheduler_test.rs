use anyhow::Result;
use tokio::test;
use uuid::Uuid;

use tonitru_downloader::download::bandwidth_scheduler::{
    BandwidthScheduler, BandwidthSchedulerImpl, TimeBasedSpeedRule, BandwidthStats, TaskPriority
};

/// Test creating a BandwidthSchedulerImpl
#[test]
async fn test_bandwidth_scheduler_creation() {
    let scheduler = BandwidthSchedulerImpl::new();

    // Verify initial state
    assert_eq!(scheduler.get_global_download_limit().await.unwrap(), None);
    assert_eq!(scheduler.get_global_upload_limit().await.unwrap(), None);
}

/// Test setting and getting global download limit
#[tokio::test]
async fn test_global_download_limit() -> Result<()> {
    let scheduler = BandwidthSchedulerImpl::new();

    // Initially, limit should be None
    let limit = scheduler.get_global_download_limit().await?;
    assert_eq!(limit, None);

    // Set a limit
    scheduler.set_global_download_limit(Some(1024 * 1024)).await?; // 1 MB/s

    // Verify the limit was set
    let limit = scheduler.get_global_download_limit().await?;
    assert_eq!(limit, Some(1024 * 1024));

    // Clear the limit
    scheduler.set_global_download_limit(None).await?;

    // Verify the limit was cleared
    let limit = scheduler.get_global_download_limit().await?;
    assert_eq!(limit, None);

    Ok(())
}

/// Test setting and getting task download limit
#[tokio::test]
async fn test_task_download_limit() -> Result<()> {
    let scheduler = BandwidthSchedulerImpl::new();
    let task_id = Uuid::new_v4();

    // Initially, limit should be None
    let limit = scheduler.get_task_download_limit(task_id).await?;
    assert_eq!(limit, None);

    // Set a limit
    scheduler.set_task_download_limit(task_id, Some(512 * 1024)).await?; // 512 KB/s

    // Verify the limit was set
    let limit = scheduler.get_task_download_limit(task_id).await?;
    assert_eq!(limit, Some(512 * 1024));

    // Clear the limit
    scheduler.set_task_download_limit(task_id, None).await?;

    // Verify the limit was cleared
    let limit = scheduler.get_task_download_limit(task_id).await?;
    assert_eq!(limit, None);

    Ok(())
}

/// Test setting and getting global upload limit
#[tokio::test]
async fn test_global_upload_limit() -> Result<()> {
    let scheduler = BandwidthSchedulerImpl::new();

    // Initially, limit should be None
    let limit = scheduler.get_global_upload_limit().await?;
    assert_eq!(limit, None);

    // Set a limit
    scheduler.set_global_upload_limit(Some(1024 * 1024)).await?; // 1 MB/s

    // Verify the limit was set
    let limit = scheduler.get_global_upload_limit().await?;
    assert_eq!(limit, Some(1024 * 1024));

    // Clear the limit
    scheduler.set_global_upload_limit(None).await?;

    // Verify the limit was cleared
    let limit = scheduler.get_global_upload_limit().await?;
    assert_eq!(limit, None);

    Ok(())
}

/// Test setting and getting task upload limit
#[tokio::test]
async fn test_task_upload_limit() -> Result<()> {
    let scheduler = BandwidthSchedulerImpl::new();
    let task_id = Uuid::new_v4();

    // Initially, limit should be None
    let limit = scheduler.get_task_upload_limit(task_id).await?;
    assert_eq!(limit, None);

    // Set a limit
    scheduler.set_task_upload_limit(task_id, Some(512 * 1024)).await?; // 512 KB/s

    // Verify the limit was set
    let limit = scheduler.get_task_upload_limit(task_id).await?;
    assert_eq!(limit, Some(512 * 1024));

    // Clear the limit
    scheduler.set_task_upload_limit(task_id, None).await?;

    // Verify the limit was cleared
    let limit = scheduler.get_task_upload_limit(task_id).await?;
    assert_eq!(limit, None);

    Ok(())
}

/// Test updating download stats
#[tokio::test]
async fn test_update_download_stats() -> Result<()> {
    let scheduler = BandwidthSchedulerImpl::new();
    let task_id = Uuid::new_v4();

    // Initially, stats should be empty
    let stats = scheduler.get_task_stats(task_id).await?;
    assert_eq!(stats.total_downloaded, 0);
    assert_eq!(stats.total_uploaded, 0);
    assert_eq!(stats.download_rate, 0);
    assert_eq!(stats.upload_rate, 0);

    // Update download stats
    scheduler.update_download_stats(task_id, 1024).await?; // 1 KB

    // Verify stats were updated
    let stats = scheduler.get_task_stats(task_id).await?;
    assert_eq!(stats.total_downloaded, 1024);

    // Update again
    scheduler.update_download_stats(task_id, 2048).await?; // 2 KB

    // Verify stats were updated
    let stats = scheduler.get_task_stats(task_id).await?;
    assert_eq!(stats.total_downloaded, 3072); // 1 KB + 2 KB

    Ok(())
}

/// Test updating upload stats
#[tokio::test]
async fn test_update_upload_stats() -> Result<()> {
    let scheduler = BandwidthSchedulerImpl::new();
    let task_id = Uuid::new_v4();

    // Initially, stats should be empty
    let stats = scheduler.get_task_stats(task_id).await?;
    assert_eq!(stats.total_uploaded, 0);

    // Update upload stats
    scheduler.update_upload_stats(task_id, 1024).await?; // 1 KB

    // Verify stats were updated
    let stats = scheduler.get_task_stats(task_id).await?;
    assert_eq!(stats.total_uploaded, 1024);

    // Update again
    scheduler.update_upload_stats(task_id, 2048).await?; // 2 KB

    // Verify stats were updated
    let stats = scheduler.get_task_stats(task_id).await?;
    assert_eq!(stats.total_uploaded, 3072); // 1 KB + 2 KB

    Ok(())
}

/// Test setting and getting task priority
#[tokio::test]
async fn test_task_priority() -> Result<()> {
    let scheduler = BandwidthSchedulerImpl::new();
    let task_id = Uuid::new_v4();

    // Initially, priority should be Normal
    let priority = scheduler.get_task_priority(task_id).await?;
    assert_eq!(priority, TaskPriority::Normal);

    // Set priority to High
    scheduler.set_task_priority(task_id, TaskPriority::High).await?;

    // Verify priority was set
    let priority = scheduler.get_task_priority(task_id).await?;
    assert_eq!(priority, TaskPriority::High);

    // Set priority to Low
    scheduler.set_task_priority(task_id, TaskPriority::Low).await?;

    // Verify priority was set
    let priority = scheduler.get_task_priority(task_id).await?;
    assert_eq!(priority, TaskPriority::Low);

    Ok(())
}

/// Test adding and removing time-based rules
#[tokio::test]
async fn test_time_based_rules() -> Result<()> {
    let scheduler = BandwidthSchedulerImpl::new();

    // Create a time-based rule
    let rule = TimeBasedSpeedRule {
        id: Uuid::nil(), // Will be replaced by the scheduler
        name: "Test Rule".to_string(),
        start_hour: 9,
        start_minute: 0,
        end_hour: 17,
        end_minute: 0,
        days_of_week: vec![1, 2, 3, 4, 5], // Monday to Friday
        download_limit: Some(512 * 1024), // 512 KB/s
        upload_limit: Some(256 * 1024), // 256 KB/s
    };

    // Add the rule
    let rule_id = scheduler.add_time_based_rule(rule.clone()).await?;

    // Verify the rule was added
    let rules = scheduler.get_time_based_rules().await?;
    assert_eq!(rules.len(), 1);

    let added_rule = &rules[0];
    assert_eq!(added_rule.id, rule_id);
    assert_eq!(added_rule.name, rule.name);
    assert_eq!(added_rule.start_hour, rule.start_hour);
    assert_eq!(added_rule.start_minute, rule.start_minute);
    assert_eq!(added_rule.end_hour, rule.end_hour);
    assert_eq!(added_rule.end_minute, rule.end_minute);
    assert_eq!(added_rule.days_of_week, rule.days_of_week);
    assert_eq!(added_rule.download_limit, rule.download_limit);
    assert_eq!(added_rule.upload_limit, rule.upload_limit);

    // Remove the rule
    scheduler.remove_time_based_rule(rule_id).await?;

    // Verify the rule was removed
    let rules = scheduler.get_time_based_rules().await?;
    assert!(rules.is_empty());

    Ok(())
}
