use std::time::{Duration, SystemTime, UNIX_EPOCH};
use anyhow::{Result, anyhow};
use serde::{Serialize, Deserialize};
use sha1::{Sha1, Digest};
use ed25519_dalek::Signature;
use ed25519_dalek::VerifyingKey;
use ed25519_dalek::Verifier;

/// DHT值类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DHTValue {
    /// 不可变值
    Immutable(ImmutableValue),
    /// 可变值
    Mutable(MutableValue),
}

impl DHTValue {
    /// 获取值的大小（字节）
    pub fn size(&self) -> usize {
        match self {
            DHTValue::Immutable(v) => v.value.len(),
            DHTValue::Mutable(v) => v.value.len(),
        }
    }

    /// 获取值的序列号
    pub fn sequence(&self) -> Option<i64> {
        match self {
            DHTValue::Immutable(_) => None,
            DHTValue::Mutable(v) => Some(v.sequence),
        }
    }

    /// 获取值的原始数据
    pub fn raw_value(&self) -> &[u8] {
        match self {
            DHTValue::Immutable(v) => &v.value,
            DHTValue::Mutable(v) => &v.value,
        }
    }

    /// 获取值的哈希
    pub fn hash(&self) -> [u8; 20] {
        match self {
            DHTValue::Immutable(v) => v.hash(),
            DHTValue::Mutable(v) => v.hash(),
        }
    }

    /// 验证值
    pub fn verify(&self) -> Result<bool> {
        match self {
            DHTValue::Immutable(_) => Ok(true), // 不可变值不需要验证
            DHTValue::Mutable(v) => v.verify(),
        }
    }
}

/// 不可变值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImmutableValue {
    /// 值数据
    pub value: Vec<u8>,
}

impl ImmutableValue {
    /// 创建新的不可变值
    pub fn new(value: Vec<u8>) -> Self {
        Self { value }
    }

    /// 计算值的哈希
    pub fn hash(&self) -> [u8; 20] {
        let mut hasher = Sha1::new();
        hasher.update("v1:".as_bytes());
        hasher.update(&self.value);
        let result = hasher.finalize();

        let mut hash = [0u8; 20];
        hash.copy_from_slice(&result);
        hash
    }
}

/// 可变值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MutableValue {
    /// 值数据
    pub value: Vec<u8>,
    /// 序列号
    pub sequence: i64,
    /// 公钥
    #[serde(with = "serde_bytes")]
    pub public_key: Vec<u8>,
    /// 签名
    #[serde(with = "serde_bytes")]
    pub signature: Vec<u8>,
    /// 盐（可选）
    pub salt: Option<Vec<u8>>,
}

impl MutableValue {
    /// 创建新的可变值
    pub fn new(
        value: Vec<u8>,
        sequence: i64,
        public_key: Vec<u8>,
        signature: Vec<u8>,
        salt: Option<Vec<u8>>,
    ) -> Self {
        Self {
            value,
            sequence,
            public_key,
            signature,
            salt,
        }
    }

    /// 计算值的哈希
    pub fn hash(&self) -> [u8; 20] {
        let mut hasher = Sha1::new();

        // 计算公钥的SHA-1哈希
        let mut key_hasher = Sha1::new();
        key_hasher.update(&self.public_key);
        let key_hash = key_hasher.finalize();

        // 如果有盐，则与公钥哈希组合
        if let Some(salt) = &self.salt {
            hasher.update(&key_hash);
            hasher.update(salt);
        } else {
            hasher.update(&key_hash);
        }

        let result = hasher.finalize();

        let mut hash = [0u8; 20];
        hash.copy_from_slice(&result);
        hash
    }

    /// 验证签名
    pub fn verify(&self) -> Result<bool> {
        // 构建要签名的消息
        let mut message = Vec::with_capacity(self.value.len() + 8 + self.salt.as_ref().map_or(0, |s| s.len()));
        message.extend_from_slice(&self.value);
        message.extend_from_slice(&self.sequence.to_be_bytes());

        if let Some(salt) = &self.salt {
            message.extend_from_slice(salt);
        }

        // 检查公钥长度
        if self.public_key.len() != 32 {
            return Err(anyhow!("Invalid public key length"));
        }

        // 检查签名长度
        if self.signature.len() != 64 {
            return Err(anyhow!("Invalid signature length"));
        }

        // 解析公钥
        let mut pk_bytes = [0u8; 32];
        pk_bytes.copy_from_slice(&self.public_key);
        let public_key = VerifyingKey::from_bytes(&pk_bytes)
            .map_err(|e| anyhow!("Invalid public key: {}", e))?;

        // 解析签名
        let mut sig_bytes = [0u8; 64];
        if self.signature.len() != 64 {
            return Err(anyhow!("Invalid signature length"));
        }
        sig_bytes.copy_from_slice(&self.signature);

        // 使用 ed25519_dalek 2.x 版本的 API
        let signature = Signature::try_from(&sig_bytes[..])
            .map_err(|_| anyhow!("Invalid signature"))?;

        // 验证签名
        match public_key.verify(&message, &signature) {
            Ok(_) => Ok(true),
            Err(e) => Err(anyhow!("Signature verification failed: {}", e)),
        }
    }

    /// 获取当前时间戳（秒）
    pub fn current_timestamp() -> i64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_secs() as i64
    }
}
