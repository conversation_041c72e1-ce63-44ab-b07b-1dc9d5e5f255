//! 速度限制命令模块
//!
//! 实现与下载速度限制相关的命令。

use anyhow::Result;
use clap::{Args, Subcommand};
use std::sync::Arc;
use log::{debug, trace, info, warn};
use serde_json::json;
use uuid::Uuid;

use crate::cli::backend::backend::CliBackend;
use crate::cli::backend::models::TimeRule;
use crate::cli::utils::formatter::{format_output, OutputFormat};
use crate::cli::utils::debug::{print_debug_info, print_debug_object, print_debug_json, DebugLevel, DebugTimer};

/// 速度控制命令
#[derive(Subcommand, Debug)]
pub enum SpeedCommands {
    /// 设置全局下载速度限制
    SetGlobalDownload(SpeedLimitArgs),
    /// 获取全局下载速度限制
    GetGlobalDownload,
    /// 设置全局上传速度限制
    SetGlobalUpload(SpeedLimitArgs),
    /// 获取全局上传速度限制
    GetGlobalUpload,
    /// 设置任务下载速度限制
    SetTaskDownload(TaskSpeedLimitArgs),
    /// 获取任务下载速度限制
    GetTaskDownload(TaskIdArgs),
    /// 添加时间规则
    AddTimeRule(TimeRuleArgs),
    /// 获取所有时间规则
    GetTimeRules,
    /// 删除时间规则
    RemoveTimeRule(TimeRuleIdArgs),
    /// 获取当前速度限制
    Get,
    /// 设置速度限制
    Set(SetSpeedArgs),
    /// 移除速度限制
    Remove,
}

/// 速度限制参数
#[derive(Args, Debug)]
pub struct SpeedLimitArgs {
    /// 速度限制（字节/秒）
    pub limit: u64,
}

/// 设置速度限制参数
#[derive(Args, Debug)]
pub struct SetSpeedArgs {
    /// 速度限制（KB/s）
    pub limit: u64,
}

/// 任务速度限制参数
#[derive(Args, Debug)]
pub struct TaskSpeedLimitArgs {
    /// 任务ID
    pub id: Uuid,
    /// 速度限制（字节/秒）
    pub limit: u64,
}

/// 任务ID参数
#[derive(Args, Debug)]
pub struct TaskIdArgs {
    /// 任务ID
    pub id: Uuid,
}

/// 时间规则参数
#[derive(Args, Debug)]
pub struct TimeRuleArgs {
    /// 开始时间（格式：HH:MM）
    pub start_time: String,
    /// 结束时间（格式：HH:MM）
    pub end_time: String,
    /// 下载速度限制（字节/秒）
    pub download_limit: u64,
    /// 上传速度限制（字节/秒）
    pub upload_limit: u64,
    /// 星期几（1-7，1表示星期一）
    #[arg(short, long)]
    pub days: Option<Vec<u8>>,
}

/// 时间规则ID参数
#[derive(Args, Debug)]
pub struct TimeRuleIdArgs {
    /// 规则ID
    pub id: Uuid,
}

/// 处理速度控制命令
pub async fn handle_command(
    command: &SpeedCommands,
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    match command {
        SpeedCommands::SetGlobalDownload(args) => handle_set_global_download(args, backend, format).await,
        SpeedCommands::GetGlobalDownload => handle_get_global_download(backend, format).await,
        SpeedCommands::SetGlobalUpload(args) => handle_set_global_upload(args, backend, format).await,
        SpeedCommands::GetGlobalUpload => handle_get_global_upload(backend, format).await,
        SpeedCommands::SetTaskDownload(args) => handle_set_task_download(args, backend, format).await,
        SpeedCommands::GetTaskDownload(args) => handle_get_task_download(args, backend, format).await,
        SpeedCommands::AddTimeRule(args) => handle_add_time_rule(args, backend, format).await,
        SpeedCommands::GetTimeRules => handle_get_time_rules(backend, format).await,
        SpeedCommands::RemoveTimeRule(args) => handle_remove_time_rule(args, backend, format).await,
        SpeedCommands::Get => handle_get_speed_limit(backend, format).await,
        SpeedCommands::Set(args) => handle_set_speed_limit(backend, args, format).await,
        SpeedCommands::Remove => handle_remove_speed_limit(backend, format).await,
    }
}

/// 处理获取速度限制命令
async fn handle_get_speed_limit(
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行获取速度限制");
    
    let mut timer = DebugTimer::new("获取速度限制");
    
    let limit = backend.get_speed_limit().await?;
    timer.checkpoint("获取速度限制值");
    
    let limit_value = match limit {
        Some(value) => {
            debug!("当前速度限制: {} KB/s", value);
            value
        },
        None => {
            debug!("当前没有速度限制");
            0
        }
    };
    
    let json_value = json!({
        "limit": limit_value,
        "unlimited": limit.is_none(),
        "formatted": if let Some(value) = limit {
            format!("{} KB/s", value)
        } else {
            "无限制".to_string()
        }
    });
    
    print_debug_json("速度限制JSON", &json_value, DebugLevel::Verbose);
    format_output(json_value, format.unwrap_or(OutputFormat::Text));
    
    let elapsed = timer.stop();
    debug!("获取速度限制总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理设置速度限制命令
async fn handle_set_speed_limit(
    backend: &Arc<dyn CliBackend>,
    args: &SetSpeedArgs,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行设置速度限制: {} KB/s", args.limit);
    
    let mut timer = DebugTimer::new("设置速度限制");
    
    backend.set_speed_limit(args.limit).await?;
    timer.checkpoint("设置速度限制值");
    
    info!("速度限制已设置为 {} KB/s", args.limit);
    
    let json_value = json!({
        "limit": args.limit,
        "unlimited": false,
        "formatted": format!("{} KB/s", args.limit),
        "status": "success"
    });
    
    print_debug_json("设置速度限制结果", &json_value, DebugLevel::Verbose);
    format_output(json_value, format.unwrap_or(OutputFormat::Text));
    
    let elapsed = timer.stop();
    debug!("设置速度限制总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理移除速度限制命令
async fn handle_remove_speed_limit(
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    debug!("执行移除速度限制");
    
    let mut timer = DebugTimer::new("移除速度限制");
    
    backend.remove_speed_limit().await?;
    timer.checkpoint("移除速度限制值");
    
    info!("速度限制已移除");
    
    let json_value = json!({
        "limit": 0,
        "unlimited": true,
        "formatted": "无限制",
        "status": "success"
    });
    
    print_debug_json("移除速度限制结果", &json_value, DebugLevel::Verbose);
    format_output(json_value, format.unwrap_or(OutputFormat::Text));
    
    let elapsed = timer.stop();
    debug!("移除速度限制总耗时: {:?}", elapsed);
    
    Ok(())
}

/// 处理设置全局下载速度限制命令
async fn handle_set_global_download(
    args: &SpeedLimitArgs,
    backend: &Arc<dyn CliBackend>,
    _format: Option<OutputFormat>,
) -> Result<()> {
    backend.set_global_download_limit(args.limit).await?;
    println!("已设置全局下载速度限制为: {} 字节/秒", args.limit);
    Ok(())
}

/// 处理获取全局下载速度限制命令
async fn handle_get_global_download(
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    let limit = backend.get_global_download_limit().await?;
    
    if format == Some(OutputFormat::Json) || format == Some(OutputFormat::Yaml) {
        let response = serde_json::json!({
            "limit": limit.limit
        });
        format_output(response, format.unwrap());
    } else {
        println!("全局下载速度限制: {} 字节/秒", limit.limit);
    }
    
    Ok(())
}

/// 处理设置全局上传速度限制命令
async fn handle_set_global_upload(
    args: &SpeedLimitArgs,
    backend: &Arc<dyn CliBackend>,
    _format: Option<OutputFormat>,
) -> Result<()> {
    backend.set_global_upload_limit(args.limit).await?;
    println!("已设置全局上传速度限制为: {} 字节/秒", args.limit);
    Ok(())
}

/// 处理获取全局上传速度限制命令
async fn handle_get_global_upload(
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    let limit = backend.get_global_upload_limit().await?;
    
    if format == Some(OutputFormat::Json) || format == Some(OutputFormat::Yaml) {
        let response = serde_json::json!({
            "limit": limit.limit
        });
        format_output(response, format.unwrap());
    } else {
        println!("全局上传速度限制: {} 字节/秒", limit.limit);
    }
    
    Ok(())
}

/// 处理设置任务下载速度限制命令
async fn handle_set_task_download(
    args: &TaskSpeedLimitArgs,
    backend: &Arc<dyn CliBackend>,
    _format: Option<OutputFormat>,
) -> Result<()> {
    backend.set_task_download_limit(args.id, args.limit).await?;
    println!("已设置任务 {} 的下载速度限制为: {} 字节/秒", args.id, args.limit);
    Ok(())
}

/// 处理获取任务下载速度限制命令
async fn handle_get_task_download(
    args: &TaskIdArgs,
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    let limit = backend.get_task_download_limit(args.id).await?;
    
    if format == Some(OutputFormat::Json) || format == Some(OutputFormat::Yaml) {
        let response = serde_json::json!({
            "task_id": args.id,
            "limit": limit.limit
        });
        format_output(response, format.unwrap());
    } else {
        println!("任务 {} 的下载速度限制: {} 字节/秒", args.id, limit.limit);
    }
    
    Ok(())
}

/// 处理添加时间规则命令
async fn handle_add_time_rule(
    args: &TimeRuleArgs,
    backend: &Arc<dyn CliBackend>,
    _format: Option<OutputFormat>,
) -> Result<()> {
    let time_rule = TimeRule {
        id: None, // 由服务器分配
        name: format!("规则 {}-{}", args.start_time, args.end_time),
        start_time: args.start_time.clone(),
        end_time: args.end_time.clone(),
        download_limit: Some(args.download_limit),
        upload_limit: Some(args.upload_limit),
        days: args.days.clone().unwrap_or_else(|| vec![1, 2, 3, 4, 5, 6, 7]),
    };
    
    let rule_id = backend.add_time_rule(time_rule).await?;
    println!("已添加时间规则，ID: {}", rule_id);
    Ok(())
}

/// 处理获取所有时间规则命令
async fn handle_get_time_rules(
    backend: &Arc<dyn CliBackend>,
    format: Option<OutputFormat>,
) -> Result<()> {
    let rules = backend.get_time_rules().await?;
    
    // 格式化输出
    format_output(serde_json::json!(rules), format.unwrap_or(OutputFormat::Table));
    
    Ok(())
}

/// 处理删除时间规则命令
async fn handle_remove_time_rule(
    args: &TimeRuleIdArgs,
    backend: &Arc<dyn CliBackend>,
    _format: Option<OutputFormat>,
) -> Result<()> {
    backend.delete_time_rule(args.id).await?;
    println!("已删除时间规则: {}", args.id);
    Ok(())
}