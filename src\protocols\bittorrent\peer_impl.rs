// 重新导出 BitTorrentPeer 类型，保持向后兼容性
pub use self::bt_peer_impl::BitTorrentPeer;

// 导入 CommonPeer 并创建 PeerCommon 类型别名
use crate::protocols::common::peer::CommonPeer as PeerCommon;
use crate::core::p2p::piece::PieceManager;
use std::sync::Arc;
use tokio::sync::Mutex;

// 定义 BitTorrentPeer 的实现模块
mod bt_peer_impl {
    use anyhow::{Result, anyhow};
    use async_trait::async_trait;
    use std::net::SocketAddr;
    use std::sync::Arc;
    use tokio::net::TcpStream;
    use tokio::io::AsyncWriteExt;
    use tracing::{debug, warn};
    use bytes::BytesMut;
    
    use crate::core::p2p::peer::{Peer, PeerInfo};
    use crate::protocols::bittorrent::message::BitTorrentMessage;
    use crate::protocols::bittorrent::dht_manager::DHTManager;
    use crate::protocols::bittorrent::peer::connection::PeerConnection;
    use crate::protocols::bittorrent::peer::state::PeerState;
    use crate::protocols::bittorrent::torrent::TorrentInfo;
    use crate::protocols::bittorrent::piece_factory;
    
    /// BitTorrent对等点实现
    pub struct BitTorrentPeer {
        /// 对等点连接
        pub connection: PeerConnection,
        /// 对等点状态
        pub state: PeerState,
        /// 信息哈希
        pub info_hash: Vec<u8>,
        /// 本地对等点ID
        pub local_peer_id: Vec<u8>,
    }
    
    impl BitTorrentPeer {
        /// 创建新的BitTorrent对等点
    pub async fn new(addr: SocketAddr, info_hash: &[u8], local_peer_id: &[u8], timeout_secs: u64, num_pieces: u32, dht_manager: Option<Arc<DHTManager>>, torrent_info: Option<TorrentInfo>, output_path: Option<String>) -> Result<Self> {
        let piece_manager = if let (Some(torrent_info), Some(output_path)) = (torrent_info.clone(), output_path.clone()) {
            // 创建分片管理器实例
            let pm = crate::protocols::bittorrent::piece_factory::create_piece_manager(torrent_info, output_path).await?;
            Some(pm)
        } else {
            None
        };
        let mut connection = PeerConnection::new(addr, timeout_secs, piece_manager);
        connection.common.set_num_pieces(num_pieces);

            let state = PeerState::new(
                info_hash,
                local_peer_id,
                false, // initial supports_fast
                false, // initial supports_extensions
                dht_manager,
            );
    
            Ok(Self {
                connection,
                state,
                info_hash: info_hash.to_vec(),
                local_peer_id: local_peer_id.to_vec(),
            })
        }
        
        /// 设置分片数量
        pub fn set_num_pieces(&mut self, num_pieces: u32) {
            self.connection.common.set_num_pieces(num_pieces);
        }
        
        /// 设置PEX回调函数
        pub fn set_pex_callback(&mut self, callback: Arc<dyn Fn(SocketAddr) -> Result<()> + Send + Sync>) {
            self.state.extension_handler.set_pex_callback(callback);
        }
        
        /// 执行BitTorrent握手
        pub async fn handshake(&mut self) -> Result<()> {
            if self.state.handshaked {
                return Ok(());
            }
    
            // 使用握手处理器执行握手
            if self.state.handshake_handler.perform_handshake(&mut self.connection.common).await? {
                // 更新握手状态
                self.state.handshaked = true;
    
                // 更新Fast Extension支持状态
                let supports_fast = self.state.handshake_handler.supports_fast;
                self.state.upload_manager.supports_fast = supports_fast;
    
                // 更新扩展协议支持状态
                let supports_extensions = self.state.handshake_handler.supports_extensions;
                self.state.extension_handler.supports_extensions = supports_extensions;
    
                // 如果对方支持Fast Extension，计算并发送allowed_fast消息
                if supports_fast {
                    let peer_addr = self.connection.common.peer_info.addr.ip();
                    let total_pieces = self.connection.common.num_pieces;
    
                    // 计算允许快速请求的分片
                    let allowed_pieces = self.state.handshake_handler.calculate_allowed_fast(peer_addr, total_pieces, 10);
    
                    // 发送allowed_fast消息
                    for &piece_index in &allowed_pieces {
                        // 添加到上传管理器的允许快速请求列表
                        self.state.upload_manager.add_allowed_fast_piece(piece_index);
    
                        // 发送allowed_fast消息
                        self.send_bt_message(BitTorrentMessage::AllowedFast(piece_index)).await?;
                        debug!("Sent AllowedFast message for piece {}", piece_index);
                    }
                }
    
                // 如果对方支持扩展协议，发送扩展握手消息
                if supports_extensions {
                    let handshake_message = self.state.extension_handler.generate_handshake()?;
                    self.send_bt_message(handshake_message).await?;
                }
    
                debug!("Handshake successful with peer: {:?}", self.connection.common.peer_info.addr);
                Ok(())
            } else {
                Err(anyhow!("Handshake failed"))
            }
        }
    
        /// 发送BitTorrent消息
    pub async fn send_bt_message(&mut self, message: BitTorrentMessage) -> Result<()> {
        // 根据消息类型更新状态
        match &message {
            BitTorrentMessage::Choke => {
                self.state.upload_manager.set_choking(true);
            },
            BitTorrentMessage::Unchoke => {
                self.state.upload_manager.set_choking(false);
            },
            BitTorrentMessage::Interested => {
                self.state.am_interested = true;
            },
            BitTorrentMessage::NotInterested => {
                self.state.am_interested = false;
            },
            BitTorrentMessage::AllowedFast(index) => {
                self.state.upload_manager.add_allowed_fast_piece(*index);
            },
            _ => {}
        }

        // 编码消息
        let supports_fast = self.state.handshake_handler.supports_fast;
        let buf = message.encode(supports_fast)?;

        // 发送消息
        self.connection.common.send_message(&buf).await
    }
    
    /// 发送扩展消息
    pub async fn send_extension_message(&mut self, extension_name: &str, payload: &[u8]) -> Result<()> {
        if self.state.extension_handler.supports_extensions {
            // 获取扩展ID
            let extension_id = self.state.extension_handler.extension_map.get(extension_name).copied()
                .ok_or_else(|| anyhow!("Extension {} not supported by peer", extension_name))?;
            
            // 创建扩展消息
            let message = BitTorrentMessage::Extended(extension_id, payload.to_vec());
            
            // 发送消息
            self.send_bt_message(message).await
        } else {
            Err(anyhow!("Peer does not support extensions"))
        }
    }
    
        /// 接收BitTorrent消息
        pub async fn receive_bt_message(&mut self) -> Result<Option<BitTorrentMessage>> {
            let message = self.connection.common.receive_message().await?;
            let message = BytesMut::from(message.as_slice());
    
            // 使用消息模块解码消息
            let message_opt = BitTorrentMessage::decode(&message)?;
    
            // 如果是Keep-alive消息或未知消息，直接返回
            if message_opt.is_none() {
                return Ok(None);
            }
    
            // 处理消息并更新状态
            let message = message_opt.unwrap();
            match &message {
                BitTorrentMessage::Choke => {
                    self.state.peer_choking = true;
                },
                BitTorrentMessage::Unchoke => {
                    self.state.peer_choking = false;
                },
                BitTorrentMessage::Interested => {
                    self.state.upload_manager.set_peer_interested(true);
                },
                BitTorrentMessage::NotInterested => {
                    self.state.upload_manager.set_peer_interested(false);
                },
                BitTorrentMessage::Have(index) => {
                    self.state.pieces_have.insert(*index);
                },
                BitTorrentMessage::Bitfield(bitfield) => {
                    self.state.bitfield = Some(bitfield.clone());
    
                    // 更新pieces_have基于位图
                    for (byte_index, &byte) in bitfield.iter().enumerate() {
                        for bit_index in 0..8 {
                            if (byte >> (7 - bit_index)) & 1 == 1 {
                                let piece_index = byte_index * 8 + bit_index;
                                self.state.pieces_have.insert(piece_index as u32);
                            }
                        }
                    }
                },
                BitTorrentMessage::Piece(index, begin, _) => {
                    // 收到分片数据，从请求列表中移除
                    self.state.requested_blocks.remove(&(*index, *begin));
                    debug!("Received piece {} offset {}, removed from requested blocks", index, begin);
                },
                BitTorrentMessage::AllowedFast(index) => {
                    self.state.allowed_fast_pieces.insert(*index);
                    debug!("Peer allows fast request for piece {}", index);
                },
                BitTorrentMessage::HaveAll => {
                    debug!("Peer has all pieces");
                    // 标记对方拥有所有分片
                    // 注意：这里我们不知道总分片数，所以不能更新pieces_have
                },
                BitTorrentMessage::HaveNone => {
                    debug!("Peer has no pieces");
                    // 清空对方拥有的分片
                    self.state.pieces_have.clear();
                },
                _ => {}
            }
    
            Ok(Some(message))
        }
    
        /// 处理接收到的消息
        pub async fn process_messages(&mut self) -> Result<()> {
            // 确保已握手
            if !self.state.handshaked && self.connection.common.is_connected() {
                self.handshake().await?;
            }
    
            // 接收并处理消息
            match self.receive_bt_message().await {
                Ok(Some(message)) => {
                    debug!("Received message: {:?}", message);
        
                    // 处理消息
                    match message {
                        BitTorrentMessage::Request(index, begin, length) => {
                            // 使用上传管理器处理请求消息
                            if let Some(response) = self.state.upload_manager.handle_request(index, begin, length).await? {
                                self.send_bt_message(response).await?;
                            }
                        },
                        BitTorrentMessage::Extended(ext_id, ext_payload) => {
                            // 使用扩展协议处理器处理扩展消息
                            self.state.extension_handler.process_message(ext_id, &ext_payload).await?;
                        },
                        _ => {
                            // 其他消息类型在receive_bt_message中已经处理
                        }
                    }
                },
                Ok(None) => {
                    // Keep-alive或未知消息，不需要处理
                },
                Err(e) => {
                    warn!("Error receiving message: {}", e);
                    // 连接可能已断开，尝试重新连接
                    if !self.connection.common.is_connected() {
                        self.connection.common.connect().await?;
                        self.state.handshaked = false;
                        self.handshake().await?;
                    }
                }
            }
    
            Ok(())
        }
    
        /// 处理上传请求
        pub async fn process_uploads(&mut self) -> Result<()> {
            // 处理上传队列中的请求
            let mut upload_queue = &mut self.state.upload_queue;
            while let Some(request) = upload_queue.pop_front() {
                // 处理上传请求
                let (index, begin, length) = request;
                self.state.upload_manager.process_request(BitTorrentMessage::Request(index, begin, length), &mut self.connection.common.socket).await?;
                
                // 更新上传统计信息
                self.connection.common.update_upload_stats(self.state.upload_manager.bytes_uploaded() as usize);
            }
            
            Ok(())
        }
        
        /// 获取最后响应时间（以毫秒为单位）
        pub fn last_response_time(&self) -> u128 {
            self.connection.common.last_activity.elapsed().as_millis()
        }
    }
    
    // 实现Peer特性
    #[async_trait]
    impl Peer for BitTorrentPeer {
        fn info(&self) -> PeerInfo {
            self.connection.common.info()
        }

        fn download_speed(&self) -> u64 {
            self.connection.common.download_speed()
        }



        
        async fn connect(&mut self) -> Result<()> {
            self.connection.common.connect().await?;
            Ok(())
        }
        
        async fn disconnect(&mut self) -> Result<()> {
            self.connection.common.disconnect().await?;
            Ok(())
        }
        
        async fn send_message(&mut self, message: &[u8]) -> Result<()> {
            self.connection.common.send_message(message).await
        }
        
        async fn receive_message(&mut self) -> Result<Vec<u8>> {
             self.connection.common.receive_message().await
         }

        fn upload_speed(&self) -> u64 {
            self.connection.common.upload_speed()
        }

        fn downloaded(&self) -> u64 {
            self.connection.common.downloaded()
        }

        fn uploaded(&self) -> u64 {
            self.connection.common.uploaded()
        }

         fn is_connected(&self) -> bool {
            self.connection.common.is_connected()
        }

        fn has_piece(&self, piece_index: u32) -> bool {
            self.state.pieces_have.contains(&piece_index)
        }

        fn is_choking(&self) -> bool {
            self.state.peer_choking
        }
        
        async fn add_piece(&mut self, piece_index: u32) -> Result<()> {
            self.state.pieces_have.insert(piece_index);
            Ok(())
        }
        
        async fn set_bitfield(&mut self, bitfield: Vec<u8>) -> Result<()> {
            self.state.bitfield = Some(bitfield.clone());
            
            // 更新pieces_have基于位图
            for (byte_index, &byte) in bitfield.iter().enumerate() {
                for bit_index in 0..8 {
                    if (byte >> (7 - bit_index)) & 1 == 1 {
                        let piece_index = byte_index * 8 + bit_index;
                        self.state.pieces_have.insert(piece_index as u32);
                    }
                }
            }
            
            Ok(())
        }
        
        async fn set_peer_choking(&mut self, choking: bool) -> Result<()> {
            self.state.peer_choking = choking;
            Ok(())
        }
        
        async fn set_peer_interested(&mut self, interested: bool) -> Result<()> {
            self.state.upload_manager.set_peer_interested(interested);
            Ok(())
        }
        
        async fn handle_request(&mut self, piece_index: u32, offset: u32, length: u32) -> anyhow::Result<()> {
            // 检查对等点是否拥有该分片
            if !self.state.pieces_have.contains(&piece_index) {
                return Err(anyhow::anyhow!("Peer does not have the requested piece: {}", piece_index));
            }
            // 获取 PieceManager 实例
            let piece_manager = match &self.connection.piece_manager {
                Some(pm) => pm.clone(),
                None => return Err(anyhow::anyhow!("PieceManager not available for this peer")),
            };
            // 先调用 upload_manager.handle_request 进行权限和队列处理
            let response = self.state.upload_manager.handle_request(piece_index, offset, length).await?;
            if let Some(msg) = response {
                // 直接发送拒绝等消息
                let msg_bytes = msg.to_bytes();
                self.connection.common.send_message(&msg_bytes).await?;
                return Ok(());
            }
            // 处理上传队列，实际只处理当前请求
            let mut messages = self.state.upload_manager.process_uploads(&piece_manager).await?;
            for msg in messages.drain(..) {
                let msg_bytes = msg.to_bytes();
                self.connection.common.send_message(&msg_bytes).await?;
            }
            Ok(())
        }

        async fn handle_cancel(&mut self, piece_index: u32, offset: u32, length: u32) -> anyhow::Result<()> {
            // 从上传队列中移除对应的请求，避免继续发送该分片
            self.state.upload_manager.cancel_request(piece_index, offset, length).await;
            Ok(())
        }

        fn pieces(&self) -> Vec<u32> {
            self.state.pieces_have.iter().cloned().collect()
        }
    }
}

pub struct PeerConnection {
    common: PeerCommon,
    timeout_secs: u32,
    piece_manager: Option<Arc<Mutex<dyn PieceManager>>>,
}

impl PeerConnection {
    pub fn new(
        common: PeerCommon,
        timeout_secs: u32,
        piece_manager: Option<Arc<Mutex<dyn PieceManager>>>,
    ) -> Self {
        Self {
            common,
            timeout_secs,
            piece_manager,
        }
    }
}
