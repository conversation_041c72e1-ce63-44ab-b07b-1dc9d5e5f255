import { ref, onMounted, onUnmounted } from 'vue';
import type { Ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { Task } from '../stores/task';
import type { TaskStatus } from '../stores/task';
import { TaskService } from '../services/TaskService';
import { useTaskSelection } from './useTaskSelection';

/**
 * 任务列表相关的composable
 */
export function useTaskList() {
  // 任务列表数据
  const taskList: Ref<Task[]> = ref([]);
  
  // 加载状态
  const loading = ref(false);
  
  // 当前任务状态过滤器
  const taskStatus = ref<TaskStatus>('all');
  
  // 使用任务选择相关功能
  const { 
    selectedTasks, 
    previousSelectedTasks,
    handleSelectionChange,
    restoreSelection,
    clearSelection,
    getPausableTasks,
    getResumableTasks 
  } = useTaskSelection();
  
  // 定时器引用
  let timer: number | null = null;

  /**
   * 获取任务列表
   */
  const fetchTaskList = async () => {
    loading.value = true;
    try {
      // 保存当前选中的任务，以便在刷新后恢复选择
      previousSelectedTasks.value = [...selectedTasks.value];
      
      // 获取任务列表
      taskList.value = await TaskService.getTaskList(taskStatus.value);
      
      // 恢复之前选中的任务
      restoreSelection(taskList.value);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      ElMessage.error('获取任务列表失败');
    } finally {
      loading.value = false;
    }
  };

  /**
   * 暂停单个任务
   * @param taskId 任务ID
   */
  const pauseTask = async (taskId: string) => {
    try {
      const response = await TaskService.pauseTask(taskId);
      if (response.data && response.data.success) {
        ElMessage.success('任务已暂停');
        fetchTaskList();
      } else {
        ElMessage.error(response.data?.error?.message || '暂停任务失败');
      }
    } catch (error: any) {
      console.error('暂停任务失败:', error);
      ElMessage.error(error.response?.data?.error?.message || '暂停任务失败');
    }
  };

  /**
   * 恢复单个任务
   * @param taskId 任务ID
   */
  const resumeTask = async (taskId: string) => {
    try {
      const response = await TaskService.resumeTask(taskId);
      if (response.data && response.data.success) {
        ElMessage.success('任务已恢复');
        fetchTaskList();
      } else {
        ElMessage.error(response.data?.error?.message || '恢复任务失败');
      }
    } catch (error: any) {
      console.error('恢复任务失败:', error);
      ElMessage.error(error.response?.data?.error?.message || '恢复任务失败');
    }
  };

  /**
   * 删除单个任务
   * @param taskId 任务ID
   */
  const removeTask = async (taskId: string) => {
    try {
      await ElMessageBox.confirm('确定要删除此任务吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      
      const response = await TaskService.removeTask(taskId);
      if (response.data && response.data.success) {
        ElMessage.success('任务已删除');
        fetchTaskList();
      } else {
        ElMessage.error(response.data?.error?.message || '删除任务失败');
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('删除任务失败:', error);
        ElMessage.error(error.response?.data?.error?.message || '删除任务失败');
      }
    }
  };

  /**
   * 批量暂停任务
   */
  const batchPause = async () => {
    if (selectedTasks.value.length === 0) {
      ElMessage.warning('请选择要暂停的任务');
      return;
    }

    try {
      // 筛选可暂停的任务（状态为active或waiting）
      const pausableTasks = getPausableTasks(taskList.value);
      
      // 如果没有可暂停的任务，提示用户
      if (pausableTasks.length === 0) {
        ElMessage.warning('选中的任务都不在可暂停状态（只有下载中或等待中的任务可以暂停）');
        return;
      }
      
      // 批量暂停任务
      const results = await TaskService.batchPauseTasks(
        pausableTasks.map(task => task.id)
      );

      // 显示结果
      if (results.successCount > 0) {
        ElMessage.success(`成功暂停 ${results.successCount} 个任务`);
        fetchTaskList();
      }
      
      // 如果有错误，显示错误信息
      if (results.errorMessages.length > 0) {
        // 如果错误太多，只显示前3个
        const displayErrors = results.errorMessages.length > 3 
          ? results.errorMessages.slice(0, 3).concat([`...等共 ${results.errorMessages.length} 个错误`])
          : results.errorMessages;
        
        ElMessage({
          message: displayErrors.join('\n'),
          type: 'error',
          duration: 5000,
          showClose: true,
        });
      }
    } catch (error: any) {
      console.error('批量暂停失败:', error);
      ElMessage.error('批量暂停失败');
    }
  };

  /**
   * 批量恢复任务
   */
  const batchResume = async () => {
    if (selectedTasks.value.length === 0) {
      ElMessage.warning('请选择要恢复的任务');
      return;
    }

    try {
      // 筛选可恢复的任务（状态为paused）
      const resumableTasks = getResumableTasks(taskList.value);
      
      // 如果没有可恢复的任务，提示用户
      if (resumableTasks.length === 0) {
        ElMessage.warning('选中的任务都不在可恢复状态（只有已暂停的任务可以恢复）');
        return;
      }
      
      // 批量恢复任务
      const results = await TaskService.batchResumeTasks(
        resumableTasks.map(task => task.id)
      );

      // 显示结果
      if (results.successCount > 0) {
        ElMessage.success(`成功恢复 ${results.successCount} 个任务`);
        fetchTaskList();
      }
      
      // 如果有错误，显示错误信息
      if (results.errorMessages.length > 0) {
        // 如果错误太多，只显示前3个
        const displayErrors = results.errorMessages.length > 3 
          ? results.errorMessages.slice(0, 3).concat([`...等共 ${results.errorMessages.length} 个错误`])
          : results.errorMessages;
        
        ElMessage({
          message: displayErrors.join('\n'),
          type: 'error',
          duration: 5000,
          showClose: true,
        });
      }
    } catch (error: any) {
      console.error('批量恢复失败:', error);
      ElMessage.error('批量恢复失败');
    }
  };

  /**
   * 批量删除任务
   */
  const batchRemove = async () => {
    if (selectedTasks.value.length === 0) {
      ElMessage.warning('请选择要删除的任务');
      return;
    }

    try {
      await ElMessageBox.confirm(`确定要删除选中的 ${selectedTasks.value.length} 个任务吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });

      console.log('开始批量删除任务:', selectedTasks.value);
      
      // 显示加载状态
      const loadingMessage = ElMessage({
        message: `正在删除 ${selectedTasks.value.length} 个任务...`,
        type: 'info',
        duration: 0
      });

      // 批量删除任务
      const results = await TaskService.batchRemoveTasks(selectedTasks.value);

      loadingMessage.close();

      if (results.successCount > 0) {
        let message = `成功删除 ${results.successCount} 个任务`;
        if (results.failedTasks.length > 0) {
          message += `，${results.failedTasks.length} 个任务删除失败`;
        }
        ElMessage.success(message);
        console.log('批量删除完成，刷新列表');
        fetchTaskList();
        clearSelection();
      } else {
        ElMessage.error('所有任务删除失败');
        console.error('失败的任务ID:', results.failedTasks);
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('批量删除任务失败:', error);
        ElMessage.error('批量删除任务失败');
      }
    }
  };

  /**
   * 显示错误详情
   * @param errorMessage 错误信息
   */
  const showErrorDetails = (errorMessage: string) => {
    ElMessageBox.alert(errorMessage, '错误详情', {
      confirmButtonText: '确定',
      customClass: 'error-details-dialog'
    });
  };

  /**
   * 表格行类名
   * @param row 行数据
   * @returns 行类名
   */
  const tableRowClassName = ({ row }: { row: any }) => {
    if (row.status === 'error') {
      return 'error-row';
    } else if (row.status === 'complete') {
      return 'success-row';
    }
    return '';
  };

  /**
   * WebSocket事件处理函数
   * @param payload 事件数据
   */
  /**
   * 处理任务添加事件
   * @param payload 事件数据
   */
  const handleTaskAdded = (payload: any) => {
    console.log('收到任务添加事件:', payload);
    // 刷新任务列表以反映新添加的任务
    fetchTaskList();
  };

  /**
   * 处理任务删除事件
   * @param payload 事件数据
   */
  const handleTaskRemoved = (payload: any) => {
    console.log('收到任务删除事件:', payload);
    // 刷新任务列表以反映删除的任务
    fetchTaskList();
  };

  /**
   * 设置任务状态过滤器
   * @param status 任务状态
   */
  const setTaskStatus = (status: TaskStatus) => {
    taskStatus.value = status;
    fetchTaskList();
    
    // 如果是活动任务，启动定时刷新
    if (status === 'active') {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  };

  /**
   * 启动定时刷新（仅当查看活动任务时）
   */
  const startAutoRefresh = () => {
    // 清除可能存在的旧定时器
    stopAutoRefresh();
    
    // 只有在查看活动任务时才启动定时刷新
    if (taskStatus.value === 'active') {
      timer = setInterval(() => {
        fetchTaskList();
      }, 3000) as unknown as number;
    }
  };

  /**
   * 停止定时刷新
   */
  const stopAutoRefresh = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  };

  // 组件挂载时获取任务列表
  onMounted(() => {
    fetchTaskList();

    // 添加WebSocket事件监听器
    addEventListener('task.add', handleTaskAdded);
    addEventListener('task.remove', handleTaskRemoved);

    // 如果是活动任务，启动定时刷新
    if (taskStatus.value === 'active') {
      startAutoRefresh();
    }
  });

  /**
   * 更新任务信息
   * @param taskId 任务ID
   * @param taskData 任务数据
   */
  const updateTask = async (taskId: string, taskData: Partial<Task>) => {
    try {
      const response = await TaskService.updateTask(taskId, taskData);
      if (response.data && response.data.success) {
        ElMessage.success('任务已更新');
        fetchTaskList();
      } else {
        ElMessage.error(response.data?.error?.message || '更新任务失败');
      }
    } catch (error: any) {
      console.error('更新任务失败:', error);
      ElMessage.error(error.response?.data?.error?.message || '更新任务失败');
    }
  };

  // 在组件卸载时清理
  onUnmounted(() => {
    // 移除WebSocket事件监听器
    removeEventListener('task.add', handleTaskAdded);
    removeEventListener('task.remove', handleTaskRemoved);
  });

  return {
    taskList,
    loading,
    taskStatus,
    selectedTasks,
    fetchTaskList,
    setTaskStatus,
    pauseTask,
    resumeTask,
    removeTask,
    batchPause,
    batchResume,
    batchRemove,
    showErrorDetails,
    tableRowClassName,
    updateTask,
    handleSelectionChange
  };
}