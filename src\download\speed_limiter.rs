use anyhow::Result;
use async_trait::async_trait;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::{Duration, Instant, sleep};
use tracing::{debug, info};

/// Speed limiter interface
#[async_trait]
pub trait SpeedLimiter: Send + Sync {
    /// Set the speed limit in bytes per second
    async fn set_limit(&self, bytes_per_second: Option<u64>) -> Result<()>;
    
    /// Get the current speed limit
    async fn get_limit(&self) -> Result<Option<u64>>;
    
    /// Wait for the appropriate amount of time to maintain the speed limit
    async fn wait_for_quota(&self, bytes: usize) -> Result<()>;
}

/// Speed limiter implementation using a token bucket algorithm
pub struct SpeedLimiterImpl {
    limit: Arc<RwLock<Option<u64>>>,
    last_update: Arc<RwLock<Instant>>,
    tokens: Arc<RwLock<u64>>,
}

impl SpeedLimiterImpl {
    /// Create a new speed limiter
    pub fn new(limit: Option<u64>) -> Self {
        Self {
            limit: Arc::new(RwLock::new(limit)),
            last_update: Arc::new(RwLock::new(Instant::now())),
            tokens: Arc::new(RwLock::new(0)),
        }
    }
    
    /// Refill the token bucket
    async fn refill_tokens(&self) -> Result<()> {
        let mut tokens = self.tokens.write().await;
        let mut last_update = self.last_update.write().await;
        let limit = *self.limit.read().await;
        
        if let Some(limit) = limit {
            let now = Instant::now();
            let elapsed = now.duration_since(*last_update);
            let new_tokens = (elapsed.as_secs_f64() * limit as f64) as u64;
            
            *tokens = (*tokens + new_tokens).min(limit);
            *last_update = now;
        }
        
        Ok(())
    }
}

#[async_trait]
impl SpeedLimiter for SpeedLimiterImpl {
    async fn set_limit(&self, bytes_per_second: Option<u64>) -> Result<()> {
        let mut limit = self.limit.write().await;
        *limit = bytes_per_second;
        
        if let Some(bps) = bytes_per_second {
            info!("Speed limit set to {} bytes per second", bps);
        } else {
            info!("Speed limit disabled");
        }
        
        Ok(())
    }
    
    async fn get_limit(&self) -> Result<Option<u64>> {
        let limit = *self.limit.read().await;
        Ok(limit)
    }
    
    async fn wait_for_quota(&self, bytes: usize) -> Result<()> {
        let limit = *self.limit.read().await;
        
        if let Some(limit) = limit {
            // Refill tokens
            self.refill_tokens().await?;
            
            let bytes_u64 = bytes as u64;
            let mut tokens = self.tokens.write().await;
            
            if bytes_u64 > *tokens {
                // Not enough tokens, need to wait
                let tokens_needed = bytes_u64 - *tokens;
                let wait_time = Duration::from_secs_f64(tokens_needed as f64 / limit as f64);
                
                debug!("Waiting for {} seconds to maintain speed limit", wait_time.as_secs_f64());
                
                // Release the lock before sleeping
                drop(tokens);
                sleep(wait_time).await;
                
                // Refill tokens again after waiting
                self.refill_tokens().await?;
                
                // Take all the tokens we need
                let mut tokens = self.tokens.write().await;
                *tokens = tokens.saturating_sub(bytes_u64);
            } else {
                // Enough tokens, consume them
                *tokens -= bytes_u64;
            }
        }
        
        Ok(())
    }
}
