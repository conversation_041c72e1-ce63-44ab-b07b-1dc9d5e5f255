import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import axios from 'axios'

import App from './App.vue'
import router from './router'

const app = createApp(App)

// 注册所有 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 配置 axios
axios.defaults.baseURL = 'http://localhost:8080/api/v1'
app.config.globalProperties.$http = axios

app.use(createPinia())
app.use(router)
app.use(ElementPlus, { size: 'small' })

app.mount('#app')
