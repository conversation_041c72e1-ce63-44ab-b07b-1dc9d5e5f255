use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 速度限制响应
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SpeedLimitResponse {
    pub limit: u64,
}

/// 下载统计信息
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Serialize, Deserialize)]
pub struct DownloadStats {
    pub active_count: usize,
    pub paused_count: usize,
    pub completed_count: usize,
    pub failed_count: usize,
    pub total_count: usize,
    pub total_downloaded_bytes: u64,
    pub total_uploaded_bytes: u64,
}

/// 时间规则
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TimeRule {
    pub id: Option<Uuid>,
    pub name: String,
    pub start_time: String,
    pub end_time: String,
    pub days: Vec<u8>,
    pub download_limit: Option<u64>,
    pub upload_limit: Option<u64>,
}

/// 系统负载信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SystemLoad {
    pub cpu_usage: f64,
    pub disk_usage: f64,
}

/// 内存使用信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MemoryUsage {
    pub total: u64,
    pub used: u64,
    pub free: u64,
}

/// 服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
}

/// 下载配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadConfig {
    pub path: String,
    pub concurrent_downloads: u32,
    pub connections_per_download: u32,
    pub chunk_size: u64,
    pub buffer_size: u64,
    pub speed_limit: u64,
}

/// 数据库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
}

/// 镜像配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MirrorConfig {
    pub enabled: bool,
    pub max_mirrors: u32,
    pub health_check_interval_secs: u64,
}

/// 代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    pub enabled: bool,
    pub url: String,
    pub proxy_type: String,
    pub username: String,
    pub password: String,
    pub no_proxy: Vec<String>,
}

/// 完整配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub download: DownloadConfig,
    pub database: DatabaseConfig,
    pub mirror: MirrorConfig,
    pub proxy: ProxyConfig,
}

/// 系统状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Status {
    pub version: String,
    pub uptime: u64,
    pub load: SystemLoad,
    pub memory: MemoryUsage,
}