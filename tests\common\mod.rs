use std::path::PathBuf;
use std::env;
use tokio::fs;
use anyhow::Result;
use uuid::Uuid;
use axum::response::IntoResponse;

use tonitru_downloader::config::Settings;

/// Creates a temporary directory for testing
pub async fn create_temp_dir() -> Result<PathBuf> {
    let temp_dir = env::temp_dir().join(format!("tonitru_test_{}", Uuid::new_v4()));
    fs::create_dir_all(&temp_dir).await?;
    Ok(temp_dir)
}

/// Cleans up a temporary directory
pub async fn cleanup_temp_dir(path: &PathBuf) -> Result<()> {
    if path.exists() && path.starts_with(env::temp_dir()) {
        fs::remove_dir_all(path).await?;
    }
    Ok(())
}

/// Creates a test file with specified content
pub async fn create_test_file(path: &PathBuf, content: &[u8]) -> Result<()> {
    fs::write(path, content).await?;
    Ok(())
}

/// Creates default test settings
pub fn create_test_settings() -> Settings {
    Settings::default()
}

/// Starts a simple HTTP server for testing
/// Returns the server address and a shutdown signal
#[cfg(feature = "integration_tests")]
pub async fn start_test_http_server() -> Result<(String, tokio::sync::oneshot::Sender<()>)> {
    use axum::{serve,
        routing::get,
        Router,
        extract::Path,
        http::StatusCode,
        response::IntoResponse,
    };
    use std::net::SocketAddr;
    use tokio::sync::oneshot;

    // Create a shutdown channel
    let (tx, rx) = oneshot::channel::<()>();

    // Create a router
    let app = Router::new()
        .route("/files/{filename}", get(serve_file))
        .route("/no-range/{filename}", get(serve_file_no_range));

    // Find a free port
    let addr = SocketAddr::from(([127, 0, 0, 1], 0));
    let listener = tokio::net::TcpListener::bind(&addr).await?;
    let addr = listener.local_addr()?;
    let server = axum::serve(listener, app)
        .with_graceful_shutdown(async {
            rx.await.ok();
        });

    // addr is already set from listener.local_addr()

    // Spawn the server
    tokio::spawn(async move {
        server.await.unwrap();
    });

    Ok((format!("http://{}", addr), tx))
}

/// Serves a file from the test files directory
#[cfg(feature = "integration_tests")]
async fn serve_file(path: axum::extract::Path<String>) -> impl axum::response::IntoResponse {
    let filename = path.0;
    let current_dir = std::env::current_dir().unwrap();
    let path = current_dir.join("tests").join("files").join(filename);
    match tokio::fs::read(&path).await {
        Ok(content) => {
            let content_length = content.len();
            (
                axum::http::StatusCode::OK,
                [
                    ("content-type", "application/octet-stream"),
                    ("content-length", &content_length.to_string()),
                    ("accept-ranges", "bytes"),
                ],
                content
            ).into_response()
        },
        Err(e) => {
            println!("Error reading file {}: {}", path.display(), e);
            (axum::http::StatusCode::NOT_FOUND, "File not found").into_response()
        },
    }
}

/// Serves a file without range support
#[cfg(feature = "integration_tests")]
async fn serve_file_no_range(path: axum::extract::Path<String>) -> impl axum::response::IntoResponse {
    let filename = path.0;
    let current_dir = std::env::current_dir().unwrap();
    let path = current_dir.join("tests").join("files").join(filename);
    match tokio::fs::read(&path).await {
        Ok(content) => {
            let content_length = content.len();
            (
                axum::http::StatusCode::OK,
                [
                    ("content-type", "application/octet-stream"),
                    ("content-length", &content_length.to_string()),
                ],
                content
            ).into_response()
        },
        Err(e) => {
            println!("Error reading file {}: {}", path.display(), e);
            (axum::http::StatusCode::NOT_FOUND, "File not found").into_response()
        },
    }
}
