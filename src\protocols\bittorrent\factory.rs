use std::sync::Arc;
use uuid::Uuid;

use crate::config::ConfigManager;
use crate::download::resume::ResumeManager;
use crate::download::bandwidth_scheduler::BandwidthScheduler;

use super::downloader::BitTorrentDownloader;

/// BitTorrent protocol factory
#[derive(Clone)]
pub struct BitTorrentFactory {
    /// Config Manager
    config_manager: Arc<ConfigManager>,
    /// Resume manager
    resume_manager: Option<Arc<dyn ResumeManager>>,
    /// Bandwidth scheduler
    bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
}

impl BitTorrentFactory {
    /// Create a new BitTorrent protocol factory
    pub fn new(config_manager: Arc<ConfigManager>) -> Self {
        Self {
            config_manager,
            resume_manager: None,
            bandwidth_scheduler: None,
        }
    }

    /// Set resume manager
    pub fn with_resume_manager(mut self, resume_manager: Arc<dyn ResumeManager>) -> Self {
        self.resume_manager = Some(resume_manager);
        self
    }

    /// Set bandwidth scheduler
    pub fn with_bandwidth_scheduler(mut self, bandwidth_scheduler: Arc<dyn BandwidthScheduler>) -> Self {
        self.bandwidth_scheduler = Some(bandwidth_scheduler);
        self
    }

    /// Create a BitTorrent downloader
    pub fn create_downloader(&self, url: &str, output_path: &str, task_id: Uuid) -> BitTorrentDownloader {
        let mut downloader = BitTorrentDownloader::new(
            url.to_string(),
            output_path.to_string(),
            self.config_manager.clone(),
            task_id,
        );

        // Set resume manager if available
        if let Some(resume_manager) = &self.resume_manager {
            downloader = downloader.with_resume_manager(resume_manager.clone());
        }

        // Set bandwidth scheduler if available
        if let Some(bandwidth_scheduler) = &self.bandwidth_scheduler {
            downloader = downloader.with_bandwidth_scheduler(bandwidth_scheduler.clone());
        }

        downloader
    }
    
    /// Check if the factory supports the given URL
    pub fn supports_url(&self, url: &str) -> bool {
        url.starts_with("magnet:") || url.ends_with(".torrent")
    }
}
