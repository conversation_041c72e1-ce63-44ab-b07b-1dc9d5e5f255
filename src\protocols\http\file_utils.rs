//! HTTP下载器文件操作工具模块

use std::path::Path;
use anyhow::Result;
use tracing::{debug, error};

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// 获取临时文件路径
    pub fn get_temp_file_path(&self, output_path: &str) -> String {
        // 使用Path来正确处理路径
        let path = Path::new(output_path);
        let file_stem = path.file_stem().unwrap_or_default();
        let extension = path.extension().unwrap_or_default();
        
        // 构建新的文件名：原文件名+临时扩展名
        let mut temp_filename = file_stem.to_string_lossy().to_string();
        if !extension.is_empty() {
            temp_filename.push_str(".");
            temp_filename.push_str(&extension.to_string_lossy());
        }
        temp_filename.push_str(&self.temp_file_extension);
        
        // 将新文件名与父目录路径结合
        if let Some(parent) = path.parent() {
            // 确保使用相同的目录，不创建新的临时目录
            return parent.join(temp_filename).to_string_lossy().to_string();
        }
        
        // 如果没有父目录，直接返回文件名
        temp_filename
    }

    /// 处理取消清理工作
    pub(crate) async fn handle_cancellation_cleanup(&self, temp_file_path: &str) -> Result<()> {
        debug!("Handling cancellation cleanup for task {}", self.task_id);

        // 删除临时文件
        if Path::new(temp_file_path).exists() {
            debug!("Removing temporary file: {}", temp_file_path);
            match tokio::fs::remove_file(temp_file_path).await {
                Ok(_) => debug!("Temporary file removed successfully"),
                Err(e) => {
                    error!("Failed to remove temporary file {}: {}", temp_file_path, e);
                    // 不返回错误，因为这不是致命错误
                }
            }
        }

        // 删除恢复点
        if let Some(resume_manager) = &self.resume_manager {
            debug!("Removing resume point for cancelled download");
            match resume_manager.delete_resume_point(self.task_id).await {
                Ok(_) => debug!("Resume point deleted successfully"),
                Err(e) => {
                    error!("Failed to delete resume point: {}", e);
                    // 不返回错误，因为这不是致命错误
                }
            }
        }

        Ok(())
    }

    /// 处理暂停保存工作
    pub(crate) async fn handle_pause_save(&self) -> Result<()> {
        debug!("Handling pause save for task {}", self.task_id);

        // 保存恢复点
        match self.save_resume_point().await {
            Ok(_) => debug!("Resume point saved successfully"),
            Err(e) => {
                error!("Failed to save resume point: {}", e);
                return Err(e);
            }
        }

        Ok(())
    }
}
