use anyhow::{Result, anyhow};
use ed25519_dalek::{Signature, Signer, Verifier};
use ed25519_dalek::SigningKey;
use ed25519_dalek::VerifyingKey;
use rand::rngs::OsRng;
use rand::RngCore;

use super::value::MutableValue;

/// 生成新的密钥对
pub fn generate_keypair() -> Result<(Vec<u8>, Vec<u8>)> {
    let mut csprng = OsRng;
    // 在 ed25519-dalek 2.x 中，我们需要使用随机生成的字节创建 SigningKey
    let mut secret_bytes = [0u8; 32];
    csprng.fill_bytes(&mut secret_bytes);
    let signing_key = SigningKey::from_bytes(&secret_bytes);
    let verifying_key = signing_key.verifying_key();

    let public_key = verifying_key.to_bytes().to_vec();
    let secret_key = signing_key.to_bytes().to_vec();

    Ok((public_key, secret_key))
}

/// 签名值
pub fn sign_value(
    value: &[u8],
    sequence: i64,
    secret_key: &[u8],
    salt: Option<&[u8]>,
) -> Result<[u8; 64]> {
    // 检查密钥长度
    if secret_key.len() != 32 {
        return Err(anyhow!("Invalid secret key length"));
    }

    // 构建要签名的消息
    let mut message = Vec::with_capacity(value.len() + 8 + salt.map_or(0, |s| s.len()));
    message.extend_from_slice(value);
    message.extend_from_slice(&sequence.to_be_bytes());

    if let Some(s) = salt {
        message.extend_from_slice(s);
    }

    // 解析密钥
    // 使用 ed25519_dalek 2.x 版本的 API
    // 需要将 secret_key 转换为 [u8; 32]
    let mut secret_bytes = [0u8; 32];
    if secret_key.len() != 32 {
        return Err(anyhow!("Invalid secret key length"));
    }
    secret_bytes.copy_from_slice(secret_key);
    let signing_key = SigningKey::from_bytes(&secret_bytes);

    // 签名消息
    let signature = signing_key.sign(&message);

    Ok(signature.to_bytes())
}

/// 验证签名
pub fn verify_value(
    value: &[u8],
    sequence: i64,
    public_key: &[u8],
    signature: &[u8],
    salt: Option<&[u8]>,
) -> Result<bool> {
    // 检查公钥和签名长度
    if public_key.len() != 32 {
        return Err(anyhow!("Invalid public key length"));
    }

    if signature.len() != 64 {
        return Err(anyhow!("Invalid signature length"));
    }

    // 构建要验证的消息
    let mut message = Vec::with_capacity(value.len() + 8 + salt.map_or(0, |s| s.len()));
    message.extend_from_slice(value);
    message.extend_from_slice(&sequence.to_be_bytes());

    if let Some(s) = salt {
        message.extend_from_slice(s);
    }

    // 解析公钥
    let mut pk_bytes = [0u8; 32];
    if public_key.len() != 32 {
        return Err(anyhow!("Invalid public key length"));
    }
    pk_bytes.copy_from_slice(public_key);
    let verifying_key = VerifyingKey::from_bytes(&pk_bytes)
        .map_err(|e| anyhow!("Invalid public key: {}", e))?;

    // 解析签名
    // 使用 ed25519_dalek 2.x 版本的 API
    // 需要将 signature 转换为 [u8; 64]
    let mut sig_bytes = [0u8; 64];
    if signature.len() != 64 {
        return Err(anyhow!("Invalid signature length"));
    }

    sig_bytes.copy_from_slice(signature);
    let signature = Signature::try_from(&sig_bytes[..])
        .map_err(|_| anyhow!("Invalid signature"))?;

    // 验证签名
    match verifying_key.verify(&message, &signature) {
        Ok(_) => Ok(true),
        Err(e) => Err(anyhow!("Signature verification failed: {}", e)),
    }
}

/// 创建可变值
pub fn create_mutable_value(
    value: Vec<u8>,
    sequence: i64,
    secret_key: &[u8],
    salt: Option<Vec<u8>>,
) -> Result<MutableValue> {
    // 检查密钥长度
    if secret_key.len() != 32 {
        return Err(anyhow!("Invalid secret key length"));
    }

    // 解析密钥
    // 使用 ed25519_dalek 2.x 版本的 API
    // 需要将 secret_key 转换为 [u8; 32]
    let mut secret_bytes = [0u8; 32];
    if secret_key.len() != 32 {
        return Err(anyhow!("Invalid secret key length"));
    }
    secret_bytes.copy_from_slice(secret_key);
    let signing_key = SigningKey::from_bytes(&secret_bytes);

    // 获取验证密钥（公钥）
    let verifying_key = signing_key.verifying_key();

    // 获取公钥字节
    let public_key_bytes = verifying_key.to_bytes();

    // 构建要签名的消息
    let mut message = Vec::with_capacity(value.len() + 8 + salt.as_ref().map_or(0, |s| s.len()));
    message.extend_from_slice(&value);
    message.extend_from_slice(&sequence.to_be_bytes());

    if let Some(s) = &salt {
        message.extend_from_slice(s);
    }

    // 签名消息
    let signature = signing_key.sign(&message);
    let signature_bytes = signature.to_bytes();

    // 创建可变值
    let mutable_value = MutableValue::new(
        value.to_vec(),
        sequence,
        public_key_bytes.to_vec(),
        signature_bytes.to_vec(),
        salt.map(|s| s.to_vec()),
    );

    Ok(mutable_value)
}
