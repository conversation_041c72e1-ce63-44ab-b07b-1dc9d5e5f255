# Tonitru 下载器文档索引

本文档索引提供了 Tonitru 下载器项目的所有核心文档的导航。文档已经按照功能和主题进行了合并和整理，以提供更清晰、更系统的项目文档。

## 1. 核心文档

### [API完整指南](./API完整指南.md)

全面介绍 Tonitru 下载器的 API 接口，包括：
- API 概述和设计原则
- 基础 URL 和认证方式
- 通用请求/响应格式和错误码
- 下载管理、任务管理、配置管理和速度控制 API
- 前端集成指南和后端开发计划

### [BitTorrent技术实现指南](./BitTorrent技术实现指南.md)

详细描述 Tonitru 下载器的 BitTorrent 技术实现，包括：
- BitTorrent 协议概述和核心概念
- 核心组件设计（对等点架构、分片管理、性能优化）
- 扩展协议实现（DHT、PEX、元数据交换、WebSeed）
- 高级功能（加密传输、带宽管理、优先级调度）

### [CLI完整指南](./CLI完整指南.md)

提供 Tonitru 下载器命令行工具的完整指南，包括：
- CLI 概述和设计目标
- 技术选择和架构设计
- 运行模式（API 调用和直接集成）
- 用户指南（安装方法、命令结构、使用示例）
- 开发文档（实现计划、架构设计、直接集成模式）

### [项目开发指南](./项目开发指南.md)

全面介绍 Tonitru 下载器项目的开发信息，包括：
- 项目概述（简介、核心特性、技术栈）
- 架构设计（系统架构、模块设计、数据流设计）
- 设计原则（代码设计、性能设计、安全设计）
- 项目结构和开发计划
- 开发指南（环境搭建、代码规范、测试指南、贡献指南）
- 常见问题与解决方案

### [插件系统开发指南](./插件系统开发指南.md)

详细介绍 Tonitru 下载器的插件系统，包括：
- 插件系统概述（设计目标、插件类型、技术架构）
- 插件 API（核心接口、插件信息、插件上下文、事件系统）
- 插件开发流程（创建项目、实现接口、构建和测试）
- 插件类型详解（协议插件、处理器插件、通知插件、界面插件）
- 插件管理（安装、配置、生命周期）
- 最佳实践和示例插件

### [对等点质量评分系统](./对等点质量评分系统.md)

详细介绍 Tonitru 下载器的对等点质量评分系统，包括：
- 系统概述（设计目标、核心功能）
- 系统架构（核心组件、组件关系）
- 评分指标（基础性能指标、高级指标、安全指标、元数据）
- 评分计算（综合评分计算、指标归一化）
- 过滤机制（过滤器类型、过滤流程）
- 违规记录（违规类型、严重程度、处理方式）
- 系统集成（与对等点管理器、消息处理器、安全管理器的集成）
- 配置与调优（评分权重、参数配置、过滤器配置）
- 性能优化和未来扩展

## 2. 如何使用这些文档

### 对于新用户

如果您是 Tonitru 下载器的新用户，建议按照以下顺序阅读文档：

1. **项目开发指南** - 了解项目概述和架构
2. **API完整指南** - 了解如何与下载器交互
3. **CLI完整指南** - 了解如何通过命令行使用下载器

### 对于开发者

如果您是开发者，希望为 Tonitru 下载器贡献代码或开发插件，建议按照以下顺序阅读文档：

1. **项目开发指南** - 了解项目架构和开发流程
2. **API完整指南** - 了解 API 设计和实现
3. **BitTorrent技术实现指南** - 了解核心下载技术
4. **对等点质量评分系统** - 了解对等点评分和过滤机制
5. **插件系统开发指南** - 了解如何开发插件

### 对于系统管理员

如果您是系统管理员，希望部署和管理 Tonitru 下载器，建议阅读：

1. **CLI完整指南** - 了解命令行工具的使用
2. **API完整指南** - 了解如何通过 API 管理下载器

## 3. 文档更新计划

这些文档将根据项目的发展定期更新。如果您发现任何错误或有改进建议，请通过以下方式提交：

1. 在 GitHub 仓库提交 Issue
2. 提交 Pull Request 修改文档
3. 联系项目维护者

## 4. 贡献指南

我们欢迎所有形式的文档贡献，包括但不限于：

- 修正错误和拼写问题
- 改进文档结构和可读性
- 添加更多示例和用例
- 翻译文档到其他语言

请参考项目仓库中的贡献指南，了解如何提交文档改进。