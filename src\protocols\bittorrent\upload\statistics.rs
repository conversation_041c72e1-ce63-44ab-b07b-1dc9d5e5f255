use std::collections::VecDeque;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::Result;
use async_trait::async_trait;
use tokio::sync::Mutex;
use tracing::debug;

/// 上传统计信息接口
/// 定义上传统计信息的行为
#[async_trait]
pub trait UploadStatistics: Send + Sync {
    /// 添加上传字节数
    async fn add_uploaded(&self, bytes: u64) -> Result<()>;
    
    /// 获取总上传字节数
    async fn get_total_uploaded(&self) -> Result<u64>;
    
    /// 获取上传速率（字节/秒）
    async fn get_upload_rate(&self) -> Result<u64>;
    
    /// 重置统计信息
    async fn reset(&self) -> Result<()>;
    
    /// 获取上传历史
    async fn get_upload_history(&self) -> Result<Vec<(Instant, u64)>>;
}

/// 上传统计信息实现
/// 实现上传统计信息接口
#[derive(Debug)]
pub struct UploadStats {
    /// 总上传字节数
    total_uploaded: u64,
    
    /// 上传历史
    upload_history: VecDeque<(Instant, u64)>,
    
    /// 历史窗口大小（秒）
    history_window: u64,
    
    /// 最大历史记录数
    max_history_entries: usize,
    
    /// 上次速率计算时间
    last_rate_calculation: Instant,
    
    /// 当前上传速率（字节/秒）
    current_rate: u64,
}

impl UploadStats {
    /// 创建新的上传统计信息
    pub fn new() -> Self {
        Self {
            total_uploaded: 0,
            upload_history: VecDeque::new(),
            history_window: 30, // 30秒窗口
            max_history_entries: 100,
            last_rate_calculation: Instant::now(),
            current_rate: 0,
        }
    }
    
    /// 创建带自定义参数的上传统计信息
    pub fn with_params(history_window: u64, max_history_entries: usize) -> Self {
        Self {
            total_uploaded: 0,
            upload_history: VecDeque::new(),
            history_window,
            max_history_entries,
            last_rate_calculation: Instant::now(),
            current_rate: 0,
        }
    }
    
    /// 清理过期的历史记录
    fn clean_history(&mut self) {
        let now = Instant::now();
        let window = Duration::from_secs(self.history_window);
        
        // 移除超过窗口的记录
        while let Some((time, _)) = self.upload_history.front() {
            if now.duration_since(*time) > window {
                self.upload_history.pop_front();
            } else {
                break;
            }
        }
        
        // 如果历史记录超过最大数量，移除最旧的记录
        while self.upload_history.len() > self.max_history_entries {
            self.upload_history.pop_front();
        }
    }
    
    /// 计算上传速率
    fn calculate_rate(&mut self) {
        let now = Instant::now();
        
        // 如果距离上次计算时间不足1秒，不重新计算
        if now.duration_since(self.last_rate_calculation) < Duration::from_secs(1) {
            return;
        }
        
        // 清理过期的历史记录
        self.clean_history();
        
        // 如果没有历史记录，速率为0
        if self.upload_history.is_empty() {
            self.current_rate = 0;
            self.last_rate_calculation = now;
            return;
        }
        
        // 计算窗口内的总上传字节数
        let mut total_bytes = 0;
        for (_, bytes) in &self.upload_history {
            total_bytes += bytes;
        }
        
        // 获取最早和最晚的记录时间
        let (earliest_time, _) = self.upload_history.front().unwrap();
        let time_span = now.duration_since(*earliest_time).as_secs_f64();
        
        // 计算速率
        if time_span > 0.0 {
            self.current_rate = (total_bytes as f64 / time_span) as u64;
        } else {
            self.current_rate = 0;
        }
        
        self.last_rate_calculation = now;
    }
}

#[async_trait]
impl UploadStatistics for UploadStats {
    async fn add_uploaded(&self, bytes: u64) -> Result<()> {
        let mut this = self.clone();
        
        // 更新总上传字节数
        this.total_uploaded += bytes;
        
        // 添加到历史记录
        this.upload_history.push_back((Instant::now(), bytes));
        
        // 清理过期的历史记录
        this.clean_history();
        
        // 计算上传速率
        this.calculate_rate();
        
        Ok(())
    }
    
    async fn get_total_uploaded(&self) -> Result<u64> {
        Ok(self.total_uploaded)
    }
    
    async fn get_upload_rate(&self) -> Result<u64> {
        let mut this = self.clone();
        
        // 计算上传速率
        this.calculate_rate();
        
        Ok(this.current_rate)
    }
    
    async fn reset(&self) -> Result<()> {
        let mut this = self.clone();
        
        // 重置统计信息
        this.total_uploaded = 0;
        this.upload_history.clear();
        this.current_rate = 0;
        this.last_rate_calculation = Instant::now();
        
        Ok(())
    }
    
    async fn get_upload_history(&self) -> Result<Vec<(Instant, u64)>> {
        let mut this = self.clone();
        
        // 清理过期的历史记录
        this.clean_history();
        
        Ok(this.upload_history.iter().cloned().collect())
    }
}

impl Clone for UploadStats {
    fn clone(&self) -> Self {
        Self {
            total_uploaded: self.total_uploaded,
            upload_history: self.upload_history.clone(),
            history_window: self.history_window,
            max_history_entries: self.max_history_entries,
            last_rate_calculation: self.last_rate_calculation,
            current_rate: self.current_rate,
        }
    }
}
