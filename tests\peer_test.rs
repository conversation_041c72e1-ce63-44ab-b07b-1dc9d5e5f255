use anyhow::Result;
use std::net::{IpAddr, Ipv4Addr, SocketAddr};
use tokio::test;

use tonitru_downloader::core::p2p::peer::Peer;
use tonitru_downloader::protocols::bittorrent::peer::BitTorrentPeer;
use tonitru_downloader::protocols::bittorrent::message::BitTorrentMessage;

/// Test creating a BitTorrent peer
#[tokio::test]
async fn test_peer_creation() -> Result<()> {
    let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
    let info_hash = [1u8; 20];
    let peer_id = [0u8; 20];
    let timeout_secs = 10;
    let num_pieces = 10;

    let peer = BitTorrentPeer::new(addr, &info_hash, &peer_id, timeout_secs, num_pieces).await?;

    // Verify the peer was created correctly
    assert_eq!(peer.info_hash, info_hash);
    assert_eq!(peer.local_peer_id, peer_id);
    assert!(!peer.handshaked);
    assert!(!peer.am_interested);
    assert!(peer.peer_choking);

    Ok(())
}

/// Test peer address formatting
#[tokio::test]
async fn test_peer_addr() -> Result<()> {
    let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
    let info_hash = [1u8; 20];
    let peer_id = [0u8; 20];
    let timeout_secs = 10;
    let num_pieces = 10;

    let peer = BitTorrentPeer::new(addr, &info_hash, &peer_id, timeout_secs, num_pieces).await?;

    // Get peer info
    let peer_info = peer.info();
    assert_eq!(peer_info.addr, addr);

    Ok(())
}

/// Test BitTorrent message encoding and decoding
#[tokio::test]
async fn test_bt_message_encode_decode() -> Result<()> {
    // Test choke message
    let choke = BitTorrentMessage::Choke;
    let encoded = choke.encode(false)?;
    assert_eq!(encoded[0..5], [0, 0, 0, 1, 0]);

    // Test unchoke message
    let unchoke = BitTorrentMessage::Unchoke;
    let encoded = unchoke.encode(false)?;
    assert_eq!(encoded[0..5], [0, 0, 0, 1, 1]);

    // Test interested message
    let interested = BitTorrentMessage::Interested;
    let encoded = interested.encode(false)?;
    assert_eq!(encoded[0..5], [0, 0, 0, 1, 2]);

    // Test not interested message
    let not_interested = BitTorrentMessage::NotInterested;
    let encoded = not_interested.encode(false)?;
    assert_eq!(encoded[0..5], [0, 0, 0, 1, 3]);

    // Test have message
    let have = BitTorrentMessage::Have(5);
    let encoded = have.encode(false)?;
    assert_eq!(encoded[0..9], [0, 0, 0, 5, 4, 0, 0, 0, 5]);

    // Test bitfield message
    let bitfield = BitTorrentMessage::Bitfield(vec![0b10101010, 0b01010101]);
    let encoded = bitfield.encode(false)?;
    assert_eq!(encoded[0..7], [0, 0, 0, 3, 5, 0b10101010, 0b01010101]);

    // Test request message
    let request = BitTorrentMessage::Request(1, 16384, 8192);
    let encoded = request.encode(false)?;
    assert_eq!(encoded[0..17], [0, 0, 0, 13, 6, 0, 0, 0, 1, 0, 0, 0x40, 0, 0, 0, 0x20, 0]);

    Ok(())
}

/// Test handshake handler
#[tokio::test]
async fn test_handshake_handler() -> Result<()> {
    let info_hash = [1u8; 20];
    let peer_id = [2u8; 20];

    // Create a handshake handler
    let handler = tonitru_downloader::protocols::bittorrent::handshake::HandshakeHandler::new(&info_hash, &peer_id);

    // Verify the handler was created correctly
    assert_eq!(handler.info_hash(), info_hash);
    assert_eq!(handler.local_peer_id(), peer_id);
    assert!(!handler.supports_fast);
    assert!(!handler.supports_extensions);
    assert!(handler.peer_id.is_none());
    assert!(handler.client_version.is_none());

    Ok(())
}

/// Test peer connection state
#[tokio::test]
async fn test_peer_connection_state() -> Result<()> {
    let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
    let info_hash = [1u8; 20];
    let peer_id = [0u8; 20];
    let timeout_secs = 10;
    let num_pieces = 10;

    let peer = BitTorrentPeer::new(addr, &info_hash, &peer_id, timeout_secs, num_pieces).await?;

    // Initially, peer should not be connected
    assert!(!peer.is_connected());

    // Note: We can't actually test connection state transitions without a real peer
    // to connect to, but we can verify the initial state

    Ok(())
}

/// Test peer interest state
#[tokio::test]
async fn test_peer_interest_state() -> Result<()> {
    let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 6881);
    let info_hash = [1u8; 20];
    let peer_id = [0u8; 20];
    let timeout_secs = 10;
    let num_pieces = 10;

    let mut peer = BitTorrentPeer::new(addr, &info_hash, &peer_id, timeout_secs, num_pieces).await?;

    // Initial states
    assert!(!peer.am_interested);
    assert!(peer.peer_choking);

    // Change interest state by sending a message
    peer.send_bt_message(BitTorrentMessage::Interested).await?;

    // Verify state was updated
    assert!(peer.am_interested);

    // Change interest state by sending another message
    peer.send_bt_message(BitTorrentMessage::NotInterested).await?;

    // Verify state was updated
    assert!(!peer.am_interested);

    Ok(())
}
