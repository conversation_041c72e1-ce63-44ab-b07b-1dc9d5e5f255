//! HTTP下载器取消机制模块

use futures::Stream;
use futures::StreamExt;
use tracing::debug;

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// 创建可取消的下载流
    pub(crate) fn create_cancellable_stream<S, T, E>(
        &self,
        stream: S,
    ) -> impl Stream<Item = Result<T, E>>
    where
        S: Stream<Item = Result<T, E>> + Unpin,
        T: 'static,
        E: 'static + From<tokio::sync::broadcast::error::RecvError>,
    {
        let cancel_rx = match &self.cancel_sender {
            Some(sender) => sender.subscribe(),
            None => {
                let (_, rx) = tokio::sync::broadcast::channel::<()>(1);
                rx
            }
        };

        futures::stream::unfold(
            (stream, cancel_rx),
            |(mut stream, mut cancel_rx)| Box::pin(async move {
                tokio::select! {
                    // 尝试从流中获取下一个数据块
                    item = stream.next() => {
                        if let Some(item) = item {
                            Some((item, (stream, cancel_rx)))
                        } else {
                            None
                        }
                    },
                    // 如果收到取消信号，则中断流
                    _ = cancel_rx.recv() => {
                        debug!("Stream cancelled via cancel channel");
                        None
                    },
                }
            }),
        )
    }
}
