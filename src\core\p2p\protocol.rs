use async_trait::async_trait;
use anyhow::Result;
use uuid::Uuid;

/// P2P协议类型
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum ProtocolType {
    /// BitTorrent协议
    BitTorrent,
    /// 自定义P2P协议
    CustomP2P,
    /// 其他协议
    Other(u8),
}

/// 下载统计信息
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct DownloadStats {
    /// 已下载字节数
    pub downloaded: u64,
    /// 已上传字节数
    pub uploaded: u64,
    /// 下载速度
    pub download_speed: u64,
    /// 上传速度
    pub upload_speed: u64,
    /// 已连接的对等点数量
    pub connected_peers: usize,
    /// 总对等点数量
    pub total_peers: usize,
    /// 已下载的分片数量
    pub pieces_downloaded: u32,
    /// 总分片数量
    pub pieces_total: u32,
    /// 下载进度
    pub progress: f64,
    /// 预计剩余时间
    pub estimated_time: Option<std::time::Duration>,
}

/// 下载状态
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum DownloadStatus {
    /// 等待中
    Pending,
    /// 已初始化
    Initialized,
    /// 下载中
    Downloading,
    /// 已暂停
    Paused,
    /// 已完成
    Completed,
    /// 已失败
    Failed,
    /// 已取消
    Cancelled,
}

/// P2P协议接口
/// 定义P2P协议的基本操作
#[async_trait]
pub trait Protocol: Send + Sync {
    /// 获取协议类型
    fn protocol_type(&self) -> ProtocolType;
    
    /// 初始化协议
    async fn init(&mut self) -> Result<()>;
    
    /// 开始下载
    async fn download(&mut self) -> Result<()>;
    
    /// 暂停下载
    async fn pause(&mut self) -> Result<()>;
    
    /// 恢复下载
    async fn resume(&mut self) -> Result<()>;
    
    /// 取消下载
    async fn cancel(&mut self) -> Result<()>;
    
    /// 获取下载进度
    async fn progress(&self) -> Result<f64>;
    
    /// 获取下载速度
    async fn speed(&self) -> Result<u64>;
    
    /// 获取已下载数据大小
    async fn downloaded_size(&self) -> Result<u64>;
    
    /// 获取总数据大小
    async fn total_size(&self) -> Result<Option<u64>>;
    
    /// 获取下载状态
    fn status(&self) -> DownloadStatus;
    
    /// 获取下载统计信息
    async fn stats(&self) -> DownloadStats;
    
    /// 克隆为Box<dyn Protocol>
    fn clone_box(&self) -> Box<dyn Protocol>;
}

/// P2P协议工厂接口
/// 用于创建P2P协议实例
#[async_trait]
pub trait ProtocolFactory: Send + Sync {
    /// 创建P2P协议实例
    async fn create_protocol(&self, url: &str, output_path: &str, task_id: Uuid) -> Result<Box<dyn Protocol>>;
    
    /// 检查是否支持指定的URL
    fn supports_url(&self, url: &str) -> bool;
    
    /// 获取支持的协议类型
    fn supported_protocol_types(&self) -> Vec<ProtocolType>;
}
