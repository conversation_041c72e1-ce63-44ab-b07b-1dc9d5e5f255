use serde::{Serialize, Deserialize};
use serde_bencode::{to_bytes, from_bytes};
use std::collections::HashMap;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

/// 扩展消息类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ExtensionMessageType {
    /// 握手消息
    Handshake,
    /// PEX 消息
    PEX,
    /// 元数据交换消息
    Metadata,
    /// 未知扩展消息
    Unknown(u8),
}

impl From<u8> for ExtensionMessageType {
    fn from(id: u8) -> Self {
        match id {
            0 => ExtensionMessageType::Handshake,
            1 => ExtensionMessageType::PEX,
            2 => ExtensionMessageType::Metadata,
            _ => ExtensionMessageType::Unknown(id),
        }
    }
}

impl From<ExtensionMessageType> for u8 {
    fn from(message_type: ExtensionMessageType) -> Self {
        match message_type {
            ExtensionMessageType::Handshake => 0,
            ExtensionMessageType::PEX => 1,
            ExtensionMessageType::Metadata => 2,
            ExtensionMessageType::Unknown(id) => id,
        }
    }
}

/// 扩展消息接口
pub trait ExtensionMessage: Send + Sync {
    /// 获取消息类型
    fn message_type(&self) -> ExtensionMessageType;
    /// 编码消息为字节
    fn encode(&self) -> Result<Vec<u8>, BitTorrentError>;
    /// 从字节解码消息
    fn decode(data: &[u8]) -> Result<Self, BitTorrentError> where Self: Sized;
}

/// 扩展握手消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtensionHandshake {
    /// 支持的扩展映射 (扩展名 -> 消息ID)
    #[serde(rename = "m")]
    pub extensions: HashMap<String, u8>,
    
    /// 客户端版本
    #[serde(rename = "v")]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub version: Option<String>,
    
    /// 元数据大小 (用于ut_metadata扩展)
    #[serde(rename = "metadata_size")]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata_size: Option<u64>,
    
    /// 是否支持上传 (用于ut_metadata扩展)
    #[serde(rename = "upload_only")]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub upload_only: Option<bool>,
    
    /// 是否支持请求种子 (用于ut_metadata扩展)
    #[serde(rename = "reqq")]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub request_queue_size: Option<u32>,
}

impl ExtensionHandshake {
    /// 创建新的扩展握手消息
    pub fn new() -> Self {
        Self {
            extensions: HashMap::new(),
            version: Some(format!("Tonitru/0.1.0")),
            metadata_size: None,
            upload_only: None,
            request_queue_size: None,
        }
    }
    
    /// 添加扩展支持
    pub fn add_extension(&mut self, name: &str, id: u8) {
        self.extensions.insert(name.to_string(), id);
    }
    
    /// 设置元数据大小
    pub fn set_metadata_size(&mut self, size: u64) {
        self.metadata_size = Some(size);
    }
    
    /// 设置是否仅上传
    pub fn set_upload_only(&mut self, upload_only: bool) {
        self.upload_only = Some(upload_only);
    }
    
    /// 设置请求队列大小
    pub fn set_request_queue_size(&mut self, size: u32) {
        self.request_queue_size = Some(size);
    }
}

impl ExtensionMessage for ExtensionHandshake {
    fn message_type(&self) -> ExtensionMessageType {
        ExtensionMessageType::Handshake
    }
    
    fn encode(&self) -> Result<Vec<u8>, BitTorrentError> {
        match to_bytes(self) {
            Ok(data) => Ok(data),
            Err(e) => Err(BitTorrentError::EncodeError(format!("Failed to encode extension handshake: {}", e))),
        }
    }
    
    fn decode(data: &[u8]) -> Result<Self, BitTorrentError> {
        match from_bytes(data) {
            Ok(handshake) => Ok(handshake),
            Err(e) => Err(BitTorrentError::DecodeError(format!("Failed to decode extension handshake: {}", e))),
        }
    }
}
