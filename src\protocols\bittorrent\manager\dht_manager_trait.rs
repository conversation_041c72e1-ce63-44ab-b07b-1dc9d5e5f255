use async_trait::async_trait;
use std::net::SocketAddr;
use crate::protocols::bittorrent::utils::error::BitTorrentError;

/// DHT管理器异步trait，定义DHT相关异步操作接口
#[async_trait]
pub trait DHTManagerTrait: Send + Sync {
    /// 初始化DHT服务
    async fn init(&mut self) -> Result<(), BitTorrentError>;
    /// 启动DHT服务
    async fn start(&mut self) -> Result<(), BitTorrentError>;
    /// 停止DHT服务
    async fn stop(&mut self) -> Result<(), BitTorrentError>;
    /// 获取指定info_hash的peers
    async fn get_peers(&self, info_hash: [u8; 20]) -> Result<Vec<SocketAddr>, BitTorrentError>;
    /// 宣布peer
    async fn announce_peer(&self, info_hash: [u8; 20], port: u16) -> Result<(), BitTorrentError>;
    // 可扩展其它DHT相关异步接口
}
