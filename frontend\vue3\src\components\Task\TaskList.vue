<script setup lang="ts">
import { useRoute } from 'vue-router';
import { onMounted, ref } from 'vue';
import {
  Delete,
  Loading,
  Refresh,
  Select,
  VideoPause,
  VideoPlay,
  WarningFilled,
  InfoFilled,
  Edit
} from '@element-plus/icons-vue';

// 导入自定义composables和服务
import { useTaskList } from '../../composables/useTaskList';
import { useTaskFormat } from '../../composables/useTaskFormat';
import type { TaskStatus, Task } from '../../stores/task';

// 获取路由信息
const route = useRoute();

// 使用任务列表相关功能
const {
  taskList,
  loading,
  taskStatus,
  selectedTasks,
  fetchTaskList,
  pauseTask,
  resumeTask,
  removeTask,
  batchPause,
  batchResume,
  batchRemove,
  showErrorDetails,
  tableRowClassName,
  updateTask,
  handleSelectionChange
} = useTaskList();

// 使用任务格式化相关功能
const {
  formatSize,
  calculatePercentage,
  formatProgress,
  formatStatus
} = useTaskFormat();

// 编辑任务对话框
const editDialogVisible = ref(false);
const currentEditTask = ref<Partial<Task>>({});

// 打开编辑任务对话框
const openEditDialog = (task: Task) => {
  currentEditTask.value = { ...task };
  editDialogVisible.value = true;
};

// 提交编辑任务
const submitEditTask = async () => {
  if (currentEditTask.value.id) {
    await updateTask(currentEditTask.value.id, currentEditTask.value);
    editDialogVisible.value = false;
  }
};

// 组件挂载时，如果路由参数中有状态，则设置当前状态
onMounted(() => {
  if (route.params.status) {
    taskStatus.value = route.params.status as TaskStatus;
  }
});
</script>

<template>
  <div class="task-list-container">
    <div class="task-toolbar">
      <div class="batch-actions">
        <el-button-group>
          <el-button @click="batchPause" :disabled="selectedTasks.length === 0">
            <el-icon><VideoPause /></el-icon> 暂停
          </el-button>
          <el-button @click="batchResume" :disabled="selectedTasks.length === 0">
            <el-icon><VideoPlay /></el-icon> 恢复
          </el-button>
          <el-button @click="batchRemove" :disabled="selectedTasks.length === 0">
            <el-icon><Delete /></el-icon> 删除
          </el-button>
        </el-button-group>
      </div>
      <div class="refresh-action">
        <el-button @click="fetchTaskList">
          <el-icon><Refresh /></el-icon> 刷新
        </el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="taskList"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      :row-class-name="tableRowClassName"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column label="名称" min-width="200">
        <template #default="{ row }">
          <div class="task-name">
            <el-icon v-if="row.status === 'active'"><Loading /></el-icon>
            <el-icon v-else-if="row.status === 'complete'"><Select /></el-icon>
            <el-icon v-else-if="row.status === 'error'"><WarningFilled /></el-icon>
            <el-icon v-else-if="row.status === 'paused'"><VideoPause /></el-icon>
            <el-icon v-else><InfoFilled /></el-icon>
            <span>{{ row.fileName || row.id }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="大小" width="120">
        <template #default="{ row }">
          {{ formatSize(Number(row.totalLength)) }}
        </template>
      </el-table-column>

      <el-table-column label="进度" width="120">
        <template #default="{ row }">
          <div class="progress-cell">
            <el-progress
              :percentage="calculatePercentage(row)"
              :format="() => formatProgress(row)"
              :status="row.status === 'error' ? 'exception' : ''"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="120">
        <template #default="{ row }">
          <div class="status-cell">
            {{ formatStatus(row.status) }}
            <el-tooltip
              v-if="row.status === 'error' && row.errorMessage"
              content="点击查看错误详情"
              placement="top"
            >
              <el-icon class="error-icon" @click="showErrorDetails(row.errorMessage)">
                <WarningFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="180">
        <template #default="{ row }">
          <el-button-group>
            <el-button
              v-if="row.status === 'active' || row.status === 'waiting'"
              @click="pauseTask(row.id)"
              size="small"
            >
              <el-icon><VideoPause /></el-icon>
            </el-button>
            <el-button
              v-if="row.status === 'paused' || row.status === 'waiting'"
              @click="resumeTask(row.id)"
              size="small"
            >
              <el-icon><VideoPlay /></el-icon>
            </el-button>
            <el-button
              @click="openEditDialog(row)"
              size="small"
              type="primary"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button
              @click="removeTask(row.id)"
              size="small"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 编辑任务对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑任务"
      width="500px"
    >
      <el-form :model="currentEditTask" label-width="100px">
        <el-form-item label="文件名">
          <el-input v-model="currentEditTask.fileName" />
        </el-form-item>
        <el-form-item label="保存目录">
          <el-input v-model="currentEditTask.dir" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditTask">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.task-list-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.task-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.task-name {
  display: flex;
  align-items: center;
}

.task-name .el-icon {
  margin-right: 8px;
}

.progress-cell {
  padding: 5px 0;
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 5px;
}

.error-icon {
  color: var(--el-color-danger);
  cursor: pointer;
}

:deep(.error-row) {
  --el-table-tr-bg-color: var(--el-color-danger-light-9);
}

:deep(.success-row) {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>
