use anyhow::Result;
use async_trait::async_trait;
use tracing::{info, warn};
use std::any::Any;

use crate::core::interfaces::{Downloader, ProtocolType, DownloadStatus, DownloadProgress, DownloadOptions};
use crate::core::error::{CoreResult, CoreError};

use super::core::BitTorrentProtocol;



#[async_trait]
impl Downloader for BitTorrentProtocol {
    fn protocol_type(&self) -> ProtocolType {
        ProtocolType::BitTorrent
    }
    
    fn supports_url(&self, url: &str) -> bool {
        url.starts_with("magnet:") || url.ends_with(".torrent")
    }
    
    async fn init(&mut self, url: &str, output_path: &str, _options: &DownloadOptions) -> CoreResult<()> {
        // 保存URL和输出路径
        self.url = url.to_string();
        self.output_path = output_path.to_string();
        if self.initialized {
            return Ok(());
        }

        // Parse the torrent file or magnet link
        self.parse_torrent().await?;

        // Initialize the tracker manager
        self.init_tracker().await?;

        // Initialize the DHT manager
        self.init_dht().await?;

        // Initialize the peer manager
        self.init_peer_manager()?;

        // Initialize extensions
        self.init_extensions().await?;

        // Initialize WebSeed support
        self.init_webseed().await?;

        // Announce to tracker and get peers
        match self.announce_to_tracker("started").await {
            Ok(tracker_response) => {
                // Connect to peers from tracker
                if let Some(peer_manager) = &mut self.peer_manager {
                    if let Some(torrent_info) = &self.torrent_info {
                        peer_manager.add_peers_from_tracker(&tracker_response, &torrent_info.info_hash, torrent_info.pieces.len() as u32).await?;
                    }
                }

                // 打印Tracker统计信息
                if let Some(tracker_manager) = &self.tracker_manager {
                    info!("Connected to {} active trackers out of {} total trackers",
                        tracker_manager.active_tracker_count(),
                        tracker_manager.total_tracker_count());
                }
            },
            Err(e) => {
                warn!("Failed to announce to tracker: {}", e);
                // 继续执行，因为我们可以通过DHT获取对等点
            }
        }

        // 使用DHT查找对等点
        self.find_peers_via_dht().await?;

        self.initialized = true;
        self.status = DownloadStatus::Initializing;

        Ok(())
    }
    
    async fn start(&mut self) -> CoreResult<()> {
        if !self.initialized {
            // 克隆字段以避免借用冲突
            let url = self.url.clone();
            let output_path = self.output_path.clone();
            let _options = DownloadOptions::default();
            self.init(&url, &output_path, &_options).await?;
        }

        self.status = DownloadStatus::Downloading;

        // 直接启动下载循环
        // 克隆字段以避免借用冲突
        let url = self.url.clone();
        let output_path = self.output_path.clone();
        let _options = DownloadOptions::default();
        self.download_loop(&url, &output_path, &_options).await?;

        Ok(())
    }
    
    async fn pause(&mut self) -> CoreResult<()> {
        self.status = DownloadStatus::Paused;
        Ok(())
    }
    
    async fn resume(&mut self) -> CoreResult<()> {
        self.status = DownloadStatus::Downloading;
        Ok(())
    }
    
    async fn cancel(&mut self) -> CoreResult<()> {
        // Announce to tracker that we're stopping
        if self.initialized {
            let _ = self.announce_to_tracker("stopped").await;
        }

        self.status = DownloadStatus::Cancelled;

        Ok(())
    }
    
    async fn progress(&self) -> CoreResult<DownloadProgress> {
        let progress = self.stats_manager.progress();
        let total_size = if let Some(torrent_info) = &self.torrent_info {
            Some(torrent_info.total_size)
        } else {
            None
        };
        
        Ok(DownloadProgress {
            progress_percentage: progress,
            downloaded_size: self.downloaded,
            total_size,
            speed: 0, // 这里需要从stats_manager获取速度
            eta: None, // 这里需要计算预计完成时间
        })
    }
    
    async fn status(&self) -> CoreResult<DownloadStatus> {
        Ok(self.status)
    }
    
    fn id(&self) -> uuid::Uuid {
        self.task_id
    }
    
    fn url(&self) -> &str {
        &self.url
    }
    
    fn output_path(&self) -> &str {
        &self.output_path
    }
    
    fn clone_box(&self) -> Box<dyn Downloader> {
        Box::new(Self {
            task_id: self.task_id,
            url: self.url.clone(),
            output_path: self.output_path.clone(),
            settings: self.settings.clone(),
            config_manager: self.config_manager.clone(),
            torrent_info: self.torrent_info.clone(),
            tracker_manager: self.tracker_manager.clone(),
            dht_manager: self.dht_manager.clone(),
            peer_manager: self.peer_manager.clone(),
            peer_connection_manager: self.peer_connection_manager.clone(),
            piece_manager: self.piece_manager.clone(),
            block_manager: self.block_manager.clone(),
            stats_manager: self.stats_manager.clone(),
            webseed_manager: self.webseed_manager.clone(),
            webseed_enabled: self.webseed_enabled,
            upload_enabled: self.upload_enabled,
            bandwidth_scheduler: self.bandwidth_scheduler.clone(),
            resume_manager: self.resume_manager.clone(),
            status: self.status,
            peer_id: self.peer_id.clone(),
            initialized: self.initialized,
            downloaded: self.downloaded,
            uploaded: self.uploaded,
            needs_metadata: self.needs_metadata,
        })
    }
    
    async fn speed(&self) -> CoreResult<u64> {
        Ok(self.stats_manager.download_speed())
    }
    
    async fn get_total_size(&self) -> CoreResult<Option<u64>> {
        if let Some(torrent_info) = &self.torrent_info {
            Ok(Some(torrent_info.total_size))
        } else {
            Ok(None)
        }
    }
    
    async fn get_downloaded_size(&self) -> CoreResult<u64> {
        Ok(self.downloaded)
    }
    
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
    
    async fn set_download_limit(&mut self, limit: Option<u64>) -> CoreResult<()> {
        // 实现下载限速
        if let Some(peer_manager) = &mut self.peer_manager {
            // 对每个对等点设置下载速率限制
            let peers = peer_manager.get_peers();
            for (_, peer_arc) in peers {
                if let Ok(mut peer) = peer_arc.try_lock() {
                    if let Some(limit_value) = limit {
                        peer.connection.common.set_download_rate_limit(limit_value);
                    } else {
                        peer.connection.common.set_download_rate_limit(0); // 0 表示无限制
                    }
                }
            }
        }
        Ok(())
    }
    
    async fn set_upload_limit(&mut self, limit: Option<u64>) -> CoreResult<()> {
        // 实现上传限速
        if let Some(peer_manager) = &mut self.peer_manager {
            // 对每个对等点设置上传速率限制
            let peers = peer_manager.get_peers();
            for (_, peer_arc) in peers {
                if let Ok(mut peer) = peer_arc.try_lock() {
                    peer.upload_manager.set_upload_rate_limit(limit);
                }
            }
        }
        Ok(())
    }
}
