use anyhow::{Result, anyhow};
use async_trait::async_trait;
use std::path::Path;
use tracing::debug;
use url::Url;
use reqwest::redirect::Policy;
use reqwest::Client;
use std::time::Duration;

use crate::config::constants::{
    PROTOCOL_HTTP, PROTOCOL_HTTPS, PROTOCOL_BT, PROTOCOL_MAGNET, PROTOCOL_P2P, PROTOCOL_R2,
    TORRENT_EXTENSION, CONNECTION_TIMEOUT,
};

#[cfg(test)]
mod tests {
    use super::*;
    use httpmock::prelude::*;
    use httpmock::Method::GET;

    #[tokio::test]
    async fn test_redirect_count() {
        // 创建一个模拟服务器
        let server = MockServer::start();

        // 创建一个链接分析器
        let analyzer = LinkAnalyzerImpl::new();

        // 测试没有重定向的情况
        {
            // 创建一个模拟端点
            let mock = server.mock(|when, then| {
                when.method(GET).path("/no_redirect");
                then.status(200).body("No redirect");
            });

            // 获取URL
            let url = format!("{}/no_redirect", server.base_url());

            // 跟踪重定向
            let result = analyzer.follow_redirects(&url).await.unwrap();

            // 验证结果
            assert_eq!(result.redirect_count, 0);
            assert_eq!(result.final_url, url);
            assert!(!result.is_redirect_loop);

            // 验证模拟端点被调用
            mock.assert();
        }

        // 测试单次重定向的情况
        {
            // 创建模拟端点
            let mock1 = server.mock(|when, then| {
                when.method(GET).path("/single_redirect");
                then.status(302)
                    .header("Location", format!("{}/destination", server.base_url()));
            });

            let mock2 = server.mock(|when, then| {
                when.method(GET).path("/destination");
                then.status(200).body("Destination");
            });

            // 获取URL
            let url = format!("{}/single_redirect", server.base_url());

            // 跟踪重定向
            let result = analyzer.follow_redirects(&url).await.unwrap();

            // 验证结果
            assert_eq!(result.redirect_count, 1);
            assert_eq!(result.final_url, format!("{}/destination", server.base_url()));
            assert!(!result.is_redirect_loop);

            // 验证模拟端点被调用
            mock1.assert();
            mock2.assert();
        }

        // 测试多次重定向的情况
        {
            // 创建模拟端点
            let mock1 = server.mock(|when, then| {
                when.method(GET).path("/multi_redirect_1");
                then.status(302)
                    .header("Location", format!("{}/multi_redirect_2", server.base_url()));
            });

            let mock2 = server.mock(|when, then| {
                when.method(GET).path("/multi_redirect_2");
                then.status(302)
                    .header("Location", format!("{}/multi_redirect_3", server.base_url()));
            });

            let mock3 = server.mock(|when, then| {
                when.method(GET).path("/multi_redirect_3");
                then.status(200).body("Final destination");
            });

            // 获取URL
            let url = format!("{}/multi_redirect_1", server.base_url());

            // 跟踪重定向
            let result = analyzer.follow_redirects(&url).await.unwrap();

            // 验证结果
            assert_eq!(result.redirect_count, 2);
            assert_eq!(result.final_url, format!("{}/multi_redirect_3", server.base_url()));
            assert!(!result.is_redirect_loop);

            // 验证模拟端点被调用
            mock1.assert();
            mock2.assert();
            mock3.assert();
        }

        // 测试循环重定向的情况
        {
            // 创建模拟端点
            let mock1 = server.mock(|when, then| {
                when.method(GET).path("/loop_redirect_1");
                then.status(302)
                    .header("Location", format!("{}/loop_redirect_2", server.base_url()));
            });

            let mock2 = server.mock(|when, then| {
                when.method(GET).path("/loop_redirect_2");
                then.status(302)
                    .header("Location", format!("{}/loop_redirect_1", server.base_url()));
            });

            // 获取URL
            let url = format!("{}/loop_redirect_1", server.base_url());

            // 跟踪重定向
            let result = analyzer.follow_redirects(&url).await.unwrap();

            // 验证结果
            assert!(result.redirect_count >= 1);
            assert!(result.is_redirect_loop);
            
            // 验证最终URL是循环中的一个URL
            let expected_urls = [
                format!("{}/loop_redirect_1", server.base_url()),
                format!("{}/loop_redirect_2", server.base_url())
            ];
            assert!(expected_urls.contains(&result.final_url));
            
            // 验证循环URL列表
            assert!(result.loop_urls.is_some());
            if let Some(loop_urls) = result.loop_urls {
                assert!(!loop_urls.is_empty());
                // 验证循环中的所有URL都在预期列表中
                for url in loop_urls {
                    assert!(expected_urls.contains(&url));
                }
            }

            // 验证模拟端点被调用
            mock1.assert();
            mock2.assert();
        }
    }
}

/// 重定向结果
#[derive(Debug, Clone)]
pub struct RedirectResult {
    /// 最终URL
    pub final_url: String,
    /// 重定向次数
    #[allow(dead_code)]
    pub redirect_count: usize,
    /// 是否是循环重定向
    pub is_redirect_loop: bool,
    /// 文件名
    pub file_name: Option<String>,
    /// 循环中的URL列表（如果存在循环）
    #[allow(dead_code)]
    pub loop_urls: Option<Vec<String>>,
}

/// 手动跟踪重定向的结果
#[derive(Debug, Clone)]
pub struct TrackRedirectResult {
    /// 重定向次数
    pub redirect_count: usize,
    /// 是否检测到循环
    pub is_loop: bool,
    /// 最终URL
    pub final_url: String,
    /// 循环中的URL列表（如果存在循环）
    pub loop_urls: Option<Vec<String>>,
}

/// Link analyzer interface
#[async_trait]
pub trait LinkAnalyzer: Send + Sync {
    /// Analyze a URL and determine its protocol
    async fn analyze(&self, url: &str) -> Result<String>;

    /// Extract file name from a URL
    fn extract_file_name(&self, url: &str) -> Result<String>;

    /// Follow redirects and get the final URL
    async fn follow_redirects(&self, url: &str) -> Result<RedirectResult>;
}

/// Link analyzer implementation
#[derive(Clone)]
pub struct LinkAnalyzerImpl {
    /// HTTP客户端，用于处理重定向
    http_client: Client,
}

impl LinkAnalyzerImpl {
    /// Create a new link analyzer
    pub fn new() -> Self {
        // 创建一个HTTP客户端，最多允许10次重定向
        let http_client = Client::builder()
            .timeout(Duration::from_secs(CONNECTION_TIMEOUT))
            .redirect(Policy::limited(10))
            .build().expect("Failed to build reqwest client");

        Self {
            http_client,
        }
    }

    /// 检查URL是否是特殊协议
    fn check_special_protocols(&self, url: &str) -> Option<String> {
        if url.starts_with(PROTOCOL_BT) {
            return Some(PROTOCOL_BT.to_string());
        }
        if url.starts_with(PROTOCOL_MAGNET) {
            return Some(PROTOCOL_MAGNET.to_string());
        }
        if url.starts_with(PROTOCOL_P2P) {
            return Some(PROTOCOL_P2P.to_string());
        }
        if url.starts_with(PROTOCOL_R2) {
            return Some(PROTOCOL_R2.to_string());
        }
        if url.ends_with(TORRENT_EXTENSION) {
            return Some(PROTOCOL_BT.to_string());
        }
        None
    }

    /// 计算重定向次数
    async fn calculate_redirect_count(
        &self,
        original_url: &str,
        final_url: &str,
        response: &reqwest::Response,
    ) -> usize {
        // 如果URL没有变化，则没有重定向
        if original_url == final_url {
            return 0;
        }

        // 尝试从响应头中获取重定向次数
        // 检查是否有 X-Redirects-Count 头（一些服务器会设置这个头）
        if let Some(count) = response.headers()
            .get("x-redirects-count")
            .and_then(|h| h.to_str().ok())
            .and_then(|s| s.parse::<usize>().ok()) {
            return count;
        }

        // 尝试使用更准确的方法手动跟踪重定向
        if let Ok(track_result) = self.track_redirects(original_url).await {
            return track_result.redirect_count;
        }

        // 如果手动跟踪失败，使用启发式方法
        self.estimate_redirect_count(original_url, final_url)
    }

    /// 手动跟踪重定向并计算次数
    async fn track_redirects(&self, url: &str) -> Result<TrackRedirectResult> {
    // 创建一个不自动跟踪重定向的客户端
    let client = reqwest::Client::builder()
    .timeout(Duration::from_secs(CONNECTION_TIMEOUT))
    .redirect(reqwest::redirect::Policy::none())
    .build()?;
    
    let mut current_url = url.to_string();
    let mut redirect_count = 0;
    let mut visited_urls = std::collections::HashMap::new();
    let mut loop_detected = false;
    let mut loop_start_index = 0;
    
    // 最多跟踪10次重定向
    for i in 0..10 {
    // 添加到已访问URL集合，记录访问顺序
    visited_urls.insert(current_url.clone(), i);
    
    // 发送HEAD请求
    let response = match client.head(&current_url).send().await {
    Ok(resp) => resp,
    Err(_) => {
    // 如果HEAD请求失败，尝试GET请求
    match client.get(&current_url).send().await {
    Ok(resp) => resp,
    Err(_) => break, // 如果GET也失败，停止跟踪
    }
    }
    };
    
    // 检查状态码
    let status = response.status();
    if !(status.is_redirection() && (status.as_u16() == 301 || status.as_u16() == 302 ||
    status.as_u16() == 303 || status.as_u16() == 307 ||
    status.as_u16() == 308)) {
    // 如果不是重定向状态码，停止跟踪
    break;
    }
    
    // 获取Location头
    let location = match response.headers().get(reqwest::header::LOCATION) {
    Some(loc) => {
    match loc.to_str() {
    Ok(s) => s,
    Err(_) => break, // 如果无法解析Location头，停止跟踪
    }
    },
    None => break, // 如果没有Location头，停止跟踪
    };
    
    // 解析新的URL
    let new_url = if location.starts_with("http://") || location.starts_with("https://") {
    // 绝对URL
    location.to_string()
    } else {
    // 相对URL
    let base_url = Url::parse(&current_url)?;
    match base_url.join(location) {
    Ok(url) => url.to_string(),
    Err(_) => break, // 如果无法解析相对URL，停止跟踪
    }
    };
    
    // 检查是否已经访问过这个URL（循环重定向）
    if let Some(prev_index) = visited_urls.get(&new_url) {
    // 发现循环重定向
    loop_detected = true;
    loop_start_index = *prev_index;
    redirect_count += 1;
    break;
    }
    
    // 更新当前URL和重定向计数
    current_url = new_url;
    redirect_count += 1;
    }
    
    // 如果检测到循环，收集循环中的URL
    let loop_urls = if loop_detected {
    let mut urls = Vec::new();
    let urls_vec: Vec<(String, usize)> = visited_urls.into_iter().collect();
    
    // 按访问顺序排序
    let mut sorted_urls = urls_vec.clone();
    sorted_urls.sort_by_key(|(_, idx)| *idx);
    
    // 收集循环中的URL
    for (url, idx) in sorted_urls {
    if idx >= loop_start_index {
    urls.push(url);
    }
    }
    
    Some(urls)
    } else {
    None
    };
    
    Ok(TrackRedirectResult {
    redirect_count,
    is_loop: loop_detected,
    final_url: current_url,
    loop_urls,
    })
    }

    /// 使用启发式方法估计重定向次数
    fn estimate_redirect_count(&self, original_url: &str, final_url: &str) -> usize {
        // 解析原始URL和最终URL
        let original_parsed = match Url::parse(original_url) {
            Ok(url) => url,
            Err(_) => return 1, // 如果无法解析原始URL，假设有一次重定向
        };

        let final_parsed = match Url::parse(final_url) {
            Ok(url) => url,
            Err(_) => return 1, // 如果无法解析最终URL，假设有一次重定向
        };

        // 检查是否有协议变化（如 http -> https）
        let protocol_change = original_parsed.scheme() != final_parsed.scheme();

        // 检查是否有主机变化（如 example.com -> www.example.com）
        let host_change = original_parsed.host_str() != final_parsed.host_str();

        // 检查是否有端口变化
        let port_change = original_parsed.port() != final_parsed.port();

        // 检查是否有路径变化
        let path_change = original_parsed.path() != final_parsed.path();

        // 检查是否有查询参数变化
        let query_change = original_parsed.query() != final_parsed.query();

        // 计算变化的数量
        let changes = [protocol_change, host_change, port_change, path_change, query_change]
            .iter()
            .filter(|&&change| change)
            .count();

        // 如果有多个变化，可能经过了多次重定向
        // 但我们不能确定具体次数，所以使用一个启发式方法
        if changes > 1 {
            // 如果有多个变化，假设每个变化对应一次重定向
            // 但最多不超过5次（这是一个合理的上限）
            changes.min(5)
        } else {
            // 如果只有一个变化，假设只有一次重定向
            1
        }
    }
}

#[async_trait]
impl LinkAnalyzer for LinkAnalyzerImpl {
    async fn analyze(&self, url: &str) -> Result<String> {
        // 首先检查特殊协议
        if let Some(protocol) = self.check_special_protocols(url) {
            return Ok(protocol);
        }

        // 解析URL
        let parsed_url = Url::parse(url)?;

        // 检查协议
        match parsed_url.scheme() {
            "http" => Ok(PROTOCOL_HTTP.to_string()),
            "https" => Ok(PROTOCOL_HTTPS.to_string()),
            "r2" => Ok(PROTOCOL_R2.to_string()),
            "bt" => Ok(PROTOCOL_BT.to_string()),
            "p2p" => Ok(PROTOCOL_P2P.to_string()),
            _ => Err(anyhow!("Unsupported protocol: {}", parsed_url.scheme())),
        }
    }

    fn extract_file_name(&self, url: &str) -> Result<String> {
        // 解析URL
        let parsed_url = Url::parse(url)?;

        // 获取路径
        let path = parsed_url.path();

        // 提取文件名
        let file_name = Path::new(path)
            .file_name()
            .and_then(|name| name.to_str())
            .ok_or_else(|| anyhow!("Could not extract file name from URL: {}", url))?;

        Ok(file_name.to_string())
    }

    async fn follow_redirects(&self, url: &str) -> Result<RedirectResult> {
        // 首先检查特殊协议，如果是特殊协议，不需要跟踪重定向
        if self.check_special_protocols(url).is_some() {
            return Ok(RedirectResult {
                final_url: url.to_string(),
                redirect_count: 0,
                is_redirect_loop: false,
                file_name: self.extract_file_name(url).ok(),
                loop_urls: None,
            });
        }

        // 使用改进的track_redirects方法手动跟踪重定向
        let track_result = self.track_redirects(url).await;
        
        // 如果手动跟踪成功
        if let Ok(track_result) = track_result {
            // 提取文件名
            let file_name = if track_result.final_url != url {
                // 如果有重定向，从最终URL提取文件名
                self.extract_file_name(&track_result.final_url).ok()
            } else {
                // 否则从原始URL提取文件名
                self.extract_file_name(url).ok()
            };
            
            return Ok(RedirectResult {
                final_url: track_result.final_url,
                redirect_count: track_result.redirect_count,
                is_redirect_loop: track_result.is_loop,
                file_name,
                loop_urls: track_result.loop_urls,
            });
        }
        
        // 如果手动跟踪失败，回退到原来的方法
        // 发送HEAD请求，跟踪重定向
        let response = match self.http_client.head(url).send().await {
            Ok(resp) => resp,
            Err(e) => {
                // 如果HEAD请求失败，尝试GET请求
                debug!("HEAD request failed: {}, trying GET", e);
                match self.http_client.get(url).send().await {
                    Ok(resp) => resp,
                    Err(e) => return Err(anyhow!("Failed to follow redirects: {}", e)),
                }
            }
        };

        // 获取最终URL
        let final_url = response.url().to_string();

        // 计算重定向次数
        // 计算重定向次数
        let redirect_count = self.calculate_redirect_count(url, &final_url, &response).await;
        
        // 检查是否是循环重定向
        // 使用更保守的判断标准：如果重定向次数达到或超过10次，认为可能是循环重定向
        let is_redirect_loop = redirect_count >= 10;

        // 提取文件名
        let file_name = if final_url != url {
            // 如果有重定向，从最终URL提取文件名
            self.extract_file_name(&final_url).ok()
        } else {
            // 否则从原始URL提取文件名
            self.extract_file_name(url).ok()
        };

        // 如果从URL无法提取文件名，尝试从Content-Disposition头提取
        let file_name = if file_name.is_none() {
            response
                .headers()
                .get(reqwest::header::CONTENT_DISPOSITION)
                .and_then(|h| h.to_str().ok())
                .and_then(|s| {
                    // 解析Content-Disposition头
                    if let Some(pos) = s.find("filename=") {
                        let filename_part = &s[pos + 9..];
                        if let Some(end) = filename_part.find(';') {
                            Some(filename_part[..end].trim_matches('"').to_string())
                        } else {
                            Some(filename_part.trim_matches('"').to_string())
                        }
                    } else {
                        None
                    }
                })
                .or(file_name)
        } else {
            file_name
        };

        Ok(RedirectResult {
            final_url,
            redirect_count,
            is_redirect_loop,
            file_name,
            loop_urls: None, // 在这种情况下，我们没有循环URL的信息
        })
    }
}

