use std::net::{SocketAddr, IpAddr};
use std::path::Path;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use tokio::net::UdpSocket;
use tokio::sync::{Mutex, RwLock, oneshot};
use tokio::time::timeout;
use tracing::{debug, warn, info, error};

use crate::core::p2p::dht::{DHT, DHTEvent, DHTEventListener, DHTStatus};

use super::node::{DHTNode, NodeId};
use super::routing::RoutingTable;
use super::message::{DHTMessage, TokenManager, MessageCodec, DHTMessageType, DHTResponse};
use super::config::DHTClientConfig;
use super::query::{DHTQuery, QueryManager};
use super::event::DHTEventDispatcher;
use super::bootstrap::DHTBootstrapper;
use super::peer_finder::DHTPeerFinder;
use super::message_handler::DHTMessageHandler;





/// DHT客户端
pub struct DHTClient {
    /// 配置
    config: DHTClientConfig,
    /// 路由表
    routing_table: Arc<RwLock<RoutingTable>>,
    /// UDP套接字
    socket: Arc<UdpSocket>,
    /// 本地节点ID
    node_id: NodeId,
    /// 查询管理器
    query_manager: Arc<RwLock<QueryManager>>,
    /// 令牌管理器
    token_manager: Arc<Mutex<TokenManager>>,
    /// 事件分发器
    event_dispatcher: Arc<DHTEventDispatcher>,
    /// 消息处理器
    message_handler: Arc<DHTMessageHandler>,
    /// 引导器
    bootstrapper: Arc<RwLock<DHTBootstrapper>>,
    /// 对等点查找器
    peer_finder: Arc<DHTPeerFinder>,
    /// 是否已初始化
    initialized: bool,
    /// 是否正在运行
    running: bool,
    /// 启动时间
    start_time: Option<Instant>,
    /// 发现的对等点计数
    discovered_peers_count: Arc<RwLock<usize>>,
}

impl DHTClient {
    /// 创建新的DHT客户端
    pub async fn new(config: DHTClientConfig) -> Result<Self> {
        // 生成随机节点ID
        let node_id = NodeId::new_random();

        // 创建路由表
        let routing_table = Arc::new(RwLock::new(RoutingTable::new(
            node_id,
            config.routing_table_config.clone(),
        )));

        // 创建UDP套接字
        let socket = if config.enable_ipv6 {
            // 如果启用IPv6，首先尝试创建双栈套接字（同时支持IPv4和IPv6）
            match UdpSocket::bind(format!("[::]:{}", config.port)).await {
                Ok(socket) => {
                    info!("Created dual-stack (IPv4/IPv6) DHT socket on port {}", config.port);
                    Arc::new(socket)
                },
                Err(e) => {
                    warn!("Failed to bind dual-stack DHT socket to port {}: {}", config.port, e);

                    // 尝试使用随机端口
                    match UdpSocket::bind("[::]:0").await {
                        Ok(socket) => {
                            let port = socket.local_addr()?.port();
                            info!("Created dual-stack DHT socket on random port {}", port);
                            Arc::new(socket)
                        },
                        Err(e) => {
                            warn!("Failed to bind dual-stack DHT socket on random port: {}", e);

                            // 如果双栈失败，回退到IPv4
                            match UdpSocket::bind(format!("0.0.0.0:{}", config.port)).await {
                                Ok(socket) => {
                                    info!("Created IPv4-only DHT socket on port {}", config.port);
                                    Arc::new(socket)
                                },
                                Err(e) => {
                                    warn!("Failed to bind IPv4 DHT socket to port {}: {}", config.port, e);

                                    // 最后尝试IPv4随机端口
                                    let socket = UdpSocket::bind("0.0.0.0:0").await?;
                                    let port = socket.local_addr()?.port();
                                    info!("Created IPv4-only DHT socket on random port {}", port);
                                    Arc::new(socket)
                                }
                            }
                        }
                    }
                }
            }
        } else {
            // 如果不启用IPv6，只使用IPv4
            match UdpSocket::bind(format!("0.0.0.0:{}", config.port)).await {
                Ok(socket) => {
                    info!("Created IPv4-only DHT socket on port {}", config.port);
                    Arc::new(socket)
                },
                Err(e) => {
                    warn!("Failed to bind IPv4 DHT socket to port {}: {}", config.port, e);

                    // 尝试使用随机端口
                    let socket = UdpSocket::bind("0.0.0.0:0").await?;
                    let port = socket.local_addr()?.port();
                    info!("Created IPv4-only DHT socket on random port {}", port);
                    Arc::new(socket)
                }
            }
        };

        // 创建查询管理器
        let query_manager = Arc::new(RwLock::new(QueryManager::new(
            config.parallel_queries,
            config.query_timeout,
        )));

        // 创建令牌管理器
        let token_manager = Arc::new(Mutex::new(TokenManager::new()));

        // 创建事件分发器
        let event_dispatcher = Arc::new(DHTEventDispatcher::new());

        // 创建发现的对等点计数
        let discovered_peers_count = Arc::new(RwLock::new(0));

        // 创建消息处理器
        let message_handler = Arc::new(DHTMessageHandler::new(
            node_id,
            socket.clone(),
            routing_table.clone(),
            query_manager.clone(),
            token_manager.clone(),
            event_dispatcher.clone(),
            discovered_peers_count.clone(),
        ));

        // 创建引导器
        let bootstrapper = Arc::new(RwLock::new(DHTBootstrapper::with_config(
            node_id,
            socket.clone(),
            routing_table.clone(),
            query_manager.clone(),
            event_dispatcher.clone(),
            config.bootstrap_nodes.clone(),
            config.parallel_queries,
            config.query_timeout,
        )));

        // 创建对等点查找器
        let peer_finder = Arc::new(DHTPeerFinder::new(
            node_id,
            socket.clone(),
            routing_table.clone(),
            query_manager.clone(),
            token_manager.clone(),
            event_dispatcher.clone(),
            config.query_timeout,
            config.parallel_queries,
        ));

        Ok(Self {
            config,
            routing_table,
            socket,
            node_id,
            query_manager,
            token_manager,
            event_dispatcher,
            message_handler,
            bootstrapper,
            peer_finder,
            initialized: false,
            running: false,
            start_time: None,
            discovered_peers_count,
        })
    }

    /// 启动消息处理循环
    async fn start_message_handler(&self) -> Result<()> {
        let socket = self.socket.clone();
        let message_handler = self.message_handler.clone();

        tokio::spawn(async move {
            let mut buf = vec![0u8; 2048];

            loop {
                match socket.recv_from(&mut buf).await {
                    Ok((len, addr)) => {
                        let data = &buf[..len];

                        // 处理消息
                        if let Err(e) = message_handler.handle_message(data, addr).await {
                            debug!("Error handling DHT message: {}", e);
                        }
                    },
                    Err(e) => {
                        warn!("Error receiving DHT message: {}", e);
                        // 如果是致命错误，退出循环
                        if e.kind() == std::io::ErrorKind::BrokenPipe {
                            warn!("DHT socket broken pipe, exiting message handler");
                            break;
                        }
                    }
                }
            }
        });

        Ok(())
    }

    /// 启动查询超时清理任务
    async fn start_query_cleanup_task(&self) -> Result<()> {
        let query_manager = self.query_manager.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(1));

            loop {
                interval.tick().await;

                // 清理超时查询
                let mut manager = query_manager.write().await;
                let expired = manager.cleanup_expired_queries();

                if !expired.is_empty() {
                    debug!("Cleaned up {} expired DHT queries", expired.len());
                }
            }
        });

        Ok(())
    }



    /// 引导过程
    async fn bootstrap(&mut self) -> Result<()> {
        debug!("Starting DHT bootstrap process");

        // 使用引导器执行引导过程
        let mut bootstrapper = self.bootstrapper.write().await;
        bootstrapper.bootstrap().await?;

        debug!("DHT bootstrap completed");

        Ok(())
    }

    /// 发送DHT消息
    async fn send_message(&self, message: &DHTMessage, addr: &SocketAddr) -> Result<()> {
        let encoded = MessageCodec::encode(message)?;
        self.socket.send_to(&encoded, addr).await?;
        Ok(())
    }

    /// 获取节点ID
    pub fn node_id(&self) -> &NodeId {
        &self.node_id
    }

    /// 发送查询并等待响应
    pub async fn send_query(&self, addr: SocketAddr, query_type: DHTMessageType, query: DHTQuery) -> Result<DHTResponse> {
        // 创建事务ID
        let transaction_id = query.query_id.clone();

        // 创建响应通道
        let (tx, rx) = oneshot::channel();

        // 将查询添加到查询管理器
        {
            let mut query_manager = self.query_manager.write().await;
            query_manager.add_query_with_response_channel(query.clone(), tx)?;
        }

        // 创建消息
        let message = DHTMessage {
            message_type: query_type,
            transaction_id: transaction_id.clone(),
            sender_id: self.node_id,
            target_id: Some(query.target_id),
            nodes: None,
            peers: None,
            token: None,
            port: None,
            error_code: None,
            error_message: None,
        };

        // 发送消息
        self.send_message(&message, &addr).await?;

        // 等待响应，设置超时
        let query_timeout = {
            let query_manager = self.query_manager.read().await;
            query_manager.query_timeout
        };

        match timeout(Duration::from_secs(query_timeout), rx).await {
            Ok(result) => {
                match result {
                    Ok(response) => response,
                    Err(_) => Err(anyhow!("Response channel closed")),
                }
            },
            Err(_) => {
                // 超时，从查询管理器中移除查询
                let mut query_manager = self.query_manager.write().await;
                query_manager.remove_query(&transaction_id);
                Err(anyhow!("Query timeout after {} seconds", query_timeout))
            }
        }
    }

    /// 发送ping请求
    pub async fn ping(&self, addr: SocketAddr) -> Result<()> {
        // 创建事务ID
        let transaction_id: Vec<u8> = (0..4).map(|_| rand::random::<u8>()).collect();

        // 创建ping查询
        let query = DHTQuery::new(
            transaction_id,
            self.node_id,
            DHTMessageType::Ping
        );

        // 发送查询
        self.send_query(addr, DHTMessageType::Ping, query).await?;

        Ok(())
    }

    /// 获取引导节点列表
    pub fn get_bootstrap_nodes(&self) -> Vec<SocketAddr> {
        self.config.bootstrap_nodes.clone()
    }
    
    /// 获取当前配置
    pub fn get_config(&self) -> DHTClientConfig {
        self.config.clone()
    }
    
    /// 更新配置
    pub fn update_config(&mut self, config: DHTClientConfig) {
        self.config = config;
    }

    /// 发送find_node请求
    pub async fn find_node(&self, addr: SocketAddr, target: [u8; 20]) -> Result<Vec<DHTNode>> {
        // 创建事务ID
        let transaction_id: Vec<u8> = (0..4).map(|_| rand::random::<u8>()).collect();

        // 创建find_node查询
        let query = DHTQuery::new(
            transaction_id,
            NodeId::from_bytes(target),
            DHTMessageType::FindNode
        );

        // 发送查询
        let response = self.send_query(addr, DHTMessageType::FindNode, query).await?;

        // 解析响应中的节点
        match response {
            DHTResponse::FindNode { nodes, .. } => {
                Ok(nodes.unwrap_or_default())
            },
            _ => {
                debug!("Unexpected response type for find_node: {:?}", response);
                Ok(vec![])
            }
        }
    }

    /// 发送事件给所有监听器
    pub async fn send_event(&self, event: crate::core::p2p::dht::DHTEvent) -> Result<()> {
        // 将核心DHTEvent转换为内部DHTEvent
        let internal_event = match event {
            crate::core::p2p::dht::DHTEvent::NodeStateChanged { node_count, bootstrapped } => {
                super::event::DHTEvent::NodeStateChanged { node_count, bootstrapped }
            },
            crate::core::p2p::dht::DHTEvent::PeersFound { info_hash, peers } => {
                super::event::DHTEvent::PeersFound { info_hash, peers }
            },
            crate::core::p2p::dht::DHTEvent::Error { message } => {
                // 创建一个错误事件
                return Err(anyhow!("DHT error: {}", message));
            },
        };

        self.event_dispatcher.dispatch(internal_event).await
    }

    /// 保存路由表到文件
    pub async fn save_routing_table<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let routing_table = self.routing_table.read().await;

        // 保存路由表
        match routing_table.save_to_file(path.as_ref()) {
            Ok(_) => {
                info!("DHT routing table saved to {}", path.as_ref().display());
                Ok(())
            },
            Err(e) => {
                error!("Failed to save DHT routing table: {}", e);
                Err(anyhow!("Failed to save DHT routing table: {}", e))
            }
        }
    }

    /// 从文件加载路由表
    pub async fn load_routing_table<P: AsRef<Path>>(&mut self, path: P) -> Result<()> {
        // 检查文件是否存在
        if !path.as_ref().exists() {
            return Err(anyhow!("Routing table file does not exist: {}", path.as_ref().display()));
        }

        // 加载路由表
        match super::routing::RoutingTable::load_from_file(path.as_ref()) {
            Ok(table) => {
                // 更新路由表
                let mut routing_table = self.routing_table.write().await;
                *routing_table = table;

                // 获取节点数
                let node_count = routing_table.node_count();

                info!("DHT routing table loaded from {} with {} nodes", path.as_ref().display(), node_count);

                // 发送状态变更事件
                drop(routing_table); // 释放锁
                self.send_event(DHTEvent::NodeStateChanged {
                    node_count,
                    bootstrapped: true,
                }).await?;

                Ok(())
            },
            Err(e) => {
                error!("Failed to load DHT routing table: {}", e);
                Err(anyhow!("Failed to load DHT routing table: {}", e))
            }
        }
    }

    /// 定期保存路由表
    pub async fn start_periodic_save<P: AsRef<Path> + Send + Sync + 'static>(&self, path: P, interval_secs: u64) -> Result<()> {
        let routing_table = self.routing_table.clone();
        let path_buf = path.as_ref().to_path_buf();

        // 保存路径显示用于日志
        let path_display = format!("{}", path_buf.display());

        // 创建目录（如果不存在）
        if let Some(parent) = path_buf.parent() {
            if !parent.exists() {
                std::fs::create_dir_all(parent)?;
            }
        }

        // 启动定期保存任务
        tokio::spawn(async move {
            let interval = Duration::from_secs(interval_secs);

            loop {
                // 等待指定时间
                tokio::time::sleep(interval).await;

                // 保存路由表
                let table = routing_table.read().await;
                if let Err(e) = table.save_to_file(&path_buf) {
                    error!("Failed to save DHT routing table: {}", e);
                } else {
                    debug!("DHT routing table saved to {}", path_buf.display());
                }
            }
        });

        info!("Started periodic DHT routing table save every {} seconds to {}", interval_secs, path_display);

        Ok(())
    }
}

/// 事件监听器适配器，将外部监听器转换为内部监听器
struct EventListenerAdapter {
    listener: Arc<dyn DHTEventListener>,
}

#[async_trait::async_trait]
impl super::event::DHTEventListener for EventListenerAdapter {
    async fn on_event(&self, event: super::event::DHTEvent) -> Result<()> {
        // 将内部DHTEvent转换为外部DHTEvent
        let external_event = match event {
            super::event::DHTEvent::NodeStateChanged { node_count, bootstrapped } => {
                crate::core::p2p::dht::DHTEvent::NodeStateChanged { node_count, bootstrapped }
            },
            super::event::DHTEvent::PeersFound { info_hash, peers } => {
                crate::core::p2p::dht::DHTEvent::PeersFound { info_hash, peers }
            },
        };

        // 调用外部监听器
        self.listener.on_event(external_event).await
    }
}

#[async_trait::async_trait]
impl DHT for DHTClient {
    async fn init(&mut self) -> Result<()> {
        if self.initialized {
            return Ok(());
        }

        // 启动消息处理循环
        self.start_message_handler().await?;

        // 启动查询超时清理任务
        self.start_query_cleanup_task().await?;

        self.initialized = true;
        Ok(())
    }

    async fn start(&mut self) -> Result<()> {
        if !self.initialized {
            self.init().await?;
        }

        if self.running {
            return Ok(());
        }

        // 引导过程
        self.bootstrap().await?;

        self.running = true;
        self.start_time = Some(Instant::now());

        // 设置对等点查找器为运行状态
        let peer_finder = Arc::get_mut(&mut self.peer_finder)
            .ok_or_else(|| anyhow!("Failed to get mutable reference to peer finder"))?;
        peer_finder.set_running(true);

        // 获取路由表中的节点数
        let node_count = {
            let routing_table = self.routing_table.read().await;
            routing_table.node_count()
        };

        // 发送状态变更事件
        self.send_event(DHTEvent::NodeStateChanged {
            node_count,
            bootstrapped: true,
        }).await?;

        debug!("DHT client started with {} nodes in routing table", node_count);

        Ok(())
    }

    async fn stop(&mut self) -> Result<()> {
        self.running = false;

        // 设置对等点查找器为停止状态
        if let Some(peer_finder) = Arc::get_mut(&mut self.peer_finder) {
            peer_finder.set_running(false);
        }

        debug!("DHT client stopped");

        Ok(())
    }

    async fn get_peers(&self, info_hash: [u8; 20]) -> Result<Vec<SocketAddr>> {
        if !self.running {
            return Err(anyhow!("DHT client is not running"));
        }

        // 使用对等点查找器获取对等点
        self.peer_finder.get_peers(info_hash).await
    }

    async fn announce_peer(&self, info_hash: [u8; 20], port: u16) -> Result<()> {
        if !self.running {
            return Err(anyhow!("DHT client is not running"));
        }

        // 使用对等点查找器宣布对等点
        self.peer_finder.announce_peer(info_hash, port).await
    }

    async fn add_event_listener(&mut self, listener: Arc<dyn DHTEventListener>) -> Result<()> {
        // 创建一个适配器，将外部监听器转换为内部监听器
        let adapter = Arc::new(EventListenerAdapter {
            listener: listener.clone(),
        });

        self.event_dispatcher.add_listener(adapter).await
    }

    async fn remove_event_listener(&mut self, listener_id: usize) -> Result<()> {
        self.event_dispatcher.remove_listener(listener_id).await
    }

    async fn get_status(&self) -> Result<DHTStatus> {
        let routing_table = self.routing_table.read().await;
        let query_manager = self.query_manager.read().await;
        let discovered_peers = *self.discovered_peers_count.read().await;

        // 计算IPv6节点数量
        let ipv6_node_count = routing_table.count_ipv6_nodes();

        // 检测是否支持IPv6
        let socket_addr = self.socket.local_addr()?;
        let ipv6_enabled = match socket_addr.ip() {
            IpAddr::V6(_) => true,
            IpAddr::V4(_) => self.config.enable_ipv6,
        };

        Ok(DHTStatus {
            initialized: self.initialized,
            running: self.running,
            node_id: Some(self.node_id.to_string()),
            port: socket_addr.port(),
            node_count: routing_table.node_count(),
            active_queries: query_manager.count(),
            discovered_peers,
            uptime: self.start_time.map_or(Duration::from_secs(0), |t| t.elapsed()),
            ipv6_enabled,
            ipv6_node_count,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use anyhow::Result;
    use async_trait::async_trait;
    use tokio::sync::Mutex;
    use crate::protocols::bittorrent::dht::routing::RoutingTableConfig;

    // 测试事件监听器
    struct TestDHTEventListener {
        events: Arc<Mutex<Vec<DHTEvent>>>,
    }

    impl TestDHTEventListener {
        fn new() -> Self {
            Self {
                events: Arc::new(Mutex::new(Vec::new())),
            }
        }

        async fn get_events(&self) -> Vec<DHTEvent> {
            self.events.lock().await.clone()
        }
    }

    #[async_trait]
    impl DHTEventListener for TestDHTEventListener {
        async fn on_event(&self, event: DHTEvent) -> Result<()> {
            self.events.lock().await.push(event);
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_dht_client_new() {
        let config = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true,
        };

        let client = DHTClient::new(config).await;
        assert!(client.is_ok());

        let client = client.unwrap();
        assert!(!client.initialized);
        assert!(!client.running);
        assert!(client.start_time.is_none());
    }

    #[tokio::test]
    async fn test_dht_client_add_remove_event_listener() {
        let config = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true,
        };

        let mut client = DHTClient::new(config).await.unwrap();

        // 添加事件监听器
        let listener = Arc::new(TestDHTEventListener::new());
        let result = client.add_event_listener(listener.clone()).await;
        assert!(result.is_ok());

        // 验证监听器已添加

        // 移除事件监听器
        let result = client.remove_event_listener(0).await;
        assert!(result.is_ok());

        // 验证监听器已移除

        // 移除不存在的监听器应该失败
        let result = client.remove_event_listener(0).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_dht_client_send_event() {
        let config = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true,
        };

        let mut client = DHTClient::new(config).await.unwrap();

        // 添加事件监听器
        let listener = Arc::new(TestDHTEventListener::new());
        client.add_event_listener(listener.clone()).await.unwrap();

        // 发送事件
        let event = DHTEvent::NodeStateChanged {
            node_count: 10,
            bootstrapped: true,
        };
        client.send_event(event.clone()).await.unwrap();

        // 验证事件已接收
        let received_events = listener.get_events().await;
        assert_eq!(received_events.len(), 1);

        match &received_events[0] {
            DHTEvent::NodeStateChanged { node_count, bootstrapped } => {
                assert_eq!(*node_count, 10);
                assert!(*bootstrapped);
            },
            _ => panic!("Unexpected event type"),
        }
    }

    #[tokio::test]
    async fn test_dht_client_ipv6_support() {
        // 创建启用IPv6的配置
        let config_ipv6 = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: true,
        };

        // 创建禁用IPv6的配置
        let config_no_ipv6 = DHTClientConfig {
            port: 0, // 使用随机端口
            bootstrap_nodes: vec![],
            routing_table_config: RoutingTableConfig::default(),
            query_timeout: 30,
            parallel_queries: 3,
            enable_ipv6: false,
        };

        // 创建两个客户端
        let client_ipv6 = DHTClient::new(config_ipv6).await.unwrap();
        let client_no_ipv6 = DHTClient::new(config_no_ipv6).await.unwrap();

        // 获取状态
        let status_ipv6 = client_ipv6.get_status().await.unwrap();
        let status_no_ipv6 = client_no_ipv6.get_status().await.unwrap();

        // 验证IPv6支持状态
        // 注意：实际的ipv6_enabled值可能取决于系统支持情况
        assert_eq!(status_ipv6.ipv6_node_count, 0); // 初始状态下没有节点
        assert_eq!(status_no_ipv6.ipv6_node_count, 0);

        // 验证socket地址类型
        // 这个测试可能会根据系统支持情况而有所不同
        if status_ipv6.ipv6_enabled {
            println!("System supports IPv6, socket bound to IPv6 address");
        } else {
            println!("System may not support IPv6, socket bound to IPv4 address");
        }

        assert!(!status_no_ipv6.ipv6_enabled, "IPv6 disabled client should report ipv6_enabled as false");
    }
}