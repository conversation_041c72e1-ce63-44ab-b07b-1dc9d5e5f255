use anyhow::{Result, anyhow};
use async_trait::async_trait;
use uuid::Uuid;

use crate::config::ConfigManager;
use std::sync::Arc;
use crate::core::p2p::protocol::{Protocol, ProtocolFactory, ProtocolType};

/// Custom P2P protocol factory
pub struct CustomP2PFactory {
    /// Config Manager
    config_manager: Arc<ConfigManager>,
}

impl CustomP2PFactory {
    /// Create a new custom P2P protocol factory
    pub fn new(config_manager: Arc<ConfigManager>) -> Self {
        Self {
            config_manager,
        }
    }
}

#[async_trait]
impl ProtocolFactory for CustomP2PFactory {
    async fn create_protocol(&self, _url: &str, _output_path: &str, _task_id: Uuid) -> Result<Box<dyn Protocol>> {
        // TODO: Implement custom P2P protocol
        Err(anyhow!("Custom P2P protocol not implemented yet"))
    }

    fn supports_url(&self, url: &str) -> bool {
        url.starts_with("p2p://")
    }

    fn supported_protocol_types(&self) -> Vec<ProtocolType> {
        vec![ProtocolType::CustomP2P]
    }
}
