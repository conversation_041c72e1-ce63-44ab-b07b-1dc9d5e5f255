//! 请求管理器模块
//! 
//! 这个模块负责管理 BitTorrent 协议中的请求，包括下载请求和上传请求。
//! 它处理请求的创建、跟踪、超时和取消等功能。

use std::collections::{HashMap, HashSet};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};

use anyhow::{anyhow, Result};
use tracing::{debug, error, info, trace, warn};

use crate::core::Peer;
use crate::protocols::bittorrent::message::BitTorrentMessage;

/// 请求状态
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RequestStatus {
    /// 等待中
    Pending,
    /// 已完成
    Completed,
    /// 已取消
    Cancelled,
    /// 已超时
    TimedOut,
}

/// 请求信息
#[derive(Debug, Clone)]
pub struct RequestInfo {
    /// 分片索引
    pub piece_index: u32,
    /// 块偏移量
    pub offset: u32,
    /// 块长度
    pub length: u32,
    /// 请求状态
    pub status: RequestStatus,
    /// 请求时间
    pub request_time: Instant,
    /// 对等点地址
    pub peer_addr: SocketAddr,
}

/// 请求管理器
/// 
/// 负责管理 BitTorrent 协议中的请求。
pub struct RequestManager {
    /// 下载请求
    download_requests: HashMap<(u32, u32), RequestInfo>,
    
    /// 上传请求
    upload_requests: HashMap<(SocketAddr, u32, u32), RequestInfo>,
    
    /// 已请求的分片
    requested_pieces: HashSet<u32>,
    
    /// 请求超时时间
    request_timeout: Duration,
    
    /// 种子信息哈希
    info_hash: Option<Vec<u8>>,
}

impl RequestManager {
    /// 创建新的请求管理器
    pub fn new() -> Self {
        Self {
            download_requests: HashMap::new(),
            upload_requests: HashMap::new(),
            requested_pieces: HashSet::new(),
            request_timeout: Duration::from_secs(30),
            info_hash: None,
        }
    }
    
    /// 设置种子信息哈希
    pub fn set_info_hash(&mut self, info_hash: Vec<u8>) {
        self.info_hash = Some(info_hash);
    }
    
    /// 设置请求超时时间
    pub fn set_request_timeout(&mut self, timeout: Duration) {
        self.request_timeout = timeout;
    }
    
    /// 获取已请求的分片
    pub fn get_requested_pieces(&self) -> &HashSet<u32> {
        &self.requested_pieces
    }
    
    /// 添加下载请求
    pub fn add_download_request(
        &mut self,
        piece_index: u32,
        offset: u32,
        length: u32,
        peer_addr: SocketAddr
    ) {
        let key = (piece_index, offset);
        
        // 创建请求信息
        let request_info = RequestInfo {
            piece_index,
            offset,
            length,
            status: RequestStatus::Pending,
            request_time: Instant::now(),
            peer_addr,
        };
        
        // 添加到下载请求
        self.download_requests.insert(key, request_info);
        
        // 添加到已请求分片
        self.requested_pieces.insert(piece_index);
        
        debug!("添加下载请求：分片 {}，偏移量 {}，长度 {}", piece_index, offset, length);
    }
    
    /// 添加上传请求
    pub fn add_upload_request(
        &mut self,
        peer_addr: SocketAddr,
        piece_index: u32,
        offset: u32,
        length: u32
    ) {
        let key = (peer_addr, piece_index, offset);
        
        // 创建请求信息
        let request_info = RequestInfo {
            piece_index,
            offset,
            length,
            status: RequestStatus::Pending,
            request_time: Instant::now(),
            peer_addr,
        };
        
        // 添加到上传请求
        self.upload_requests.insert(key, request_info);
        
        debug!("添加上传请求：对等点 {}，分片 {}，偏移量 {}，长度 {}", peer_addr, piece_index, offset, length);
    }
    
    /// 完成下载请求
    pub fn complete_download_request(
        &mut self,
        piece_index: u32,
        offset: u32
    ) -> Option<RequestInfo> {
        let key = (piece_index, offset);
        
        // 获取请求信息
        if let Some(mut request) = self.download_requests.remove(&key) {
            // 更新状态
            request.status = RequestStatus::Completed;
            
            debug!("完成下载请求：分片 {}，偏移量 {}", piece_index, offset);
            
            // 检查分片是否已完成
            self.check_piece_completion(piece_index);
            
            Some(request)
        } else {
            None
        }
    }
    
    /// 完成上传请求
    pub fn complete_upload_request(
        &mut self,
        peer_addr: SocketAddr,
        piece_index: u32,
        offset: u32
    ) -> Option<RequestInfo> {
        let key = (peer_addr, piece_index, offset);
        
        // 获取请求信息
        if let Some(mut request) = self.upload_requests.remove(&key) {
            // 更新状态
            request.status = RequestStatus::Completed;
            
            debug!("完成上传请求：对等点 {}，分片 {}，偏移量 {}", peer_addr, piece_index, offset);
            
            Some(request)
        } else {
            None
        }
    }
    
    /// 取消下载请求
    pub fn cancel_download_request(
        &mut self,
        piece_index: u32,
        offset: u32
    ) -> Option<RequestInfo> {
        let key = (piece_index, offset);
        
        // 获取请求信息
        if let Some(mut request) = self.download_requests.remove(&key) {
            // 更新状态
            request.status = RequestStatus::Cancelled;
            
            debug!("取消下载请求：分片 {}，偏移量 {}", piece_index, offset);
            
            // 检查分片是否已完成
            self.check_piece_completion(piece_index);
            
            Some(request)
        } else {
            None
        }
    }
    
    /// 取消上传请求
    pub fn cancel_upload_request(
        &mut self,
        peer_addr: SocketAddr,
        piece_index: u32,
        offset: u32
    ) -> Option<RequestInfo> {
        let key = (peer_addr, piece_index, offset);
        
        // 获取请求信息
        if let Some(mut request) = self.upload_requests.remove(&key) {
            // 更新状态
            request.status = RequestStatus::Cancelled;
            
            debug!("取消上传请求：对等点 {}，分片 {}，偏移量 {}", peer_addr, piece_index, offset);
            
            Some(request)
        } else {
            None
        }
    }
    
    /// 检查并处理超时请求
    pub fn check_timeouts(&mut self) {
        let now = Instant::now();
        
        // 检查下载请求超时
        let mut timed_out_downloads = Vec::new();
        for (key, request) in &self.download_requests {
            if now.duration_since(request.request_time) > self.request_timeout {
                timed_out_downloads.push(*key);
            }
        }
        
        // 处理超时的下载请求
        for key in timed_out_downloads {
            if let Some(mut request) = self.download_requests.remove(&key) {
                // 更新状态
                request.status = RequestStatus::TimedOut;
                
                debug!("下载请求超时：分片 {}，偏移量 {}", request.piece_index, request.offset);
                
                // 检查分片是否已完成
                self.check_piece_completion(request.piece_index);
            }
        }
        
        // 检查上传请求超时
        let mut timed_out_uploads = Vec::new();
        for (key, request) in &self.upload_requests {
            if now.duration_since(request.request_time) > self.request_timeout {
                timed_out_uploads.push(*key);
            }
        }
        
        // 处理超时的上传请求
        for key in timed_out_uploads {
            if let Some(mut request) = self.upload_requests.remove(&key) {
                // 更新状态
                request.status = RequestStatus::TimedOut;
                
                debug!("上传请求超时：对等点 {}，分片 {}，偏移量 {}", request.peer_addr, request.piece_index, request.offset);
            }
        }
    }
    
    /// 检查分片是否已完成
    fn check_piece_completion(&mut self, piece_index: u32) {
        // 检查是否还有该分片的下载请求
        let has_pending_requests = self.download_requests.iter()
            .any(|((p, _), _)| *p == piece_index);
        
        // 如果没有待处理的请求，则从已请求分片中移除
        if !has_pending_requests {
            self.requested_pieces.remove(&piece_index);
            debug!("分片 {} 的所有请求已处理完成", piece_index);
        }
    }
    
    /// 发送下载请求
    pub async fn send_download_requests(
        &mut self,
        peers: &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>,
        piece_selection: &[(u32, SocketAddr)],
        block_size: usize
    ) -> Result<()> {
        // 处理每个选择的分片
        for (piece_index, peer_addr) in piece_selection {
            // 获取对等点
            if let Some(peer) = peers.get(peer_addr) {
                // 获取分片大小
                let piece_size = self.get_piece_size(*piece_index);
                
                // 计算块数量
                let num_blocks = (piece_size + block_size - 1) / block_size; // 向上取整
                
                // 请求分片的所有块
                for i in 0..num_blocks {
                    let offset = i * block_size;
                    let length = if i == num_blocks - 1 && piece_size % block_size != 0 {
                        piece_size % block_size
                    } else {
                        block_size
                    };
                    
                    // 创建请求消息
                    let request = BitTorrentMessage::Request(
                        *piece_index,
                        offset as u32,
                        length as u32
                    );
                    
                    // 发送请求
                    let encoded = request.encode(true);
                    match encoded {
                        Ok(ref bytes) => {
                            if let Err(e) = peer.lock().await.send_message(&bytes[..]).await {
                                warn!("向对等点 {} 发送请求失败: {}", peer_addr, e);
                                continue;
                            }
                        }
                        Err(e) => {
                            warn!("请求消息编码失败: {}", e);
                            continue;
                        }
                    }
                    
                    // 添加下载请求
                    self.add_download_request(*piece_index, offset as u32, length as u32, *peer_addr);
                }
            }
        }
        
        Ok(())
    }
    
    /// 处理上传请求
    pub async fn process_upload_requests(
        &mut self,
        peers: &HashMap<SocketAddr, Arc<tokio::sync::Mutex<dyn Peer>>>
    ) -> Result<()> {
        // 获取待处理的上传请求
        let upload_requests: Vec<_> = self.upload_requests.iter()
            .filter(|(_, request)| request.status == RequestStatus::Pending)
            .map(|((addr, piece, offset), request)| (*addr, *piece, *offset, request.length))
            .collect();
        
        // 处理每个上传请求
        for (addr, piece_index, offset, length) in upload_requests {
            if let Some(peer) = peers.get(&addr) {
                let data = vec![0u8; length as usize];
                let piece_message = BitTorrentMessage::Piece(
                    piece_index,
                    offset,
                    data
                );
                let encoded = piece_message.encode(true);
                match encoded {
                    Ok(ref bytes) => {
                        if let Err(e) = peer.lock().await.send_message(&bytes[..]).await {
                            warn!("向对等点 {} 发送分片失败: {}", addr, e);
                            continue;
                        }
                    }
                    Err(e) => {
                        warn!("分片消息编码失败: {}", e);
                        continue;
                    }
                }
                self.complete_upload_request(addr, piece_index, offset);
            }
        }
        Ok(())
    }
    
    /// 获取分片大小
    fn get_piece_size(&self, piece_index: u32) -> usize {
        // 这里应该从种子信息获取分片大小
        // 暂时返回标准大小，实际实现中应该从种子信息中获取
        16384 * 4 // 默认为 64KB
    }
}