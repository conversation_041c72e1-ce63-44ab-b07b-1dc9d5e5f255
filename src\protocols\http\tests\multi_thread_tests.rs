#[cfg(test)]
mod multi_thread_tests {
    use super::super::*;
    use crate::config::{Config<PERSON>anager, Settings};
    use crate::download::resume::ResumeManagerImpl;
    use crate::storage::storage_impl::LocalStorage;
    use std::sync::Arc;
    use uuid::Uuid;

    async fn create_test_downloader_with_multi_thread() -> HttpDownloader {
        let mut settings = Settings::default();
        settings.download.max_concurrent_chunks = Some(4);
        
        let config_manager = Arc::new(ConfigManager::with_settings(settings.clone()));
        let storage = Arc::new(LocalStorage::new(settings.download.path.clone()));
        let resume_manager = Arc::new(ResumeManagerImpl::new(settings, storage));
        
        let task_id = Uuid::new_v4();
        let url = "https://httpbin.org/bytes/10485760".to_string(); // 10MB test file
        let output_path = "test_multi_thread_download.bin".to_string();
        
        HttpDownloader::new(url, output_path, config_manager, task_id)
            .with_resume_manager(resume_manager)
            .with_multi_thread(true, Some(4))
    }

    #[tokio::test]
    async fn test_chunk_manager_initialization() {
        use super::super::chunk_manager::ChunkManager;
        
        let task_id = Uuid::new_v4();
        let total_size = 10 * 1024 * 1024; // 10MB
        let chunk_size = 1024 * 1024; // 1MB
        let max_concurrent = 4;
        let max_retries = 3;
        
        let chunk_manager = ChunkManager::new(
            task_id,
            total_size,
            chunk_size,
            max_concurrent,
            max_retries,
        );
        
        // 初始化分片
        chunk_manager.initialize_chunks().await.unwrap();
        
        // 检查分片数量
        let stats = chunk_manager.get_statistics().await;
        assert_eq!(stats.get("total").unwrap(), &10); // 应该有10个1MB的分片
        assert_eq!(stats.get("pending").unwrap(), &10);
        assert_eq!(stats.get("downloading").unwrap(), &0);
        assert_eq!(stats.get("completed").unwrap(), &0);
        assert_eq!(stats.get("failed").unwrap(), &0);
    }

    #[tokio::test]
    async fn test_chunk_manager_progress_tracking() {
        use super::super::chunk_manager::ChunkManager;
        
        let task_id = Uuid::new_v4();
        let total_size = 5 * 1024 * 1024; // 5MB
        let chunk_size = 1024 * 1024; // 1MB
        let max_concurrent = 2;
        let max_retries = 3;
        
        let chunk_manager = ChunkManager::new(
            task_id,
            total_size,
            chunk_size,
            max_concurrent,
            max_retries,
        );
        
        // 初始化分片
        chunk_manager.initialize_chunks().await.unwrap();
        
        // 获取第一个待下载的分片
        let chunk = chunk_manager.get_next_pending_chunk().await.unwrap();
        assert_eq!(chunk.index, 0);
        assert_eq!(chunk.start, 0);
        assert_eq!(chunk.end, chunk_size - 1);
        
        // 标记为正在下载
        chunk_manager.mark_chunk_downloading(chunk.index).await.unwrap();
        
        // 更新进度
        chunk_manager.update_chunk_progress(chunk.index, 512 * 1024, 1024 * 1024).await.unwrap();
        
        // 标记为完成
        chunk_manager.mark_chunk_completed(chunk.index, None).await.unwrap();
        
        // 检查统计
        let stats = chunk_manager.get_statistics().await;
        assert_eq!(stats.get("completed").unwrap(), &1);
        assert_eq!(stats.get("pending").unwrap(), &4);
    }

    #[tokio::test]
    async fn test_performance_stats() {
        use super::super::performance_optimizer::PerformanceStats;
        use std::time::Duration;
        
        let mut stats = PerformanceStats::default();
        
        // 添加速度数据
        stats.add_speed(1024 * 1024); // 1MB/s
        stats.add_speed(2 * 1024 * 1024); // 2MB/s
        stats.add_speed(1536 * 1024); // 1.5MB/s
        
        // 检查平均速度
        let avg_speed = stats.average_speed();
        assert!(avg_speed > 1024 * 1024); // 应该大于1MB/s
        assert!(avg_speed < 2 * 1024 * 1024); // 应该小于2MB/s
        
        // 添加延迟数据
        stats.add_latency(Duration::from_millis(100));
        stats.add_latency(Duration::from_millis(150));
        stats.add_latency(Duration::from_millis(120));
        
        // 检查平均延迟
        let avg_latency = stats.average_latency();
        assert!(avg_latency.as_millis() >= 100);
        assert!(avg_latency.as_millis() <= 150);
    }

    #[tokio::test]
    async fn test_metadata_management() {
        use super::super::metadata_manager::DownloadMetadata;
        
        let task_id = Uuid::new_v4();
        let url = "https://example.com/test.zip".to_string();
        
        let mut metadata = DownloadMetadata::new(task_id, url.clone());
        
        // 测试基本信息
        assert_eq!(metadata.task_id, task_id);
        assert_eq!(metadata.url, url);
        assert_eq!(metadata.retry_count, 0);
        
        // 测试速度统计更新
        metadata.update_speed_stats(1024 * 1024); // 1MB/s
        assert_eq!(metadata.average_speed, 1024 * 1024);
        assert_eq!(metadata.peak_speed, 1024 * 1024);
        
        metadata.update_speed_stats(2 * 1024 * 1024); // 2MB/s
        assert_eq!(metadata.peak_speed, 2 * 1024 * 1024);
        assert!(metadata.average_speed > 1024 * 1024);
        
        // 测试错误记录
        metadata.add_error("Connection timeout".to_string());
        assert_eq!(metadata.retry_count, 1);
        assert_eq!(metadata.error_history.len(), 1);
        
        // 测试自定义元数据
        metadata.set_custom_metadata("user_agent".to_string(), "test-agent".to_string());
        assert_eq!(metadata.get_custom_metadata("user_agent").unwrap(), "test-agent");
    }

    #[tokio::test]
    async fn test_network_quality_assessment() {
        use super::super::performance_optimizer::{PerformanceStats, NetworkQuality};
        
        let downloader = create_test_downloader_with_multi_thread().await;
        
        // 测试优秀网络质量
        let mut excellent_stats = PerformanceStats::default();
        excellent_stats.add_speed(20 * 1024 * 1024); // 20MB/s
        excellent_stats.add_latency(std::time::Duration::from_millis(30)); // 30ms
        excellent_stats.add_error_rate(0.005); // 0.5%
        
        let quality = downloader.assess_network_quality(&excellent_stats).await;
        assert_eq!(quality, NetworkQuality::Excellent);
        
        // 测试较差网络质量
        let mut poor_stats = PerformanceStats::default();
        poor_stats.add_speed(100 * 1024); // 100KB/s
        poor_stats.add_latency(std::time::Duration::from_millis(800)); // 800ms
        poor_stats.add_error_rate(0.25); // 25%
        
        let quality = downloader.assess_network_quality(&poor_stats).await;
        assert_eq!(quality, NetworkQuality::VeryPoor);
    }

    #[tokio::test]
    async fn test_adaptive_chunk_size() {
        use super::super::performance_optimizer::PerformanceStats;
        
        let mut downloader = create_test_downloader_with_multi_thread().await;
        let initial_chunk_size = downloader.chunk_size;
        
        // 模拟高速网络
        let mut high_speed_stats = PerformanceStats::default();
        for _ in 0..10 {
            high_speed_stats.add_speed(15 * 1024 * 1024); // 15MB/s
        }
        
        // 调整块大小
        downloader.adaptive_chunk_size_adjustment(&high_speed_stats).await.unwrap();
        
        // 高速网络应该增加块大小
        assert!(downloader.chunk_size >= initial_chunk_size);
        
        // 模拟低速网络
        let mut low_speed_stats = PerformanceStats::default();
        for _ in 0..10 {
            low_speed_stats.add_speed(500 * 1024); // 500KB/s
        }
        
        // 重置块大小
        downloader.chunk_size = initial_chunk_size;
        
        // 调整块大小
        downloader.adaptive_chunk_size_adjustment(&low_speed_stats).await.unwrap();
        
        // 低速网络应该减少块大小
        assert!(downloader.chunk_size <= initial_chunk_size);
    }

    #[tokio::test]
    async fn test_smart_retry_strategy() {
        let downloader = create_test_downloader_with_multi_thread().await;
        
        // 测试网络超时错误
        let (delay, should_retry) = downloader.smart_retry_strategy(2, "connection timeout").await;
        assert!(should_retry);
        assert!(delay.as_secs() >= 1);
        
        // 测试404错误
        let (_, should_retry) = downloader.smart_retry_strategy(1, "404 Not Found").await;
        assert!(!should_retry); // 不应该重试客户端错误
        
        // 测试过多重试
        let (_, should_retry) = downloader.smart_retry_strategy(10, "network error").await;
        assert!(!should_retry); // 重试次数过多
    }

    #[tokio::test]
    async fn test_download_report_generation() {
        use super::super::metadata_manager::DownloadMetadata;
        
        let task_id = Uuid::new_v4();
        let url = "https://example.com/large_file.zip".to_string();
        
        let mut metadata = DownloadMetadata::new(task_id, url.clone());
        metadata.filename = "large_file.zip".to_string();
        metadata.file_size = Some(100 * 1024 * 1024); // 100MB
        metadata.mime_type = Some("application/zip".to_string());
        metadata.supports_range = true;
        metadata.update_speed_stats(5 * 1024 * 1024); // 5MB/s
        metadata.add_error("Temporary network issue".to_string());
        
        let report = metadata.generate_report();
        
        // 检查报告内容
        assert!(report.contains(&task_id.to_string()));
        assert!(report.contains("large_file.zip"));
        assert!(report.contains("100.00 MB"));
        assert!(report.contains("application/zip"));
        assert!(report.contains("Supports Range Requests: true"));
        assert!(report.contains("Error History:"));
        assert!(report.contains("Temporary network issue"));
    }
}
