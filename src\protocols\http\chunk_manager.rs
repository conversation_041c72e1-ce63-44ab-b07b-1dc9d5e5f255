//! HTTP下载器分片管理模块

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Instant;
use anyhow::Result;
use tokio::sync::{Mute<PERSON>, RwLock};
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use tracing::{debug, error, warn, info};
use uuid::Uuid;

use crate::download::resume::ChunkInfo;

/// 分片状态
#[derive(Debug, Clone, PartialEq)]
pub enum ChunkStatus {
    /// 等待下载
    Pending,
    /// 正在下载
    Downloading,
    /// 下载完成
    Completed,
    /// 下载失败
    Failed,
    /// 已验证
    Verified,
}

/// 分片下载信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct ChunkDownloadInfo {
    /// 分片索引
    pub index: usize,
    /// 起始位置
    pub start: u64,
    /// 结束位置
    pub end: u64,
    /// 当前状态
    pub status: ChunkStatus,
    /// 已下载大小
    pub downloaded_size: u64,
    /// 下载速度
    pub speed: u64,
    /// 重试次数
    pub retry_count: u32,
    /// 最后更新时间
    pub last_update: Instant,
    /// 校验和
    pub checksum: Option<String>,
    /// 错误信息
    pub error_message: Option<String>,
}

impl ChunkDownloadInfo {
    pub fn new(index: usize, start: u64, end: u64) -> Self {
        Self {
            index,
            start,
            end,
            status: ChunkStatus::Pending,
            downloaded_size: 0,
            speed: 0,
            retry_count: 0,
            last_update: Instant::now(),
            checksum: None,
            error_message: None,
        }
    }

    /// 获取分片大小
    pub fn size(&self) -> u64 {
        self.end - self.start + 1
    }

    /// 检查是否完成
    pub fn is_completed(&self) -> bool {
        self.status == ChunkStatus::Completed || self.status == ChunkStatus::Verified
    }

    /// 检查是否可以重试
    pub fn can_retry(&self, max_retries: u32) -> bool {
        self.status == ChunkStatus::Failed && self.retry_count < max_retries
    }

    /// 更新进度
    pub fn update_progress(&mut self, downloaded: u64, speed: u64) {
        self.downloaded_size = downloaded;
        self.speed = speed;
        self.last_update = Instant::now();
    }

    /// 标记为失败
    pub fn mark_failed(&mut self, error: String) {
        self.status = ChunkStatus::Failed;
        self.error_message = Some(error);
        self.retry_count += 1;
        self.last_update = Instant::now();
    }

    /// 标记为完成
    pub fn mark_completed(&mut self, checksum: Option<String>) {
        self.status = ChunkStatus::Completed;
        self.checksum = checksum;
        self.last_update = Instant::now();
    }
}

/// 分片下载管理器
pub struct ChunkManager {
    /// 任务ID
    task_id: Uuid,
    /// 文件总大小
    total_size: u64,
    /// 分片大小
    chunk_size: u64,
    /// 最大并发数
    max_concurrent: usize,
    /// 最大重试次数
    max_retries: u32,
    /// 分片信息
    chunks: Arc<RwLock<HashMap<usize, ChunkDownloadInfo>>>,
    /// 活动的下载任务
    active_downloads: Arc<Mutex<HashMap<usize, JoinHandle<Result<()>>>>>,
    /// 是否暂停
    is_paused: Arc<RwLock<bool>>,
    /// 是否取消
    is_cancelled: Arc<RwLock<bool>>,
}

impl ChunkManager {
    /// 创建新的分片管理器
    pub fn new(
        task_id: Uuid,
        total_size: u64,
        chunk_size: u64,
        max_concurrent: usize,
        max_retries: u32,
    ) -> Self {
        Self {
            task_id,
            total_size,
            chunk_size,
            max_concurrent,
            max_retries,
            chunks: Arc::new(RwLock::new(HashMap::new())),
            active_downloads: Arc::new(Mutex::new(HashMap::new())),
            is_paused: Arc::new(RwLock::new(false)),
            is_cancelled: Arc::new(RwLock::new(false)),
        }
    }

    /// 初始化分片
    pub async fn initialize_chunks(&self) -> Result<()> {
        let mut chunks = self.chunks.write().await;
        chunks.clear();

        let mut current_offset = 0;
        let mut chunk_index = 0;

        while current_offset < self.total_size {
            let end_offset = std::cmp::min(current_offset + self.chunk_size - 1, self.total_size - 1);
            let chunk_info = ChunkDownloadInfo::new(chunk_index, current_offset, end_offset);
            
            chunks.insert(chunk_index, chunk_info);
            
            current_offset = end_offset + 1;
            chunk_index += 1;
        }

        info!("Initialized {} chunks for task {}", chunks.len(), self.task_id);
        Ok(())
    }

    /// 从恢复点加载分片状态
    pub async fn load_from_resume_point(&self, chunk_infos: &[ChunkInfo]) -> Result<()> {
        let mut chunks = self.chunks.write().await;
        
        for chunk_info in chunk_infos {
            if let Some(chunk) = chunks.get_mut(&chunk_info.index) {
                chunk.status = if chunk_info.downloaded {
                    ChunkStatus::Completed
                } else {
                    ChunkStatus::Pending
                };
                chunk.checksum = chunk_info.checksum.clone();
                
                if chunk_info.downloaded {
                    chunk.downloaded_size = chunk.size();
                }
            }
        }

        debug!("Loaded chunk states from resume point for task {}", self.task_id);
        Ok(())
    }

    /// 获取下一个待下载的分片
    pub async fn get_next_pending_chunk(&self) -> Option<ChunkDownloadInfo> {
        let chunks = self.chunks.read().await;
        
        // 首先查找失败但可以重试的分片
        for chunk in chunks.values() {
            if chunk.can_retry(self.max_retries) {
                return Some(chunk.clone());
            }
        }
        
        // 然后查找待下载的分片
        for chunk in chunks.values() {
            if chunk.status == ChunkStatus::Pending {
                return Some(chunk.clone());
            }
        }
        
        None
    }

    /// 标记分片为正在下载
    pub async fn mark_chunk_downloading(&self, chunk_index: usize) -> Result<()> {
        let mut chunks = self.chunks.write().await;
        if let Some(chunk) = chunks.get_mut(&chunk_index) {
            chunk.status = ChunkStatus::Downloading;
            chunk.last_update = Instant::now();
        }
        Ok(())
    }

    /// 更新分片进度
    pub async fn update_chunk_progress(&self, chunk_index: usize, downloaded: u64, speed: u64) -> Result<()> {
        let mut chunks = self.chunks.write().await;
        if let Some(chunk) = chunks.get_mut(&chunk_index) {
            chunk.update_progress(downloaded, speed);
        }
        Ok(())
    }

    /// 标记分片完成
    pub async fn mark_chunk_completed(&self, chunk_index: usize, checksum: Option<String>) -> Result<()> {
        let mut chunks = self.chunks.write().await;
        if let Some(chunk) = chunks.get_mut(&chunk_index) {
            chunk.mark_completed(checksum);
        }
        Ok(())
    }

    /// 标记分片失败
    pub async fn mark_chunk_failed(&self, chunk_index: usize, error: String) -> Result<()> {
        let mut chunks = self.chunks.write().await;
        if let Some(chunk) = chunks.get_mut(&chunk_index) {
            chunk.mark_failed(error);
        }
        Ok(())
    }

    /// 获取总体进度
    pub async fn get_progress(&self) -> (u64, u64, f64) {
        let chunks = self.chunks.read().await;
        let mut downloaded = 0;
        let mut total = 0;

        for chunk in chunks.values() {
            downloaded += chunk.downloaded_size;
            total += chunk.size();
        }

        let percentage = if total > 0 {
            (downloaded as f64 / total as f64) * 100.0
        } else {
            0.0
        };

        (downloaded, total, percentage)
    }

    /// 获取下载速度
    pub async fn get_speed(&self) -> u64 {
        let chunks = self.chunks.read().await;
        chunks.values()
            .filter(|chunk| chunk.status == ChunkStatus::Downloading)
            .map(|chunk| chunk.speed)
            .sum()
    }

    /// 检查是否所有分片都已完成
    pub async fn is_all_completed(&self) -> bool {
        let chunks = self.chunks.read().await;
        chunks.values().all(|chunk| chunk.is_completed())
    }

    /// 获取活动下载数量
    pub async fn get_active_downloads_count(&self) -> usize {
        let active = self.active_downloads.lock().await;
        active.len()
    }

    /// 检查是否可以启动新的下载
    pub async fn can_start_new_download(&self) -> bool {
        let active_count = self.get_active_downloads_count().await;
        active_count < self.max_concurrent && !*self.is_paused.read().await && !*self.is_cancelled.read().await
    }

    /// 暂停所有下载
    pub async fn pause(&self) {
        *self.is_paused.write().await = true;
        debug!("Paused chunk downloads for task {}", self.task_id);
    }

    /// 恢复所有下载
    pub async fn resume(&self) {
        *self.is_paused.write().await = false;
        debug!("Resumed chunk downloads for task {}", self.task_id);
    }

    /// 取消所有下载
    pub async fn cancel(&self) {
        *self.is_cancelled.write().await = true;
        
        // 取消所有活动的下载任务
        let mut active = self.active_downloads.lock().await;
        for (chunk_index, handle) in active.drain() {
            handle.abort();
            debug!("Cancelled chunk download {}", chunk_index);
        }
        
        debug!("Cancelled all chunk downloads for task {}", self.task_id);
    }

    /// 获取分片信息用于保存恢复点
    pub async fn get_chunk_infos(&self) -> Vec<ChunkInfo> {
        let chunks = self.chunks.read().await;
        chunks.values()
            .map(|chunk| ChunkInfo {
                index: chunk.index,
                start: chunk.start,
                end: chunk.end,
                downloaded: chunk.is_completed(),
                checksum: chunk.checksum.clone(),
            })
            .collect()
    }

    /// 添加活动下载任务
    pub async fn add_active_download(&self, chunk_index: usize, handle: JoinHandle<Result<()>>) {
        let mut active = self.active_downloads.lock().await;
        active.insert(chunk_index, handle);
    }

    /// 移除活动下载任务
    pub async fn remove_active_download(&self, chunk_index: usize) {
        let mut active = self.active_downloads.lock().await;
        active.remove(&chunk_index);
    }

    /// 获取统计信息
    pub async fn get_statistics(&self) -> HashMap<String, u64> {
        let chunks = self.chunks.read().await;
        let mut stats = HashMap::new();
        
        let mut pending = 0;
        let mut downloading = 0;
        let mut completed = 0;
        let mut failed = 0;
        
        for chunk in chunks.values() {
            match chunk.status {
                ChunkStatus::Pending => pending += 1,
                ChunkStatus::Downloading => downloading += 1,
                ChunkStatus::Completed | ChunkStatus::Verified => completed += 1,
                ChunkStatus::Failed => failed += 1,
            }
        }
        
        stats.insert("total".to_string(), chunks.len() as u64);
        stats.insert("pending".to_string(), pending);
        stats.insert("downloading".to_string(), downloading);
        stats.insert("completed".to_string(), completed);
        stats.insert("failed".to_string(), failed);
        
        stats
    }
}
