use anyhow::Result;
use std::path::Path;
use tokio::fs;

use tonitru_downloader::protocols::bittorrent::torrent::{parse_torrent_file, parse_magnet_link};

/// Test parsing a valid torrent file
#[tokio::test]
async fn test_parse_valid_torrent_file() -> Result<()> {
    // Use a test torrent file from the tests directory
    let torrent_path = "tests/files/ubuntu.torrent";

    // Ensure the test file exists
    if !Path::new(torrent_path).exists() {
        // Create a simple test torrent file if it doesn't exist
        // This is just a placeholder - in a real test, you'd use a real torrent file
        fs::write(torrent_path, include_bytes!("files/test_file.txt")).await?;
    }

    // Parse the torrent file
    let torrent_info = parse_torrent_file(torrent_path).await?;

    // Verify basic torrent information
    assert!(!torrent_info.name.is_empty(), "Torrent name should not be empty");
    assert!(torrent_info.total_size > 0, "Torrent size should be greater than 0");
    assert!(torrent_info.piece_length > 0, "Piece length should be greater than 0");
    assert!(!torrent_info.pieces.is_empty(), "Pieces should not be empty");
    assert!(!torrent_info.info_hash.is_empty(), "Info hash should not be empty");
    assert!(!torrent_info.info_hash_hex.is_empty(), "Info hash hex should not be empty");

    Ok(())
}

/// Test parsing a magnet link
#[tokio::test]
async fn test_parse_magnet_link() -> Result<()> {
    // A sample magnet link
    let magnet_link = "magnet:?xt=urn:btih:dd8255ecdc7ca55fb0bbf81323d87062db1f6d1c&dn=Big+Buck+Bunny&tr=udp%3A%2F%2Fexplodie.org%3A6969&tr=udp%3A%2F%2Ftracker.coppersurfer.tk%3A6969&tr=udp%3A%2F%2Ftracker.empire-js.us%3A1337&tr=udp%3A%2F%2Ftracker.leechers-paradise.org%3A6969&tr=udp%3A%2F%2Ftracker.opentrackr.org%3A1337&tr=wss%3A%2F%2Ftracker.btorrent.xyz&tr=wss%3A%2F%2Ftracker.fastcast.nz&tr=wss%3A%2F%2Ftracker.openwebtorrent.com&ws=https%3A%2F%2Fwebtorrent.io%2Ftorrents%2F&xs=https%3A%2F%2Fwebtorrent.io%2Ftorrents%2Fbig-buck-bunny.torrent";

    // Parse the magnet link
    let torrent_info = parse_magnet_link(magnet_link)?;

    // Verify basic torrent information
    assert_eq!(torrent_info.name, "Big Buck Bunny", "Torrent name should match");
    assert_eq!(torrent_info.info_hash_hex.to_lowercase(), "dd8255ecdc7ca55fb0bbf81323d87062db1f6d1c", "Info hash should match");

    // Verify trackers
    assert!(torrent_info.announce_list.is_some(), "Announce list should be present");
    if let Some(announce_list) = &torrent_info.announce_list {
        assert!(!announce_list.is_empty(), "Announce list should not be empty");

        // Check if at least one tracker is present
        let has_tracker = announce_list.iter().any(|tier| {
            tier.iter().any(|tracker| tracker.contains("tracker"))
        });
        assert!(has_tracker, "At least one tracker should be present");
    }

    // Verify web seeds
    assert!(torrent_info.url_list.is_some(), "URL list should be present");
    if let Some(url_list) = &torrent_info.url_list {
        assert!(!url_list.is_empty(), "URL list should not be empty");
        assert!(url_list.iter().any(|url| url.contains("webtorrent.io")), "URL list should contain webtorrent.io");
    }

    Ok(())
}

/// Test parsing an invalid magnet link
#[tokio::test]
async fn test_parse_invalid_magnet_link() -> Result<()> {
    // An invalid magnet link (missing hash)
    let invalid_magnet = "magnet:?dn=test&tr=udp://tracker.example.com:6969";

    // Parse should fail
    let result = parse_magnet_link(invalid_magnet);
    assert!(result.is_err(), "Parsing invalid magnet link should fail");

    Ok(())
}

/// Test parsing a magnet link with minimal information
#[tokio::test]
async fn test_parse_minimal_magnet_link() -> Result<()> {
    // A minimal magnet link with just the hash
    let minimal_magnet = "magnet:?xt=urn:btih:dd8255ecdc7ca55fb0bbf81323d87062db1f6d1c";

    // Parse the magnet link
    let torrent_info = parse_magnet_link(minimal_magnet)?;

    // Verify basic torrent information
    assert_eq!(torrent_info.info_hash_hex.to_lowercase(), "dd8255ecdc7ca55fb0bbf81323d87062db1f6d1c", "Info hash should match");
    assert!(torrent_info.name.is_empty(), "Name should be empty for minimal magnet link");
    assert!(torrent_info.announce_list.is_none() || torrent_info.announce_list.as_ref().unwrap().is_empty(),
           "Announce list should be empty for minimal magnet link");

    Ok(())
}

/// Test parsing a magnet link with base32 encoded hash
#[tokio::test]
async fn test_parse_base32_magnet_link() -> Result<()> {
    // A magnet link with base32 encoded hash
    let base32_magnet = "magnet:?xt=urn:btih:MFRGGZDFMZTWQ2LKNNWG23TPOBYXE43UOVZGC3TH&dn=Test";

    // Parse the magnet link
    let torrent_info = parse_magnet_link(base32_magnet)?;

    // Verify the hash was correctly decoded
    assert!(!torrent_info.info_hash.is_empty(), "Info hash should not be empty");
    assert!(!torrent_info.info_hash_hex.is_empty(), "Info hash hex should not be empty");
    assert_eq!(torrent_info.name, "Test", "Name should match");

    Ok(())
}
