use anyhow::{Result, anyhow};
use async_trait::async_trait;
use std::sync::Arc;
use tracing::{error, warn};
use uuid::Uuid;
use tokio::time::{interval, Duration};

use crate::api::events::EventManager;
use crate::config::{Settings, ConfigManager};
use crate::core::interfaces::storage::Storage;
use crate::download::manager::{DownloadManager, TaskInfo, TaskStatus};
use crate::download::downloader_factory_impl::DownloaderFactoryImpl;
use crate::download::task_manager::{TaskManager, TaskManagerImpl};
use crate::download::event_notifier::{EventNotifier, EventNotifierImpl};
use crate::download::resume::ResumeManagerImpl;
use crate::download::bandwidth_scheduler::{BandwidthScheduler};
use crate::analyzer::LinkAnalyzer;

use crate::core::interfaces::downloader::Downloader;
use crate::core::interfaces::DownloaderFactory;

/// WebSocket下载管理器
pub struct WebSocketDownloadManager {
    task_manager: Arc<dyn TaskManager>,
    event_notifier: Arc<dyn EventNotifier>,
    bandwidth_scheduler: Arc<dyn BandwidthScheduler>,
    downloader_factory: Arc<dyn DownloaderFactory>,
    link_analyzer: Arc<dyn LinkAnalyzer>,
}

impl WebSocketDownloadManager {
    /// 创建新的WebSocket下载管理器
    pub fn new(
        settings: Settings,
        link_analyzer: Arc<dyn LinkAnalyzer>,
        event_manager: EventManager,
        storage: Arc<dyn Storage>,
        bandwidth_scheduler: Arc<dyn BandwidthScheduler>,
    ) -> Self {
        // 创建恢复管理器
        let resume_manager = Arc::new(ResumeManagerImpl::new(settings.clone(), storage));

        // 初始化恢复管理器
        let resume_manager_clone = resume_manager.clone();
        tokio::spawn(async move {
            if let Err(e) = resume_manager_clone.init().await {
                error!("Failed to initialize resume manager: {}", e);
            }
        });

        // 创建下载器工厂
        let downloader_factory = Arc::new(DownloaderFactoryImpl::new(
            Arc::new(ConfigManager::with_settings(settings.clone())),
            link_analyzer.clone(),
            resume_manager,
        ).with_bandwidth_scheduler(bandwidth_scheduler.clone()));

        // 创建任务管理器
        let task_manager = Arc::new(TaskManagerImpl::new(
            settings,
            downloader_factory.clone(),
        ));

        // 创建事件通知器
        let event_notifier = Arc::new(EventNotifierImpl::new(event_manager));

        let manager = Self {
            task_manager,
            event_notifier,
            bandwidth_scheduler,
            downloader_factory,
            link_analyzer: link_analyzer.clone(),
        };

        // 启动全局速度监控
        manager.start_global_speed_monitor();

        manager
    }

    /// 启动全局速度监控
    fn start_global_speed_monitor(&self) {
        let bandwidth_scheduler = self.bandwidth_scheduler.clone();
        let event_notifier = self.event_notifier.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(1));

            loop {
                interval.tick().await;

                // 获取全局带宽统计
                match bandwidth_scheduler.get_global_stats().await {
                    Ok(stats) => {
                        // 通知全局速度
                        if let Err(e) = event_notifier.notify_global_speed(stats.download_rate, stats.upload_rate).await {
                            warn!("Failed to notify global speed: {}", e);
                        }
                    },
                    Err(e) => {
                        warn!("Failed to get global bandwidth stats: {}", e);
                    }
                }
            }
        });
    }
}

#[async_trait]
impl DownloadManager for WebSocketDownloadManager {
    async fn add_task(&self, task: TaskInfo) -> Result<Uuid> {
        // 添加任务
        let task_id = self.task_manager.add_task(task.url.clone(), Some(task.output_path.clone())).await?;

        // 获取任务信息
        let task = self.task_manager.get_task_info(task_id).await?;

        // 通知任务添加
        self.event_notifier.notify_task_added(&task).await?;

        Ok(task_id)
    }

    async fn start_task(&self, task_id: Uuid) -> Result<()> {
        // 启动任务
        self.task_manager.start_task(task_id).await?;

        // 获取任务信息
        let task = self.task_manager.get_task_info(task_id).await?;

        // 通知任务开始
        self.event_notifier.notify_task_started(&task).await?;

        Ok(())
    }

    async fn pause_task(&self, task_id: Uuid) -> Result<()> {
        // 暂停任务
        self.task_manager.pause_task(task_id).await?;

        // 获取任务信息
        let task = self.task_manager.get_task_info(task_id).await?;

        // 通知任务暂停
        self.event_notifier.notify_task_paused(&task).await?;

        Ok(())
    }

    async fn resume_task(&self, task_id: Uuid) -> Result<()> {
        // 恢复任务
        self.task_manager.resume_task(task_id).await?;

        // 获取任务信息
        let task = self.task_manager.get_task_info(task_id).await?;

        // 通知任务恢复
        self.event_notifier.notify_task_resumed(&task).await?;

        Ok(())
    }

    async fn cancel_task(&self, task_id: Uuid) -> Result<()> {
        // 取消任务
        self.task_manager.cancel_task(task_id).await?;

        // 获取任务信息
        let task = self.task_manager.get_task_info(task_id).await?;

        // 通知任务取消
        self.event_notifier.notify_task_cancelled(&task).await?;

        Ok(())
    }

    async fn get_task(&self, task_id: Uuid) -> Result<TaskInfo> {
        self.task_manager.get_task_info(task_id).await
    }
    
    async fn update_task(&self, task_id: Uuid, task: TaskInfo) -> Result<()> {
        // 获取当前任务
        let current_task = self.task_manager.get_task_info(task_id).await?;
        
        // 保存旧状态用于通知
        let old_status = current_task.status;
        
        // 如果状态不同，更新状态
        if current_task.status != task.status {
            // 这里我们可能需要根据状态调用不同的方法
            match task.status {
                TaskStatus::Paused => self.task_manager.pause_task(task_id).await?,
                TaskStatus::Downloading => {
                    if current_task.status == TaskStatus::Paused {
                        self.task_manager.resume_task(task_id).await?
                    }
                },
                TaskStatus::Cancelled => self.task_manager.cancel_task(task_id).await?,
                TaskStatus::Completed => {
                    self.task_manager.complete_task(task_id).await?;
                }
                TaskStatus::Error => {
                    self.task_manager.fail_task(task_id, "Download failed".to_string()).await?;
                }
                _ => {}
            }
            
            // 获取更新后的任务信息
            let updated_task = self.task_manager.get_task_info(task_id).await?;
            
            // 通知任务状态变更
            self.event_notifier.notify_task_status_changed(&updated_task, old_status).await?;
        }
        
        Ok(())
    }
    
    // 修改 create_downloader 方法的返回类型
    async fn create_downloader(&self, url: &str, output_path: &str, task_id: Uuid) -> Result<Box<dyn Downloader>> {
        // 分析URL协议类型
        let protocol_str = self.link_analyzer.analyze(url).await?;
        
        // 根据协议类型创建对应的下载器
        match protocol_str.as_str() {
            "http" | "https" => {
                // 创建 HTTP 下载器
                let mut http_downloader = crate::protocols::http::HttpDownloader::new(
                    url.to_string(),
                    output_path.to_string(),
                    // 从 task_manager 获取 settings 并转换为 Arc<ConfigManager>
                    Arc::new(ConfigManager::with_settings(self.task_manager.get_settings().clone())),
                    task_id,
                );
                
                // 设置带宽调度器
                http_downloader = http_downloader.with_bandwidth_scheduler(self.bandwidth_scheduler.clone());
                
                Ok(Box::new(http_downloader))
            },
            // 其他协议类型的处理可以根据需要添加
            _ => Err(anyhow!("Unsupported protocol: {}", protocol_str))
        }
    }

    async fn get_all_tasks(&self) -> Result<Vec<TaskInfo>> {
        // 获取所有任务
        let tasks = self.task_manager.get_all_tasks().await?;

        // 计算任务状态统计
        let active_count = tasks.iter().filter(|t| t.status == TaskStatus::Downloading || t.status == TaskStatus::Initializing).count();
        let waiting_count = tasks.iter().filter(|t| t.status == TaskStatus::Pending).count();
        let stopped_count = tasks.iter().filter(|t| t.status == TaskStatus::Completed || t.status == TaskStatus::Failed || t.status == TaskStatus::Cancelled).count();

        // 通知全局状态
        self.event_notifier.notify_global_status(active_count, waiting_count, stopped_count).await?;

        Ok(tasks)
    }

    async fn remove_task(&self, task_id: Uuid) -> Result<()> {
        // 获取任务信息（用于通知）
        let task = match self.task_manager.get_task_info(task_id).await {
            Ok(t) => Some(t),
            Err(e) => {
                error!("Failed to get task info before removal: {}", e);
                None
            }
        };
        
        // 删除任务
        let result = self.task_manager.remove_task(task_id).await;
        
        // 如果删除成功且有任务信息，发送通知
        if result.is_ok() && task.is_some() {
            if let Err(e) = self.event_notifier.notify_task_removed(&task.unwrap()).await {
                error!("Failed to send task removed notification: {}", e);
            }
        }
        
        result
    }

    async fn get_download_stats(&self) -> crate::download::manager::DownloadStats {
        self.task_manager.get_download_stats().await
    }
}
